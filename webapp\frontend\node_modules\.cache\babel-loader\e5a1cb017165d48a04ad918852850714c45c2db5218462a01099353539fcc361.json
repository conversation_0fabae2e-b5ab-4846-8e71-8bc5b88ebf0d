{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Typography,Button,Paper,Grid,Card,CardContent,CardActions,Dialog,DialogTitle,DialogContent,DialogActions,TextField,FormControl,InputLabel,Select,MenuItem,List,ListItem,ListItemText,ListItemIcon,ListItemButton,Divider,Alert,CircularProgress,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,IconButton}from'@mui/material';import{Add as AddIcon,Edit as EditIcon,Delete as DeleteIcon,Save as SaveIcon,Search as SearchIcon,Print as PrintIcon,ViewList as ViewListIcon,Assignment as AssignmentIcon}from'@mui/icons-material';import comandeService from'../../services/comandeService';import caviService from'../../services/caviService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const GestioneComande=_ref=>{let{cantiereId,onSuccess,onError}=_ref;const[loading,setLoading]=useState(false);const[comande,setComande]=useState([]);const[cavi,setCavi]=useState([]);const[selectedOption,setSelectedOption]=useState(null);const[openDialog,setOpenDialog]=useState(false);const[dialogType,setDialogType]=useState('');const[selectedComanda,setSelectedComanda]=useState(null);const[selectedCavo,setSelectedCavo]=useState(null);const[formData,setFormData]=useState({numero_comanda:'',data_comanda:'',tipo_comanda:'POSA',id_cavo:'',operatore:'',note:''});// Carica le comande\nconst loadComande=async()=>{try{setLoading(true);const data=await comandeService.getComande(cantiereId);setComande(data);}catch(error){onError('Errore nel caricamento delle comande');console.error('Errore nel caricamento delle comande:',error);}finally{setLoading(false);}};// Carica i cavi disponibili\nconst loadCavi=async()=>{try{setLoading(true);const data=await caviService.getCavi(cantiereId);setCavi(data);}catch(error){onError('Errore nel caricamento dei cavi');console.error('Errore nel caricamento dei cavi:',error);}finally{setLoading(false);}};// Carica i dati all'avvio del componente\nuseEffect(()=>{loadComande();},[cantiereId]);// Gestisce la selezione di un'opzione dal menu\nconst handleOptionSelect=option=>{setSelectedOption(option);if(option==='visualizzaComande'){loadComande();}else if(option==='creaComanda'){loadCavi();setDialogType('creaComanda');// Imposta la data di oggi come default\nconst today=new Date().toISOString().split('T')[0];setFormData({...formData,data_comanda:today});setOpenDialog(true);}else if(option==='modificaComanda'){loadComande();setDialogType('selezionaComanda');setOpenDialog(true);}else if(option==='eliminaComanda'){loadComande();setDialogType('eliminaComanda');setOpenDialog(true);}else if(option==='stampaComanda'){loadComande();setDialogType('stampaComanda');setOpenDialog(true);}else if(option==='assegnaComanda'){loadComande();loadCavi();setDialogType('selezionaCavoComanda');setOpenDialog(true);}};// Gestisce la chiusura del dialog\nconst handleCloseDialog=()=>{setOpenDialog(false);setSelectedComanda(null);setSelectedCavo(null);setFormData({numero_comanda:'',data_comanda:'',tipo_comanda:'POSA',id_cavo:'',operatore:'',note:''});};// Gestisce la selezione di una comanda\nconst handleComandaSelect=comanda=>{setSelectedComanda(comanda);if(dialogType==='selezionaComanda'){setDialogType('modificaComanda');setFormData({numero_comanda:comanda.numero_comanda,data_comanda:comanda.data_comanda.split('T')[0],tipo_comanda:comanda.tipo_comanda,id_cavo:comanda.id_cavo||'',operatore:comanda.operatore||'',note:comanda.note||''});}else if(dialogType==='stampaComanda'){handleStampaComanda(comanda.id_comanda);}};// Gestisce la selezione di un cavo\nconst handleCavoSelect=cavo=>{setSelectedCavo(cavo);setFormData({...formData,id_cavo:cavo.id_cavo});if(dialogType==='selezionaCavoComanda'){setDialogType('selezionaComandaPerCavo');}};// Gestisce il cambio dei valori nel form\nconst handleFormChange=e=>{const{name,value}=e.target;setFormData({...formData,[name]:value});};// Gestisce la creazione di una comanda\nconst handleCreaComanda=async()=>{try{if(!formData.numero_comanda||!formData.data_comanda||!formData.tipo_comanda){onError('Compila tutti i campi obbligatori');return;}setLoading(true);await comandeService.createComanda(cantiereId,formData);onSuccess('Comanda creata con successo');handleCloseDialog();loadComande();}catch(error){onError('Errore nella creazione della comanda: '+(error.message||'Errore sconosciuto'));console.error('Errore nella creazione della comanda:',error);}finally{setLoading(false);}};// Gestisce la modifica di una comanda\nconst handleModificaComanda=async()=>{try{if(!formData.numero_comanda||!formData.data_comanda||!formData.tipo_comanda){onError('Compila tutti i campi obbligatori');return;}setLoading(true);await comandeService.updateComanda(cantiereId,selectedComanda.id_comanda,formData);onSuccess('Comanda modificata con successo');handleCloseDialog();loadComande();}catch(error){onError('Errore nella modifica della comanda: '+(error.message||'Errore sconosciuto'));console.error('Errore nella modifica della comanda:',error);}finally{setLoading(false);}};// Gestisce l'eliminazione di una comanda\nconst handleEliminaComanda=async()=>{try{if(!selectedComanda){onError('Seleziona una comanda da eliminare');return;}setLoading(true);await comandeService.deleteComanda(cantiereId,selectedComanda.id_comanda);onSuccess('Comanda eliminata con successo');handleCloseDialog();loadComande();}catch(error){onError('Errore nell\\'eliminazione della comanda: '+(error.message||'Errore sconosciuto'));console.error('Errore nell\\'eliminazione della comanda:',error);}finally{setLoading(false);}};// Gestisce l'assegnazione di una comanda a un cavo\nconst handleAssegnaComanda=async()=>{try{if(!selectedCavo||!selectedComanda){onError('Seleziona un cavo e una comanda');return;}setLoading(true);await comandeService.assignComandaToCavo(cantiereId,selectedComanda.id_comanda,selectedCavo.id_cavo);onSuccess('Comanda assegnata al cavo con successo');handleCloseDialog();loadComande();}catch(error){onError('Errore nell\\'assegnazione della comanda: '+(error.message||'Errore sconosciuto'));console.error('Errore nell\\'assegnazione della comanda:',error);}finally{setLoading(false);}};// Gestisce la stampa di una comanda\nconst handleStampaComanda=async idComanda=>{try{setLoading(true);const response=await comandeService.printComanda(cantiereId,idComanda);// Apri il PDF in una nuova finestra\nwindow.open(response.file_url,'_blank');onSuccess('PDF della comanda generato con successo');handleCloseDialog();}catch(error){onError('Errore nella generazione del PDF della comanda: '+(error.message||'Errore sconosciuto'));console.error('Errore nella generazione del PDF della comanda:',error);}finally{setLoading(false);}};// Renderizza le comande in formato tabella\nconst renderComandeTable=()=>{if(comande.length===0){return/*#__PURE__*/_jsx(Alert,{severity:\"info\",children:\"Nessuna comanda trovata\"});}return/*#__PURE__*/_jsx(TableContainer,{component:Paper,children:/*#__PURE__*/_jsxs(Table,{size:\"small\",children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:\"ID\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Numero\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Data\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Tipo\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Cavo\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Operatore\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Azioni\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:comande.map(comanda=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:comanda.id_comanda}),/*#__PURE__*/_jsx(TableCell,{children:comanda.numero_comanda}),/*#__PURE__*/_jsx(TableCell,{children:new Date(comanda.data_comanda).toLocaleDateString()}),/*#__PURE__*/_jsx(TableCell,{children:comanda.tipo_comanda}),/*#__PURE__*/_jsx(TableCell,{children:comanda.id_cavo||'-'}),/*#__PURE__*/_jsx(TableCell,{children:comanda.operatore||'-'}),/*#__PURE__*/_jsxs(TableCell,{children:[/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>{setSelectedComanda(comanda);setDialogType('modificaComanda');setFormData({numero_comanda:comanda.numero_comanda,data_comanda:comanda.data_comanda.split('T')[0],tipo_comanda:comanda.tipo_comanda,id_cavo:comanda.id_cavo||'',operatore:comanda.operatore||'',note:comanda.note||''});setOpenDialog(true);},children:/*#__PURE__*/_jsx(EditIcon,{fontSize:\"small\"})}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>handleStampaComanda(comanda.id_comanda),children:/*#__PURE__*/_jsx(PrintIcon,{fontSize:\"small\"})}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",color:\"error\",onClick:()=>{setSelectedComanda(comanda);setDialogType('eliminaComanda');setOpenDialog(true);},children:/*#__PURE__*/_jsx(DeleteIcon,{fontSize:\"small\"})})]})]},comanda.id_comanda))})]})});};// Renderizza il dialog in base al tipo\nconst renderDialog=()=>{if(dialogType==='creaComanda'||dialogType==='modificaComanda'){return/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:handleCloseDialog,maxWidth:\"md\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:dialogType==='creaComanda'?'Crea Nuova Comanda':'Modifica Comanda'}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,sx:{mt:1},children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{name:\"numero_comanda\",label:\"Numero Comanda\",fullWidth:true,variant:\"outlined\",value:formData.numero_comanda,onChange:handleFormChange,required:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{name:\"data_comanda\",label:\"Data Comanda\",type:\"date\",fullWidth:true,variant:\"outlined\",value:formData.data_comanda,onChange:handleFormChange,InputLabelProps:{shrink:true},required:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,variant:\"outlined\",children:[/*#__PURE__*/_jsx(InputLabel,{children:\"Tipo Comanda\"}),/*#__PURE__*/_jsxs(Select,{name:\"tipo_comanda\",value:formData.tipo_comanda,onChange:handleFormChange,label:\"Tipo Comanda\",required:true,children:[/*#__PURE__*/_jsx(MenuItem,{value:\"POSA\",children:\"POSA\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"PARTENZA\",children:\"PARTENZA\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"ARRIVO\",children:\"ARRIVO\"})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{name:\"id_cavo\",label:\"ID Cavo (opzionale)\",fullWidth:true,variant:\"outlined\",value:formData.id_cavo,onChange:handleFormChange})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{name:\"operatore\",label:\"Operatore\",fullWidth:true,variant:\"outlined\",value:formData.operatore,onChange:handleFormChange})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(TextField,{name:\"note\",label:\"Note\",fullWidth:true,multiline:true,rows:3,variant:\"outlined\",value:formData.note,onChange:handleFormChange})})]})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleCloseDialog,children:\"Annulla\"}),/*#__PURE__*/_jsx(Button,{onClick:dialogType==='creaComanda'?handleCreaComanda:handleModificaComanda,disabled:loading||!formData.numero_comanda||!formData.data_comanda||!formData.tipo_comanda,startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):/*#__PURE__*/_jsx(SaveIcon,{}),children:\"Salva\"})]})]});}else if(dialogType==='selezionaComanda'){return/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:handleCloseDialog,maxWidth:\"md\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Seleziona Comanda da Modificare\"}),/*#__PURE__*/_jsx(DialogContent,{children:loading?/*#__PURE__*/_jsx(CircularProgress,{}):comande.length===0?/*#__PURE__*/_jsx(Alert,{severity:\"info\",children:\"Nessuna comanda disponibile\"}):/*#__PURE__*/_jsx(List,{children:comande.map(comanda=>/*#__PURE__*/_jsx(ListItem,{button:true,onClick:()=>handleComandaSelect(comanda),children:/*#__PURE__*/_jsx(ListItemText,{primary:`${comanda.numero_comanda} - ${comanda.tipo_comanda}`,secondary:`Data: ${new Date(comanda.data_comanda).toLocaleDateString()} - Cavo: ${comanda.id_cavo||'Non assegnato'}`})},comanda.id_comanda))})}),/*#__PURE__*/_jsx(DialogActions,{children:/*#__PURE__*/_jsx(Button,{onClick:handleCloseDialog,children:\"Annulla\"})})]});}else if(dialogType==='eliminaComanda'){return/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:handleCloseDialog,maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Elimina Comanda\"}),/*#__PURE__*/_jsx(DialogContent,{children:!selectedComanda?loading?/*#__PURE__*/_jsx(CircularProgress,{}):comande.length===0?/*#__PURE__*/_jsx(Alert,{severity:\"info\",children:\"Nessuna comanda disponibile\"}):/*#__PURE__*/_jsx(List,{children:comande.map(comanda=>/*#__PURE__*/_jsx(ListItem,{button:true,onClick:()=>setSelectedComanda(comanda),children:/*#__PURE__*/_jsx(ListItemText,{primary:`${comanda.numero_comanda} - ${comanda.tipo_comanda}`,secondary:`Data: ${new Date(comanda.data_comanda).toLocaleDateString()}`})},comanda.id_comanda))}):/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Alert,{severity:\"warning\",sx:{mb:2},children:[\"Sei sicuro di voler eliminare la comanda \",selectedComanda.numero_comanda,\"?\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:\"Questa operazione non pu\\xF2 essere annullata.\"})]})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleCloseDialog,children:\"Annulla\"}),selectedComanda&&/*#__PURE__*/_jsx(Button,{onClick:handleEliminaComanda,disabled:loading,color:\"error\",startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):/*#__PURE__*/_jsx(DeleteIcon,{}),children:\"Elimina\"})]})]});}else if(dialogType==='stampaComanda'){return/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:handleCloseDialog,maxWidth:\"md\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Seleziona Comanda da Stampare\"}),/*#__PURE__*/_jsx(DialogContent,{children:loading?/*#__PURE__*/_jsx(CircularProgress,{}):comande.length===0?/*#__PURE__*/_jsx(Alert,{severity:\"info\",children:\"Nessuna comanda disponibile\"}):/*#__PURE__*/_jsx(List,{children:comande.map(comanda=>/*#__PURE__*/_jsx(ListItem,{button:true,onClick:()=>handleComandaSelect(comanda),children:/*#__PURE__*/_jsx(ListItemText,{primary:`${comanda.numero_comanda} - ${comanda.tipo_comanda}`,secondary:`Data: ${new Date(comanda.data_comanda).toLocaleDateString()} - Cavo: ${comanda.id_cavo||'Non assegnato'}`})},comanda.id_comanda))})}),/*#__PURE__*/_jsx(DialogActions,{children:/*#__PURE__*/_jsx(Button,{onClick:handleCloseDialog,children:\"Annulla\"})})]});}else if(dialogType==='selezionaCavoComanda'){return/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:handleCloseDialog,maxWidth:\"md\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Seleziona Cavo per Assegnare Comanda\"}),/*#__PURE__*/_jsx(DialogContent,{children:loading?/*#__PURE__*/_jsx(CircularProgress,{}):cavi.length===0?/*#__PURE__*/_jsx(Alert,{severity:\"info\",children:\"Nessun cavo disponibile\"}):/*#__PURE__*/_jsx(List,{children:cavi.map(cavo=>/*#__PURE__*/_jsx(ListItem,{button:true,onClick:()=>handleCavoSelect(cavo),children:/*#__PURE__*/_jsx(ListItemText,{primary:cavo.id_cavo,secondary:`${cavo.tipologia||'N/A'} - Da: ${cavo.ubicazione_partenza||'N/A'} A: ${cavo.ubicazione_arrivo||'N/A'}`})},cavo.id_cavo))})}),/*#__PURE__*/_jsx(DialogActions,{children:/*#__PURE__*/_jsx(Button,{onClick:handleCloseDialog,children:\"Annulla\"})})]});}else if(dialogType==='selezionaComandaPerCavo'){return/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:handleCloseDialog,maxWidth:\"md\",fullWidth:true,children:[/*#__PURE__*/_jsxs(DialogTitle,{children:[\"Seleziona Comanda da Assegnare al Cavo \",selectedCavo===null||selectedCavo===void 0?void 0:selectedCavo.id_cavo]}),/*#__PURE__*/_jsx(DialogContent,{children:loading?/*#__PURE__*/_jsx(CircularProgress,{}):comande.length===0?/*#__PURE__*/_jsx(Alert,{severity:\"info\",children:\"Nessuna comanda disponibile\"}):/*#__PURE__*/_jsx(List,{children:comande.map(comanda=>/*#__PURE__*/_jsx(ListItem,{button:true,onClick:()=>setSelectedComanda(comanda),children:/*#__PURE__*/_jsx(ListItemText,{primary:`${comanda.numero_comanda} - ${comanda.tipo_comanda}`,secondary:`Data: ${new Date(comanda.data_comanda).toLocaleDateString()}`})},comanda.id_comanda))})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleCloseDialog,children:\"Annulla\"}),selectedComanda&&/*#__PURE__*/_jsx(Button,{onClick:handleAssegnaComanda,disabled:loading,startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):/*#__PURE__*/_jsx(SaveIcon,{}),children:\"Assegna\"})]})]});}return null;};return/*#__PURE__*/_jsxs(Box,{children:[selectedOption==='visualizzaComande'&&!openDialog?/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Comande\"}),loading?/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',my:4},children:/*#__PURE__*/_jsx(CircularProgress,{})}):renderComandeTable()]}):!openDialog?/*#__PURE__*/_jsx(Paper,{sx:{p:3,minHeight:'300px',display:'flex',alignItems:'center',justifyContent:'center'},children:!selectedOption?/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:\"Seleziona un'opzione dal menu principale per iniziare.\"}):/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",gutterBottom:true,children:[selectedOption==='creaComanda'&&'Crea nuova comanda',selectedOption==='modificaComanda'&&'Modifica comanda',selectedOption==='eliminaComanda'&&'Elimina comanda',selectedOption==='stampaComanda'&&'Stampa comanda',selectedOption==='assegnaComanda'&&'Assegna comanda a cavo']}),/*#__PURE__*/_jsx(CircularProgress,{sx:{mt:2}})]})}):null,renderDialog()]});};export default GestioneComande;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Divider", "<PERSON><PERSON>", "CircularProgress", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "IconButton", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Save", "SaveIcon", "Search", "SearchIcon", "Print", "PrintIcon", "ViewList", "ViewListIcon", "Assignment", "AssignmentIcon", "comandeService", "caviService", "jsx", "_jsx", "jsxs", "_jsxs", "GestioneComande", "_ref", "cantiereId", "onSuccess", "onError", "loading", "setLoading", "comande", "setComande", "cavi", "<PERSON><PERSON><PERSON>", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedComanda", "setSelectedComanda", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "formData", "setFormData", "numero_comanda", "data_comanda", "tipo_comanda", "id_cavo", "operatore", "note", "loadComande", "data", "getComande", "error", "console", "loadCavi", "get<PERSON><PERSON>", "handleOptionSelect", "option", "today", "Date", "toISOString", "split", "handleCloseDialog", "handleComandaSelect", "comanda", "handleStampaComanda", "id_comanda", "handleCavoSelect", "cavo", "handleFormChange", "e", "name", "value", "target", "handleCreaComanda", "createComanda", "message", "handleModificaComanda", "updateComanda", "handleEliminaComanda", "deleteComanda", "handleAssegnaComanda", "assignComandaToCavo", "idComanda", "response", "printComanda", "window", "open", "file_url", "renderComandeTable", "length", "severity", "children", "component", "size", "map", "toLocaleDateString", "onClick", "fontSize", "color", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "container", "spacing", "sx", "mt", "item", "xs", "sm", "label", "variant", "onChange", "required", "type", "InputLabelProps", "shrink", "multiline", "rows", "disabled", "startIcon", "button", "primary", "secondary", "mb", "tipologia", "ubicazione_partenza", "ubicazione_arrivo", "p", "gutterBottom", "display", "justifyContent", "my", "minHeight", "alignItems", "textAlign"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/GestioneComande.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Divider,\n  Alert,\n  CircularProgress,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  IconButton\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Save as SaveIcon,\n  Search as SearchIcon,\n  Print as PrintIcon,\n  ViewList as ViewListIcon,\n  Assignment as AssignmentIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport caviService from '../../services/caviService';\n\nconst GestioneComande = ({ cantiereId, onSuccess, onError }) => {\n  const [loading, setLoading] = useState(false);\n  const [comande, setComande] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    numero_comanda: '',\n    data_comanda: '',\n    tipo_comanda: 'POSA',\n    id_cavo: '',\n    operatore: '',\n    note: ''\n  });\n\n  // Carica le comande\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const data = await comandeService.getComande(cantiereId);\n      setComande(data);\n    } catch (error) {\n      onError('Errore nel caricamento delle comande');\n      console.error('Errore nel caricamento delle comande:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i cavi disponibili\n  const loadCavi = async () => {\n    try {\n      setLoading(true);\n      const data = await caviService.getCavi(cantiereId);\n      setCavi(data);\n    } catch (error) {\n      onError('Errore nel caricamento dei cavi');\n      console.error('Errore nel caricamento dei cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati all'avvio del componente\n  useEffect(() => {\n    loadComande();\n  }, [cantiereId]);\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'visualizzaComande') {\n      loadComande();\n    } else if (option === 'creaComanda') {\n      loadCavi();\n      setDialogType('creaComanda');\n      // Imposta la data di oggi come default\n      const today = new Date().toISOString().split('T')[0];\n      setFormData({\n        ...formData,\n        data_comanda: today\n      });\n      setOpenDialog(true);\n    } else if (option === 'modificaComanda') {\n      loadComande();\n      setDialogType('selezionaComanda');\n      setOpenDialog(true);\n    } else if (option === 'eliminaComanda') {\n      loadComande();\n      setDialogType('eliminaComanda');\n      setOpenDialog(true);\n    } else if (option === 'stampaComanda') {\n      loadComande();\n      setDialogType('stampaComanda');\n      setOpenDialog(true);\n    } else if (option === 'assegnaComanda') {\n      loadComande();\n      loadCavi();\n      setDialogType('selezionaCavoComanda');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedComanda(null);\n    setSelectedCavo(null);\n    setFormData({\n      numero_comanda: '',\n      data_comanda: '',\n      tipo_comanda: 'POSA',\n      id_cavo: '',\n      operatore: '',\n      note: ''\n    });\n  };\n\n  // Gestisce la selezione di una comanda\n  const handleComandaSelect = (comanda) => {\n    setSelectedComanda(comanda);\n\n    if (dialogType === 'selezionaComanda') {\n      setDialogType('modificaComanda');\n      setFormData({\n        numero_comanda: comanda.numero_comanda,\n        data_comanda: comanda.data_comanda.split('T')[0],\n        tipo_comanda: comanda.tipo_comanda,\n        id_cavo: comanda.id_cavo || '',\n        operatore: comanda.operatore || '',\n        note: comanda.note || ''\n      });\n    } else if (dialogType === 'stampaComanda') {\n      handleStampaComanda(comanda.id_comanda);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    setSelectedCavo(cavo);\n    setFormData({\n      ...formData,\n      id_cavo: cavo.id_cavo\n    });\n\n    if (dialogType === 'selezionaCavoComanda') {\n      setDialogType('selezionaComandaPerCavo');\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la creazione di una comanda\n  const handleCreaComanda = async () => {\n    try {\n      if (!formData.numero_comanda || !formData.data_comanda || !formData.tipo_comanda) {\n        onError('Compila tutti i campi obbligatori');\n        return;\n      }\n\n      setLoading(true);\n      await comandeService.createComanda(cantiereId, formData);\n      onSuccess('Comanda creata con successo');\n      handleCloseDialog();\n      loadComande();\n    } catch (error) {\n      onError('Errore nella creazione della comanda: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella creazione della comanda:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la modifica di una comanda\n  const handleModificaComanda = async () => {\n    try {\n      if (!formData.numero_comanda || !formData.data_comanda || !formData.tipo_comanda) {\n        onError('Compila tutti i campi obbligatori');\n        return;\n      }\n\n      setLoading(true);\n      await comandeService.updateComanda(cantiereId, selectedComanda.id_comanda, formData);\n      onSuccess('Comanda modificata con successo');\n      handleCloseDialog();\n      loadComande();\n    } catch (error) {\n      onError('Errore nella modifica della comanda: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella modifica della comanda:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'eliminazione di una comanda\n  const handleEliminaComanda = async () => {\n    try {\n      if (!selectedComanda) {\n        onError('Seleziona una comanda da eliminare');\n        return;\n      }\n\n      setLoading(true);\n      await comandeService.deleteComanda(cantiereId, selectedComanda.id_comanda);\n      onSuccess('Comanda eliminata con successo');\n      handleCloseDialog();\n      loadComande();\n    } catch (error) {\n      onError('Errore nell\\'eliminazione della comanda: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'eliminazione della comanda:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'assegnazione di una comanda a un cavo\n  const handleAssegnaComanda = async () => {\n    try {\n      if (!selectedCavo || !selectedComanda) {\n        onError('Seleziona un cavo e una comanda');\n        return;\n      }\n\n      setLoading(true);\n      await comandeService.assignComandaToCavo(cantiereId, selectedComanda.id_comanda, selectedCavo.id_cavo);\n      onSuccess('Comanda assegnata al cavo con successo');\n      handleCloseDialog();\n      loadComande();\n    } catch (error) {\n      onError('Errore nell\\'assegnazione della comanda: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'assegnazione della comanda:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la stampa di una comanda\n  const handleStampaComanda = async (idComanda) => {\n    try {\n      setLoading(true);\n      const response = await comandeService.printComanda(cantiereId, idComanda);\n\n      // Apri il PDF in una nuova finestra\n      window.open(response.file_url, '_blank');\n\n      onSuccess('PDF della comanda generato con successo');\n      handleCloseDialog();\n    } catch (error) {\n      onError('Errore nella generazione del PDF della comanda: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella generazione del PDF della comanda:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza le comande in formato tabella\n  const renderComandeTable = () => {\n    if (comande.length === 0) {\n      return (\n        <Alert severity=\"info\">Nessuna comanda trovata</Alert>\n      );\n    }\n\n    return (\n      <TableContainer component={Paper}>\n        <Table size=\"small\">\n          <TableHead>\n            <TableRow>\n              <TableCell>ID</TableCell>\n              <TableCell>Numero</TableCell>\n              <TableCell>Data</TableCell>\n              <TableCell>Tipo</TableCell>\n              <TableCell>Cavo</TableCell>\n              <TableCell>Operatore</TableCell>\n              <TableCell>Azioni</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {comande.map((comanda) => (\n              <TableRow key={comanda.id_comanda}>\n                <TableCell>{comanda.id_comanda}</TableCell>\n                <TableCell>{comanda.numero_comanda}</TableCell>\n                <TableCell>{new Date(comanda.data_comanda).toLocaleDateString()}</TableCell>\n                <TableCell>{comanda.tipo_comanda}</TableCell>\n                <TableCell>{comanda.id_cavo || '-'}</TableCell>\n                <TableCell>{comanda.operatore || '-'}</TableCell>\n                <TableCell>\n                  <IconButton\n                    size=\"small\"\n                    onClick={() => {\n                      setSelectedComanda(comanda);\n                      setDialogType('modificaComanda');\n                      setFormData({\n                        numero_comanda: comanda.numero_comanda,\n                        data_comanda: comanda.data_comanda.split('T')[0],\n                        tipo_comanda: comanda.tipo_comanda,\n                        id_cavo: comanda.id_cavo || '',\n                        operatore: comanda.operatore || '',\n                        note: comanda.note || ''\n                      });\n                      setOpenDialog(true);\n                    }}\n                  >\n                    <EditIcon fontSize=\"small\" />\n                  </IconButton>\n                  <IconButton\n                    size=\"small\"\n                    onClick={() => handleStampaComanda(comanda.id_comanda)}\n                  >\n                    <PrintIcon fontSize=\"small\" />\n                  </IconButton>\n                  <IconButton\n                    size=\"small\"\n                    color=\"error\"\n                    onClick={() => {\n                      setSelectedComanda(comanda);\n                      setDialogType('eliminaComanda');\n                      setOpenDialog(true);\n                    }}\n                  >\n                    <DeleteIcon fontSize=\"small\" />\n                  </IconButton>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n    );\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaComanda' || dialogType === 'modificaComanda') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>\n            {dialogType === 'creaComanda' ? 'Crea Nuova Comanda' : 'Modifica Comanda'}\n          </DialogTitle>\n          <DialogContent>\n            <Grid container spacing={2} sx={{ mt: 1 }}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"numero_comanda\"\n                  label=\"Numero Comanda\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.numero_comanda}\n                  onChange={handleFormChange}\n                  required\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"data_comanda\"\n                  label=\"Data Comanda\"\n                  type=\"date\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.data_comanda}\n                  onChange={handleFormChange}\n                  InputLabelProps={{ shrink: true }}\n                  required\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth variant=\"outlined\">\n                  <InputLabel>Tipo Comanda</InputLabel>\n                  <Select\n                    name=\"tipo_comanda\"\n                    value={formData.tipo_comanda}\n                    onChange={handleFormChange}\n                    label=\"Tipo Comanda\"\n                    required\n                  >\n                    <MenuItem value=\"POSA\">POSA</MenuItem>\n                    <MenuItem value=\"PARTENZA\">PARTENZA</MenuItem>\n                    <MenuItem value=\"ARRIVO\">ARRIVO</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_cavo\"\n                  label=\"ID Cavo (opzionale)\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_cavo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"operatore\"\n                  label=\"Operatore\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.operatore}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12}>\n                <TextField\n                  name=\"note\"\n                  label=\"Note\"\n                  fullWidth\n                  multiline\n                  rows={3}\n                  variant=\"outlined\"\n                  value={formData.note}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n            </Grid>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={dialogType === 'creaComanda' ? handleCreaComanda : handleModificaComanda}\n              disabled={loading || !formData.numero_comanda || !formData.data_comanda || !formData.tipo_comanda}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaComanda') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Seleziona Comanda da Modificare</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : comande.length === 0 ? (\n              <Alert severity=\"info\">Nessuna comanda disponibile</Alert>\n            ) : (\n              <List>\n                {comande.map((comanda) => (\n                  <ListItem\n                    button\n                    key={comanda.id_comanda}\n                    onClick={() => handleComandaSelect(comanda)}\n                  >\n                    <ListItemText\n                      primary={`${comanda.numero_comanda} - ${comanda.tipo_comanda}`}\n                      secondary={`Data: ${new Date(comanda.data_comanda).toLocaleDateString()} - Cavo: ${comanda.id_cavo || 'Non assegnato'}`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'eliminaComanda') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Elimina Comanda</DialogTitle>\n          <DialogContent>\n            {!selectedComanda ? (\n              loading ? (\n                <CircularProgress />\n              ) : comande.length === 0 ? (\n                <Alert severity=\"info\">Nessuna comanda disponibile</Alert>\n              ) : (\n                <List>\n                  {comande.map((comanda) => (\n                    <ListItem\n                      button\n                      key={comanda.id_comanda}\n                      onClick={() => setSelectedComanda(comanda)}\n                    >\n                      <ListItemText\n                        primary={`${comanda.numero_comanda} - ${comanda.tipo_comanda}`}\n                        secondary={`Data: ${new Date(comanda.data_comanda).toLocaleDateString()}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              )\n            ) : (\n              <Box>\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  Sei sicuro di voler eliminare la comanda {selectedComanda.numero_comanda}?\n                </Alert>\n                <Typography variant=\"body1\">\n                  Questa operazione non può essere annullata.\n                </Typography>\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedComanda && (\n              <Button\n                onClick={handleEliminaComanda}\n                disabled={loading}\n                color=\"error\"\n                startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}\n              >\n                Elimina\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'stampaComanda') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Seleziona Comanda da Stampare</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : comande.length === 0 ? (\n              <Alert severity=\"info\">Nessuna comanda disponibile</Alert>\n            ) : (\n              <List>\n                {comande.map((comanda) => (\n                  <ListItem\n                    button\n                    key={comanda.id_comanda}\n                    onClick={() => handleComandaSelect(comanda)}\n                  >\n                    <ListItemText\n                      primary={`${comanda.numero_comanda} - ${comanda.tipo_comanda}`}\n                      secondary={`Data: ${new Date(comanda.data_comanda).toLocaleDateString()} - Cavo: ${comanda.id_cavo || 'Non assegnato'}`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaCavoComanda') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Seleziona Cavo per Assegnare Comanda</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : (\n              <List>\n                {cavi.map((cavo) => (\n                  <ListItem\n                    button\n                    key={cavo.id_cavo}\n                    onClick={() => handleCavoSelect(cavo)}\n                  >\n                    <ListItemText\n                      primary={cavo.id_cavo}\n                      secondary={`${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaComandaPerCavo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Seleziona Comanda da Assegnare al Cavo {selectedCavo?.id_cavo}</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : comande.length === 0 ? (\n              <Alert severity=\"info\">Nessuna comanda disponibile</Alert>\n            ) : (\n              <List>\n                {comande.map((comanda) => (\n                  <ListItem\n                    button\n                    key={comanda.id_comanda}\n                    onClick={() => setSelectedComanda(comanda)}\n                  >\n                    <ListItemText\n                      primary={`${comanda.numero_comanda} - ${comanda.tipo_comanda}`}\n                      secondary={`Data: ${new Date(comanda.data_comanda).toLocaleDateString()}`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedComanda && (\n              <Button\n                onClick={handleAssegnaComanda}\n                disabled={loading}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Assegna\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box>\n      {selectedOption === 'visualizzaComande' && !openDialog ? (\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Comande\n          </Typography>\n\n          {loading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            renderComandeTable()\n          )}\n        </Paper>\n      ) : !openDialog ? (\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {!selectedOption ? (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu principale per iniziare.\n            </Typography>\n          ) : (\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedOption === 'creaComanda' && 'Crea nuova comanda'}\n                {selectedOption === 'modificaComanda' && 'Modifica comanda'}\n                {selectedOption === 'eliminaComanda' && 'Elimina comanda'}\n                {selectedOption === 'stampaComanda' && 'Stampa comanda'}\n                {selectedOption === 'assegnaComanda' && 'Assegna comanda a cavo'}\n              </Typography>\n              <CircularProgress sx={{ mt: 2 }} />\n            </Box>\n          )}\n        </Paper>\n      ) : null}\n\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default GestioneComande;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,UAAU,CACVC,MAAM,CACNC,KAAK,CACLC,IAAI,CACJC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,SAAS,CACTC,WAAW,CACXC,UAAU,CACVC,MAAM,CACNC,QAAQ,CACRC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,OAAO,CACPC,KAAK,CACLC,gBAAgB,CAChBC,KAAK,CACLC,SAAS,CACTC,SAAS,CACTC,cAAc,CACdC,SAAS,CACTC,QAAQ,CACRC,UAAU,KACL,eAAe,CACtB,OACEC,GAAG,GAAI,CAAAC,OAAO,CACdC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,KAAK,GAAI,CAAAC,SAAS,CAClBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,UAAU,GAAI,CAAAC,cAAc,KACvB,qBAAqB,CAC5B,MAAO,CAAAC,cAAc,KAAM,+BAA+B,CAC1D,MAAO,CAAAC,WAAW,KAAM,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErD,KAAM,CAAAC,eAAe,CAAGC,IAAA,EAAwC,IAAvC,CAAEC,UAAU,CAAEC,SAAS,CAAEC,OAAQ,CAAC,CAAAH,IAAA,CACzD,KAAM,CAACI,OAAO,CAAEC,UAAU,CAAC,CAAG9D,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC+D,OAAO,CAAEC,UAAU,CAAC,CAAGhE,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACiE,IAAI,CAAEC,OAAO,CAAC,CAAGlE,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAACmE,cAAc,CAAEC,iBAAiB,CAAC,CAAGpE,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACqE,UAAU,CAAEC,aAAa,CAAC,CAAGtE,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACuE,UAAU,CAAEC,aAAa,CAAC,CAAGxE,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACyE,eAAe,CAAEC,kBAAkB,CAAC,CAAG1E,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAAC2E,YAAY,CAAEC,eAAe,CAAC,CAAG5E,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAAC6E,QAAQ,CAAEC,WAAW,CAAC,CAAG9E,QAAQ,CAAC,CACvC+E,cAAc,CAAE,EAAE,CAClBC,YAAY,CAAE,EAAE,CAChBC,YAAY,CAAE,MAAM,CACpBC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EAAE,CACbC,IAAI,CAAE,EACR,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACFvB,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAwB,IAAI,CAAG,KAAM,CAAApC,cAAc,CAACqC,UAAU,CAAC7B,UAAU,CAAC,CACxDM,UAAU,CAACsB,IAAI,CAAC,CAClB,CAAE,MAAOE,KAAK,CAAE,CACd5B,OAAO,CAAC,sCAAsC,CAAC,CAC/C6B,OAAO,CAACD,KAAK,CAAC,uCAAuC,CAAEA,KAAK,CAAC,CAC/D,CAAC,OAAS,CACR1B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA4B,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3B,GAAI,CACF5B,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAwB,IAAI,CAAG,KAAM,CAAAnC,WAAW,CAACwC,OAAO,CAACjC,UAAU,CAAC,CAClDQ,OAAO,CAACoB,IAAI,CAAC,CACf,CAAE,MAAOE,KAAK,CAAE,CACd5B,OAAO,CAAC,iCAAiC,CAAC,CAC1C6B,OAAO,CAACD,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CAC1D,CAAC,OAAS,CACR1B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA7D,SAAS,CAAC,IAAM,CACdoF,WAAW,CAAC,CAAC,CACf,CAAC,CAAE,CAAC3B,UAAU,CAAC,CAAC,CAEhB;AACA,KAAM,CAAAkC,kBAAkB,CAAIC,MAAM,EAAK,CACrCzB,iBAAiB,CAACyB,MAAM,CAAC,CAEzB,GAAIA,MAAM,GAAK,mBAAmB,CAAE,CAClCR,WAAW,CAAC,CAAC,CACf,CAAC,IAAM,IAAIQ,MAAM,GAAK,aAAa,CAAE,CACnCH,QAAQ,CAAC,CAAC,CACVlB,aAAa,CAAC,aAAa,CAAC,CAC5B;AACA,KAAM,CAAAsB,KAAK,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACpDnB,WAAW,CAAC,CACV,GAAGD,QAAQ,CACXG,YAAY,CAAEc,KAChB,CAAC,CAAC,CACFxB,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,IAAIuB,MAAM,GAAK,iBAAiB,CAAE,CACvCR,WAAW,CAAC,CAAC,CACbb,aAAa,CAAC,kBAAkB,CAAC,CACjCF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,IAAIuB,MAAM,GAAK,gBAAgB,CAAE,CACtCR,WAAW,CAAC,CAAC,CACbb,aAAa,CAAC,gBAAgB,CAAC,CAC/BF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,IAAIuB,MAAM,GAAK,eAAe,CAAE,CACrCR,WAAW,CAAC,CAAC,CACbb,aAAa,CAAC,eAAe,CAAC,CAC9BF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,IAAIuB,MAAM,GAAK,gBAAgB,CAAE,CACtCR,WAAW,CAAC,CAAC,CACbK,QAAQ,CAAC,CAAC,CACVlB,aAAa,CAAC,sBAAsB,CAAC,CACrCF,aAAa,CAAC,IAAI,CAAC,CACrB,CACF,CAAC,CAED;AACA,KAAM,CAAA4B,iBAAiB,CAAGA,CAAA,GAAM,CAC9B5B,aAAa,CAAC,KAAK,CAAC,CACpBI,kBAAkB,CAAC,IAAI,CAAC,CACxBE,eAAe,CAAC,IAAI,CAAC,CACrBE,WAAW,CAAC,CACVC,cAAc,CAAE,EAAE,CAClBC,YAAY,CAAE,EAAE,CAChBC,YAAY,CAAE,MAAM,CACpBC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EAAE,CACbC,IAAI,CAAE,EACR,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAe,mBAAmB,CAAIC,OAAO,EAAK,CACvC1B,kBAAkB,CAAC0B,OAAO,CAAC,CAE3B,GAAI7B,UAAU,GAAK,kBAAkB,CAAE,CACrCC,aAAa,CAAC,iBAAiB,CAAC,CAChCM,WAAW,CAAC,CACVC,cAAc,CAAEqB,OAAO,CAACrB,cAAc,CACtCC,YAAY,CAAEoB,OAAO,CAACpB,YAAY,CAACiB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAChDhB,YAAY,CAAEmB,OAAO,CAACnB,YAAY,CAClCC,OAAO,CAAEkB,OAAO,CAAClB,OAAO,EAAI,EAAE,CAC9BC,SAAS,CAAEiB,OAAO,CAACjB,SAAS,EAAI,EAAE,CAClCC,IAAI,CAAEgB,OAAO,CAAChB,IAAI,EAAI,EACxB,CAAC,CAAC,CACJ,CAAC,IAAM,IAAIb,UAAU,GAAK,eAAe,CAAE,CACzC8B,mBAAmB,CAACD,OAAO,CAACE,UAAU,CAAC,CACzC,CACF,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAIC,IAAI,EAAK,CACjC5B,eAAe,CAAC4B,IAAI,CAAC,CACrB1B,WAAW,CAAC,CACV,GAAGD,QAAQ,CACXK,OAAO,CAAEsB,IAAI,CAACtB,OAChB,CAAC,CAAC,CAEF,GAAIX,UAAU,GAAK,sBAAsB,CAAE,CACzCC,aAAa,CAAC,yBAAyB,CAAC,CAC1C,CACF,CAAC,CAED;AACA,KAAM,CAAAiC,gBAAgB,CAAIC,CAAC,EAAK,CAC9B,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAChC/B,WAAW,CAAC,CACV,GAAGD,QAAQ,CACX,CAAC8B,IAAI,EAAGC,KACV,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAE,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACF,GAAI,CAACjC,QAAQ,CAACE,cAAc,EAAI,CAACF,QAAQ,CAACG,YAAY,EAAI,CAACH,QAAQ,CAACI,YAAY,CAAE,CAChFrB,OAAO,CAAC,mCAAmC,CAAC,CAC5C,OACF,CAEAE,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAZ,cAAc,CAAC6D,aAAa,CAACrD,UAAU,CAAEmB,QAAQ,CAAC,CACxDlB,SAAS,CAAC,6BAA6B,CAAC,CACxCuC,iBAAiB,CAAC,CAAC,CACnBb,WAAW,CAAC,CAAC,CACf,CAAE,MAAOG,KAAK,CAAE,CACd5B,OAAO,CAAC,wCAAwC,EAAI4B,KAAK,CAACwB,OAAO,EAAI,oBAAoB,CAAC,CAAC,CAC3FvB,OAAO,CAACD,KAAK,CAAC,uCAAuC,CAAEA,KAAK,CAAC,CAC/D,CAAC,OAAS,CACR1B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAmD,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC,GAAI,CACF,GAAI,CAACpC,QAAQ,CAACE,cAAc,EAAI,CAACF,QAAQ,CAACG,YAAY,EAAI,CAACH,QAAQ,CAACI,YAAY,CAAE,CAChFrB,OAAO,CAAC,mCAAmC,CAAC,CAC5C,OACF,CAEAE,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAZ,cAAc,CAACgE,aAAa,CAACxD,UAAU,CAAEe,eAAe,CAAC6B,UAAU,CAAEzB,QAAQ,CAAC,CACpFlB,SAAS,CAAC,iCAAiC,CAAC,CAC5CuC,iBAAiB,CAAC,CAAC,CACnBb,WAAW,CAAC,CAAC,CACf,CAAE,MAAOG,KAAK,CAAE,CACd5B,OAAO,CAAC,uCAAuC,EAAI4B,KAAK,CAACwB,OAAO,EAAI,oBAAoB,CAAC,CAAC,CAC1FvB,OAAO,CAACD,KAAK,CAAC,sCAAsC,CAAEA,KAAK,CAAC,CAC9D,CAAC,OAAS,CACR1B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAqD,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CACF,GAAI,CAAC1C,eAAe,CAAE,CACpBb,OAAO,CAAC,oCAAoC,CAAC,CAC7C,OACF,CAEAE,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAZ,cAAc,CAACkE,aAAa,CAAC1D,UAAU,CAAEe,eAAe,CAAC6B,UAAU,CAAC,CAC1E3C,SAAS,CAAC,gCAAgC,CAAC,CAC3CuC,iBAAiB,CAAC,CAAC,CACnBb,WAAW,CAAC,CAAC,CACf,CAAE,MAAOG,KAAK,CAAE,CACd5B,OAAO,CAAC,2CAA2C,EAAI4B,KAAK,CAACwB,OAAO,EAAI,oBAAoB,CAAC,CAAC,CAC9FvB,OAAO,CAACD,KAAK,CAAC,0CAA0C,CAAEA,KAAK,CAAC,CAClE,CAAC,OAAS,CACR1B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAuD,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CACF,GAAI,CAAC1C,YAAY,EAAI,CAACF,eAAe,CAAE,CACrCb,OAAO,CAAC,iCAAiC,CAAC,CAC1C,OACF,CAEAE,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAZ,cAAc,CAACoE,mBAAmB,CAAC5D,UAAU,CAAEe,eAAe,CAAC6B,UAAU,CAAE3B,YAAY,CAACO,OAAO,CAAC,CACtGvB,SAAS,CAAC,wCAAwC,CAAC,CACnDuC,iBAAiB,CAAC,CAAC,CACnBb,WAAW,CAAC,CAAC,CACf,CAAE,MAAOG,KAAK,CAAE,CACd5B,OAAO,CAAC,2CAA2C,EAAI4B,KAAK,CAACwB,OAAO,EAAI,oBAAoB,CAAC,CAAC,CAC9FvB,OAAO,CAACD,KAAK,CAAC,0CAA0C,CAAEA,KAAK,CAAC,CAClE,CAAC,OAAS,CACR1B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAuC,mBAAmB,CAAG,KAAO,CAAAkB,SAAS,EAAK,CAC/C,GAAI,CACFzD,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAA0D,QAAQ,CAAG,KAAM,CAAAtE,cAAc,CAACuE,YAAY,CAAC/D,UAAU,CAAE6D,SAAS,CAAC,CAEzE;AACAG,MAAM,CAACC,IAAI,CAACH,QAAQ,CAACI,QAAQ,CAAE,QAAQ,CAAC,CAExCjE,SAAS,CAAC,yCAAyC,CAAC,CACpDuC,iBAAiB,CAAC,CAAC,CACrB,CAAE,MAAOV,KAAK,CAAE,CACd5B,OAAO,CAAC,kDAAkD,EAAI4B,KAAK,CAACwB,OAAO,EAAI,oBAAoB,CAAC,CAAC,CACrGvB,OAAO,CAACD,KAAK,CAAC,iDAAiD,CAAEA,KAAK,CAAC,CACzE,CAAC,OAAS,CACR1B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA+D,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,GAAI9D,OAAO,CAAC+D,MAAM,GAAK,CAAC,CAAE,CACxB,mBACEzE,IAAA,CAAC5B,KAAK,EAACsG,QAAQ,CAAC,MAAM,CAAAC,QAAA,CAAC,yBAAuB,CAAO,CAAC,CAE1D,CAEA,mBACE3E,IAAA,CAACvB,cAAc,EAACmG,SAAS,CAAE5H,KAAM,CAAA2H,QAAA,cAC/BzE,KAAA,CAAC5B,KAAK,EAACuG,IAAI,CAAC,OAAO,CAAAF,QAAA,eACjB3E,IAAA,CAACtB,SAAS,EAAAiG,QAAA,cACRzE,KAAA,CAACvB,QAAQ,EAAAgG,QAAA,eACP3E,IAAA,CAACxB,SAAS,EAAAmG,QAAA,CAAC,IAAE,CAAW,CAAC,cACzB3E,IAAA,CAACxB,SAAS,EAAAmG,QAAA,CAAC,QAAM,CAAW,CAAC,cAC7B3E,IAAA,CAACxB,SAAS,EAAAmG,QAAA,CAAC,MAAI,CAAW,CAAC,cAC3B3E,IAAA,CAACxB,SAAS,EAAAmG,QAAA,CAAC,MAAI,CAAW,CAAC,cAC3B3E,IAAA,CAACxB,SAAS,EAAAmG,QAAA,CAAC,MAAI,CAAW,CAAC,cAC3B3E,IAAA,CAACxB,SAAS,EAAAmG,QAAA,CAAC,WAAS,CAAW,CAAC,cAChC3E,IAAA,CAACxB,SAAS,EAAAmG,QAAA,CAAC,QAAM,CAAW,CAAC,EACrB,CAAC,CACF,CAAC,cACZ3E,IAAA,CAACzB,SAAS,EAAAoG,QAAA,CACPjE,OAAO,CAACoE,GAAG,CAAE/B,OAAO,eACnB7C,KAAA,CAACvB,QAAQ,EAAAgG,QAAA,eACP3E,IAAA,CAACxB,SAAS,EAAAmG,QAAA,CAAE5B,OAAO,CAACE,UAAU,CAAY,CAAC,cAC3CjD,IAAA,CAACxB,SAAS,EAAAmG,QAAA,CAAE5B,OAAO,CAACrB,cAAc,CAAY,CAAC,cAC/C1B,IAAA,CAACxB,SAAS,EAAAmG,QAAA,CAAE,GAAI,CAAAjC,IAAI,CAACK,OAAO,CAACpB,YAAY,CAAC,CAACoD,kBAAkB,CAAC,CAAC,CAAY,CAAC,cAC5E/E,IAAA,CAACxB,SAAS,EAAAmG,QAAA,CAAE5B,OAAO,CAACnB,YAAY,CAAY,CAAC,cAC7C5B,IAAA,CAACxB,SAAS,EAAAmG,QAAA,CAAE5B,OAAO,CAAClB,OAAO,EAAI,GAAG,CAAY,CAAC,cAC/C7B,IAAA,CAACxB,SAAS,EAAAmG,QAAA,CAAE5B,OAAO,CAACjB,SAAS,EAAI,GAAG,CAAY,CAAC,cACjD5B,KAAA,CAAC1B,SAAS,EAAAmG,QAAA,eACR3E,IAAA,CAACpB,UAAU,EACTiG,IAAI,CAAC,OAAO,CACZG,OAAO,CAAEA,CAAA,GAAM,CACb3D,kBAAkB,CAAC0B,OAAO,CAAC,CAC3B5B,aAAa,CAAC,iBAAiB,CAAC,CAChCM,WAAW,CAAC,CACVC,cAAc,CAAEqB,OAAO,CAACrB,cAAc,CACtCC,YAAY,CAAEoB,OAAO,CAACpB,YAAY,CAACiB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAChDhB,YAAY,CAAEmB,OAAO,CAACnB,YAAY,CAClCC,OAAO,CAAEkB,OAAO,CAAClB,OAAO,EAAI,EAAE,CAC9BC,SAAS,CAAEiB,OAAO,CAACjB,SAAS,EAAI,EAAE,CAClCC,IAAI,CAAEgB,OAAO,CAAChB,IAAI,EAAI,EACxB,CAAC,CAAC,CACFd,aAAa,CAAC,IAAI,CAAC,CACrB,CAAE,CAAA0D,QAAA,cAEF3E,IAAA,CAAChB,QAAQ,EAACiG,QAAQ,CAAC,OAAO,CAAE,CAAC,CACnB,CAAC,cACbjF,IAAA,CAACpB,UAAU,EACTiG,IAAI,CAAC,OAAO,CACZG,OAAO,CAAEA,CAAA,GAAMhC,mBAAmB,CAACD,OAAO,CAACE,UAAU,CAAE,CAAA0B,QAAA,cAEvD3E,IAAA,CAACR,SAAS,EAACyF,QAAQ,CAAC,OAAO,CAAE,CAAC,CACpB,CAAC,cACbjF,IAAA,CAACpB,UAAU,EACTiG,IAAI,CAAC,OAAO,CACZK,KAAK,CAAC,OAAO,CACbF,OAAO,CAAEA,CAAA,GAAM,CACb3D,kBAAkB,CAAC0B,OAAO,CAAC,CAC3B5B,aAAa,CAAC,gBAAgB,CAAC,CAC/BF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAE,CAAA0D,QAAA,cAEF3E,IAAA,CAACd,UAAU,EAAC+F,QAAQ,CAAC,OAAO,CAAE,CAAC,CACrB,CAAC,EACJ,CAAC,GA3CClC,OAAO,CAACE,UA4Cb,CACX,CAAC,CACO,CAAC,EACP,CAAC,CACM,CAAC,CAErB,CAAC,CAED;AACA,KAAM,CAAAkC,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAIjE,UAAU,GAAK,aAAa,EAAIA,UAAU,GAAK,iBAAiB,CAAE,CACpE,mBACEhB,KAAA,CAAC7C,MAAM,EAACiH,IAAI,CAAEtD,UAAW,CAACoE,OAAO,CAAEvC,iBAAkB,CAACwC,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAX,QAAA,eAC3E3E,IAAA,CAAC1C,WAAW,EAAAqH,QAAA,CACTzD,UAAU,GAAK,aAAa,CAAG,oBAAoB,CAAG,kBAAkB,CAC9D,CAAC,cACdlB,IAAA,CAACzC,aAAa,EAAAoH,QAAA,cACZzE,KAAA,CAACjD,IAAI,EAACsI,SAAS,MAACC,OAAO,CAAE,CAAE,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAf,QAAA,eACxC3E,IAAA,CAAC/C,IAAI,EAAC0I,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,cACvB3E,IAAA,CAACvC,SAAS,EACR6F,IAAI,CAAC,gBAAgB,CACrBwC,KAAK,CAAC,gBAAgB,CACtBR,SAAS,MACTS,OAAO,CAAC,UAAU,CAClBxC,KAAK,CAAE/B,QAAQ,CAACE,cAAe,CAC/BsE,QAAQ,CAAE5C,gBAAiB,CAC3B6C,QAAQ,MACT,CAAC,CACE,CAAC,cACPjG,IAAA,CAAC/C,IAAI,EAAC0I,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,cACvB3E,IAAA,CAACvC,SAAS,EACR6F,IAAI,CAAC,cAAc,CACnBwC,KAAK,CAAC,cAAc,CACpBI,IAAI,CAAC,MAAM,CACXZ,SAAS,MACTS,OAAO,CAAC,UAAU,CAClBxC,KAAK,CAAE/B,QAAQ,CAACG,YAAa,CAC7BqE,QAAQ,CAAE5C,gBAAiB,CAC3B+C,eAAe,CAAE,CAAEC,MAAM,CAAE,IAAK,CAAE,CAClCH,QAAQ,MACT,CAAC,CACE,CAAC,cACPjG,IAAA,CAAC/C,IAAI,EAAC0I,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,cACvBzE,KAAA,CAACxC,WAAW,EAAC4H,SAAS,MAACS,OAAO,CAAC,UAAU,CAAApB,QAAA,eACvC3E,IAAA,CAACrC,UAAU,EAAAgH,QAAA,CAAC,cAAY,CAAY,CAAC,cACrCzE,KAAA,CAACtC,MAAM,EACL0F,IAAI,CAAC,cAAc,CACnBC,KAAK,CAAE/B,QAAQ,CAACI,YAAa,CAC7BoE,QAAQ,CAAE5C,gBAAiB,CAC3B0C,KAAK,CAAC,cAAc,CACpBG,QAAQ,MAAAtB,QAAA,eAER3E,IAAA,CAACnC,QAAQ,EAAC0F,KAAK,CAAC,MAAM,CAAAoB,QAAA,CAAC,MAAI,CAAU,CAAC,cACtC3E,IAAA,CAACnC,QAAQ,EAAC0F,KAAK,CAAC,UAAU,CAAAoB,QAAA,CAAC,UAAQ,CAAU,CAAC,cAC9C3E,IAAA,CAACnC,QAAQ,EAAC0F,KAAK,CAAC,QAAQ,CAAAoB,QAAA,CAAC,QAAM,CAAU,CAAC,EACpC,CAAC,EACE,CAAC,CACV,CAAC,cACP3E,IAAA,CAAC/C,IAAI,EAAC0I,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,cACvB3E,IAAA,CAACvC,SAAS,EACR6F,IAAI,CAAC,SAAS,CACdwC,KAAK,CAAC,qBAAqB,CAC3BR,SAAS,MACTS,OAAO,CAAC,UAAU,CAClBxC,KAAK,CAAE/B,QAAQ,CAACK,OAAQ,CACxBmE,QAAQ,CAAE5C,gBAAiB,CAC5B,CAAC,CACE,CAAC,cACPpD,IAAA,CAAC/C,IAAI,EAAC0I,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,cACvB3E,IAAA,CAACvC,SAAS,EACR6F,IAAI,CAAC,WAAW,CAChBwC,KAAK,CAAC,WAAW,CACjBR,SAAS,MACTS,OAAO,CAAC,UAAU,CAClBxC,KAAK,CAAE/B,QAAQ,CAACM,SAAU,CAC1BkE,QAAQ,CAAE5C,gBAAiB,CAC5B,CAAC,CACE,CAAC,cACPpD,IAAA,CAAC/C,IAAI,EAAC0I,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAjB,QAAA,cAChB3E,IAAA,CAACvC,SAAS,EACR6F,IAAI,CAAC,MAAM,CACXwC,KAAK,CAAC,MAAM,CACZR,SAAS,MACTe,SAAS,MACTC,IAAI,CAAE,CAAE,CACRP,OAAO,CAAC,UAAU,CAClBxC,KAAK,CAAE/B,QAAQ,CAACO,IAAK,CACrBiE,QAAQ,CAAE5C,gBAAiB,CAC5B,CAAC,CACE,CAAC,EACH,CAAC,CACM,CAAC,cAChBlD,KAAA,CAAC1C,aAAa,EAAAmH,QAAA,eACZ3E,IAAA,CAACjD,MAAM,EAACiI,OAAO,CAAEnC,iBAAkB,CAAA8B,QAAA,CAAC,SAAO,CAAQ,CAAC,cACpD3E,IAAA,CAACjD,MAAM,EACLiI,OAAO,CAAE9D,UAAU,GAAK,aAAa,CAAGuC,iBAAiB,CAAGG,qBAAsB,CAClF2C,QAAQ,CAAE/F,OAAO,EAAI,CAACgB,QAAQ,CAACE,cAAc,EAAI,CAACF,QAAQ,CAACG,YAAY,EAAI,CAACH,QAAQ,CAACI,YAAa,CAClG4E,SAAS,CAAEhG,OAAO,cAAGR,IAAA,CAAC3B,gBAAgB,EAACwG,IAAI,CAAE,EAAG,CAAE,CAAC,cAAG7E,IAAA,CAACZ,QAAQ,GAAE,CAAE,CAAAuF,QAAA,CACpE,OAED,CAAQ,CAAC,EACI,CAAC,EACV,CAAC,CAEb,CAAC,IAAM,IAAIzD,UAAU,GAAK,kBAAkB,CAAE,CAC5C,mBACEhB,KAAA,CAAC7C,MAAM,EAACiH,IAAI,CAAEtD,UAAW,CAACoE,OAAO,CAAEvC,iBAAkB,CAACwC,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAX,QAAA,eAC3E3E,IAAA,CAAC1C,WAAW,EAAAqH,QAAA,CAAC,iCAA+B,CAAa,CAAC,cAC1D3E,IAAA,CAACzC,aAAa,EAAAoH,QAAA,CACXnE,OAAO,cACNR,IAAA,CAAC3B,gBAAgB,GAAE,CAAC,CAClBqC,OAAO,CAAC+D,MAAM,GAAK,CAAC,cACtBzE,IAAA,CAAC5B,KAAK,EAACsG,QAAQ,CAAC,MAAM,CAAAC,QAAA,CAAC,6BAA2B,CAAO,CAAC,cAE1D3E,IAAA,CAAClC,IAAI,EAAA6G,QAAA,CACFjE,OAAO,CAACoE,GAAG,CAAE/B,OAAO,eACnB/C,IAAA,CAACjC,QAAQ,EACP0I,MAAM,MAENzB,OAAO,CAAEA,CAAA,GAAMlC,mBAAmB,CAACC,OAAO,CAAE,CAAA4B,QAAA,cAE5C3E,IAAA,CAAChC,YAAY,EACX0I,OAAO,CAAE,GAAG3D,OAAO,CAACrB,cAAc,MAAMqB,OAAO,CAACnB,YAAY,EAAG,CAC/D+E,SAAS,CAAE,SAAS,GAAI,CAAAjE,IAAI,CAACK,OAAO,CAACpB,YAAY,CAAC,CAACoD,kBAAkB,CAAC,CAAC,YAAYhC,OAAO,CAAClB,OAAO,EAAI,eAAe,EAAG,CACzH,CAAC,EANGkB,OAAO,CAACE,UAOL,CACX,CAAC,CACE,CACP,CACY,CAAC,cAChBjD,IAAA,CAACxC,aAAa,EAAAmH,QAAA,cACZ3E,IAAA,CAACjD,MAAM,EAACiI,OAAO,CAAEnC,iBAAkB,CAAA8B,QAAA,CAAC,SAAO,CAAQ,CAAC,CACvC,CAAC,EACV,CAAC,CAEb,CAAC,IAAM,IAAIzD,UAAU,GAAK,gBAAgB,CAAE,CAC1C,mBACEhB,KAAA,CAAC7C,MAAM,EAACiH,IAAI,CAAEtD,UAAW,CAACoE,OAAO,CAAEvC,iBAAkB,CAACwC,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAX,QAAA,eAC3E3E,IAAA,CAAC1C,WAAW,EAAAqH,QAAA,CAAC,iBAAe,CAAa,CAAC,cAC1C3E,IAAA,CAACzC,aAAa,EAAAoH,QAAA,CACX,CAACvD,eAAe,CACfZ,OAAO,cACLR,IAAA,CAAC3B,gBAAgB,GAAE,CAAC,CAClBqC,OAAO,CAAC+D,MAAM,GAAK,CAAC,cACtBzE,IAAA,CAAC5B,KAAK,EAACsG,QAAQ,CAAC,MAAM,CAAAC,QAAA,CAAC,6BAA2B,CAAO,CAAC,cAE1D3E,IAAA,CAAClC,IAAI,EAAA6G,QAAA,CACFjE,OAAO,CAACoE,GAAG,CAAE/B,OAAO,eACnB/C,IAAA,CAACjC,QAAQ,EACP0I,MAAM,MAENzB,OAAO,CAAEA,CAAA,GAAM3D,kBAAkB,CAAC0B,OAAO,CAAE,CAAA4B,QAAA,cAE3C3E,IAAA,CAAChC,YAAY,EACX0I,OAAO,CAAE,GAAG3D,OAAO,CAACrB,cAAc,MAAMqB,OAAO,CAACnB,YAAY,EAAG,CAC/D+E,SAAS,CAAE,SAAS,GAAI,CAAAjE,IAAI,CAACK,OAAO,CAACpB,YAAY,CAAC,CAACoD,kBAAkB,CAAC,CAAC,EAAG,CAC3E,CAAC,EANGhC,OAAO,CAACE,UAOL,CACX,CAAC,CACE,CACP,cAED/C,KAAA,CAACrD,GAAG,EAAA8H,QAAA,eACFzE,KAAA,CAAC9B,KAAK,EAACsG,QAAQ,CAAC,SAAS,CAACe,EAAE,CAAE,CAAEmB,EAAE,CAAE,CAAE,CAAE,CAAAjC,QAAA,EAAC,2CACE,CAACvD,eAAe,CAACM,cAAc,CAAC,GAC3E,EAAO,CAAC,cACR1B,IAAA,CAAClD,UAAU,EAACiJ,OAAO,CAAC,OAAO,CAAApB,QAAA,CAAC,gDAE5B,CAAY,CAAC,EACV,CACN,CACY,CAAC,cAChBzE,KAAA,CAAC1C,aAAa,EAAAmH,QAAA,eACZ3E,IAAA,CAACjD,MAAM,EAACiI,OAAO,CAAEnC,iBAAkB,CAAA8B,QAAA,CAAC,SAAO,CAAQ,CAAC,CACnDvD,eAAe,eACdpB,IAAA,CAACjD,MAAM,EACLiI,OAAO,CAAElB,oBAAqB,CAC9ByC,QAAQ,CAAE/F,OAAQ,CAClB0E,KAAK,CAAC,OAAO,CACbsB,SAAS,CAAEhG,OAAO,cAAGR,IAAA,CAAC3B,gBAAgB,EAACwG,IAAI,CAAE,EAAG,CAAE,CAAC,cAAG7E,IAAA,CAACd,UAAU,GAAE,CAAE,CAAAyF,QAAA,CACtE,SAED,CAAQ,CACT,EACY,CAAC,EACV,CAAC,CAEb,CAAC,IAAM,IAAIzD,UAAU,GAAK,eAAe,CAAE,CACzC,mBACEhB,KAAA,CAAC7C,MAAM,EAACiH,IAAI,CAAEtD,UAAW,CAACoE,OAAO,CAAEvC,iBAAkB,CAACwC,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAX,QAAA,eAC3E3E,IAAA,CAAC1C,WAAW,EAAAqH,QAAA,CAAC,+BAA6B,CAAa,CAAC,cACxD3E,IAAA,CAACzC,aAAa,EAAAoH,QAAA,CACXnE,OAAO,cACNR,IAAA,CAAC3B,gBAAgB,GAAE,CAAC,CAClBqC,OAAO,CAAC+D,MAAM,GAAK,CAAC,cACtBzE,IAAA,CAAC5B,KAAK,EAACsG,QAAQ,CAAC,MAAM,CAAAC,QAAA,CAAC,6BAA2B,CAAO,CAAC,cAE1D3E,IAAA,CAAClC,IAAI,EAAA6G,QAAA,CACFjE,OAAO,CAACoE,GAAG,CAAE/B,OAAO,eACnB/C,IAAA,CAACjC,QAAQ,EACP0I,MAAM,MAENzB,OAAO,CAAEA,CAAA,GAAMlC,mBAAmB,CAACC,OAAO,CAAE,CAAA4B,QAAA,cAE5C3E,IAAA,CAAChC,YAAY,EACX0I,OAAO,CAAE,GAAG3D,OAAO,CAACrB,cAAc,MAAMqB,OAAO,CAACnB,YAAY,EAAG,CAC/D+E,SAAS,CAAE,SAAS,GAAI,CAAAjE,IAAI,CAACK,OAAO,CAACpB,YAAY,CAAC,CAACoD,kBAAkB,CAAC,CAAC,YAAYhC,OAAO,CAAClB,OAAO,EAAI,eAAe,EAAG,CACzH,CAAC,EANGkB,OAAO,CAACE,UAOL,CACX,CAAC,CACE,CACP,CACY,CAAC,cAChBjD,IAAA,CAACxC,aAAa,EAAAmH,QAAA,cACZ3E,IAAA,CAACjD,MAAM,EAACiI,OAAO,CAAEnC,iBAAkB,CAAA8B,QAAA,CAAC,SAAO,CAAQ,CAAC,CACvC,CAAC,EACV,CAAC,CAEb,CAAC,IAAM,IAAIzD,UAAU,GAAK,sBAAsB,CAAE,CAChD,mBACEhB,KAAA,CAAC7C,MAAM,EAACiH,IAAI,CAAEtD,UAAW,CAACoE,OAAO,CAAEvC,iBAAkB,CAACwC,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAX,QAAA,eAC3E3E,IAAA,CAAC1C,WAAW,EAAAqH,QAAA,CAAC,sCAAoC,CAAa,CAAC,cAC/D3E,IAAA,CAACzC,aAAa,EAAAoH,QAAA,CACXnE,OAAO,cACNR,IAAA,CAAC3B,gBAAgB,GAAE,CAAC,CAClBuC,IAAI,CAAC6D,MAAM,GAAK,CAAC,cACnBzE,IAAA,CAAC5B,KAAK,EAACsG,QAAQ,CAAC,MAAM,CAAAC,QAAA,CAAC,yBAAuB,CAAO,CAAC,cAEtD3E,IAAA,CAAClC,IAAI,EAAA6G,QAAA,CACF/D,IAAI,CAACkE,GAAG,CAAE3B,IAAI,eACbnD,IAAA,CAACjC,QAAQ,EACP0I,MAAM,MAENzB,OAAO,CAAEA,CAAA,GAAM9B,gBAAgB,CAACC,IAAI,CAAE,CAAAwB,QAAA,cAEtC3E,IAAA,CAAChC,YAAY,EACX0I,OAAO,CAAEvD,IAAI,CAACtB,OAAQ,CACtB8E,SAAS,CAAE,GAAGxD,IAAI,CAAC0D,SAAS,EAAI,KAAK,UAAU1D,IAAI,CAAC2D,mBAAmB,EAAI,KAAK,OAAO3D,IAAI,CAAC4D,iBAAiB,EAAI,KAAK,EAAG,CAC1H,CAAC,EANG5D,IAAI,CAACtB,OAOF,CACX,CAAC,CACE,CACP,CACY,CAAC,cAChB7B,IAAA,CAACxC,aAAa,EAAAmH,QAAA,cACZ3E,IAAA,CAACjD,MAAM,EAACiI,OAAO,CAAEnC,iBAAkB,CAAA8B,QAAA,CAAC,SAAO,CAAQ,CAAC,CACvC,CAAC,EACV,CAAC,CAEb,CAAC,IAAM,IAAIzD,UAAU,GAAK,yBAAyB,CAAE,CACnD,mBACEhB,KAAA,CAAC7C,MAAM,EAACiH,IAAI,CAAEtD,UAAW,CAACoE,OAAO,CAAEvC,iBAAkB,CAACwC,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAX,QAAA,eAC3EzE,KAAA,CAAC5C,WAAW,EAAAqH,QAAA,EAAC,yCAAuC,CAACrD,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEO,OAAO,EAAc,CAAC,cACzF7B,IAAA,CAACzC,aAAa,EAAAoH,QAAA,CACXnE,OAAO,cACNR,IAAA,CAAC3B,gBAAgB,GAAE,CAAC,CAClBqC,OAAO,CAAC+D,MAAM,GAAK,CAAC,cACtBzE,IAAA,CAAC5B,KAAK,EAACsG,QAAQ,CAAC,MAAM,CAAAC,QAAA,CAAC,6BAA2B,CAAO,CAAC,cAE1D3E,IAAA,CAAClC,IAAI,EAAA6G,QAAA,CACFjE,OAAO,CAACoE,GAAG,CAAE/B,OAAO,eACnB/C,IAAA,CAACjC,QAAQ,EACP0I,MAAM,MAENzB,OAAO,CAAEA,CAAA,GAAM3D,kBAAkB,CAAC0B,OAAO,CAAE,CAAA4B,QAAA,cAE3C3E,IAAA,CAAChC,YAAY,EACX0I,OAAO,CAAE,GAAG3D,OAAO,CAACrB,cAAc,MAAMqB,OAAO,CAACnB,YAAY,EAAG,CAC/D+E,SAAS,CAAE,SAAS,GAAI,CAAAjE,IAAI,CAACK,OAAO,CAACpB,YAAY,CAAC,CAACoD,kBAAkB,CAAC,CAAC,EAAG,CAC3E,CAAC,EANGhC,OAAO,CAACE,UAOL,CACX,CAAC,CACE,CACP,CACY,CAAC,cAChB/C,KAAA,CAAC1C,aAAa,EAAAmH,QAAA,eACZ3E,IAAA,CAACjD,MAAM,EAACiI,OAAO,CAAEnC,iBAAkB,CAAA8B,QAAA,CAAC,SAAO,CAAQ,CAAC,CACnDvD,eAAe,eACdpB,IAAA,CAACjD,MAAM,EACLiI,OAAO,CAAEhB,oBAAqB,CAC9BuC,QAAQ,CAAE/F,OAAQ,CAClBgG,SAAS,CAAEhG,OAAO,cAAGR,IAAA,CAAC3B,gBAAgB,EAACwG,IAAI,CAAE,EAAG,CAAE,CAAC,cAAG7E,IAAA,CAACZ,QAAQ,GAAE,CAAE,CAAAuF,QAAA,CACpE,SAED,CAAQ,CACT,EACY,CAAC,EACV,CAAC,CAEb,CAEA,MAAO,KAAI,CACb,CAAC,CAED,mBACEzE,KAAA,CAACrD,GAAG,EAAA8H,QAAA,EACD7D,cAAc,GAAK,mBAAmB,EAAI,CAACE,UAAU,cACpDd,KAAA,CAAClD,KAAK,EAACyI,EAAE,CAAE,CAAEuB,CAAC,CAAE,CAAE,CAAE,CAAArC,QAAA,eAClB3E,IAAA,CAAClD,UAAU,EAACiJ,OAAO,CAAC,IAAI,CAACkB,YAAY,MAAAtC,QAAA,CAAC,SAEtC,CAAY,CAAC,CAEZnE,OAAO,cACNR,IAAA,CAACnD,GAAG,EAAC4I,EAAE,CAAE,CAAEyB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAzC,QAAA,cAC5D3E,IAAA,CAAC3B,gBAAgB,GAAE,CAAC,CACjB,CAAC,CAENmG,kBAAkB,CAAC,CACpB,EACI,CAAC,CACN,CAACxD,UAAU,cACbhB,IAAA,CAAChD,KAAK,EAACyI,EAAE,CAAE,CAAEuB,CAAC,CAAE,CAAC,CAAEK,SAAS,CAAE,OAAO,CAAEH,OAAO,CAAE,MAAM,CAAEI,UAAU,CAAE,QAAQ,CAAEH,cAAc,CAAE,QAAS,CAAE,CAAAxC,QAAA,CACtG,CAAC7D,cAAc,cACdd,IAAA,CAAClD,UAAU,EAACiJ,OAAO,CAAC,OAAO,CAAApB,QAAA,CAAC,wDAE5B,CAAY,CAAC,cAEbzE,KAAA,CAACrD,GAAG,EAAC4I,EAAE,CAAE,CAAE8B,SAAS,CAAE,QAAS,CAAE,CAAA5C,QAAA,eAC/BzE,KAAA,CAACpD,UAAU,EAACiJ,OAAO,CAAC,IAAI,CAACkB,YAAY,MAAAtC,QAAA,EAClC7D,cAAc,GAAK,aAAa,EAAI,oBAAoB,CACxDA,cAAc,GAAK,iBAAiB,EAAI,kBAAkB,CAC1DA,cAAc,GAAK,gBAAgB,EAAI,iBAAiB,CACxDA,cAAc,GAAK,eAAe,EAAI,gBAAgB,CACtDA,cAAc,GAAK,gBAAgB,EAAI,wBAAwB,EACtD,CAAC,cACbd,IAAA,CAAC3B,gBAAgB,EAACoH,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,EAChC,CACN,CACI,CAAC,CACN,IAAI,CAEPP,YAAY,CAAC,CAAC,EACZ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAhF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}