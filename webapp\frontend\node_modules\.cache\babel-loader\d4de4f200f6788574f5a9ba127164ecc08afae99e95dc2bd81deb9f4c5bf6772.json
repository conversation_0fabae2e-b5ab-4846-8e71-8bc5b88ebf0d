{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\AccessoRapidoComanda.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Card, CardContent, Typography, TextField, Button, Alert, Paper, Chip, List, ListItem, ListItemText, Divider, Grid } from '@mui/material';\nimport { Search as SearchIcon, Assignment as ComandaIcon, Person as PersonIcon, Schedule as ScheduleIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AccessoRapidoComanda = () => {\n  _s();\n  const [codiceComanda, setCodiceComanda] = useState('');\n  const [comanda, setComanda] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const handleCercaComanda = async () => {\n    if (!codiceComanda.trim()) {\n      setError('Inserisci un codice comanda');\n      return;\n    }\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await comandeService.getDettagliComanda(codiceComanda.trim());\n      setComanda(response);\n    } catch (err) {\n      console.error('Errore nella ricerca:', err);\n      setError('Comanda non trovata o errore nella ricerca');\n      setComanda(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleCercaComanda();\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Collegamento Arrivo';\n      case 'TESTING':\n        return 'Testing';\n      default:\n        return tipo;\n    }\n  };\n  const getStatoColor = stato => {\n    switch (stato) {\n      case 'CREATA':\n        return 'default';\n      case 'ASSEGNATA':\n        return 'primary';\n      case 'IN_CORSO':\n        return 'warning';\n      case 'COMPLETATA':\n        return 'success';\n      case 'ANNULLATA':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const getPrioritaColor = priorita => {\n    switch (priorita) {\n      case 'BASSA':\n        return 'default';\n      case 'NORMALE':\n        return 'primary';\n      case 'ALTA':\n        return 'warning';\n      case 'URGENTE':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      maxWidth: 800,\n      mx: 'auto',\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3,\n        textAlign: 'center',\n        bgcolor: 'primary.main',\n        color: 'white'\n      },\n      children: [/*#__PURE__*/_jsxDEV(ComandaIcon, {\n        sx: {\n          fontSize: 48,\n          mb: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: \"Accesso Rapido Comanda\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: \"Simula l'accesso da app mobile per responsabili\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Cerca Comanda per Codice\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Codice Comanda\",\n            value: codiceComanda,\n            onChange: e => setCodiceComanda(e.target.value.toUpperCase()),\n            onKeyPress: handleKeyPress,\n            placeholder: \"es: POS20241201001\",\n            helperText: \"Inserisci il codice univoco della comanda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 26\n            }, this),\n            onClick: handleCercaComanda,\n            disabled: loading,\n            sx: {\n              minWidth: 120\n            },\n            children: loading ? 'Cerca...' : 'Cerca'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 9\n    }, this), comanda && /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2,\n          mb: 2,\n          children: [/*#__PURE__*/_jsxDEV(ComandaIcon, {\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            children: comanda.codice_comanda\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: comanda.stato,\n            color: getStatoColor(comanda.stato),\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              color: \"primary\",\n              children: \"\\uD83D\\uDCCB Informazioni Comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              dense: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Tipo Attivit\\xE0\",\n                  secondary: getTipoComandaLabel(comanda.tipo_comanda)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Priorit\\xE0\",\n                  secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: comanda.priorita || 'NORMALE',\n                    color: getPrioritaColor(comanda.priorita || 'NORMALE'),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Responsabile\",\n                  secondary: /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 1,\n                    children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 27\n                    }, this), comanda.responsabile]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this), comanda.data_scadenza && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Scadenza\",\n                    secondary: /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 1,\n                      children: [/*#__PURE__*/_jsxDEV(ScheduleIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 200,\n                        columnNumber: 31\n                      }, this), new Date(comanda.data_scadenza).toLocaleDateString('it-IT')]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 199,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              color: \"primary\",\n              children: \"\\uD83D\\uDD27 Dettagli Lavoro\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              dense: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Descrizione\",\n                  secondary: comanda.descrizione || 'Nessuna descrizione'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 19\n              }, this), comanda.note_capo_cantiere && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Istruzioni Capo Cantiere\",\n                    secondary: comanda.note_capo_cantiere\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Cavi Assegnati\",\n                  secondary: `${comanda.numero_cavi_assegnati || 0} cavi`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Progresso\",\n                  secondary: `${(comanda.percentuale_completamento || 0).toFixed(1)}% completato`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\uD83D\\uDE80 Futuro App Mobile:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this), \" Qui il responsabile potr\\xE0 aggiornare il rapportino di lavoro giornaliero:\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this), \"\\u2022 Segnare cavi posati/collegati/testati\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this), \"\\u2022 Indicare bobine utilizzate\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this), \"\\u2022 Aggiungere note e problemi riscontrati\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this), \"\\u2022 Aggiornare il progresso in tempo reale\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: 'grey.100',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"\\uD83D\\uDCDD Rapportino Lavoro (Simulazione)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Questa sezione sar\\xE0 implementata nell'app mobile per permettere al responsabile di aggiornare il lavoro svolto direttamente dal cantiere.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 9\n    }, this), !comanda && !error && /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"\\uD83D\\uDCA1 Come funziona\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          paragraph: true,\n          children: [\"1. Il \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"capo cantiere\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 21\n          }, this), \" crea le comande dal sistema web\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          paragraph: true,\n          children: [\"2. Ogni comanda ha un \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"codice univoco\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 37\n          }, this), \" (es: POS20241201001)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          paragraph: true,\n          children: [\"3. Il \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"responsabile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 21\n          }, this), \" accede alla comanda tramite app mobile usando il codice\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          paragraph: true,\n          children: [\"4. Il responsabile usa la comanda come \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"rapportino di lavoro giornaliero\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 54\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: \"5. Il sistema traccia tutto il lavoro svolto per reportistica e controllo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n};\n_s(AccessoRapidoComanda, \"dJJS1iYyV8YcIJgUbbtoQvbeGTc=\");\n_c = AccessoRapidoComanda;\nexport default AccessoRapidoComanda;\nvar _c;\n$RefreshReg$(_c, \"AccessoRapidoComanda\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "TextField", "<PERSON><PERSON>", "<PERSON><PERSON>", "Paper", "Chip", "List", "ListItem", "ListItemText", "Divider", "Grid", "Search", "SearchIcon", "Assignment", "ComandaIcon", "Person", "PersonIcon", "Schedule", "ScheduleIcon", "comandeService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AccessoRapidoComanda", "_s", "codiceComanda", "setCodiceComanda", "comanda", "setComanda", "loading", "setLoading", "error", "setError", "handleCercaComanda", "trim", "response", "getDettagliComanda", "err", "console", "handleKeyPress", "e", "key", "getTipoComandaLabel", "tipo", "getStatoColor", "stato", "getPrioritaColor", "priorita", "sx", "max<PERSON><PERSON><PERSON>", "mx", "p", "children", "mb", "textAlign", "bgcolor", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "gutterBottom", "display", "gap", "alignItems", "fullWidth", "label", "value", "onChange", "target", "toUpperCase", "onKeyPress", "placeholder", "helperText", "startIcon", "onClick", "disabled", "min<PERSON><PERSON><PERSON>", "severity", "codice_comanda", "size", "container", "spacing", "item", "xs", "md", "dense", "primary", "secondary", "tipo_comanda", "responsabile", "data_scadenza", "Date", "toLocaleDateString", "descrizione", "note_capo_cantiere", "numero_cavi_assegnati", "percentuale_completamento", "toFixed", "mt", "borderRadius", "paragraph", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/AccessoRapidoComanda.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  TextField,\n  Button,\n  Alert,\n  Paper,\n  Chip,\n  List,\n  ListItem,\n  ListItemText,\n  Divider,\n  Grid\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Assignment as ComandaIcon,\n  Person as PersonIcon,\n  Schedule as ScheduleIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\n\nconst AccessoRapidoComanda = () => {\n  const [codiceComanda, setCodiceComanda] = useState('');\n  const [comanda, setComanda] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const handleCercaComanda = async () => {\n    if (!codiceComanda.trim()) {\n      setError('Inserisci un codice comanda');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await comandeService.getDettagliComanda(codiceComanda.trim());\n      setComanda(response);\n    } catch (err) {\n      console.error('Errore nella ricerca:', err);\n      setError('Comanda non trovata o errore nella ricerca');\n      setComanda(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter') {\n      handleCercaComanda();\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA': return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO': return 'Collegamento Arrivo';\n      case 'TESTING': return 'Testing';\n      default: return tipo;\n    }\n  };\n\n  const getStatoColor = (stato) => {\n    switch (stato) {\n      case 'CREATA': return 'default';\n      case 'ASSEGNATA': return 'primary';\n      case 'IN_CORSO': return 'warning';\n      case 'COMPLETATA': return 'success';\n      case 'ANNULLATA': return 'error';\n      default: return 'default';\n    }\n  };\n\n  const getPrioritaColor = (priorita) => {\n    switch (priorita) {\n      case 'BASSA': return 'default';\n      case 'NORMALE': return 'primary';\n      case 'ALTA': return 'warning';\n      case 'URGENTE': return 'error';\n      default: return 'default';\n    }\n  };\n\n  return (\n    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>\n      {/* Header */}\n      <Paper sx={{ p: 3, mb: 3, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>\n        <ComandaIcon sx={{ fontSize: 48, mb: 1 }} />\n        <Typography variant=\"h4\" gutterBottom>\n          Accesso Rapido Comanda\n        </Typography>\n        <Typography variant=\"body1\">\n          Simula l'accesso da app mobile per responsabili\n        </Typography>\n      </Paper>\n\n      {/* Ricerca */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>\n            Cerca Comanda per Codice\n          </Typography>\n          <Box display=\"flex\" gap={2} alignItems=\"center\">\n            <TextField\n              fullWidth\n              label=\"Codice Comanda\"\n              value={codiceComanda}\n              onChange={(e) => setCodiceComanda(e.target.value.toUpperCase())}\n              onKeyPress={handleKeyPress}\n              placeholder=\"es: POS20241201001\"\n              helperText=\"Inserisci il codice univoco della comanda\"\n            />\n            <Button\n              variant=\"contained\"\n              startIcon={<SearchIcon />}\n              onClick={handleCercaComanda}\n              disabled={loading}\n              sx={{ minWidth: 120 }}\n            >\n              {loading ? 'Cerca...' : 'Cerca'}\n            </Button>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* Errore */}\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Risultato */}\n      {comanda && (\n        <Card>\n          <CardContent>\n            <Box display=\"flex\" alignItems=\"center\" gap={2} mb={2}>\n              <ComandaIcon color=\"primary\" />\n              <Typography variant=\"h5\">\n                {comanda.codice_comanda}\n              </Typography>\n              <Chip \n                label={comanda.stato}\n                color={getStatoColor(comanda.stato)}\n                size=\"small\"\n              />\n            </Box>\n\n            <Grid container spacing={3}>\n              {/* Informazioni Principali */}\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"h6\" gutterBottom color=\"primary\">\n                  📋 Informazioni Comanda\n                </Typography>\n                <List dense>\n                  <ListItem>\n                    <ListItemText \n                      primary=\"Tipo Attività\" \n                      secondary={getTipoComandaLabel(comanda.tipo_comanda)} \n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText \n                      primary=\"Priorità\" \n                      secondary={\n                        <Chip \n                          label={comanda.priorita || 'NORMALE'}\n                          color={getPrioritaColor(comanda.priorita || 'NORMALE')}\n                          size=\"small\"\n                        />\n                      }\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText \n                      primary=\"Responsabile\" \n                      secondary={\n                        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                          <PersonIcon fontSize=\"small\" />\n                          {comanda.responsabile}\n                        </Box>\n                      }\n                    />\n                  </ListItem>\n                  {comanda.data_scadenza && (\n                    <>\n                      <Divider />\n                      <ListItem>\n                        <ListItemText \n                          primary=\"Scadenza\" \n                          secondary={\n                            <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                              <ScheduleIcon fontSize=\"small\" />\n                              {new Date(comanda.data_scadenza).toLocaleDateString('it-IT')}\n                            </Box>\n                          }\n                        />\n                      </ListItem>\n                    </>\n                  )}\n                </List>\n              </Grid>\n\n              {/* Dettagli Lavoro */}\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"h6\" gutterBottom color=\"primary\">\n                  🔧 Dettagli Lavoro\n                </Typography>\n                <List dense>\n                  <ListItem>\n                    <ListItemText \n                      primary=\"Descrizione\" \n                      secondary={comanda.descrizione || 'Nessuna descrizione'} \n                    />\n                  </ListItem>\n                  {comanda.note_capo_cantiere && (\n                    <>\n                      <Divider />\n                      <ListItem>\n                        <ListItemText \n                          primary=\"Istruzioni Capo Cantiere\" \n                          secondary={comanda.note_capo_cantiere} \n                        />\n                      </ListItem>\n                    </>\n                  )}\n                  <Divider />\n                  <ListItem>\n                    <ListItemText \n                      primary=\"Cavi Assegnati\" \n                      secondary={`${comanda.numero_cavi_assegnati || 0} cavi`} \n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText \n                      primary=\"Progresso\" \n                      secondary={`${(comanda.percentuale_completamento || 0).toFixed(1)}% completato`} \n                    />\n                  </ListItem>\n                </List>\n              </Grid>\n            </Grid>\n\n            {/* Note Future App Mobile */}\n            <Alert severity=\"info\" sx={{ mt: 3 }}>\n              <Typography variant=\"body2\">\n                <strong>🚀 Futuro App Mobile:</strong> Qui il responsabile potrà aggiornare il rapportino di lavoro giornaliero:\n                <br />• Segnare cavi posati/collegati/testati\n                <br />• Indicare bobine utilizzate\n                <br />• Aggiungere note e problemi riscontrati\n                <br />• Aggiornare il progresso in tempo reale\n              </Typography>\n            </Alert>\n\n            {/* Simulazione Rapportino */}\n            <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                📝 Rapportino Lavoro (Simulazione)\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Questa sezione sarà implementata nell'app mobile per permettere al responsabile \n                di aggiornare il lavoro svolto direttamente dal cantiere.\n              </Typography>\n            </Box>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Istruzioni */}\n      {!comanda && !error && (\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              💡 Come funziona\n            </Typography>\n            <Typography variant=\"body2\" paragraph>\n              1. Il <strong>capo cantiere</strong> crea le comande dal sistema web\n            </Typography>\n            <Typography variant=\"body2\" paragraph>\n              2. Ogni comanda ha un <strong>codice univoco</strong> (es: POS20241201001)\n            </Typography>\n            <Typography variant=\"body2\" paragraph>\n              3. Il <strong>responsabile</strong> accede alla comanda tramite app mobile usando il codice\n            </Typography>\n            <Typography variant=\"body2\" paragraph>\n              4. Il responsabile usa la comanda come <strong>rapportino di lavoro giornaliero</strong>\n            </Typography>\n            <Typography variant=\"body2\">\n              5. Il sistema traccia tutto il lavoro svolto per reportistica e controllo\n            </Typography>\n          </CardContent>\n        </Card>\n      )}\n    </Box>\n  );\n};\n\nexport default AccessoRapidoComanda;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,OAAO,EACPC,IAAI,QACC,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,WAAW,EACzBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3D,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAMsC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACR,aAAa,CAACS,IAAI,CAAC,CAAC,EAAE;MACzBF,QAAQ,CAAC,6BAA6B,CAAC;MACvC;IACF;IAEA,IAAI;MACFF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMG,QAAQ,GAAG,MAAMjB,cAAc,CAACkB,kBAAkB,CAACX,aAAa,CAACS,IAAI,CAAC,CAAC,CAAC;MAC9EN,UAAU,CAACO,QAAQ,CAAC;IACtB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACP,KAAK,CAAC,uBAAuB,EAAEM,GAAG,CAAC;MAC3CL,QAAQ,CAAC,4CAA4C,CAAC;MACtDJ,UAAU,CAAC,IAAI,CAAC;IAClB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMS,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBR,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC;EAED,MAAMS,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B,KAAK,uBAAuB;QAAE,OAAO,uBAAuB;MAC5D,KAAK,qBAAqB;QAAE,OAAO,qBAAqB;MACxD,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAOA,IAAI;IACtB;EACF,CAAC;EAED,MAAMC,aAAa,GAAIC,KAAK,IAAK;IAC/B,QAAQA,KAAK;MACX,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,YAAY;QAAE,OAAO,SAAS;MACnC,KAAK,WAAW;QAAE,OAAO,OAAO;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,oBACE3B,OAAA,CAACxB,GAAG;IAACoD,EAAE,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,EAAE,EAAE,MAAM;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAE3ChC,OAAA,CAACjB,KAAK;MAAC6C,EAAE,EAAE;QAAEG,CAAC,EAAE,CAAC;QAAEE,EAAE,EAAE,CAAC;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE,cAAc;QAAEC,KAAK,EAAE;MAAQ,CAAE;MAAAJ,QAAA,gBACvFhC,OAAA,CAACP,WAAW;QAACmC,EAAE,EAAE;UAAES,QAAQ,EAAE,EAAE;UAAEJ,EAAE,EAAE;QAAE;MAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5CzC,OAAA,CAACrB,UAAU;QAAC+D,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAX,QAAA,EAAC;MAEtC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbzC,OAAA,CAACrB,UAAU;QAAC+D,OAAO,EAAC,OAAO;QAAAV,QAAA,EAAC;MAE5B;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGRzC,OAAA,CAACvB,IAAI;MAACmD,EAAE,EAAE;QAAEK,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,eAClBhC,OAAA,CAACtB,WAAW;QAAAsD,QAAA,gBACVhC,OAAA,CAACrB,UAAU;UAAC+D,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAX,QAAA,EAAC;QAEtC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzC,OAAA,CAACxB,GAAG;UAACoE,OAAO,EAAC,MAAM;UAACC,GAAG,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAAd,QAAA,gBAC7ChC,OAAA,CAACpB,SAAS;YACRmE,SAAS;YACTC,KAAK,EAAC,gBAAgB;YACtBC,KAAK,EAAE5C,aAAc;YACrB6C,QAAQ,EAAG9B,CAAC,IAAKd,gBAAgB,CAACc,CAAC,CAAC+B,MAAM,CAACF,KAAK,CAACG,WAAW,CAAC,CAAC,CAAE;YAChEC,UAAU,EAAElC,cAAe;YAC3BmC,WAAW,EAAC,oBAAoB;YAChCC,UAAU,EAAC;UAA2C;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACFzC,OAAA,CAACnB,MAAM;YACL6D,OAAO,EAAC,WAAW;YACnBc,SAAS,eAAExD,OAAA,CAACT,UAAU;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BgB,OAAO,EAAE5C,kBAAmB;YAC5B6C,QAAQ,EAAEjD,OAAQ;YAClBmB,EAAE,EAAE;cAAE+B,QAAQ,EAAE;YAAI,CAAE;YAAA3B,QAAA,EAErBvB,OAAO,GAAG,UAAU,GAAG;UAAO;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGN9B,KAAK,iBACJX,OAAA,CAAClB,KAAK;MAAC8E,QAAQ,EAAC,OAAO;MAAChC,EAAE,EAAE;QAAEK,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,EACnCrB;IAAK;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAGAlC,OAAO,iBACNP,OAAA,CAACvB,IAAI;MAAAuD,QAAA,eACHhC,OAAA,CAACtB,WAAW;QAAAsD,QAAA,gBACVhC,OAAA,CAACxB,GAAG;UAACoE,OAAO,EAAC,MAAM;UAACE,UAAU,EAAC,QAAQ;UAACD,GAAG,EAAE,CAAE;UAACZ,EAAE,EAAE,CAAE;UAAAD,QAAA,gBACpDhC,OAAA,CAACP,WAAW;YAAC2C,KAAK,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/BzC,OAAA,CAACrB,UAAU;YAAC+D,OAAO,EAAC,IAAI;YAAAV,QAAA,EACrBzB,OAAO,CAACsD;UAAc;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACbzC,OAAA,CAAChB,IAAI;YACHgE,KAAK,EAAEzC,OAAO,CAACkB,KAAM;YACrBW,KAAK,EAAEZ,aAAa,CAACjB,OAAO,CAACkB,KAAK,CAAE;YACpCqC,IAAI,EAAC;UAAO;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENzC,OAAA,CAACX,IAAI;UAAC0E,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAhC,QAAA,gBAEzBhC,OAAA,CAACX,IAAI;YAAC4E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnC,QAAA,gBACvBhC,OAAA,CAACrB,UAAU;cAAC+D,OAAO,EAAC,IAAI;cAACC,YAAY;cAACP,KAAK,EAAC,SAAS;cAAAJ,QAAA,EAAC;YAEtD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzC,OAAA,CAACf,IAAI;cAACmF,KAAK;cAAApC,QAAA,gBACThC,OAAA,CAACd,QAAQ;gBAAA8C,QAAA,eACPhC,OAAA,CAACb,YAAY;kBACXkF,OAAO,EAAC,kBAAe;kBACvBC,SAAS,EAAEhD,mBAAmB,CAACf,OAAO,CAACgE,YAAY;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACXzC,OAAA,CAACZ,OAAO;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXzC,OAAA,CAACd,QAAQ;gBAAA8C,QAAA,eACPhC,OAAA,CAACb,YAAY;kBACXkF,OAAO,EAAC,aAAU;kBAClBC,SAAS,eACPtE,OAAA,CAAChB,IAAI;oBACHgE,KAAK,EAAEzC,OAAO,CAACoB,QAAQ,IAAI,SAAU;oBACrCS,KAAK,EAAEV,gBAAgB,CAACnB,OAAO,CAACoB,QAAQ,IAAI,SAAS,CAAE;oBACvDmC,IAAI,EAAC;kBAAO;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACXzC,OAAA,CAACZ,OAAO;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXzC,OAAA,CAACd,QAAQ;gBAAA8C,QAAA,eACPhC,OAAA,CAACb,YAAY;kBACXkF,OAAO,EAAC,cAAc;kBACtBC,SAAS,eACPtE,OAAA,CAACxB,GAAG;oBAACoE,OAAO,EAAC,MAAM;oBAACE,UAAU,EAAC,QAAQ;oBAACD,GAAG,EAAE,CAAE;oBAAAb,QAAA,gBAC7ChC,OAAA,CAACL,UAAU;sBAAC0C,QAAQ,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAC9BlC,OAAO,CAACiE,YAAY;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,EACVlC,OAAO,CAACkE,aAAa,iBACpBzE,OAAA,CAAAE,SAAA;gBAAA8B,QAAA,gBACEhC,OAAA,CAACZ,OAAO;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACXzC,OAAA,CAACd,QAAQ;kBAAA8C,QAAA,eACPhC,OAAA,CAACb,YAAY;oBACXkF,OAAO,EAAC,UAAU;oBAClBC,SAAS,eACPtE,OAAA,CAACxB,GAAG;sBAACoE,OAAO,EAAC,MAAM;sBAACE,UAAU,EAAC,QAAQ;sBAACD,GAAG,EAAE,CAAE;sBAAAb,QAAA,gBAC7ChC,OAAA,CAACH,YAAY;wBAACwC,QAAQ,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EAChC,IAAIiC,IAAI,CAACnE,OAAO,CAACkE,aAAa,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAC;oBAAA;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC;cAAA,eACX,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPzC,OAAA,CAACX,IAAI;YAAC4E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnC,QAAA,gBACvBhC,OAAA,CAACrB,UAAU;cAAC+D,OAAO,EAAC,IAAI;cAACC,YAAY;cAACP,KAAK,EAAC,SAAS;cAAAJ,QAAA,EAAC;YAEtD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzC,OAAA,CAACf,IAAI;cAACmF,KAAK;cAAApC,QAAA,gBACThC,OAAA,CAACd,QAAQ;gBAAA8C,QAAA,eACPhC,OAAA,CAACb,YAAY;kBACXkF,OAAO,EAAC,aAAa;kBACrBC,SAAS,EAAE/D,OAAO,CAACqE,WAAW,IAAI;gBAAsB;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,EACVlC,OAAO,CAACsE,kBAAkB,iBACzB7E,OAAA,CAAAE,SAAA;gBAAA8B,QAAA,gBACEhC,OAAA,CAACZ,OAAO;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACXzC,OAAA,CAACd,QAAQ;kBAAA8C,QAAA,eACPhC,OAAA,CAACb,YAAY;oBACXkF,OAAO,EAAC,0BAA0B;oBAClCC,SAAS,EAAE/D,OAAO,CAACsE;kBAAmB;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC;cAAA,eACX,CACH,eACDzC,OAAA,CAACZ,OAAO;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXzC,OAAA,CAACd,QAAQ;gBAAA8C,QAAA,eACPhC,OAAA,CAACb,YAAY;kBACXkF,OAAO,EAAC,gBAAgB;kBACxBC,SAAS,EAAE,GAAG/D,OAAO,CAACuE,qBAAqB,IAAI,CAAC;gBAAQ;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACXzC,OAAA,CAACZ,OAAO;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXzC,OAAA,CAACd,QAAQ;gBAAA8C,QAAA,eACPhC,OAAA,CAACb,YAAY;kBACXkF,OAAO,EAAC,WAAW;kBACnBC,SAAS,EAAE,GAAG,CAAC/D,OAAO,CAACwE,yBAAyB,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;gBAAe;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPzC,OAAA,CAAClB,KAAK;UAAC8E,QAAQ,EAAC,MAAM;UAAChC,EAAE,EAAE;YAAEqD,EAAE,EAAE;UAAE,CAAE;UAAAjD,QAAA,eACnChC,OAAA,CAACrB,UAAU;YAAC+D,OAAO,EAAC,OAAO;YAAAV,QAAA,gBACzBhC,OAAA;cAAAgC,QAAA,EAAQ;YAAqB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,iFACtC,eAAAzC,OAAA;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gDACN,eAAAzC,OAAA;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,qCACN,eAAAzC,OAAA;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,iDACN,eAAAzC,OAAA;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,iDACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAGRzC,OAAA,CAACxB,GAAG;UAACoD,EAAE,EAAE;YAAEqD,EAAE,EAAE,CAAC;YAAElD,CAAC,EAAE,CAAC;YAAEI,OAAO,EAAE,UAAU;YAAE+C,YAAY,EAAE;UAAE,CAAE;UAAAlD,QAAA,gBAC7DhC,OAAA,CAACrB,UAAU;YAAC+D,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAX,QAAA,EAAC;UAEtC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzC,OAAA,CAACrB,UAAU;YAAC+D,OAAO,EAAC,OAAO;YAACN,KAAK,EAAC,gBAAgB;YAAAJ,QAAA,EAAC;UAGnD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP,EAGA,CAAClC,OAAO,IAAI,CAACI,KAAK,iBACjBX,OAAA,CAACvB,IAAI;MAAAuD,QAAA,eACHhC,OAAA,CAACtB,WAAW;QAAAsD,QAAA,gBACVhC,OAAA,CAACrB,UAAU;UAAC+D,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAX,QAAA,EAAC;QAEtC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzC,OAAA,CAACrB,UAAU;UAAC+D,OAAO,EAAC,OAAO;UAACyC,SAAS;UAAAnD,QAAA,GAAC,QAC9B,eAAAhC,OAAA;YAAAgC,QAAA,EAAQ;UAAa;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,oCACtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzC,OAAA,CAACrB,UAAU;UAAC+D,OAAO,EAAC,OAAO;UAACyC,SAAS;UAAAnD,QAAA,GAAC,wBACd,eAAAhC,OAAA;YAAAgC,QAAA,EAAQ;UAAc;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,yBACvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzC,OAAA,CAACrB,UAAU;UAAC+D,OAAO,EAAC,OAAO;UAACyC,SAAS;UAAAnD,QAAA,GAAC,QAC9B,eAAAhC,OAAA;YAAAgC,QAAA,EAAQ;UAAY;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,4DACrC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzC,OAAA,CAACrB,UAAU;UAAC+D,OAAO,EAAC,OAAO;UAACyC,SAAS;UAAAnD,QAAA,GAAC,yCACG,eAAAhC,OAAA;YAAAgC,QAAA,EAAQ;UAAgC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACbzC,OAAA,CAACrB,UAAU;UAAC+D,OAAO,EAAC,OAAO;UAAAV,QAAA,EAAC;QAE5B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrC,EAAA,CAtRID,oBAAoB;AAAAiF,EAAA,GAApBjF,oBAAoB;AAwR1B,eAAeA,oBAAoB;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}