{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Box, Paper, Typography, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, Divider, Alert, CircularProgress, FormHelperText, IconButton, Chip, Dialog, DialogTitle, DialogContent, DialogActions, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, List, ListItem, ListItemText, ListItemSecondaryAction } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, Cancel as CancelIcon, Warning as WarningIcon, Info as InfoIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stato per la gestione dei passi (mantenuto per compatibilità con funzioni esistenti)\n  const [activeStep, setActiveStep] = useState(0);\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null);\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = idBobina => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response || loadError.code === 'ECONNABORTED' || loadError.message && loadError.message.includes('Network Error')) {\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(`${API_URL}/cavi/${cantiereId}`, {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 30000 // 30 secondi\n            });\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Se c'è un cavo selezionato, carica le bobine compatibili\n      if (selectedCavo) {\n        console.log('Caricamento bobine compatibili per il cavo selezionato...');\n        try {\n          // Log dettagliato per debug\n          console.log('FRONTEND - Valori del cavo selezionato:', {\n            id_cavo: selectedCavo.id_cavo,\n            tipologia: selectedCavo.tipologia,\n            n_conduttori: selectedCavo.n_conduttori,\n            sezione: selectedCavo.sezione,\n            tipi: {\n              tipologia: typeof selectedCavo.tipologia,\n              n_conduttori: typeof selectedCavo.n_conduttori,\n              sezione: typeof selectedCavo.sezione\n            }\n          });\n\n          // Assicurati che i valori siano stringhe per la compatibilità\n          const tipologia = selectedCavo.tipologia !== undefined && selectedCavo.tipologia !== null ? String(selectedCavo.tipologia) : '';\n\n          // Gestione speciale per n_conduttori che potrebbe essere nel formato \"X x Y\"\n          let n_conduttori = selectedCavo.n_conduttori !== undefined && selectedCavo.n_conduttori !== null ? String(selectedCavo.n_conduttori) : '0';\n          // Se il formato è \"X x Y\", estrai solo il primo numero\n          if (n_conduttori.includes(' x ')) {\n            const parts = n_conduttori.split(' x ');\n            n_conduttori = parts[0];\n            console.log(`Formato n_conduttori 'X x Y' rilevato: ${selectedCavo.n_conduttori} -> ${n_conduttori}`);\n          }\n          const sezione = selectedCavo.sezione !== undefined && selectedCavo.sezione !== null ? String(selectedCavo.sezione) : '0';\n          console.log('FRONTEND - Parametri convertiti per API:', {\n            tipologia,\n            n_conduttori,\n            sezione\n          });\n\n          // Usa il metodo API per ottenere le bobine compatibili\n          const bobineCompatibili = await parcoCaviService.getBobineCompatibili(cantiereId, tipologia, n_conduttori, sezione);\n          if (bobineCompatibili && bobineCompatibili.length > 0) {\n            console.log(`Trovate ${bobineCompatibili.length} bobine compatibili via API`);\n            // Ordina le bobine per metri residui (decrescente)\n            bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n            setBobine(bobineCompatibili);\n            return;\n          } else {\n            console.log('Nessuna bobina compatibile trovata via API, carico tutte le bobine disponibili');\n            // Carica tutte le bobine disponibili\n            const bobineData = await parcoCaviService.getBobine(cantiereId);\n            // Filtra solo per stato (disponibile o in uso) ma non per compatibilità\n            const bobineUtilizzabili = bobineData.filter(bobina => (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') && bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata');\n            // Ordina le bobine per metri residui (decrescente)\n            bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n            setBobine(bobineUtilizzabili);\n            return;\n          }\n        } catch (error) {\n          console.error('Errore nel caricamento delle bobine compatibili:', error);\n          console.log('Fallback al caricamento di tutte le bobine...');\n        }\n      }\n\n      // Aggiungi un log per debug\n      console.log('Caricamento di tutte le bobine disponibili (fallback)...');\n\n      // Metodo fallback: carica tutte le bobine e filtra manualmente\n      console.log('Caricamento di tutte le bobine disponibili (fallback)...');\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n\n      // Filtra le bobine per stato (disponibile o in uso) e per compatibilità con il cavo selezionato\n      let bobineUtilizzabili = bobineData.filter(bobina => (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') && bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata');\n\n      // Se c'è un cavo selezionato, filtra ulteriormente per caratteristiche del cavo\n      if (selectedCavo) {\n        // Filtra per tipologia, numero conduttori e sezione se disponibili\n        if (selectedCavo.tipologia !== undefined && selectedCavo.n_conduttori !== undefined && selectedCavo.sezione !== undefined) {\n          console.log('Filtraggio manuale delle bobine compatibili...');\n\n          // Gestisci valori null o vuoti in modo più robusto\n          const bobineCompatibili = bobineUtilizzabili.filter(bobina => {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n\n            // Gestione speciale per n_conduttori che potrebbe essere nel formato \"X x Y\"\n            let cavoConduttoriRaw = String(selectedCavo.n_conduttori || '0');\n            let cavoConduttori = cavoConduttoriRaw;\n            // Se il formato è \"X x Y\", estrai solo il primo numero\n            if (cavoConduttoriRaw.includes(' x ')) {\n              const parts = cavoConduttoriRaw.split(' x ');\n              cavoConduttori = parts[0];\n              console.log(`Filtro manuale - Formato n_conduttori 'X x Y' rilevato: ${cavoConduttoriRaw} -> ${cavoConduttori}`);\n            }\n            const cavoSezione = String(selectedCavo.sezione || '0');\n            const bobinaTipologia = String(bobina.tipologia || '');\n            const bobinaConduttori = String(bobina.n_conduttori || '0');\n            const bobinaSezione = String(bobina.sezione || '0');\n\n            // Log per debug\n            console.log(`Confronto bobina ${bobina.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              n_conduttori: `${bobinaConduttori} === ${cavoConduttori}`,\n              sezione: `${bobinaSezione} === ${cavoSezione}`\n            });\n            return bobinaTipologia === cavoTipologia && bobinaConduttori === cavoConduttori && bobinaSezione === cavoSezione;\n          });\n\n          // Se ci sono bobine compatibili, usa quelle, altrimenti mostra tutte le bobine utilizzabili\n          if (bobineCompatibili.length > 0) {\n            bobineUtilizzabili = bobineCompatibili;\n            console.log(`Filtrate ${bobineCompatibili.length} bobine compatibili manualmente`);\n          } else {\n            console.log('Nessuna bobina compatibile trovata con filtro manuale, mostro tutte le bobine disponibili');\n            // Non filtrare ulteriormente, mostra tutte le bobine disponibili\n            bobineUtilizzabili = bobineData.filter(bobina => (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') && bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata');\n          }\n        }\n\n        // Ordina le bobine per metri residui (decrescente)\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n      }\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con pattern: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi che corrispondono al pattern\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi in base all'input (ricerca parziale)\n      const filteredCavi = caviData.filter(cavo => cavo.id_cavo.toLowerCase().includes(cavoIdInput.trim().toLowerCase()));\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti al pattern`);\n\n      // Se c'è una corrispondenza esatta, seleziona direttamente quel cavo\n      const exactMatch = filteredCavi.find(cavo => cavo.id_cavo.toLowerCase() === cavoIdInput.trim().toLowerCase());\n      if (exactMatch) {\n        console.log('Trovata corrispondenza esatta:', exactMatch);\n\n        // Verifica se il cavo è già installato\n        if (exactMatch.stato_installazione === 'Installato' || exactMatch.metratura_reale && exactMatch.metratura_reale > 0) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatch);\n          setAlreadyLaidCavo(exactMatch);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatch.modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatch);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatch);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con pattern \"${cavoIdInput.trim()}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = {\n            ...cavo,\n            modificato_manualmente: 0\n          };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n\n          // Carica le bobine compatibili\n          loadBobine();\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n\n      // Carica le bobine compatibili\n      if (cavo.tipologia && cavo.n_conduttori && cavo.sezione) {\n        console.log(`Caricamento bobine compatibili per il cavo ${cavo.id_cavo}...`);\n        loadBobine();\n      }\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async cavoId => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Gestione speciale per il cambio di bobina\n    if (name === 'id_bobina' && value && value !== 'BOBINA_VUOTA') {\n      // Verifica compatibilità tra cavo e bobina\n      const bobina = bobine.find(b => b.id_bobina === value);\n      if (bobina && selectedCavo) {\n        // Gestione speciale per n_conduttori che potrebbe essere nel formato \"X x Y\"\n        let cavoConduttori = selectedCavo.n_conduttori !== undefined && selectedCavo.n_conduttori !== null ? String(selectedCavo.n_conduttori) : '0';\n        if (cavoConduttori.includes(' x ')) {\n          const parts = cavoConduttori.split(' x ');\n          cavoConduttori = parts[0];\n          console.log(`Formato n_conduttori 'X x Y' rilevato: ${selectedCavo.n_conduttori} -> ${cavoConduttori}`);\n        }\n        const bobinaConduttori = String(bobina.n_conduttori || '0');\n        const isCompatible = bobina.tipologia === selectedCavo.tipologia && bobinaConduttori === cavoConduttori && String(bobina.sezione) === String(selectedCavo.sezione);\n        if (!isCompatible) {\n          console.log('Bobina incompatibile selezionata:', bobina);\n          console.log('Cavo corrente:', selectedCavo);\n          console.log('Confronto conduttori:', {\n            cavoConduttori,\n            bobinaConduttori\n          });\n\n          // Mostra il dialogo di incompatibilità\n          setIncompatibleReel(bobina);\n          setShowIncompatibleReelDialog(true);\n\n          // Non aggiornare il valore del form finché l'utente non conferma\n          return;\n        }\n      }\n    }\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    } else if (name === 'id_bobina') {\n      // Controllo che sia selezionata una bobina\n      if (!value || value.trim() === '') {\n        error = 'È necessario selezionare una bobina';\n        return false;\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Nota: Lo stato per il dialogo di bobina incompatibile è già dichiarato sopra\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    // Validazione bobina (deve essere selezionata)\n    if (!formData.id_bobina || formData.id_bobina.trim() === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina';\n      isValid = false;\n    }\n    if (isValid) {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n    setActiveStep(prevActiveStep => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep(prevActiveStep => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n\n      // Gestione differenziata per cavi non posati e cavi posati\n      if (!idBobina || idBobina === '') {\n        // È necessario selezionare una bobina, anche BOBINA_VUOTA\n        setFormErrors({\n          ...formErrors,\n          id_bobina: 'È necessario selezionare una bobina'\n        });\n        setLoading(false);\n        return;\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        // Per cavi posati con bobina specifica\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Verifica se è necessario forzare lo stato OVER della bobina\n      forceOver = false;\n\n      // Se si usa BOBINA_VUOTA, imposta sempre forceOver a true\n      if (idBobina === 'BOBINA_VUOTA') {\n        forceOver = true;\n        console.log('Forzando operazione per BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          forceOver = true;\n          console.log(`Forzando stato OVER per la bobina ${idBobina} (metri posati > metri residui)`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        // Anche in questo caso forziamo l'operazione\n        forceOver = true;\n        console.log(`Forzando operazione per metri posati (${metriPosati}) > metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n              await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, forceOver);\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n              if (error.response) {\n                var _error$response$data;\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message;\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, forceOver);\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if (error.response) {\n        var _error$response$data2;\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.detail) || error.message;\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Seleziona un cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1029,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Cerca cavo per ID\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1035,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 9,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavo\",\n              variant: \"outlined\",\n              value: cavoIdInput,\n              onChange: e => setCavoIdInput(e.target.value),\n              placeholder: \"Inserisci l'ID del cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1040,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1039,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              color: \"primary\",\n              onClick: handleSearchCavoById,\n              disabled: caviLoading || !cavoIdInput.trim(),\n              startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1056,\n                columnNumber: 42\n              }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1056,\n                columnNumber: 75\n              }, this),\n              children: \"Cerca\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1050,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1049,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1038,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1034,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Seleziona dalla lista\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1066,\n          columnNumber: 11\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1072,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1071,\n          columnNumber: 13\n        }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Non ci sono cavi disponibili da installare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1075,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            maxHeight: '400px',\n            overflow: 'auto'\n          },\n          children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCavoSelect(cavo),\n              children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1086,\n                    columnNumber: 27\n                  }, this), isCableSpare(cavo) ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"SPARE\",\n                    color: \"error\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1088,\n                    columnNumber: 29\n                  }, this) : isCableInstalled(cavo) ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"Installato\",\n                    color: \"success\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1095,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: getCableStateColor(cavo.stato_installazione),\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1102,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1085,\n                  columnNumber: 25\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [cavo.tipologia || 'N/A', \" - \", cavo.n_conduttori || 'N/A', \" x \", cavo.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1113,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1116,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', \" - A: \", cavo.ubicazione_arrivo || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1117,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1120,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A', \" - Metri posati: \", cavo.metratura_reale || '0']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1121,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1083,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  edge: \"end\",\n                  onClick: e => {\n                    e.stopPropagation(); // Prevent triggering the ListItem click\n                    setSelectedCavo(cavo);\n                    setShowCavoDetailsDialog(true);\n                  },\n                  children: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1133,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1128,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1127,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1082,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1137,\n              columnNumber: 19\n            }, this)]\n          }, cavo.id_cavo, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1081,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1079,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1065,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1028,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserisci metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n        cavo: selectedCavo,\n        compact: true,\n        title: \"Dettagli del cavo selezionato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 'bold'\n          },\n          children: \"Inserisci i metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                bgcolor: '#f5f5f5',\n                borderRadius: 1,\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold',\n                  color: 'primary.main'\n                },\n                children: \"Informazioni cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 1,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium'\n                    },\n                    children: \"Metri teorici:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1177,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1176,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [selectedCavo.metri_teorici || 'N/A', \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1180,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1179,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium'\n                    },\n                    children: \"Stato attuale:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1183,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1182,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: selectedCavo.stato_installazione || 'N/D',\n                    size: \"small\",\n                    color: getCableStateColor(selectedCavo.stato_installazione),\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1186,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1185,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1175,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1171,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                bgcolor: '#f5f5f5',\n                borderRadius: 1,\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold',\n                  color: 'secondary.main'\n                },\n                children: \"Informazioni bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1199,\n                columnNumber: 17\n              }, this), formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' ? (() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                return bobina ? /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"ID Bobina:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1207,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1206,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: getBobinaNumber(bobina.id_bobina)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1210,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1209,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"Metri residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1213,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1212,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1216,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1215,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1219,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1218,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: bobina.stato_bobina || 'N/D',\n                      size: \"small\",\n                      color: getReelStateColor(bobina.stato_bobina),\n                      variant: \"outlined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1222,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1221,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1205,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Bobina non trovata\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1231,\n                  columnNumber: 21\n                }, this);\n              })() : /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: formData.id_bobina === 'BOBINA_VUOTA' ? \"Utilizzo BOBINA VUOTA (nessuna bobina associata)\" : \"Nessuna bobina selezionata\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1234,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1198,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Metratura posata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            fullWidth: true,\n            label: \"Metri posati\",\n            variant: \"outlined\",\n            name: \"metri_posati\",\n            type: \"number\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            sx: {\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1244,\n          columnNumber: 11\n        }, this), formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: formWarnings.metri_posati\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1267,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1273,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1272,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1163,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1152,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = numeroBobina => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = bobina => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = e => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo) {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n            const cavoConduttori = String(selectedCavo.n_conduttori || '0');\n            const cavoSezione = String(selectedCavo.sezione || '0');\n            const bobinaTipologia = String(bobinaEsistente.tipologia || '');\n            const bobinaConduttori = String(bobinaEsistente.n_conduttori || '0');\n            const bobinaSezione = String(bobinaEsistente.sezione || '0');\n\n            // Log per debug\n            console.log(`Verifica compatibilità bobina ${bobinaEsistente.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              n_conduttori: `${bobinaConduttori} === ${cavoConduttori}`,\n              sezione: `${bobinaSezione} === ${cavoSezione}`\n            });\n            if (bobinaTipologia !== cavoTipologia || bobinaConduttori !== cavoConduttori || bobinaSezione !== cavoSezione) {\n              // Mostra il dialogo per bobine incompatibili\n              setIncompatibleReel(bobinaEsistente);\n              setShowIncompatibleReelDialog(true);\n              return;\n            }\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Associa bobina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1398,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          paragraph: true,\n          children: \"Seleziona una bobina da associare al cavo. \\xC8 necessario associare sempre una bobina, anche utilizzando l'opzione \\\"BOBINA VUOTA\\\" se non si desidera associare una bobina specifica.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1403,\n          columnNumber: 11\n        }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1409,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1408,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: \"Inserimento diretto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1416,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                size: \"small\",\n                fullWidth: true,\n                label: \"Numero bobina\",\n                variant: \"outlined\",\n                placeholder: \"Solo il numero (Y)\",\n                helperText: formErrors.id_bobina_input || \"Inserisci solo il numero della bobina\",\n                error: !!formErrors.id_bobina_input,\n                onBlur: handleBobinaNumberInput,\n                sx: {\n                  mb: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1419,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mb: 1\n                },\n                children: [\"ID Bobina: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: formData.id_bobina || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1431,\n                  columnNumber: 32\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1430,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1415,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: \"Selezione dalla lista\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1437,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  gutterBottom: true,\n                  sx: {\n                    color: 'primary.main',\n                    fontWeight: 'bold',\n                    mb: 1\n                  },\n                  children: selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1441,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  size: \"small\",\n                  error: !!formErrors.id_bobina,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    id: \"bobina-select-label\",\n                    children: \"Seleziona bobina\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1446,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    labelId: \"bobina-select-label\",\n                    id: \"bobina-select\",\n                    name: \"id_bobina\",\n                    value: formData.id_bobina,\n                    label: \"Seleziona bobina\",\n                    onChange: handleFormChange,\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"BOBINA_VUOTA\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"BOBINA VUOTA\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1456,\n                        columnNumber: 27\n                      }, this), \" (nessuna bobina associata)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1455,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1458,\n                      columnNumber: 25\n                    }, this), bobine.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                      component: \"li\",\n                      sx: {\n                        p: 1,\n                        bgcolor: 'background.paper'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          color: 'success.main'\n                        },\n                        children: [bobine.length, \" bobine compatibili trovate\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1461,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1460,\n                      columnNumber: 27\n                    }, this) : null, bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: bobina.id_bobina,\n                      disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                      sx: {\n                        '&.Mui-selected': {\n                          bgcolor: 'success.light'\n                        },\n                        '&.Mui-selected:hover': {\n                          bgcolor: 'success.light'\n                        },\n                        bgcolor: selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) && String(bobina.sezione) === String(selectedCavo.sezione) ? 'rgba(76, 175, 80, 0.08)' : 'inherit'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          flexDirection: 'column',\n                          width: '100%'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center',\n                            width: '100%'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"body2\",\n                            sx: {\n                              fontWeight: 'bold'\n                            },\n                            children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A']\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1483,\n                            columnNumber: 33\n                          }, this), selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) && String(bobina.sezione) === String(selectedCavo.sezione) && /*#__PURE__*/_jsxDEV(Chip, {\n                            size: \"small\",\n                            label: \"Compatibile\",\n                            color: \"success\",\n                            variant: \"outlined\",\n                            sx: {\n                              height: 20,\n                              fontSize: '0.6rem'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1490,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1482,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            width: '100%'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"caption\",\n                            children: [bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1500,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"caption\",\n                            sx: {\n                              fontWeight: 'bold',\n                              color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                            },\n                            children: [bobina.metri_residui || 0, \" m disponibili\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1503,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1499,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1481,\n                        columnNumber: 29\n                      }, this)\n                    }, bobina.id_bobina, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1467,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1447,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                    children: formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1511,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1445,\n                  columnNumber: 21\n                }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n                  severity: \"warning\",\n                  sx: {\n                    mt: 2,\n                    fontSize: '0.8rem'\n                  },\n                  children: \"Nessuna bobina compatibile trovata. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1517,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1440,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1436,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1413,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Nota\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1527,\n                columnNumber: 19\n              }, this), \": Se selezioni \\\"BOBINA VUOTA\\\", potrai associare una bobina specifica in un secondo momento.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1526,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1525,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1412,\n          columnNumber: 13\n        }, this), !bobineLoading && formData.id_bobina && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: 'background.paper',\n            borderRadius: 1,\n            border: '1px solid #e0e0e0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Dettagli bobina selezionata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1536,\n            columnNumber: 15\n          }, this), (() => {\n            const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n            if (bobina) {\n              return /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Numero:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1546,\n                      columnNumber: 27\n                    }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1545,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Tipologia:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1549,\n                      columnNumber: 27\n                    }, this), \" \", bobina.tipologia || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1548,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Conduttori:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1552,\n                      columnNumber: 27\n                    }, this), \" \", bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1551,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1544,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri totali:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1557,\n                      columnNumber: 27\n                    }, this), \" \", bobina.metri_totali || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1556,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1560,\n                      columnNumber: 27\n                    }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1559,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1563,\n                      columnNumber: 27\n                    }, this), \" \", bobina.stato_bobina || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1562,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1555,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1543,\n                columnNumber: 21\n              }, this);\n            }\n            return /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"error\",\n              children: \"Bobina non trovata nel database\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1570,\n              columnNumber: 19\n            }, this);\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1535,\n          columnNumber: 13\n        }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: \"Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1579,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1402,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1397,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Conferma inserimento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1608,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Riepilogo dati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1613,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo,\n          compact: true,\n          title: \"Dettagli del cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1618,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Informazioni sull'operazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1626,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1632,\n                  columnNumber: 19\n                }, this), \" \", formData.metri_posati, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1631,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato Installazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1635,\n                  columnNumber: 19\n                }, this), \" \", statoInstallazione]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1634,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1630,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina Associata:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1640,\n                  columnNumber: 19\n                }, this), \" \", numeroBobina]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1639,\n                columnNumber: 17\n              }, this), bobinaInfo && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Residui Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1644,\n                  columnNumber: 21\n                }, this), \" \", bobinaInfo.metri_residui, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1643,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1638,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1629,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1625,\n          columnNumber: 11\n        }, this), bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Attenzione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1653,\n            columnNumber: 15\n          }, this), \" I metri posati (\", formData.metri_posati, \"m) superano i metri residui della bobina (\", bobinaInfo.metri_residui, \"m). Questo porter\\xE0 la bobina in stato OVER.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1652,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 3\n          },\n          children: [\"Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\", formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1658,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1612,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1607,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = step => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      // Seleziona Cavo\n      case 1:\n        return renderStep3();\n      // Associa Bobina\n      case 2:\n        return renderStep2();\n      // Inserisci Metri\n      case 3:\n        return renderStep4();\n      // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReel(null);\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    if (!selectedCavo || !incompatibleReel) return;\n    try {\n      setLoading(true);\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, selectedCavo.id_cavo, incompatibleReel.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, selectedCavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: incompatibleReel.id_bobina\n      });\n      onSuccess(`Caratteristiche del cavo ${selectedCavo.id_cavo} aggiornate per corrispondere alla bobina ${incompatibleReel.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Cerca cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1757,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 9,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"ID Cavo\",\n            variant: \"outlined\",\n            value: cavoIdInput,\n            onChange: e => setCavoIdInput(e.target.value),\n            placeholder: \"Inserisci l'ID del cavo o parte di esso\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1762,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1761,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 3,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleSearchCavoById,\n            disabled: caviLoading || !cavoIdInput.trim(),\n            startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1778,\n              columnNumber: 40\n            }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1778,\n              columnNumber: 73\n            }, this),\n            children: \"Cerca\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1772,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1771,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1760,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1756,\n      columnNumber: 7\n    }, this), showSearchResults && searchResults.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Risultati della ricerca\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1789,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                bgcolor: '#f5f5f5'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1796,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1797,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Conduttori\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1798,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1799,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri Teorici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1800,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1801,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1802,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1795,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1794,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: searchResults.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1808,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1809,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [cavo.n_conduttori || 'N/A', \" x \", cavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1810,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1811,\n                  columnNumber: 71\n                }, this), \"A: \", cavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1811,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [cavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1812,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: cavo.stato_installazione || 'N/D',\n                  size: \"small\",\n                  color: getCableStateColor(cavo.stato_installazione),\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1814,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1813,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  variant: \"contained\",\n                  color: \"primary\",\n                  onClick: () => handleCavoSelect(cavo),\n                  disabled: isCableInstalled(cavo),\n                  children: \"Seleziona\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1822,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1821,\n                columnNumber: 21\n              }, this)]\n            }, cavo.id_cavo, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1807,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1805,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1793,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1792,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1788,\n      columnNumber: 9\n    }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserimento metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1843,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2,\n          bgcolor: '#f5f5f5',\n          borderRadius: 1,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 'bold',\n            color: 'primary.main'\n          },\n          children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1849,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1854,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1854,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Conduttori:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1855,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.n_conduttori || 'N/A', \" x \", selectedCavo.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1855,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri teorici:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1856,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.metri_teorici || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1856,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1853,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione partenza:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1859,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1859,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione arrivo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1860,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1860,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Stato:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1862,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: selectedCavo.stato_installazione || 'N/D',\n                size: \"small\",\n                color: getCableStateColor(selectedCavo.stato_installazione),\n                variant: \"outlined\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1863,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1861,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1858,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1852,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1848,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Metratura posata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1878,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            fullWidth: true,\n            label: \"Metri posati\",\n            variant: \"outlined\",\n            name: \"metri_posati\",\n            type: \"number\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            sx: {\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1881,\n            columnNumber: 15\n          }, this), formWarnings.metri_posati && !formErrors.metri_posati && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2\n            },\n            children: formWarnings.metri_posati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1898,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1877,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Associa bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1906,\n            columnNumber: 15\n          }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'center',\n              my: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1911,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1910,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              sx: {\n                color: 'primary.main',\n                fontWeight: 'bold',\n                mb: 1\n              },\n              children: selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1915,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              error: !!formErrors.id_bobina,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"bobina-select-label\",\n                children: \"Seleziona bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1920,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"bobina-select-label\",\n                id: \"bobina-select\",\n                name: \"id_bobina\",\n                value: formData.id_bobina,\n                label: \"Seleziona bobina\",\n                onChange: handleFormChange,\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"BOBINA_VUOTA\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"BOBINA VUOTA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1930,\n                    columnNumber: 25\n                  }, this), \" (nessuna bobina associata)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1929,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1932,\n                  columnNumber: 23\n                }, this), bobine.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"li\",\n                  sx: {\n                    p: 1,\n                    bgcolor: 'background.paper'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      color: 'success.main'\n                    },\n                    children: [bobine.length, \" bobine compatibili trovate\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1935,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1934,\n                  columnNumber: 25\n                }, this) : null, bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: bobina.id_bobina,\n                  disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                  sx: {\n                    '&.Mui-selected': {\n                      bgcolor: 'success.light'\n                    },\n                    '&.Mui-selected:hover': {\n                      bgcolor: 'success.light'\n                    },\n                    bgcolor: selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) && String(bobina.sezione) === String(selectedCavo.sezione) ? 'rgba(76, 175, 80, 0.08)' : 'inherit'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      flexDirection: 'column',\n                      width: '100%'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        width: '100%'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          fontWeight: 'bold'\n                        },\n                        children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1957,\n                        columnNumber: 31\n                      }, this), selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) && String(bobina.sezione) === String(selectedCavo.sezione) && /*#__PURE__*/_jsxDEV(Chip, {\n                        size: \"small\",\n                        label: \"Compatibile\",\n                        color: \"success\",\n                        variant: \"outlined\",\n                        sx: {\n                          height: 20,\n                          fontSize: '0.6rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1964,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1956,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        width: '100%'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        children: [bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1974,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                        },\n                        children: [bobina.metri_residui || 0, \" m disponibili\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1977,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1973,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1955,\n                    columnNumber: 27\n                  }, this)\n                }, bobina.id_bobina, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1941,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1921,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                children: formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1985,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1919,\n              columnNumber: 19\n            }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mt: 2,\n                fontSize: '0.8rem'\n              },\n              children: \"Nessuna bobina compatibile trovata. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1991,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1914,\n            columnNumber: 17\n          }, this), !bobineLoading && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && (() => {\n            const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n            if (bobina) {\n              return /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 2,\n                  p: 2,\n                  bgcolor: '#f5f5f5',\n                  borderRadius: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Bobina:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2004,\n                    columnNumber: 51\n                  }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2004,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Metri residui:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2005,\n                    columnNumber: 51\n                  }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2005,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Stato:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2007,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: bobina.stato_bobina || 'N/D',\n                    size: \"small\",\n                    color: getReelStateColor(bobina.stato_bobina),\n                    variant: \"outlined\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2008,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2006,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2003,\n                columnNumber: 21\n              }, this);\n            }\n            return null;\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1905,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1875,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          onClick: () => {\n            setSelectedCavo(null);\n            setFormData({\n              id_cavo: '',\n              metri_posati: '',\n              id_bobina: ''\n            });\n          },\n          startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2036,\n            columnNumber: 26\n          }, this),\n          disabled: loading,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2025,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleSubmit,\n          endIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2046,\n            columnNumber: 24\n          }, this),\n          disabled: loading || !formData.metri_posati || !formData.id_bobina,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2049,\n            columnNumber: 26\n          }, this) : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2042,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2024,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1842,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showConfirmDialog,\n      onClose: () => setShowConfirmDialog(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2059,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: confirmDialogProps.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2060,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2058,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2057,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            mt: 2\n          },\n          children: confirmDialogProps.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2064,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2063,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowConfirmDialog(false),\n          color: \"secondary\",\n          variant: \"outlined\",\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2069,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setShowConfirmDialog(false);\n            confirmDialogProps.onConfirm();\n          },\n          color: \"primary\",\n          variant: \"contained\",\n          autoFocus: true,\n          children: \"Conferma\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2072,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2068,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2056,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showAlreadyLaidDialog,\n      onClose: handleCloseAlreadyLaidDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2090,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavo gi\\xE0 posato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2091,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2089,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2088,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: alreadyLaidCavo && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: alreadyLaidCavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2098,\n              columnNumber: 25\n            }, this), \" risulta gi\\xE0 posato (\", alreadyLaidCavo.metratura_reale || 0, \"m).\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2097,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: \"Puoi scegliere di:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2100,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            component: \"ul\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Modificare la bobina associata al cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2104,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Selezionare un altro cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2105,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Annullare l'operazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2106,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2103,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2096,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2094,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 2,\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAlreadyLaidDialog,\n          color: \"secondary\",\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSelectAnotherCable,\n            color: \"primary\",\n            sx: {\n              mr: 1\n            },\n            children: \"Seleziona altro cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleModifyReel,\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Modifica bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2087,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: handleCloseIncompatibleReelDialog,\n      cavo: selectedCavo,\n      bobina: incompatibleReel,\n      onUpdateCavo: handleUpdateCavoToMatchReel,\n      onSelectAnotherReel: handleSelectAnotherReel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showCavoDetailsDialog,\n      onClose: () => setShowCavoDetailsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(InfoIcon, {\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Dettagli Cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2144,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2150,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowCavoDetailsDialog(false),\n          color: \"primary\",\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2153,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2152,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2137,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1754,\n    columnNumber: 5\n  }, this);\n};\n_s(InserisciMetriForm, \"hsuyd/WWnSfyS7dylwwvyUS2Ql4=\", false, function () {\n  return [useNavigate];\n});\n_c = InserisciMetriForm;\nexport default InserisciMetriForm;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Search", "SearchIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "useNavigate", "caviService", "axiosInstance", "IncompatibleReelDialog", "CavoDetailsView", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "parcoCaviService", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "searchResults", "setSearchResults", "showSearchResults", "setShowSearchResults", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "activeStep", "setActiveStep", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReel", "setIncompatibleReel", "showAlreadyLaidDialog", "setShowAlreadyLaidDialog", "alreadyLaidCavo", "setAlreadyLaidCavo", "showCavoDetailsDialog", "setShowCavoDetailsDialog", "loadBobine", "getBobinaNumber", "idBobina", "includes", "split", "loadCavi", "console", "log", "caviData", "get<PERSON><PERSON>", "length", "loadError", "error", "isNetworkError", "response", "code", "message", "Promise", "resolve", "setTimeout", "token", "localStorage", "getItem", "API_URL", "defaults", "baseURL", "retryResponse", "get", "headers", "timeout", "data", "retryError", "errorMessage", "detail", "tipologia", "n_conduttori", "sezione", "tipi", "undefined", "String", "parts", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "getBobineCompatibili", "sort", "a", "b", "metri_residui", "bobine<PERSON><PERSON>", "getBobine", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "bobina", "stato_bobina", "cavoTipologia", "cavoConduttoriRaw", "cavoConduttori", "cavoSezione", "bobinaTipologia", "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bobinaSezione", "handleSearchCavoById", "trim", "filteredCavi", "cavo", "toLowerCase", "exactMatch", "find", "stato_installazione", "metratura_reale", "modificato_manualmente", "handleCavoSelect", "status", "window", "confirm", "reactivateSpare", "then", "updatedCavo", "catch", "cavoId", "handleFormChange", "e", "name", "value", "target", "isCompatible", "validateField", "warning", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "prev", "notificationShown", "setNotificationShown", "showConfirmDialog", "setShowConfirmDialog", "confirmDialogProps", "setConfirmDialogProps", "title", "onConfirm", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "handleNext", "prevActiveStep", "handleBack", "handleReset", "determineInstallationStatus", "metriTeorici", "handleSubmit", "statoInstallazione", "forceOver", "confirmMessage", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "success", "_error$response$data", "request", "_error$response$data2", "renderStep1", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "container", "spacing", "alignItems", "item", "xs", "fullWidth", "label", "onChange", "placeholder", "color", "onClick", "disabled", "startIcon", "size", "display", "justifyContent", "my", "severity", "maxHeight", "overflow", "map", "button", "primary", "ml", "secondary", "component", "ubicazione_partenza", "ubicazione_arrivo", "edge", "stopPropagation", "renderStep2", "compact", "fontWeight", "md", "bgcolor", "borderRadius", "height", "mt", "type", "helperText", "FormHelperTextProps", "renderStep3", "buildFullBobinaId", "numeroBobina", "hasSufficientMeters", "handleBobinaNumberInput", "id_bobina_input", "idBobinaCompleto", "<PERSON><PERSON><PERSON>", "paragraph", "onBlur", "id", "labelId", "flexDirection", "width", "fontSize", "border", "metri_totali", "renderStep4", "bobinaInfo", "getStepContent", "step", "handleCloseAlreadyLaidDialog", "handleModifyReel", "handleSelectAnotherCable", "handleCloseIncompatibleReelDialog", "handleUpdateCavoToMatchReel", "updateCavoForCompatibility", "getCavoById", "handleSelectAnotherReel", "endIcon", "open", "onClose", "max<PERSON><PERSON><PERSON>", "gap", "autoFocus", "mr", "onUpdateCavo", "onSelectAnotherReel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/InserisciMetriForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst InserisciMetriForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stato per la gestione dei passi (mantenuto per compatibilità con funzioni esistenti)\n  const [activeStep, setActiveStep] = useState(0);\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null);\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina) => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response ||\n            loadError.code === 'ECONNABORTED' ||\n            (loadError.message && loadError.message.includes('Network Error'))) {\n\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(\n              `${API_URL}/cavi/${cantiereId}`,\n              {\n                headers: {\n                  'Authorization': `Bearer ${token}`\n                },\n                timeout: 30000 // 30 secondi\n              }\n            );\n\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Se c'è un cavo selezionato, carica le bobine compatibili\n      if (selectedCavo) {\n        console.log('Caricamento bobine compatibili per il cavo selezionato...');\n        try {\n          // Log dettagliato per debug\n          console.log('FRONTEND - Valori del cavo selezionato:', {\n            id_cavo: selectedCavo.id_cavo,\n            tipologia: selectedCavo.tipologia,\n            n_conduttori: selectedCavo.n_conduttori,\n            sezione: selectedCavo.sezione,\n            tipi: {\n              tipologia: typeof selectedCavo.tipologia,\n              n_conduttori: typeof selectedCavo.n_conduttori,\n              sezione: typeof selectedCavo.sezione\n            }\n          });\n\n          // Assicurati che i valori siano stringhe per la compatibilità\n          const tipologia = selectedCavo.tipologia !== undefined && selectedCavo.tipologia !== null ? String(selectedCavo.tipologia) : '';\n\n          // Gestione speciale per n_conduttori che potrebbe essere nel formato \"X x Y\"\n          let n_conduttori = selectedCavo.n_conduttori !== undefined && selectedCavo.n_conduttori !== null ? String(selectedCavo.n_conduttori) : '0';\n          // Se il formato è \"X x Y\", estrai solo il primo numero\n          if (n_conduttori.includes(' x ')) {\n            const parts = n_conduttori.split(' x ');\n            n_conduttori = parts[0];\n            console.log(`Formato n_conduttori 'X x Y' rilevato: ${selectedCavo.n_conduttori} -> ${n_conduttori}`);\n          }\n\n          const sezione = selectedCavo.sezione !== undefined && selectedCavo.sezione !== null ? String(selectedCavo.sezione) : '0';\n\n          console.log('FRONTEND - Parametri convertiti per API:', { tipologia, n_conduttori, sezione });\n\n          // Usa il metodo API per ottenere le bobine compatibili\n          const bobineCompatibili = await parcoCaviService.getBobineCompatibili(\n            cantiereId,\n            tipologia,\n            n_conduttori,\n            sezione\n          );\n\n          if (bobineCompatibili && bobineCompatibili.length > 0) {\n            console.log(`Trovate ${bobineCompatibili.length} bobine compatibili via API`);\n            // Ordina le bobine per metri residui (decrescente)\n            bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n            setBobine(bobineCompatibili);\n            return;\n          } else {\n            console.log('Nessuna bobina compatibile trovata via API, carico tutte le bobine disponibili');\n            // Carica tutte le bobine disponibili\n            const bobineData = await parcoCaviService.getBobine(cantiereId);\n            // Filtra solo per stato (disponibile o in uso) ma non per compatibilità\n            const bobineUtilizzabili = bobineData.filter(bobina =>\n              (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') &&\n              bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata'\n            );\n            // Ordina le bobine per metri residui (decrescente)\n            bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n            setBobine(bobineUtilizzabili);\n            return;\n          }\n        } catch (error) {\n          console.error('Errore nel caricamento delle bobine compatibili:', error);\n          console.log('Fallback al caricamento di tutte le bobine...');\n        }\n      }\n\n      // Aggiungi un log per debug\n      console.log('Caricamento di tutte le bobine disponibili (fallback)...');\n\n      // Metodo fallback: carica tutte le bobine e filtra manualmente\n      console.log('Caricamento di tutte le bobine disponibili (fallback)...');\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n\n      // Filtra le bobine per stato (disponibile o in uso) e per compatibilità con il cavo selezionato\n      let bobineUtilizzabili = bobineData.filter(bobina =>\n        (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') &&\n        bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata'\n      );\n\n      // Se c'è un cavo selezionato, filtra ulteriormente per caratteristiche del cavo\n      if (selectedCavo) {\n        // Filtra per tipologia, numero conduttori e sezione se disponibili\n        if (selectedCavo.tipologia !== undefined && selectedCavo.n_conduttori !== undefined && selectedCavo.sezione !== undefined) {\n          console.log('Filtraggio manuale delle bobine compatibili...');\n\n          // Gestisci valori null o vuoti in modo più robusto\n          const bobineCompatibili = bobineUtilizzabili.filter(bobina => {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n\n            // Gestione speciale per n_conduttori che potrebbe essere nel formato \"X x Y\"\n            let cavoConduttoriRaw = String(selectedCavo.n_conduttori || '0');\n            let cavoConduttori = cavoConduttoriRaw;\n            // Se il formato è \"X x Y\", estrai solo il primo numero\n            if (cavoConduttoriRaw.includes(' x ')) {\n              const parts = cavoConduttoriRaw.split(' x ');\n              cavoConduttori = parts[0];\n              console.log(`Filtro manuale - Formato n_conduttori 'X x Y' rilevato: ${cavoConduttoriRaw} -> ${cavoConduttori}`);\n            }\n\n            const cavoSezione = String(selectedCavo.sezione || '0');\n\n            const bobinaTipologia = String(bobina.tipologia || '');\n            const bobinaConduttori = String(bobina.n_conduttori || '0');\n            const bobinaSezione = String(bobina.sezione || '0');\n\n            // Log per debug\n            console.log(`Confronto bobina ${bobina.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              n_conduttori: `${bobinaConduttori} === ${cavoConduttori}`,\n              sezione: `${bobinaSezione} === ${cavoSezione}`\n            });\n\n            return bobinaTipologia === cavoTipologia &&\n                   bobinaConduttori === cavoConduttori &&\n                   bobinaSezione === cavoSezione;\n          });\n\n          // Se ci sono bobine compatibili, usa quelle, altrimenti mostra tutte le bobine utilizzabili\n          if (bobineCompatibili.length > 0) {\n            bobineUtilizzabili = bobineCompatibili;\n            console.log(`Filtrate ${bobineCompatibili.length} bobine compatibili manualmente`);\n          } else {\n            console.log('Nessuna bobina compatibile trovata con filtro manuale, mostro tutte le bobine disponibili');\n            // Non filtrare ulteriormente, mostra tutte le bobine disponibili\n            bobineUtilizzabili = bobineData.filter(bobina =>\n              (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') &&\n              bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata'\n            );\n          }\n        }\n\n        // Ordina le bobine per metri residui (decrescente)\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n      }\n\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con pattern: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi che corrispondono al pattern\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi in base all'input (ricerca parziale)\n      const filteredCavi = caviData.filter(cavo =>\n        cavo.id_cavo.toLowerCase().includes(cavoIdInput.trim().toLowerCase())\n      );\n\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti al pattern`);\n\n      // Se c'è una corrispondenza esatta, seleziona direttamente quel cavo\n      const exactMatch = filteredCavi.find(cavo =>\n        cavo.id_cavo.toLowerCase() === cavoIdInput.trim().toLowerCase()\n      );\n\n      if (exactMatch) {\n        console.log('Trovata corrispondenza esatta:', exactMatch);\n\n        // Verifica se il cavo è già installato\n        if (exactMatch.stato_installazione === 'Installato' || (exactMatch.metratura_reale && exactMatch.metratura_reale > 0)) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatch);\n          setAlreadyLaidCavo(exactMatch);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatch.modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatch);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatch);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con pattern \"${cavoIdInput.trim()}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0)) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = { ...cavo, modificato_manualmente: 0 };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n\n          // Carica le bobine compatibili\n          loadBobine();\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n\n      // Carica le bobine compatibili\n      if (cavo.tipologia && cavo.n_conduttori && cavo.sezione) {\n        console.log(`Caricamento bobine compatibili per il cavo ${cavo.id_cavo}...`);\n        loadBobine();\n      }\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async (cavoId) => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Gestione speciale per il cambio di bobina\n    if (name === 'id_bobina' && value && value !== 'BOBINA_VUOTA') {\n      // Verifica compatibilità tra cavo e bobina\n      const bobina = bobine.find(b => b.id_bobina === value);\n\n      if (bobina && selectedCavo) {\n        // Gestione speciale per n_conduttori che potrebbe essere nel formato \"X x Y\"\n        let cavoConduttori = selectedCavo.n_conduttori !== undefined && selectedCavo.n_conduttori !== null ? String(selectedCavo.n_conduttori) : '0';\n        if (cavoConduttori.includes(' x ')) {\n          const parts = cavoConduttori.split(' x ');\n          cavoConduttori = parts[0];\n          console.log(`Formato n_conduttori 'X x Y' rilevato: ${selectedCavo.n_conduttori} -> ${cavoConduttori}`);\n        }\n\n        const bobinaConduttori = String(bobina.n_conduttori || '0');\n\n        const isCompatible =\n          bobina.tipologia === selectedCavo.tipologia &&\n          bobinaConduttori === cavoConduttori &&\n          String(bobina.sezione) === String(selectedCavo.sezione);\n\n        if (!isCompatible) {\n          console.log('Bobina incompatibile selezionata:', bobina);\n          console.log('Cavo corrente:', selectedCavo);\n          console.log('Confronto conduttori:', { cavoConduttori, bobinaConduttori });\n\n          // Mostra il dialogo di incompatibilità\n          setIncompatibleReel(bobina);\n          setShowIncompatibleReelDialog(true);\n\n          // Non aggiornare il valore del form finché l'utente non conferma\n          return;\n        }\n      }\n    }\n\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    } else if (name === 'id_bobina') {\n      // Controllo che sia selezionata una bobina\n      if (!value || value.trim() === '') {\n        error = 'È necessario selezionare una bobina';\n        return false;\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Nota: Lo stato per il dialogo di bobina incompatibile è già dichiarato sopra\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    // Validazione bobina (deve essere selezionata)\n    if (!formData.id_bobina || formData.id_bobina.trim() === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina';\n      isValid = false;\n    }\n\n    if (isValid) {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n\n      // Gestione differenziata per cavi non posati e cavi posati\n      if (!idBobina || idBobina === '') {\n        // È necessario selezionare una bobina, anche BOBINA_VUOTA\n        setFormErrors({\n          ...formErrors,\n          id_bobina: 'È necessario selezionare una bobina'\n        });\n        setLoading(false);\n        return;\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        // Per cavi posati con bobina specifica\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Verifica se è necessario forzare lo stato OVER della bobina\n      forceOver = false;\n\n      // Se si usa BOBINA_VUOTA, imposta sempre forceOver a true\n      if (idBobina === 'BOBINA_VUOTA') {\n        forceOver = true;\n        console.log('Forzando operazione per BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          forceOver = true;\n          console.log(`Forzando stato OVER per la bobina ${idBobina} (metri posati > metri residui)`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        // Anche in questo caso forziamo l'operazione\n        forceOver = true;\n        console.log(`Forzando operazione per metri posati (${metriPosati}) > metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n\n              await caviService.updateMetriPosati(\n                cantiereId,\n                formData.id_cavo,\n                metriPosati,\n                idBobina,\n                forceOver\n              );\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n              if (error.response) {\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = error.response.data?.detail || error.message;\n\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver\n      );\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n      if (error.response) {\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = error.response.data?.detail || error.message;\n\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Seleziona un cavo\n        </Typography>\n\n        {/* Ricerca per ID */}\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Cerca cavo per ID\n          </Typography>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={9}>\n              <TextField\n                fullWidth\n                label=\"ID Cavo\"\n                variant=\"outlined\"\n                value={cavoIdInput}\n                onChange={(e) => setCavoIdInput(e.target.value)}\n                placeholder=\"Inserisci l'ID del cavo\"\n              />\n            </Grid>\n            <Grid item xs={3}>\n              <Button\n                fullWidth\n                variant=\"contained\"\n                color=\"primary\"\n                onClick={handleSearchCavoById}\n                disabled={caviLoading || !cavoIdInput.trim()}\n                startIcon={caviLoading ? <CircularProgress size={20} /> : <SearchIcon />}\n              >\n                Cerca\n              </Button>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Lista cavi */}\n        <Paper sx={{ p: 2 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Seleziona dalla lista\n          </Typography>\n\n          {caviLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : cavi.length === 0 ? (\n            <Alert severity=\"info\">\n              Non ci sono cavi disponibili da installare.\n            </Alert>\n          ) : (\n            <List sx={{ maxHeight: '400px', overflow: 'auto' }}>\n              {cavi.map((cavo) => (\n                <React.Fragment key={cavo.id_cavo}>\n                  <ListItem button onClick={() => handleCavoSelect(cavo)}>\n                    <ListItemText\n                      primary={\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography variant=\"subtitle1\">{cavo.id_cavo}</Typography>\n                          {isCableSpare(cavo) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"SPARE\"\n                              color=\"error\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : isCableInstalled(cavo) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"Installato\"\n                              color=\"success\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : (\n                            <Chip\n                              size=\"small\"\n                              label={cavo.stato_installazione}\n                              color={getCableStateColor(cavo.stato_installazione)}\n                              sx={{ ml: 1 }}\n                            />\n                          )}\n                        </Box>\n                      }\n                      secondary={\n                        <>\n                          <Typography variant=\"body2\" component=\"span\">\n                            {cavo.tipologia || 'N/A'} - {cavo.n_conduttori || 'N/A'} x {cavo.sezione || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Da: {cavo.ubicazione_partenza || 'N/A'} - A: {cavo.ubicazione_arrivo || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Metri teorici: {cavo.metri_teorici || 'N/A'} - Metri posati: {cavo.metratura_reale || '0'}\n                          </Typography>\n                        </>\n                      }\n                    />\n                    <ListItemSecondaryAction>\n                      <IconButton edge=\"end\" onClick={(e) => {\n                        e.stopPropagation(); // Prevent triggering the ListItem click\n                        setSelectedCavo(cavo);\n                        setShowCavoDetailsDialog(true);\n                      }}>\n                        <InfoIcon />\n                      </IconButton>\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                  <Divider />\n                </React.Fragment>\n              ))}\n            </List>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserisci metri posati\n        </Typography>\n\n        <CavoDetailsView\n          cavo={selectedCavo}\n          compact={true}\n          title=\"Dettagli del cavo selezionato\"\n        />\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom sx={{ fontWeight: 'bold' }}>\n            Inserisci i metri posati\n          </Typography>\n\n          {/* Informazioni sul cavo e sulla bobina in una griglia */}\n          <Grid container spacing={2} sx={{ mb: 3 }}>\n            <Grid item xs={12} md={6}>\n              <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, height: '100%' }}>\n                <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>\n                  Informazioni cavo\n                </Typography>\n                <Grid container spacing={1}>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Metri teorici:</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\">{selectedCavo.metri_teorici || 'N/A'} m</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Stato attuale:</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Chip\n                      label={selectedCavo.stato_installazione || 'N/D'}\n                      size=\"small\"\n                      color={getCableStateColor(selectedCavo.stato_installazione)}\n                      variant=\"outlined\"\n                    />\n                  </Grid>\n                </Grid>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, height: '100%' }}>\n                <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'secondary.main' }}>\n                  Informazioni bobina\n                </Typography>\n                {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' ? (() => {\n                  const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                  return bobina ? (\n                    <Grid container spacing={1}>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>ID Bobina:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\">{getBobinaNumber(bobina.id_bobina)}</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Metri residui:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\">{bobina.metri_residui || 0} m</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Stato:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Chip\n                          label={bobina.stato_bobina || 'N/D'}\n                          size=\"small\"\n                          color={getReelStateColor(bobina.stato_bobina)}\n                          variant=\"outlined\"\n                        />\n                      </Grid>\n                    </Grid>\n                  ) : (\n                    <Typography variant=\"body2\">Bobina non trovata</Typography>\n                  );\n                })() : (\n                  <Typography variant=\"body2\">\n                    {formData.id_bobina === 'BOBINA_VUOTA' ?\n                      \"Utilizzo BOBINA VUOTA (nessuna bobina associata)\" :\n                      \"Nessuna bobina selezionata\"}\n                  </Typography>\n                )}\n              </Box>\n            </Grid>\n          </Grid>\n\n          <Box sx={{ mt: 3, mb: 3 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Metratura posata\n            </Typography>\n            <TextField\n              size=\"small\"\n              fullWidth\n              label=\"Metri posati\"\n              variant=\"outlined\"\n              name=\"metri_posati\"\n              type=\"number\"\n              value={formData.metri_posati}\n              onChange={handleFormChange}\n              error={!!formErrors.metri_posati}\n              helperText={formErrors.metri_posati || formWarnings.metri_posati}\n              FormHelperTextProps={{\n                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n              }}\n              sx={{ mb: 1 }}\n            />\n          </Box>\n\n          {formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mb: 2 }}>\n              {formWarnings.metri_posati}\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\">\n              Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\n            </Typography>\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = (numeroBobina) => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = (bobina) => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = (e) => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo) {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n            const cavoConduttori = String(selectedCavo.n_conduttori || '0');\n            const cavoSezione = String(selectedCavo.sezione || '0');\n\n            const bobinaTipologia = String(bobinaEsistente.tipologia || '');\n            const bobinaConduttori = String(bobinaEsistente.n_conduttori || '0');\n            const bobinaSezione = String(bobinaEsistente.sezione || '0');\n\n            // Log per debug\n            console.log(`Verifica compatibilità bobina ${bobinaEsistente.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              n_conduttori: `${bobinaConduttori} === ${cavoConduttori}`,\n              sezione: `${bobinaSezione} === ${cavoSezione}`\n            });\n\n            if (bobinaTipologia !== cavoTipologia ||\n                bobinaConduttori !== cavoConduttori ||\n                bobinaSezione !== cavoSezione) {\n              // Mostra il dialogo per bobine incompatibili\n              setIncompatibleReel(bobinaEsistente);\n              setShowIncompatibleReelDialog(true);\n              return;\n            }\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Associa bobina\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"body1\" paragraph>\n            Seleziona una bobina da associare al cavo. È necessario associare sempre una bobina, anche utilizzando l'opzione \"BOBINA VUOTA\" se non si desidera associare una bobina specifica.\n          </Typography>\n\n          {bobineLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              <Grid container spacing={3}>\n                {/* Colonna sinistra: Input diretto */}\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                    Inserimento diretto\n                  </Typography>\n                  <TextField\n                    size=\"small\"\n                    fullWidth\n                    label=\"Numero bobina\"\n                    variant=\"outlined\"\n                    placeholder=\"Solo il numero (Y)\"\n                    helperText={formErrors.id_bobina_input || \"Inserisci solo il numero della bobina\"}\n                    error={!!formErrors.id_bobina_input}\n                    onBlur={handleBobinaNumberInput}\n                    sx={{ mb: 1 }}\n                  />\n                  <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                    ID Bobina: <strong>{formData.id_bobina || '-'}</strong>\n                  </Typography>\n                </Grid>\n\n                {/* Colonna destra: Selezione dalla lista */}\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                    Selezione dalla lista\n                  </Typography>\n                  <Box>\n                    <Typography variant=\"subtitle2\" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold', mb: 1 }}>\n                      {selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'}\n                    </Typography>\n\n                    <FormControl fullWidth size=\"small\" error={!!formErrors.id_bobina}>\n                      <InputLabel id=\"bobina-select-label\">Seleziona bobina</InputLabel>\n                      <Select\n                        labelId=\"bobina-select-label\"\n                        id=\"bobina-select\"\n                        name=\"id_bobina\"\n                        value={formData.id_bobina}\n                        label=\"Seleziona bobina\"\n                        onChange={handleFormChange}\n                      >\n                        <MenuItem value=\"BOBINA_VUOTA\">\n                          <strong>BOBINA VUOTA</strong> (nessuna bobina associata)\n                        </MenuItem>\n                        <Divider />\n                        {bobine.length > 0 ? (\n                          <Box component=\"li\" sx={{ p: 1, bgcolor: 'background.paper' }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: 'success.main' }}>\n                              {bobine.length} bobine compatibili trovate\n                            </Typography>\n                          </Box>\n                        ) : null}\n                        {bobine.map((bobina) => (\n                          <MenuItem\n                            key={bobina.id_bobina}\n                            value={bobina.id_bobina}\n                            disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                            sx={{\n                              '&.Mui-selected': { bgcolor: 'success.light' },\n                              '&.Mui-selected:hover': { bgcolor: 'success.light' },\n                              bgcolor: selectedCavo &&\n                                     bobina.tipologia === selectedCavo.tipologia &&\n                                     String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) &&\n                                     String(bobina.sezione) === String(selectedCavo.sezione) ?\n                                     'rgba(76, 175, 80, 0.08)' : 'inherit'\n                            }}\n                          >\n                            <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>\n                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>\n                                <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                                  {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'}\n                                </Typography>\n                                {selectedCavo &&\n                                 bobina.tipologia === selectedCavo.tipologia &&\n                                 String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) &&\n                                 String(bobina.sezione) === String(selectedCavo.sezione) && (\n                                  <Chip\n                                    size=\"small\"\n                                    label=\"Compatibile\"\n                                    color=\"success\"\n                                    variant=\"outlined\"\n                                    sx={{ height: 20, fontSize: '0.6rem' }}\n                                  />\n                                )}\n                              </Box>\n                              <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>\n                                <Typography variant=\"caption\">\n                                  {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                                </Typography>\n                                <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                  {bobina.metri_residui || 0} m disponibili\n                                </Typography>\n                              </Box>\n                            </Box>\n                          </MenuItem>\n                        ))}\n                      </Select>\n                      <FormHelperText>\n                        {formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'}\n                      </FormHelperText>\n                    </FormControl>\n\n                    {bobine.length === 0 && !bobineLoading && (\n                      <Alert severity=\"warning\" sx={{ mt: 2, fontSize: '0.8rem' }}>\n                        Nessuna bobina compatibile trovata. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\n                      </Alert>\n                    )}\n                  </Box>\n                </Grid>\n              </Grid>\n\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                <Typography variant=\"body2\">\n                  <strong>Nota</strong>: Se selezioni \"BOBINA VUOTA\", potrai associare una bobina specifica in un secondo momento.\n                </Typography>\n              </Alert>\n            </Box>\n          )}\n\n          {/* Mostra dettagli della bobina selezionata */}\n          {!bobineLoading && formData.id_bobina && (\n            <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #e0e0e0' }}>\n              <Typography variant=\"subtitle2\" gutterBottom>\n                Dettagli bobina selezionata\n              </Typography>\n              {(() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                if (bobina) {\n                  return (\n                    <Grid container spacing={2}>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"body2\">\n                          <strong>Numero:</strong> {getBobinaNumber(bobina.id_bobina)}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Tipologia:</strong> {bobina.tipologia || 'N/A'}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Conduttori:</strong> {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                        </Typography>\n                      </Grid>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"body2\">\n                          <strong>Metri totali:</strong> {bobina.metri_totali || 0} m\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Metri residui:</strong> {bobina.metri_residui || 0} m\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Stato:</strong> {bobina.stato_bobina || 'N/A'}\n                        </Typography>\n                      </Grid>\n                    </Grid>\n                  );\n                }\n                return (\n                  <Typography variant=\"body2\" color=\"error\">\n                    Bobina non trovata nel database\n                  </Typography>\n                );\n              })()}\n            </Box>\n          )}\n\n          {bobine.length === 0 && !bobineLoading && (\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\n            </Alert>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Conferma inserimento\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Riepilogo dati\n          </Typography>\n\n          {/* Dettagli del cavo */}\n          <CavoDetailsView\n            cavo={selectedCavo}\n            compact={true}\n            title=\"Dettagli del cavo\"\n          />\n\n          {/* Informazioni sull'operazione */}\n          <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Informazioni sull'operazione:\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Metri Posati:</strong> {formData.metri_posati} m\n                </Typography>\n                <Typography variant=\"body2\">\n                  <strong>Stato Installazione:</strong> {statoInstallazione}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Bobina Associata:</strong> {numeroBobina}\n                </Typography>\n                {bobinaInfo && (\n                  <Typography variant=\"body2\">\n                    <strong>Metri Residui Bobina:</strong> {bobinaInfo.metri_residui} m\n                  </Typography>\n                )}\n              </Grid>\n            </Grid>\n          </Box>\n\n          {bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mt: 3 }}>\n              <strong>Attenzione:</strong> I metri posati ({formData.metri_posati}m) superano i metri residui della bobina ({bobinaInfo.metri_residui}m).\n              Questo porterà la bobina in stato OVER.\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 3 }}>\n            Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\n            {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.'}\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = (step) => {\n    switch (step) {\n      case 0:\n        return renderStep1(); // Seleziona Cavo\n      case 1:\n        return renderStep3(); // Associa Bobina\n      case 2:\n        return renderStep2(); // Inserisci Metri\n      case 3:\n        return renderStep4(); // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReel(null);\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    if (!selectedCavo || !incompatibleReel) return;\n\n    try {\n      setLoading(true);\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, selectedCavo.id_cavo, incompatibleReel.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, selectedCavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: incompatibleReel.id_bobina\n      });\n\n      onSuccess(`Caratteristiche del cavo ${selectedCavo.id_cavo} aggiornate per corrispondere alla bobina ${incompatibleReel.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n\n  return (\n    <Box>\n      {/* Sezione di ricerca */}\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Cerca cavo\n        </Typography>\n        <Grid container spacing={2} alignItems=\"center\">\n          <Grid item xs={9}>\n            <TextField\n              fullWidth\n              label=\"ID Cavo\"\n              variant=\"outlined\"\n              value={cavoIdInput}\n              onChange={(e) => setCavoIdInput(e.target.value)}\n              placeholder=\"Inserisci l'ID del cavo o parte di esso\"\n            />\n          </Grid>\n          <Grid item xs={3}>\n            <Button\n              fullWidth\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSearchCavoById}\n              disabled={caviLoading || !cavoIdInput.trim()}\n              startIcon={caviLoading ? <CircularProgress size={20} /> : <SearchIcon />}\n            >\n              Cerca\n            </Button>\n          </Grid>\n        </Grid>\n      </Paper>\n\n      {/* Risultati della ricerca */}\n      {showSearchResults && searchResults.length > 0 && (\n        <Paper sx={{ p: 3, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Risultati della ricerca\n          </Typography>\n          <TableContainer>\n            <Table size=\"small\">\n              <TableHead>\n                <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n                  <TableCell>ID Cavo</TableCell>\n                  <TableCell>Tipologia</TableCell>\n                  <TableCell>Conduttori</TableCell>\n                  <TableCell>Ubicazione</TableCell>\n                  <TableCell>Metri Teorici</TableCell>\n                  <TableCell>Stato</TableCell>\n                  <TableCell>Azioni</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {searchResults.map((cavo) => (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>{cavo.id_cavo}</TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>{cavo.n_conduttori || 'N/A'} x {cavo.sezione || 'N/A'}</TableCell>\n                    <TableCell>Da: {cavo.ubicazione_partenza || 'N/A'}<br/>A: {cavo.ubicazione_arrivo || 'N/A'}</TableCell>\n                    <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={cavo.stato_installazione || 'N/D'}\n                        size=\"small\"\n                        color={getCableStateColor(cavo.stato_installazione)}\n                        variant=\"outlined\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Button\n                        size=\"small\"\n                        variant=\"contained\"\n                        color=\"primary\"\n                        onClick={() => handleCavoSelect(cavo)}\n                        disabled={isCableInstalled(cavo)}\n                      >\n                        Seleziona\n                      </Button>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </Paper>\n      )}\n\n      {/* Form per inserimento metri e selezione bobina */}\n      {selectedCavo && (\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Inserimento metri posati\n          </Typography>\n\n          {/* Dettagli del cavo selezionato */}\n          <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, mb: 3 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>\n              Cavo selezionato: {selectedCavo.id_cavo}\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Conduttori:</strong> {selectedCavo.n_conduttori || 'N/A'} x {selectedCavo.sezione || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metri teorici:</strong> {selectedCavo.metri_teorici || 'N/A'} m</Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\"><strong>Ubicazione partenza:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ubicazione arrivo:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\">\n                  <strong>Stato:</strong>\n                  <Chip\n                    label={selectedCavo.stato_installazione || 'N/D'}\n                    size=\"small\"\n                    color={getCableStateColor(selectedCavo.stato_installazione)}\n                    variant=\"outlined\"\n                    sx={{ ml: 1 }}\n                  />\n                </Typography>\n              </Grid>\n            </Grid>\n          </Box>\n\n          <Grid container spacing={3}>\n            {/* Colonna sinistra: Metri posati */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                Metratura posata\n              </Typography>\n              <TextField\n                size=\"small\"\n                fullWidth\n                label=\"Metri posati\"\n                variant=\"outlined\"\n                name=\"metri_posati\"\n                type=\"number\"\n                value={formData.metri_posati}\n                onChange={handleFormChange}\n                error={!!formErrors.metri_posati}\n                helperText={formErrors.metri_posati || formWarnings.metri_posati}\n                FormHelperTextProps={{\n                  sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n                }}\n                sx={{ mb: 1 }}\n              />\n              {formWarnings.metri_posati && !formErrors.metri_posati && (\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  {formWarnings.metri_posati}\n                </Alert>\n              )}\n            </Grid>\n\n            {/* Colonna destra: Selezione bobina */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                Associa bobina\n              </Typography>\n              {bobineLoading ? (\n                <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n                  <CircularProgress />\n                </Box>\n              ) : (\n                <Box>\n                  <Typography variant=\"subtitle2\" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold', mb: 1 }}>\n                    {selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'}\n                  </Typography>\n\n                  <FormControl fullWidth size=\"small\" error={!!formErrors.id_bobina}>\n                    <InputLabel id=\"bobina-select-label\">Seleziona bobina</InputLabel>\n                    <Select\n                      labelId=\"bobina-select-label\"\n                      id=\"bobina-select\"\n                      name=\"id_bobina\"\n                      value={formData.id_bobina}\n                      label=\"Seleziona bobina\"\n                      onChange={handleFormChange}\n                    >\n                      <MenuItem value=\"BOBINA_VUOTA\">\n                        <strong>BOBINA VUOTA</strong> (nessuna bobina associata)\n                      </MenuItem>\n                      <Divider />\n                      {bobine.length > 0 ? (\n                        <Box component=\"li\" sx={{ p: 1, bgcolor: 'background.paper' }}>\n                          <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: 'success.main' }}>\n                            {bobine.length} bobine compatibili trovate\n                          </Typography>\n                        </Box>\n                      ) : null}\n                      {bobine.map((bobina) => (\n                        <MenuItem\n                          key={bobina.id_bobina}\n                          value={bobina.id_bobina}\n                          disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                          sx={{\n                            '&.Mui-selected': { bgcolor: 'success.light' },\n                            '&.Mui-selected:hover': { bgcolor: 'success.light' },\n                            bgcolor: selectedCavo &&\n                                   bobina.tipologia === selectedCavo.tipologia &&\n                                   String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) &&\n                                   String(bobina.sezione) === String(selectedCavo.sezione) ?\n                                   'rgba(76, 175, 80, 0.08)' : 'inherit'\n                          }}\n                        >\n                          <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>\n                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                                {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'}\n                              </Typography>\n                              {selectedCavo &&\n                               bobina.tipologia === selectedCavo.tipologia &&\n                               String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) &&\n                               String(bobina.sezione) === String(selectedCavo.sezione) && (\n                                <Chip\n                                  size=\"small\"\n                                  label=\"Compatibile\"\n                                  color=\"success\"\n                                  variant=\"outlined\"\n                                  sx={{ height: 20, fontSize: '0.6rem' }}\n                                />\n                              )}\n                            </Box>\n                            <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>\n                              <Typography variant=\"caption\">\n                                {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                              </Typography>\n                              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                {bobina.metri_residui || 0} m disponibili\n                              </Typography>\n                            </Box>\n                          </Box>\n                        </MenuItem>\n                      ))}\n                    </Select>\n                    <FormHelperText>\n                      {formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'}\n                    </FormHelperText>\n                  </FormControl>\n\n                  {bobine.length === 0 && !bobineLoading && (\n                    <Alert severity=\"warning\" sx={{ mt: 2, fontSize: '0.8rem' }}>\n                      Nessuna bobina compatibile trovata. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\n                    </Alert>\n                  )}\n                </Box>\n              )}\n\n              {/* Mostra dettagli della bobina selezionata */}\n              {!bobineLoading && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && (() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                if (bobina) {\n                  return (\n                    <Box sx={{ mt: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n                      <Typography variant=\"body2\"><strong>Bobina:</strong> {getBobinaNumber(bobina.id_bobina)}</Typography>\n                      <Typography variant=\"body2\"><strong>Metri residui:</strong> {bobina.metri_residui || 0} m</Typography>\n                      <Typography variant=\"body2\">\n                        <strong>Stato:</strong>\n                        <Chip\n                          label={bobina.stato_bobina || 'N/D'}\n                          size=\"small\"\n                          color={getReelStateColor(bobina.stato_bobina)}\n                          variant=\"outlined\"\n                          sx={{ ml: 1 }}\n                        />\n                      </Typography>\n                    </Box>\n                  );\n                }\n                return null;\n              })()}\n            </Grid>\n          </Grid>\n\n          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>\n            <Button\n              variant=\"outlined\"\n              color=\"secondary\"\n              onClick={() => {\n                setSelectedCavo(null);\n                setFormData({\n                  id_cavo: '',\n                  metri_posati: '',\n                  id_bobina: ''\n                });\n              }}\n              startIcon={<CancelIcon />}\n              disabled={loading}\n            >\n              Annulla\n            </Button>\n\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSubmit}\n              endIcon={<SaveIcon />}\n              disabled={loading || !formData.metri_posati || !formData.id_bobina}\n            >\n              {loading ? <CircularProgress size={24} /> : 'Salva'}\n            </Button>\n          </Box>\n        </Paper>\n      )}\n\n      {/* Dialogo di conferma generico */}\n      <Dialog open={showConfirmDialog} onClose={() => setShowConfirmDialog(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">{confirmDialogProps.title}</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <Typography variant=\"body1\" sx={{ mt: 2 }}>\n            {confirmDialogProps.message}\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowConfirmDialog(false)} color=\"secondary\" variant=\"outlined\">\n            Annulla\n          </Button>\n          <Button\n            onClick={() => {\n              setShowConfirmDialog(false);\n              confirmDialogProps.onConfirm();\n            }}\n            color=\"primary\"\n            variant=\"contained\"\n            autoFocus\n          >\n            Conferma\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per cavi già posati */}\n      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">Cavo già posato</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          {alreadyLaidCavo && (\n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"body1\" paragraph>\n                Il cavo <strong>{alreadyLaidCavo.id_cavo}</strong> risulta già posato ({alreadyLaidCavo.metratura_reale || 0}m).\n              </Typography>\n              <Typography variant=\"body1\" paragraph>\n                Puoi scegliere di:\n              </Typography>\n              <Typography variant=\"body2\" component=\"ul\">\n                <li>Modificare la bobina associata al cavo</li>\n                <li>Selezionare un altro cavo</li>\n                <li>Annullare l'operazione</li>\n              </Typography>\n            </Box>\n          )}\n        </DialogContent>\n        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>\n          <Button onClick={handleCloseAlreadyLaidDialog} color=\"secondary\">\n            Annulla operazione\n          </Button>\n          <Box>\n            <Button onClick={handleSelectAnotherCable} color=\"primary\" sx={{ mr: 1 }}>\n              Seleziona altro cavo\n            </Button>\n            <Button onClick={handleModifyReel} variant=\"contained\" color=\"primary\">\n              Modifica bobina\n            </Button>\n          </Box>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={handleCloseIncompatibleReelDialog}\n        cavo={selectedCavo}\n        bobina={incompatibleReel}\n        onUpdateCavo={handleUpdateCavoToMatchReel}\n        onSelectAnotherReel={handleSelectAnotherReel}\n      />\n\n      {/* Dialogo per visualizzare i dettagli del cavo */}\n      <Dialog\n        open={showCavoDetailsDialog}\n        onClose={() => setShowCavoDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <InfoIcon color=\"primary\" />\n            <Typography variant=\"h6\">Dettagli Cavo</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <CavoDetailsView cavo={selectedCavo} />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowCavoDetailsDialog(false)} color=\"primary\">\n            Chiudi\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default InserisciMetriForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,QAClB,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;AAC/B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,wBAAwB,QAAQ,6BAA6B;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuE,WAAW,EAAEC,cAAc,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyE,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2E,aAAa,EAAEC,gBAAgB,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC+E,IAAI,EAAEC,OAAO,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiF,MAAM,EAAEC,SAAS,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmF,YAAY,EAAEC,eAAe,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqF,WAAW,EAAEC,cAAc,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACuF,UAAU,EAAEC,aAAa,CAAC,GAAGxF,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAM,CAACyF,QAAQ,EAAEC,WAAW,CAAC,GAAG1F,QAAQ,CAAC;IACvC2F,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACgG,YAAY,EAAEC,eAAe,CAAC,GAAGjG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAACkG,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACoG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACsG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACwG,eAAe,EAAEC,kBAAkB,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC0G,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACd2G,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC5C,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAM6C,eAAe,GAAIC,QAAQ,IAAK;IACpC;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvC,OAAOD,QAAQ,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAOF,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMG,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFzC,cAAc,CAAC,IAAI,CAAC;MACpB0C,OAAO,CAACC,GAAG,CAAC,oCAAoCnD,UAAU,KAAK,CAAC;;MAEhE;MACA,IAAI;QACF,MAAMoD,QAAQ,GAAG,MAAMxE,WAAW,CAACyE,OAAO,CAACrD,UAAU,CAAC;QACtDkD,OAAO,CAACC,GAAG,CAAC,YAAYC,QAAQ,CAACE,MAAM,OAAO,CAAC;;QAE/C;QACA;QACAtC,OAAO,CAACoC,QAAQ,CAAC;MACnB,CAAC,CAAC,OAAOG,SAAS,EAAE;QAClBL,OAAO,CAACM,KAAK,CAAC,qDAAqD,EAAED,SAAS,CAAC;;QAE/E;QACA,IAAIA,SAAS,CAACE,cAAc,IAAI,CAACF,SAAS,CAACG,QAAQ,IAC/CH,SAAS,CAACI,IAAI,KAAK,cAAc,IAChCJ,SAAS,CAACK,OAAO,IAAIL,SAAS,CAACK,OAAO,CAACb,QAAQ,CAAC,eAAe,CAAE,EAAE;UAEtEG,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;UACtE,MAAM,IAAIU,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;UAEvD,IAAI;YACF;YACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;YAC3C,MAAMC,OAAO,GAAGtF,aAAa,CAACuF,QAAQ,CAACC,OAAO;;YAE9C;YACA,MAAMC,aAAa,GAAG,MAAMpI,KAAK,CAACqI,GAAG,CACnC,GAAGJ,OAAO,SAASnE,UAAU,EAAE,EAC/B;cACEwE,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUR,KAAK;cAClC,CAAC;cACDS,OAAO,EAAE,KAAK,CAAC;YACjB,CACF,CAAC;YAEDvB,OAAO,CAACC,GAAG,CAAC,2CAA2CmB,aAAa,CAACI,IAAI,CAACpB,MAAM,OAAO,CAAC;YACxFtC,OAAO,CAACsD,aAAa,CAACI,IAAI,CAAC;UAC7B,CAAC,CAAC,OAAOC,UAAU,EAAE;YACnBzB,OAAO,CAACM,KAAK,CAAC,uCAAuC,EAAEmB,UAAU,CAAC;YAClE,MAAMA,UAAU;UAClB;QACF,CAAC,MAAM;UACL,MAAMpB,SAAS;QACjB;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIoB,YAAY,GAAG,iCAAiC;MAEpD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEA1D,OAAO,CAAC0E,YAAY,CAAC;IACvB,CAAC,SAAS;MACRpE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMoC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFlC,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,IAAIS,YAAY,EAAE;QAChB+B,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;QACxE,IAAI;UACF;UACAD,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;YACrDxB,OAAO,EAAER,YAAY,CAACQ,OAAO;YAC7BmD,SAAS,EAAE3D,YAAY,CAAC2D,SAAS;YACjCC,YAAY,EAAE5D,YAAY,CAAC4D,YAAY;YACvCC,OAAO,EAAE7D,YAAY,CAAC6D,OAAO;YAC7BC,IAAI,EAAE;cACJH,SAAS,EAAE,OAAO3D,YAAY,CAAC2D,SAAS;cACxCC,YAAY,EAAE,OAAO5D,YAAY,CAAC4D,YAAY;cAC9CC,OAAO,EAAE,OAAO7D,YAAY,CAAC6D;YAC/B;UACF,CAAC,CAAC;;UAEF;UACA,MAAMF,SAAS,GAAG3D,YAAY,CAAC2D,SAAS,KAAKI,SAAS,IAAI/D,YAAY,CAAC2D,SAAS,KAAK,IAAI,GAAGK,MAAM,CAAChE,YAAY,CAAC2D,SAAS,CAAC,GAAG,EAAE;;UAE/H;UACA,IAAIC,YAAY,GAAG5D,YAAY,CAAC4D,YAAY,KAAKG,SAAS,IAAI/D,YAAY,CAAC4D,YAAY,KAAK,IAAI,GAAGI,MAAM,CAAChE,YAAY,CAAC4D,YAAY,CAAC,GAAG,GAAG;UAC1I;UACA,IAAIA,YAAY,CAAChC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAChC,MAAMqC,KAAK,GAAGL,YAAY,CAAC/B,KAAK,CAAC,KAAK,CAAC;YACvC+B,YAAY,GAAGK,KAAK,CAAC,CAAC,CAAC;YACvBlC,OAAO,CAACC,GAAG,CAAC,0CAA0ChC,YAAY,CAAC4D,YAAY,OAAOA,YAAY,EAAE,CAAC;UACvG;UAEA,MAAMC,OAAO,GAAG7D,YAAY,CAAC6D,OAAO,KAAKE,SAAS,IAAI/D,YAAY,CAAC6D,OAAO,KAAK,IAAI,GAAGG,MAAM,CAAChE,YAAY,CAAC6D,OAAO,CAAC,GAAG,GAAG;UAExH9B,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;YAAE2B,SAAS;YAAEC,YAAY;YAAEC;UAAQ,CAAC,CAAC;;UAE7F;UACA,MAAMK,iBAAiB,GAAG,MAAM5F,gBAAgB,CAAC6F,oBAAoB,CACnEtF,UAAU,EACV8E,SAAS,EACTC,YAAY,EACZC,OACF,CAAC;UAED,IAAIK,iBAAiB,IAAIA,iBAAiB,CAAC/B,MAAM,GAAG,CAAC,EAAE;YACrDJ,OAAO,CAACC,GAAG,CAAC,WAAWkC,iBAAiB,CAAC/B,MAAM,6BAA6B,CAAC;YAC7E;YACA+B,iBAAiB,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,aAAa,GAAGF,CAAC,CAACE,aAAa,CAAC;YACnExE,SAAS,CAACmE,iBAAiB,CAAC;YAC5B;UACF,CAAC,MAAM;YACLnC,OAAO,CAACC,GAAG,CAAC,gFAAgF,CAAC;YAC7F;YACA,MAAMwC,UAAU,GAAG,MAAMlG,gBAAgB,CAACmG,SAAS,CAAC5F,UAAU,CAAC;YAC/D;YACA,MAAM6F,kBAAkB,GAAGF,UAAU,CAACG,MAAM,CAACC,MAAM,IACjD,CAACA,MAAM,CAACC,YAAY,KAAK,aAAa,IAAID,MAAM,CAACC,YAAY,KAAK,QAAQ,KAC1ED,MAAM,CAACC,YAAY,KAAK,MAAM,IAAID,MAAM,CAACC,YAAY,KAAK,WAC5D,CAAC;YACD;YACAH,kBAAkB,CAACN,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,aAAa,GAAGF,CAAC,CAACE,aAAa,CAAC;YACpExE,SAAS,CAAC2E,kBAAkB,CAAC;YAC7B;UACF;QACF,CAAC,CAAC,OAAOrC,KAAK,EAAE;UACdN,OAAO,CAACM,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;UACxEN,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC9D;MACF;;MAEA;MACAD,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;;MAEvE;MACAD,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvE,MAAMwC,UAAU,GAAG,MAAMlG,gBAAgB,CAACmG,SAAS,CAAC5F,UAAU,CAAC;;MAE/D;MACA,IAAI6F,kBAAkB,GAAGF,UAAU,CAACG,MAAM,CAACC,MAAM,IAC/C,CAACA,MAAM,CAACC,YAAY,KAAK,aAAa,IAAID,MAAM,CAACC,YAAY,KAAK,QAAQ,KAC1ED,MAAM,CAACC,YAAY,KAAK,MAAM,IAAID,MAAM,CAACC,YAAY,KAAK,WAC5D,CAAC;;MAED;MACA,IAAI7E,YAAY,EAAE;QAChB;QACA,IAAIA,YAAY,CAAC2D,SAAS,KAAKI,SAAS,IAAI/D,YAAY,CAAC4D,YAAY,KAAKG,SAAS,IAAI/D,YAAY,CAAC6D,OAAO,KAAKE,SAAS,EAAE;UACzHhC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;;UAE7D;UACA,MAAMkC,iBAAiB,GAAGQ,kBAAkB,CAACC,MAAM,CAACC,MAAM,IAAI;YAC5D;YACA,MAAME,aAAa,GAAGd,MAAM,CAAChE,YAAY,CAAC2D,SAAS,IAAI,EAAE,CAAC;;YAE1D;YACA,IAAIoB,iBAAiB,GAAGf,MAAM,CAAChE,YAAY,CAAC4D,YAAY,IAAI,GAAG,CAAC;YAChE,IAAIoB,cAAc,GAAGD,iBAAiB;YACtC;YACA,IAAIA,iBAAiB,CAACnD,QAAQ,CAAC,KAAK,CAAC,EAAE;cACrC,MAAMqC,KAAK,GAAGc,iBAAiB,CAAClD,KAAK,CAAC,KAAK,CAAC;cAC5CmD,cAAc,GAAGf,KAAK,CAAC,CAAC,CAAC;cACzBlC,OAAO,CAACC,GAAG,CAAC,2DAA2D+C,iBAAiB,OAAOC,cAAc,EAAE,CAAC;YAClH;YAEA,MAAMC,WAAW,GAAGjB,MAAM,CAAChE,YAAY,CAAC6D,OAAO,IAAI,GAAG,CAAC;YAEvD,MAAMqB,eAAe,GAAGlB,MAAM,CAACY,MAAM,CAACjB,SAAS,IAAI,EAAE,CAAC;YACtD,MAAMwB,gBAAgB,GAAGnB,MAAM,CAACY,MAAM,CAAChB,YAAY,IAAI,GAAG,CAAC;YAC3D,MAAMwB,aAAa,GAAGpB,MAAM,CAACY,MAAM,CAACf,OAAO,IAAI,GAAG,CAAC;;YAEnD;YACA9B,OAAO,CAACC,GAAG,CAAC,oBAAoB4C,MAAM,CAAClE,SAAS,GAAG,EAAE;cACnDiD,SAAS,EAAE,GAAGuB,eAAe,QAAQJ,aAAa,EAAE;cACpDlB,YAAY,EAAE,GAAGuB,gBAAgB,QAAQH,cAAc,EAAE;cACzDnB,OAAO,EAAE,GAAGuB,aAAa,QAAQH,WAAW;YAC9C,CAAC,CAAC;YAEF,OAAOC,eAAe,KAAKJ,aAAa,IACjCK,gBAAgB,KAAKH,cAAc,IACnCI,aAAa,KAAKH,WAAW;UACtC,CAAC,CAAC;;UAEF;UACA,IAAIf,iBAAiB,CAAC/B,MAAM,GAAG,CAAC,EAAE;YAChCuC,kBAAkB,GAAGR,iBAAiB;YACtCnC,OAAO,CAACC,GAAG,CAAC,YAAYkC,iBAAiB,CAAC/B,MAAM,iCAAiC,CAAC;UACpF,CAAC,MAAM;YACLJ,OAAO,CAACC,GAAG,CAAC,2FAA2F,CAAC;YACxG;YACA0C,kBAAkB,GAAGF,UAAU,CAACG,MAAM,CAACC,MAAM,IAC3C,CAACA,MAAM,CAACC,YAAY,KAAK,aAAa,IAAID,MAAM,CAACC,YAAY,KAAK,QAAQ,KAC1ED,MAAM,CAACC,YAAY,KAAK,MAAM,IAAID,MAAM,CAACC,YAAY,KAAK,WAC5D,CAAC;UACH;QACF;;QAEA;QACAH,kBAAkB,CAACN,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,aAAa,GAAGF,CAAC,CAACE,aAAa,CAAC;MACtE;MAEAxE,SAAS,CAAC2E,kBAAkB,CAAC;IAC/B,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DtD,OAAO,CAAC,uCAAuC,IAAIsD,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRlD,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM8F,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACnF,WAAW,CAACoF,IAAI,CAAC,CAAC,EAAE;MACvBvG,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFM,cAAc,CAAC,IAAI,CAAC;MACpB0C,OAAO,CAACC,GAAG,CAAC,6BAA6B9B,WAAW,CAACoF,IAAI,CAAC,CAAC,iBAAiBzG,UAAU,EAAE,CAAC;;MAEzF;MACA,MAAMoD,QAAQ,GAAG,MAAMxE,WAAW,CAACyE,OAAO,CAACrD,UAAU,CAAC;;MAEtD;MACA,MAAM0G,YAAY,GAAGtD,QAAQ,CAAC0C,MAAM,CAACa,IAAI,IACvCA,IAAI,CAAChF,OAAO,CAACiF,WAAW,CAAC,CAAC,CAAC7D,QAAQ,CAAC1B,WAAW,CAACoF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,CACtE,CAAC;MAED1D,OAAO,CAACC,GAAG,CAAC,WAAWuD,YAAY,CAACpD,MAAM,iCAAiC,CAAC;;MAE5E;MACA,MAAMuD,UAAU,GAAGH,YAAY,CAACI,IAAI,CAACH,IAAI,IACvCA,IAAI,CAAChF,OAAO,CAACiF,WAAW,CAAC,CAAC,KAAKvF,WAAW,CAACoF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAChE,CAAC;MAED,IAAIC,UAAU,EAAE;QACd3D,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE0D,UAAU,CAAC;;QAEzD;QACA,IAAIA,UAAU,CAACE,mBAAmB,KAAK,YAAY,IAAKF,UAAU,CAACG,eAAe,IAAIH,UAAU,CAACG,eAAe,GAAG,CAAE,EAAE;UACrH9D,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE0D,UAAU,CAAC;UAC/DpE,kBAAkB,CAACoE,UAAU,CAAC;UAC9BtE,wBAAwB,CAAC,IAAI,CAAC;UAC9B/B,cAAc,CAAC,KAAK,CAAC;UACrB;QACF;;QAEA;QACA,IAAIqG,UAAU,CAACI,sBAAsB,KAAK,CAAC,EAAE;UAC3C/D,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE0D,UAAU,CAAC;UAC9C;QACF;;QAEA;QACAK,gBAAgB,CAACL,UAAU,CAAC;MAC9B,CAAC,MAAM,IAAIH,YAAY,CAACpD,MAAM,GAAG,CAAC,EAAE;QAClC;QACA1C,gBAAgB,CAAC8F,YAAY,CAAC;QAC9B5F,oBAAoB,CAAC,IAAI,CAAC;MAC5B,CAAC,MAAM;QACL;QACAZ,OAAO,CAAC,oCAAoCmB,WAAW,CAACoF,IAAI,CAAC,CAAC,kBAAkBzG,UAAU,EAAE,CAAC;MAC/F;IACF,CAAC,CAAC,OAAOwD,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;MAEtD;MACA,IAAIoB,YAAY,GAAG,+BAA+B;MAElD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAAC2D,MAAM,KAAK,GAAG,EAAE;QAC/BvC,YAAY,GAAG,gBAAgBvD,WAAW,CAACoF,IAAI,CAAC,CAAC,8BAA8BzG,UAAU,EAAE;MAC7F,CAAC,MAAM,IAAIwD,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEA1D,OAAO,CAAC0E,YAAY,CAAC;IACvB,CAAC,SAAS;MACRpE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM0G,gBAAgB,GAAIP,IAAI,IAAK;IACjC;IACA,IAAIA,IAAI,CAACI,mBAAmB,KAAK,YAAY,IAAKJ,IAAI,CAACK,eAAe,IAAIL,IAAI,CAACK,eAAe,GAAG,CAAE,EAAE;MACnG;MACAvE,kBAAkB,CAACkE,IAAI,CAAC;MACxBpE,wBAAwB,CAAC,IAAI,CAAC;MAC9B;IACF;IACA;IAAA,KACK,IAAIoE,IAAI,CAACM,sBAAsB,KAAK,CAAC,EAAE;MAC1C;MACA,IAAIG,MAAM,CAACC,OAAO,CAAC,WAAWV,IAAI,CAAChF,OAAO,0CAA0C,CAAC,EAAE;QACrF;QACA2F,eAAe,CAACX,IAAI,CAAChF,OAAO,CAAC,CAAC4F,IAAI,CAAC,MAAM;UACvC;UACA,MAAMC,WAAW,GAAG;YAAE,GAAGb,IAAI;YAAEM,sBAAsB,EAAE;UAAE,CAAC;UAC1D7F,eAAe,CAACoG,WAAW,CAAC;UAC5B9F,WAAW,CAAC;YACV,GAAGD,QAAQ;YACXE,OAAO,EAAE6F,WAAW,CAAC7F,OAAO;YAC5BC,YAAY,EAAE;UAChB,CAAC,CAAC;UACF;UACAd,oBAAoB,CAAC,KAAK,CAAC;;UAE3B;UACA8B,UAAU,CAAC,CAAC;QACd,CAAC,CAAC,CAAC6E,KAAK,CAACjE,KAAK,IAAI;UAChBN,OAAO,CAACM,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvEtD,OAAO,CAAC,kDAAkD,IAAIsD,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;QACvG,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;MACF;IACF,CAAC,MAAM;MACL;MACAxC,eAAe,CAACuF,IAAI,CAAC;MACrBjF,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEgF,IAAI,CAAChF,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF;MACAd,oBAAoB,CAAC,KAAK,CAAC;;MAE3B;MACA,IAAI6F,IAAI,CAAC7B,SAAS,IAAI6B,IAAI,CAAC5B,YAAY,IAAI4B,IAAI,CAAC3B,OAAO,EAAE;QACvD9B,OAAO,CAACC,GAAG,CAAC,8CAA8CwD,IAAI,CAAChF,OAAO,KAAK,CAAC;QAC5EiB,UAAU,CAAC,CAAC;MACd;IACF;EACF,CAAC;;EAED;EACA,MAAM0E,eAAe,GAAG,MAAOI,MAAM,IAAK;IACxC,IAAI;MACF;MACA,MAAM9I,WAAW,CAAC0I,eAAe,CAACtH,UAAU,EAAE0H,MAAM,CAAC;MACrDzH,SAAS,CAAC,QAAQyH,MAAM,0BAA0B,CAAC;MACnD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOlE,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvEtD,OAAO,CAAC,kDAAkD,IAAIsD,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACrG,MAAMJ,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMmE,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACA,IAAIF,IAAI,KAAK,WAAW,IAAIC,KAAK,IAAIA,KAAK,KAAK,cAAc,EAAE;MAC7D;MACA,MAAM/B,MAAM,GAAG9E,MAAM,CAAC6F,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAAC5D,SAAS,KAAKiG,KAAK,CAAC;MAEtD,IAAI/B,MAAM,IAAI5E,YAAY,EAAE;QAC1B;QACA,IAAIgF,cAAc,GAAGhF,YAAY,CAAC4D,YAAY,KAAKG,SAAS,IAAI/D,YAAY,CAAC4D,YAAY,KAAK,IAAI,GAAGI,MAAM,CAAChE,YAAY,CAAC4D,YAAY,CAAC,GAAG,GAAG;QAC5I,IAAIoB,cAAc,CAACpD,QAAQ,CAAC,KAAK,CAAC,EAAE;UAClC,MAAMqC,KAAK,GAAGe,cAAc,CAACnD,KAAK,CAAC,KAAK,CAAC;UACzCmD,cAAc,GAAGf,KAAK,CAAC,CAAC,CAAC;UACzBlC,OAAO,CAACC,GAAG,CAAC,0CAA0ChC,YAAY,CAAC4D,YAAY,OAAOoB,cAAc,EAAE,CAAC;QACzG;QAEA,MAAMG,gBAAgB,GAAGnB,MAAM,CAACY,MAAM,CAAChB,YAAY,IAAI,GAAG,CAAC;QAE3D,MAAMiD,YAAY,GAChBjC,MAAM,CAACjB,SAAS,KAAK3D,YAAY,CAAC2D,SAAS,IAC3CwB,gBAAgB,KAAKH,cAAc,IACnChB,MAAM,CAACY,MAAM,CAACf,OAAO,CAAC,KAAKG,MAAM,CAAChE,YAAY,CAAC6D,OAAO,CAAC;QAEzD,IAAI,CAACgD,YAAY,EAAE;UACjB9E,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE4C,MAAM,CAAC;UACxD7C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEhC,YAAY,CAAC;UAC3C+B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;YAAEgD,cAAc;YAAEG;UAAiB,CAAC,CAAC;;UAE1E;UACAjE,mBAAmB,CAAC0D,MAAM,CAAC;UAC3B5D,6BAA6B,CAAC,IAAI,CAAC;;UAEnC;UACA;QACF;MACF;IACF;IAEAT,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACoG,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACAG,aAAa,CAACJ,IAAI,EAAEC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMG,aAAa,GAAGA,CAACJ,IAAI,EAAEC,KAAK,KAAK;IACrC,IAAItE,KAAK,GAAG,IAAI;IAChB,IAAI0E,OAAO,GAAG,IAAI;IAElB,IAAIL,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACrB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjCjD,KAAK,GAAG,uCAAuC;QAC/C,OAAO,KAAK;MACd;;MAEA;MACA,IAAI2E,KAAK,CAACC,UAAU,CAACN,KAAK,CAAC,CAAC,IAAIM,UAAU,CAACN,KAAK,CAAC,IAAI,CAAC,EAAE;QACtDtE,KAAK,GAAG,sCAAsC;QAC9C,OAAO,KAAK;MACd;MAEA,MAAM6E,WAAW,GAAGD,UAAU,CAACN,KAAK,CAAC;;MAErC;MACA,IAAI3G,YAAY,IAAIA,YAAY,CAACmH,aAAa,IAAID,WAAW,GAAGD,UAAU,CAACjH,YAAY,CAACmH,aAAa,CAAC,EAAE;QACtGJ,OAAO,GAAG,mBAAmBG,WAAW,yCAAyClH,YAAY,CAACmH,aAAa,IAAI;MACjH;;MAEA;MACA,IAAI7G,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC/D,MAAMkE,MAAM,GAAG9E,MAAM,CAAC6F,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAAC5D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIkE,MAAM,IAAIsC,WAAW,GAAGD,UAAU,CAACrC,MAAM,CAACL,aAAa,CAAC,EAAE;UAC5DwC,OAAO,GAAG,mBAAmBG,WAAW,6CAA6CtC,MAAM,CAACL,aAAa,oCAAoC;QAC/I;MACF;IACF,CAAC,MAAM,IAAImC,IAAI,KAAK,WAAW,EAAE;MAC/B;MACA,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACrB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjCjD,KAAK,GAAG,qCAAqC;QAC7C,OAAO,KAAK;MACd;IACF;;IAEA;IACAzB,aAAa,CAACwG,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACV,IAAI,GAAGrE;IACV,CAAC,CAAC,CAAC;;IAEH;IACAvB,eAAe,CAACsG,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACV,IAAI,GAAGK;IACV,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC1E,KAAK;EACf,CAAC;;EAED;EACA,MAAM,CAACgF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzM,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC0M,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3M,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC4M,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7M,QAAQ,CAAC;IAC3D8M,KAAK,EAAE,EAAE;IACTlF,OAAO,EAAE,EAAE;IACXmF,SAAS,EAAEA,CAAA,KAAM,CAAC;EACpB,CAAC,CAAC;;EAEF;;EAEA;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMC,QAAQ,GAAG,CAAC,CAAC;;IAEnB;IACAV,oBAAoB,CAAC,KAAK,CAAC;;IAE3B;IACA,IAAI,CAAChH,QAAQ,CAACG,YAAY,IAAIH,QAAQ,CAACG,YAAY,CAAC6E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjEyC,MAAM,CAACtH,YAAY,GAAG,uCAAuC;MAC7DqH,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAId,KAAK,CAACC,UAAU,CAAC3G,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAIwG,UAAU,CAAC3G,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;MAC7FsH,MAAM,CAACtH,YAAY,GAAG,sCAAsC;MAC5DqH,OAAO,GAAG,KAAK;IACjB;;IAEA;IACA,IAAI,CAACxH,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,CAAC4E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC3DyC,MAAM,CAACrH,SAAS,GAAG,qCAAqC;MACxDoH,OAAO,GAAG,KAAK;IACjB;IAEA,IAAIA,OAAO,EAAE;MACX,MAAMZ,WAAW,GAAGD,UAAU,CAAC3G,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA,IAAIT,YAAY,IAAIA,YAAY,CAACmH,aAAa,IAAID,WAAW,GAAGD,UAAU,CAACjH,YAAY,CAACmH,aAAa,CAAC,EAAE;QACtGa,QAAQ,CAACvH,YAAY,GAAG,mBAAmByG,WAAW,yCAAyClH,YAAY,CAACmH,aAAa,IAAI;QAC7HG,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5B;QACA;MACF;;MAEA;MACA,IAAIQ,OAAO,IAAIxH,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC1E,MAAMkE,MAAM,GAAG9E,MAAM,CAAC6F,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAAC5D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIkE,MAAM,IAAIsC,WAAW,GAAGD,UAAU,CAACrC,MAAM,CAACL,aAAa,CAAC,EAAE;UAC5DyD,QAAQ,CAACvH,YAAY,GAAG,mBAAmByG,WAAW,6CAA6CtC,MAAM,CAACL,aAAa,oCAAoC;UAC3J+C,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;;UAE5B;UACAI,qBAAqB,CAAC;YACpBC,KAAK,EAAE,kCAAkC;YACzClF,OAAO,EAAE,mBAAmByE,WAAW,6CAA6CtC,MAAM,CAACL,aAAa,8DAA8D;YACtKqD,SAAS,EAAEA,CAAA,KAAM;cACf;cACAK,UAAU,CAAC,CAAC;YACd;UACF,CAAC,CAAC;UACFT,oBAAoB,CAAC,IAAI,CAAC;UAC1B,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;IACF;IAEA5G,aAAa,CAACmH,MAAM,CAAC;IACrBjH,eAAe,CAACkH,QAAQ,CAAC;IACzB,OAAOF,OAAO;EAChB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI7H,UAAU,KAAK,CAAC,EAAE;MACpB;MACA,IAAI,CAACyH,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;IACF,CAAC,MAAM,IAAIzH,UAAU,KAAK,CAAC,EAAE;MAC3B;MACAqB,UAAU,CAAC,CAAC;IACd;IAEApB,aAAa,CAAE6H,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB9H,aAAa,CAAE6H,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxB/H,aAAa,CAAC,CAAC,CAAC;IAChBJ,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBI,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA;EACA,MAAMuH,2BAA2B,GAAGA,CAACnB,WAAW,EAAEoB,YAAY,KAAK;IACjE,OAAOvK,mBAAmB,CAACmJ,WAAW,EAAEoB,YAAY,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B;IACA;IACA,IAAI5G,QAAQ;IACZ,IAAI6G,kBAAkB;IACtB,IAAItB,WAAW;IACf,IAAIuB,SAAS,GAAG,KAAK;IAErB,IAAI;MACFtJ,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI,CAAC0I,YAAY,CAAC,CAAC,EAAE;QACnB1I,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA+H,WAAW,GAAGD,UAAU,CAAC3G,QAAQ,CAACG,YAAY,CAAC;;MAE/C;MACAkB,QAAQ,GAAGrB,QAAQ,CAACI,SAAS;;MAE7B;MACA,IAAI,CAACiB,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;QAChC;QACAf,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbD,SAAS,EAAE;QACb,CAAC,CAAC;QACFvB,UAAU,CAAC,KAAK,CAAC;QACjB;MACF,CAAC,MAAM,IAAIwC,QAAQ,KAAK,cAAc,EAAE;QACtC;QACAI,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C;QACAL,QAAQ,GAAG,cAAc;MAC3B,CAAC,MAAM;QACL;QACAI,OAAO,CAACC,GAAG,CAAC,wBAAwBL,QAAQ,EAAE,CAAC;MACjD;;MAEA;MACA6G,kBAAkB,GAAGH,2BAA2B,CAACnB,WAAW,EAAElH,YAAY,CAACmH,aAAa,CAAC;;MAEzF;MACAsB,SAAS,GAAG,KAAK;;MAEjB;MACA,IAAI9G,QAAQ,KAAK,cAAc,EAAE;QAC/B8G,SAAS,GAAG,IAAI;QAChB1G,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACrD;MACA;MAAA,KACK,IAAIL,QAAQ,IAAIA,QAAQ,KAAK,cAAc,EAAE;QAChD,MAAMiD,MAAM,GAAG9E,MAAM,CAAC6F,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAAC5D,SAAS,KAAKiB,QAAQ,CAAC;QACzD,IAAIiD,MAAM,IAAIsC,WAAW,GAAGD,UAAU,CAACrC,MAAM,CAACL,aAAa,CAAC,EAAE;UAC5DkE,SAAS,GAAG,IAAI;UAChB1G,OAAO,CAACC,GAAG,CAAC,qCAAqCL,QAAQ,iCAAiC,CAAC;QAC7F;MACF;;MAEA;MACA,IAAI3B,YAAY,IAAIA,YAAY,CAACmH,aAAa,IAAID,WAAW,GAAGD,UAAU,CAACjH,YAAY,CAACmH,aAAa,CAAC,EAAE;QACtG;QACAsB,SAAS,GAAG,IAAI;QAChB1G,OAAO,CAACC,GAAG,CAAC,yCAAyCkF,WAAW,sBAAsBlH,YAAY,CAACmH,aAAa,GAAG,CAAC;MACtH;;MAEA;MACApF,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBnD,UAAU;QACV0H,MAAM,EAAEjG,QAAQ,CAACE,OAAO;QACxB0G,WAAW;QACXvF,QAAQ;QACR8G,SAAS;QACTD;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAACnB,iBAAiB,EAAE;QACtB,MAAMqB,cAAc,GAAG,qCAAqCpI,QAAQ,CAACE,OAAO,QAAQ0G,WAAW,WAAW;;QAE1G;QACAQ,qBAAqB,CAAC;UACpBC,KAAK,EAAE,wBAAwB;UAC/BlF,OAAO,EAAEiG,cAAc;UACvBd,SAAS,EAAE,MAAAA,CAAA,KAAY;YACrB;YACA,IAAI;cACFzI,UAAU,CAAC,IAAI,CAAC;cAEhB,MAAM1B,WAAW,CAACkL,iBAAiB,CACjC9J,UAAU,EACVyB,QAAQ,CAACE,OAAO,EAChB0G,WAAW,EACXvF,QAAQ,EACR8G,SACF,CAAC;;cAED;cACA,IAAIG,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;cAC9F,IAAI7G,QAAQ,KAAK,cAAc,EAAE;gBAC/BiH,cAAc,IAAI,iCAAiC;cACrD,CAAC,MAAM,IAAIjH,QAAQ,EAAE;gBACnB,MAAMiD,MAAM,GAAG9E,MAAM,CAAC6F,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAAC5D,SAAS,KAAKiB,QAAQ,CAAC;gBACzD,IAAIiD,MAAM,EAAE;kBACVgE,cAAc,IAAI,gCAAgCjH,QAAQ,EAAE;gBAC9D;cACF;;cAEA;cACA7C,SAAS,CAAC8J,cAAc,CAAC;;cAEzB;cACAR,WAAW,CAAC,CAAC;;cAEb;cACAtG,QAAQ,CAAC,CAAC;YACZ,CAAC,CAAC,OAAOO,KAAK,EAAE;cACdN,OAAO,CAACM,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;cAEzE;cACA,IAAIV,QAAQ,KAAK,cAAc,IAAIU,KAAK,CAACwG,OAAO,EAAE;gBAChD;gBACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;gBAC7H1J,SAAS,CAAC8J,cAAc,CAAC;;gBAEzB;gBACAR,WAAW,CAAC,CAAC;;gBAEb;gBACAtG,QAAQ,CAAC,CAAC;gBACV;cACF;;cAEA;cACA,IAAI2B,YAAY,GAAG,kDAAkD;cAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;gBAAA,IAAAuG,oBAAA;gBAClB;gBACA,MAAM9C,MAAM,GAAG3D,KAAK,CAACE,QAAQ,CAACyD,MAAM;gBACpC,MAAMtC,MAAM,GAAG,EAAAoF,oBAAA,GAAAzG,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAAuF,oBAAA,uBAAnBA,oBAAA,CAAqBpF,MAAM,KAAIrB,KAAK,CAACI,OAAO;gBAE3D,IAAIuD,MAAM,KAAK,GAAG,EAAE;kBAClB;kBACA,IAAItC,MAAM,CAAC9B,QAAQ,CAAC,eAAe,CAAC,EAAE;oBACpC6B,YAAY,GAAG,uGAAuG;kBACxH,CAAC,MAAM,IAAIC,MAAM,CAAC9B,QAAQ,CAAC,YAAY,CAAC,EAAE;oBACxC6B,YAAY,GAAG,4EAA4E;kBAC7F,CAAC,MAAM;oBACLA,YAAY,GAAGC,MAAM;kBACvB;gBACF,CAAC,MAAM,IAAIsC,MAAM,KAAK,GAAG,EAAE;kBACzB;kBACA,IAAIrE,QAAQ,KAAK,cAAc,IAAI+B,MAAM,CAAC9B,QAAQ,CAAC,aAAa,CAAC,EAAE;oBACjE,IAAIgH,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;oBAC7H1J,SAAS,CAAC8J,cAAc,CAAC;;oBAEzB;oBACAR,WAAW,CAAC,CAAC;;oBAEb;oBACAtG,QAAQ,CAAC,CAAC;oBACV;kBACF,CAAC,MAAM;oBACL2B,YAAY,GAAG,8BAA8BC,MAAM,EAAE;kBACvD;gBACF,CAAC,MAAM;kBACLD,YAAY,GAAG,sBAAsBuC,MAAM,MAAMtC,MAAM,EAAE;gBAC3D;cACF,CAAC,MAAM,IAAIrB,KAAK,CAAC0G,OAAO,EAAE;gBACxB;gBACAtF,YAAY,GAAG,+DAA+D;cAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAI/B,QAAQ,KAAK,cAAc,EAAE;gBACtD;gBACA8B,YAAY,GAAGpB,KAAK,CAACqB,MAAM;gBAC3B,IAAIrB,KAAK,CAAC2D,MAAM,KAAK,GAAG,IAAI3D,KAAK,CAACwG,OAAO,EAAE;kBACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;kBAC7H1J,SAAS,CAAC8J,cAAc,CAAC;;kBAEzB;kBACAR,WAAW,CAAC,CAAC;;kBAEb;kBACAtG,QAAQ,CAAC,CAAC;kBACV;gBACF;cACF,CAAC,MAAM;gBACL;gBACA2B,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;cACtE;cAEA3E,OAAO,CAAC0E,YAAY,CAAC;YACvB,CAAC,SAAS;cACRtE,UAAU,CAAC,KAAK,CAAC;YACnB;UACF;QACF,CAAC,CAAC;QACFqI,oBAAoB,CAAC,IAAI,CAAC;QAC1BrI,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA4C,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ED,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEnD,UAAU,CAAC;MACxCkD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE1B,QAAQ,CAACE,OAAO,CAAC;MAC3CuB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEkF,WAAW,CAAC;MAC3CnF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEL,QAAQ,EAAE,OAAOA,QAAQ,CAAC;MACtDI,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEyG,SAAS,CAAC;MAEtC,MAAMhL,WAAW,CAACkL,iBAAiB,CACjC9J,UAAU,EACVyB,QAAQ,CAACE,OAAO,EAChB0G,WAAW,EACXvF,QAAQ,EACR8G,SACF,CAAC;;MAED;MACA,IAAIG,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;MAC9F,IAAI7G,QAAQ,KAAK,cAAc,EAAE;QAC/BiH,cAAc,IAAI,iCAAiC;MACrD,CAAC,MAAM,IAAIjH,QAAQ,EAAE;QACnB,MAAMiD,MAAM,GAAG9E,MAAM,CAAC6F,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAAC5D,SAAS,KAAKiB,QAAQ,CAAC;QACzD,IAAIiD,MAAM,EAAE;UACVgE,cAAc,IAAI,gCAAgCjH,QAAQ,EAAE;QAC9D;MACF;;MAEA;MACA7C,SAAS,CAAC8J,cAAc,CAAC;;MAEzB;MACAR,WAAW,CAAC,CAAC;;MAEb;MACAtG,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;MAEzE;MACA,IAAIV,QAAQ,KAAK,cAAc,IAAIU,KAAK,CAACwG,OAAO,EAAE;QAChD;QACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;QAC7H1J,SAAS,CAAC8J,cAAc,CAAC;;QAEzB;QACAR,WAAW,CAAC,CAAC;;QAEb;QACAtG,QAAQ,CAAC,CAAC;QACV;MACF;;MAEA;MACA,IAAI2B,YAAY,GAAG,kDAAkD;MAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;QAAA,IAAAyG,qBAAA;QAClB;QACA,MAAMhD,MAAM,GAAG3D,KAAK,CAACE,QAAQ,CAACyD,MAAM;QACpC,MAAMtC,MAAM,GAAG,EAAAsF,qBAAA,GAAA3G,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAAyF,qBAAA,uBAAnBA,qBAAA,CAAqBtF,MAAM,KAAIrB,KAAK,CAACI,OAAO;QAE3D,IAAIuD,MAAM,KAAK,GAAG,EAAE;UAClB;UACA,IAAItC,MAAM,CAAC9B,QAAQ,CAAC,eAAe,CAAC,EAAE;YACpC6B,YAAY,GAAG,uGAAuG;UACxH,CAAC,MAAM,IAAIC,MAAM,CAAC9B,QAAQ,CAAC,YAAY,CAAC,EAAE;YACxC6B,YAAY,GAAG,4EAA4E;UAC7F,CAAC,MAAM;YACLA,YAAY,GAAGC,MAAM;UACvB;QACF,CAAC,MAAM,IAAIsC,MAAM,KAAK,GAAG,EAAE;UACzB;UACA,IAAIrE,QAAQ,KAAK,cAAc,IAAI+B,MAAM,CAAC9B,QAAQ,CAAC,aAAa,CAAC,EAAE;YACjE,IAAIgH,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;YAC7H1J,SAAS,CAAC8J,cAAc,CAAC;;YAEzB;YACAR,WAAW,CAAC,CAAC;;YAEb;YACAtG,QAAQ,CAAC,CAAC;YACV;UACF,CAAC,MAAM;YACL2B,YAAY,GAAG,8BAA8BC,MAAM,EAAE;UACvD;QACF,CAAC,MAAM;UACLD,YAAY,GAAG,sBAAsBuC,MAAM,MAAMtC,MAAM,EAAE;QAC3D;MACF,CAAC,MAAM,IAAIrB,KAAK,CAAC0G,OAAO,EAAE;QACxB;QACAtF,YAAY,GAAG,+DAA+D;MAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAI/B,QAAQ,KAAK,cAAc,EAAE;QACtD;QACA8B,YAAY,GAAGpB,KAAK,CAACqB,MAAM;QAC3B,IAAIrB,KAAK,CAAC2D,MAAM,KAAK,GAAG,IAAI3D,KAAK,CAACwG,OAAO,EAAE;UACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;UAC7H1J,SAAS,CAAC8J,cAAc,CAAC;;UAEzB;UACAR,WAAW,CAAC,CAAC;;UAEb;UACAtG,QAAQ,CAAC,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACL;QACA2B,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;MACtE;MAEA3E,OAAO,CAAC0E,YAAY,CAAC;IACvB,CAAC,SAAS;MACRtE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8J,WAAW,GAAGA,CAAA,KAAM;IACxB,oBACExK,OAAA,CAACzD,GAAG;MAAAkO,QAAA,gBACFzK,OAAA,CAACvD,UAAU;QAACiO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb/K,OAAA,CAACxD,KAAK;QAACwO,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzBzK,OAAA,CAACvD,UAAU;UAACiO,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb/K,OAAA,CAACpD,IAAI;UAACuO,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAAZ,QAAA,gBAC7CzK,OAAA,CAACpD,IAAI;YAAC0O,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACfzK,OAAA,CAACtD,SAAS;cACR8O,SAAS;cACTC,KAAK,EAAC,SAAS;cACff,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAEzG,WAAY;cACnBiK,QAAQ,EAAG1D,CAAC,IAAKtG,cAAc,CAACsG,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cAChDyD,WAAW,EAAC;YAAyB;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP/K,OAAA,CAACpD,IAAI;YAAC0O,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACfzK,OAAA,CAACrD,MAAM;cACL6O,SAAS;cACTd,OAAO,EAAC,WAAW;cACnBkB,KAAK,EAAC,SAAS;cACfC,OAAO,EAAEjF,oBAAqB;cAC9BkF,QAAQ,EAAEnL,WAAW,IAAI,CAACc,WAAW,CAACoF,IAAI,CAAC,CAAE;cAC7CkF,SAAS,EAAEpL,WAAW,gBAAGX,OAAA,CAAC7C,gBAAgB;gBAAC6O,IAAI,EAAE;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG/K,OAAA,CAAC1B,UAAU;gBAAAsM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAN,QAAA,EAC1E;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGR/K,OAAA,CAACxD,KAAK;QAACwO,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBzK,OAAA,CAACvD,UAAU;UAACiO,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZpK,WAAW,gBACVX,OAAA,CAACzD,GAAG;UAACyO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DzK,OAAA,CAAC7C,gBAAgB;YAAAyN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJ5J,IAAI,CAACuC,MAAM,KAAK,CAAC,gBACnB1D,OAAA,CAAC9C,KAAK;UAACkP,QAAQ,EAAC,MAAM;UAAA3B,QAAA,EAAC;QAEvB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAER/K,OAAA,CAAC/B,IAAI;UAAC+M,EAAE,EAAE;YAAEqB,SAAS,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAA7B,QAAA,EAChDtJ,IAAI,CAACoL,GAAG,CAAExF,IAAI,iBACb/G,OAAA,CAAC7D,KAAK,CAAC8D,QAAQ;YAAAwK,QAAA,gBACbzK,OAAA,CAAC9B,QAAQ;cAACsO,MAAM;cAACX,OAAO,EAAEA,CAAA,KAAMvE,gBAAgB,CAACP,IAAI,CAAE;cAAA0D,QAAA,gBACrDzK,OAAA,CAAC7B,YAAY;gBACXsO,OAAO,eACLzM,OAAA,CAACzD,GAAG;kBAACyO,EAAE,EAAE;oBAAEiB,OAAO,EAAE,MAAM;oBAAEZ,UAAU,EAAE;kBAAS,CAAE;kBAAAZ,QAAA,gBACjDzK,OAAA,CAACvD,UAAU;oBAACiO,OAAO,EAAC,WAAW;oBAAAD,QAAA,EAAE1D,IAAI,CAAChF;kBAAO;oBAAA6I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,EAC1DtL,YAAY,CAACsH,IAAI,CAAC,gBACjB/G,OAAA,CAAC1C,IAAI;oBACH0O,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,OAAO;oBACbG,KAAK,EAAC,OAAO;oBACbZ,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,GACArL,gBAAgB,CAACqH,IAAI,CAAC,gBACxB/G,OAAA,CAAC1C,IAAI;oBACH0O,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,YAAY;oBAClBG,KAAK,EAAC,SAAS;oBACfZ,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,gBAEF/K,OAAA,CAAC1C,IAAI;oBACH0O,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAE1E,IAAI,CAACI,mBAAoB;oBAChCyE,KAAK,EAAEjM,kBAAkB,CAACoH,IAAI,CAACI,mBAAmB,CAAE;oBACpD6D,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACD4B,SAAS,eACP3M,OAAA,CAAAE,SAAA;kBAAAuK,QAAA,gBACEzK,OAAA,CAACvD,UAAU;oBAACiO,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GACzC1D,IAAI,CAAC7B,SAAS,IAAI,KAAK,EAAC,KAAG,EAAC6B,IAAI,CAAC5B,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC4B,IAAI,CAAC3B,OAAO,IAAI,KAAK;kBAAA;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,eACb/K,OAAA;oBAAA4K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN/K,OAAA,CAACvD,UAAU;oBAACiO,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GAAC,MACvC,EAAC1D,IAAI,CAAC8F,mBAAmB,IAAI,KAAK,EAAC,QAAM,EAAC9F,IAAI,CAAC+F,iBAAiB,IAAI,KAAK;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,eACb/K,OAAA;oBAAA4K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN/K,OAAA,CAACvD,UAAU;oBAACiO,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GAAC,iBAC5B,EAAC1D,IAAI,CAAC2B,aAAa,IAAI,KAAK,EAAC,mBAAiB,EAAC3B,IAAI,CAACK,eAAe,IAAI,GAAG;kBAAA;oBAAAwD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC;gBAAA,eACb;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF/K,OAAA,CAAC5B,uBAAuB;gBAAAqM,QAAA,eACtBzK,OAAA,CAAC3C,UAAU;kBAAC0P,IAAI,EAAC,KAAK;kBAAClB,OAAO,EAAG7D,CAAC,IAAK;oBACrCA,CAAC,CAACgF,eAAe,CAAC,CAAC,CAAC,CAAC;oBACrBxL,eAAe,CAACuF,IAAI,CAAC;oBACrBhE,wBAAwB,CAAC,IAAI,CAAC;kBAChC,CAAE;kBAAA0H,QAAA,eACAzK,OAAA,CAAClB,QAAQ;oBAAA8L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACX/K,OAAA,CAAC/C,OAAO;cAAA2N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAxDQhE,IAAI,CAAChF,OAAO;YAAA6I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyDjB,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMkC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAAC1L,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEvB,OAAA,CAACzD,GAAG;MAAAkO,QAAA,gBACFzK,OAAA,CAACvD,UAAU;QAACiO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb/K,OAAA,CAACb,eAAe;QACd4H,IAAI,EAAExF,YAAa;QACnB2L,OAAO,EAAE,IAAK;QACdhE,KAAK,EAAC;MAA+B;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAEF/K,OAAA,CAACxD,KAAK;QAACwO,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBzK,OAAA,CAACvD,UAAU;UAACiO,OAAO,EAAC,WAAW;UAACC,YAAY;UAACK,EAAE,EAAE;YAAEmC,UAAU,EAAE;UAAO,CAAE;UAAA1C,QAAA,EAAC;QAEzE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGb/K,OAAA,CAACpD,IAAI;UAACuO,SAAS;UAACC,OAAO,EAAE,CAAE;UAACJ,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACxCzK,OAAA,CAACpD,IAAI;YAAC0O,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6B,EAAE,EAAE,CAAE;YAAA3C,QAAA,eACvBzK,OAAA,CAACzD,GAAG;cAACyO,EAAE,EAAE;gBAAEC,CAAC,EAAE,CAAC;gBAAEoC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAO,CAAE;cAAA9C,QAAA,gBACrEzK,OAAA,CAACvD,UAAU;gBAACiO,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEmC,UAAU,EAAE,MAAM;kBAAEvB,KAAK,EAAE;gBAAe,CAAE;gBAAAnB,QAAA,EAAC;cAEhG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb/K,OAAA,CAACpD,IAAI;gBAACuO,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAX,QAAA,gBACzBzK,OAAA,CAACpD,IAAI;kBAAC0O,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACfzK,OAAA,CAACvD,UAAU;oBAACiO,OAAO,EAAC,OAAO;oBAACM,EAAE,EAAE;sBAAEmC,UAAU,EAAE;oBAAS,CAAE;oBAAA1C,QAAA,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACP/K,OAAA,CAACpD,IAAI;kBAAC0O,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACfzK,OAAA,CAACvD,UAAU;oBAACiO,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAElJ,YAAY,CAACmH,aAAa,IAAI,KAAK,EAAC,IAAE;kBAAA;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eACP/K,OAAA,CAACpD,IAAI;kBAAC0O,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACfzK,OAAA,CAACvD,UAAU;oBAACiO,OAAO,EAAC,OAAO;oBAACM,EAAE,EAAE;sBAAEmC,UAAU,EAAE;oBAAS,CAAE;oBAAA1C,QAAA,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACP/K,OAAA,CAACpD,IAAI;kBAAC0O,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACfzK,OAAA,CAAC1C,IAAI;oBACHmO,KAAK,EAAElK,YAAY,CAAC4F,mBAAmB,IAAI,KAAM;oBACjD6E,IAAI,EAAC,OAAO;oBACZJ,KAAK,EAAEjM,kBAAkB,CAAC4B,YAAY,CAAC4F,mBAAmB,CAAE;oBAC5DuD,OAAO,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEP/K,OAAA,CAACpD,IAAI;YAAC0O,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6B,EAAE,EAAE,CAAE;YAAA3C,QAAA,eACvBzK,OAAA,CAACzD,GAAG;cAACyO,EAAE,EAAE;gBAAEC,CAAC,EAAE,CAAC;gBAAEoC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAO,CAAE;cAAA9C,QAAA,gBACrEzK,OAAA,CAACvD,UAAU;gBAACiO,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEmC,UAAU,EAAE,MAAM;kBAAEvB,KAAK,EAAE;gBAAiB,CAAE;gBAAAnB,QAAA,EAAC;cAElG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZlJ,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,GAAG,CAAC,MAAM;gBACpE,MAAMkE,MAAM,GAAG9E,MAAM,CAAC6F,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAAC5D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;gBACnE,OAAOkE,MAAM,gBACXnG,OAAA,CAACpD,IAAI;kBAACuO,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAX,QAAA,gBACzBzK,OAAA,CAACpD,IAAI;oBAAC0O,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfzK,OAAA,CAACvD,UAAU;sBAACiO,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEmC,UAAU,EAAE;sBAAS,CAAE;sBAAA1C,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC,eACP/K,OAAA,CAACpD,IAAI;oBAAC0O,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfzK,OAAA,CAACvD,UAAU;sBAACiO,OAAO,EAAC,OAAO;sBAAAD,QAAA,EAAExH,eAAe,CAACkD,MAAM,CAAClE,SAAS;oBAAC;sBAAA2I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC,eACP/K,OAAA,CAACpD,IAAI;oBAAC0O,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfzK,OAAA,CAACvD,UAAU;sBAACiO,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEmC,UAAU,EAAE;sBAAS,CAAE;sBAAA1C,QAAA,EAAC;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC,eACP/K,OAAA,CAACpD,IAAI;oBAAC0O,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfzK,OAAA,CAACvD,UAAU;sBAACiO,OAAO,EAAC,OAAO;sBAAAD,QAAA,GAAEtE,MAAM,CAACL,aAAa,IAAI,CAAC,EAAC,IAAE;oBAAA;sBAAA8E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACP/K,OAAA,CAACpD,IAAI;oBAAC0O,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfzK,OAAA,CAACvD,UAAU;sBAACiO,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEmC,UAAU,EAAE;sBAAS,CAAE;sBAAA1C,QAAA,EAAC;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eACP/K,OAAA,CAACpD,IAAI;oBAAC0O,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfzK,OAAA,CAAC1C,IAAI;sBACHmO,KAAK,EAAEtF,MAAM,CAACC,YAAY,IAAI,KAAM;sBACpC4F,IAAI,EAAC,OAAO;sBACZJ,KAAK,EAAEhM,iBAAiB,CAACuG,MAAM,CAACC,YAAY,CAAE;sBAC9CsE,OAAO,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEP/K,OAAA,CAACvD,UAAU;kBAACiO,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAC;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAC3D;cACH,CAAC,EAAE,CAAC,gBACF/K,OAAA,CAACvD,UAAU;gBAACiO,OAAO,EAAC,OAAO;gBAAAD,QAAA,EACxB5I,QAAQ,CAACI,SAAS,KAAK,cAAc,GACpC,kDAAkD,GAClD;cAA4B;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEP/K,OAAA,CAACzD,GAAG;UAACyO,EAAE,EAAE;YAAEwC,EAAE,EAAE,CAAC;YAAEtC,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACxBzK,OAAA,CAACvD,UAAU;YAACiO,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEmC,UAAU,EAAE;YAAO,CAAE;YAAA1C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/K,OAAA,CAACtD,SAAS;YACRsP,IAAI,EAAC,OAAO;YACZR,SAAS;YACTC,KAAK,EAAC,cAAc;YACpBf,OAAO,EAAC,UAAU;YAClBzC,IAAI,EAAC,cAAc;YACnBwF,IAAI,EAAC,QAAQ;YACbvF,KAAK,EAAErG,QAAQ,CAACG,YAAa;YAC7B0J,QAAQ,EAAE3D,gBAAiB;YAC3BnE,KAAK,EAAE,CAAC,CAAC1B,UAAU,CAACF,YAAa;YACjC0L,UAAU,EAAExL,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;YACjE2L,mBAAmB,EAAE;cACnB3C,EAAE,EAAE;gBAAEY,KAAK,EAAExJ,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACFgJ,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL3I,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,IAAI,CAAC4G,iBAAiB,iBAC1E5I,OAAA,CAAC9C,KAAK;UAACkP,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,EACrCrI,YAAY,CAACJ;QAAY;UAAA4I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACR,eAED/K,OAAA,CAAC9C,KAAK;UAACkP,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,eACnCzK,OAAA,CAACvD,UAAU;YAACiO,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAC;UAE5B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAM6C,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,MAAMC,iBAAiB,GAAIC,YAAY,IAAK;MAC1C,OAAO,IAAI1N,UAAU,KAAK0N,YAAY,EAAE;IAC1C,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAI5H,MAAM,IAAK;MACtC,IAAI,CAACA,MAAM,IAAI,CAACtE,QAAQ,CAACG,YAAY,EAAE,OAAO,IAAI;MAClD,OAAOwG,UAAU,CAACrC,MAAM,CAACL,aAAa,CAAC,IAAI0C,UAAU,CAAC3G,QAAQ,CAACG,YAAY,CAAC;IAC9E,CAAC;;IAED;IACA,MAAMgM,uBAAuB,GAAIhG,CAAC,IAAK;MACrC,MAAM8F,YAAY,GAAG9F,CAAC,CAACG,MAAM,CAACD,KAAK,CAACrB,IAAI,CAAC,CAAC;;MAE1C;MACA,IAAIiH,YAAY,CAAC9G,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QACtClF,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb+L,eAAe,EAAE;QACnB,CAAC,CAAC;QACF;MACF;MAEA,IAAIH,YAAY,EAAE;QAChB;QACA,MAAMI,gBAAgB,GAAGL,iBAAiB,CAACC,YAAY,CAAC;;QAExD;QACA,MAAMK,eAAe,GAAG9M,MAAM,CAAC6F,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAAC5D,SAAS,KAAKiM,gBAAgB,CAAC;QAE1E,IAAIC,eAAe,EAAE;UACnB;UACA,IAAIA,eAAe,CAAC/H,YAAY,KAAK,MAAM,IAAI+H,eAAe,CAAC/H,YAAY,KAAK,WAAW,EAAE;YAC3FjE,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb+L,eAAe,EAAE,aAAaH,YAAY,eAAeK,eAAe,CAAC/H,YAAY;YACvF,CAAC,CAAC;YACF;UACF;;UAEA;UACA,IAAI7E,YAAY,EAAE;YAChB;YACA,MAAM8E,aAAa,GAAGd,MAAM,CAAChE,YAAY,CAAC2D,SAAS,IAAI,EAAE,CAAC;YAC1D,MAAMqB,cAAc,GAAGhB,MAAM,CAAChE,YAAY,CAAC4D,YAAY,IAAI,GAAG,CAAC;YAC/D,MAAMqB,WAAW,GAAGjB,MAAM,CAAChE,YAAY,CAAC6D,OAAO,IAAI,GAAG,CAAC;YAEvD,MAAMqB,eAAe,GAAGlB,MAAM,CAAC4I,eAAe,CAACjJ,SAAS,IAAI,EAAE,CAAC;YAC/D,MAAMwB,gBAAgB,GAAGnB,MAAM,CAAC4I,eAAe,CAAChJ,YAAY,IAAI,GAAG,CAAC;YACpE,MAAMwB,aAAa,GAAGpB,MAAM,CAAC4I,eAAe,CAAC/I,OAAO,IAAI,GAAG,CAAC;;YAE5D;YACA9B,OAAO,CAACC,GAAG,CAAC,iCAAiC4K,eAAe,CAAClM,SAAS,GAAG,EAAE;cACzEiD,SAAS,EAAE,GAAGuB,eAAe,QAAQJ,aAAa,EAAE;cACpDlB,YAAY,EAAE,GAAGuB,gBAAgB,QAAQH,cAAc,EAAE;cACzDnB,OAAO,EAAE,GAAGuB,aAAa,QAAQH,WAAW;YAC9C,CAAC,CAAC;YAEF,IAAIC,eAAe,KAAKJ,aAAa,IACjCK,gBAAgB,KAAKH,cAAc,IACnCI,aAAa,KAAKH,WAAW,EAAE;cACjC;cACA/D,mBAAmB,CAAC0L,eAAe,CAAC;cACpC5L,6BAA6B,CAAC,IAAI,CAAC;cACnC;YACF;UACF;;UAEA;UACA,IAAIwL,mBAAmB,CAACI,eAAe,CAAC,EAAE;YACxC;YACArM,WAAW,CAAC;cACV,GAAGD,QAAQ;cACXI,SAAS,EAAEiM;YACb,CAAC,CAAC;YACF/L,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb+L,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACA9L,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb+L,eAAe,EAAE,aAAaH,YAAY,sCAAsCK,eAAe,CAACrI,aAAa;YAC/G,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL;UACA3D,aAAa,CAAC;YACZ,GAAGD,UAAU;YACb+L,eAAe,EAAE,UAAUH,YAAY;UACzC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACAhM,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb+L,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,oBACEjO,OAAA,CAACzD,GAAG;MAAAkO,QAAA,gBACFzK,OAAA,CAACvD,UAAU;QAACiO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb/K,OAAA,CAACxD,KAAK;QAACwO,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBzK,OAAA,CAACvD,UAAU;UAACiO,OAAO,EAAC,OAAO;UAAC0D,SAAS;UAAA3D,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZlK,aAAa,gBACZb,OAAA,CAACzD,GAAG;UAACyO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DzK,OAAA,CAAC7C,gBAAgB;YAAAyN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAEN/K,OAAA,CAACzD,GAAG;UAAAkO,QAAA,gBACFzK,OAAA,CAACpD,IAAI;YAACuO,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBAEzBzK,OAAA,CAACpD,IAAI;cAAC0O,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAA3C,QAAA,gBACvBzK,OAAA,CAACvD,UAAU;gBAACiO,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEmC,UAAU,EAAE;gBAAO,CAAE;gBAAA1C,QAAA,EAAC;cAEzE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb/K,OAAA,CAACtD,SAAS;gBACRsP,IAAI,EAAC,OAAO;gBACZR,SAAS;gBACTC,KAAK,EAAC,eAAe;gBACrBf,OAAO,EAAC,UAAU;gBAClBiB,WAAW,EAAC,oBAAoB;gBAChC+B,UAAU,EAAExL,UAAU,CAAC+L,eAAe,IAAI,uCAAwC;gBAClFrK,KAAK,EAAE,CAAC,CAAC1B,UAAU,CAAC+L,eAAgB;gBACpCI,MAAM,EAAEL,uBAAwB;gBAChChD,EAAE,EAAE;kBAAEE,EAAE,EAAE;gBAAE;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACF/K,OAAA,CAACvD,UAAU;gBAACiO,OAAO,EAAC,OAAO;gBAACM,EAAE,EAAE;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAT,QAAA,GAAC,aAC9B,eAAAzK,OAAA;kBAAAyK,QAAA,EAAS5I,QAAQ,CAACI,SAAS,IAAI;gBAAG;kBAAA2I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAGP/K,OAAA,CAACpD,IAAI;cAAC0O,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAA3C,QAAA,gBACvBzK,OAAA,CAACvD,UAAU;gBAACiO,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEmC,UAAU,EAAE;gBAAO,CAAE;gBAAA1C,QAAA,EAAC;cAEzE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb/K,OAAA,CAACzD,GAAG;gBAAAkO,QAAA,gBACFzK,OAAA,CAACvD,UAAU;kBAACiO,OAAO,EAAC,WAAW;kBAACC,YAAY;kBAACK,EAAE,EAAE;oBAAEY,KAAK,EAAE,cAAc;oBAAEuB,UAAU,EAAE,MAAM;oBAAEjC,EAAE,EAAE;kBAAE,CAAE;kBAAAT,QAAA,EACnGlJ,YAAY,GAAG,4CAA4C,GAAG;gBAAsB;kBAAAqJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC,eAEb/K,OAAA,CAACnD,WAAW;kBAAC2O,SAAS;kBAACQ,IAAI,EAAC,OAAO;kBAACpI,KAAK,EAAE,CAAC,CAAC1B,UAAU,CAACD,SAAU;kBAAAwI,QAAA,gBAChEzK,OAAA,CAAClD,UAAU;oBAACwR,EAAE,EAAC,qBAAqB;oBAAA7D,QAAA,EAAC;kBAAgB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClE/K,OAAA,CAACjD,MAAM;oBACLwR,OAAO,EAAC,qBAAqB;oBAC7BD,EAAE,EAAC,eAAe;oBAClBrG,IAAI,EAAC,WAAW;oBAChBC,KAAK,EAAErG,QAAQ,CAACI,SAAU;oBAC1BwJ,KAAK,EAAC,kBAAkB;oBACxBC,QAAQ,EAAE3D,gBAAiB;oBAAA0C,QAAA,gBAE3BzK,OAAA,CAAChD,QAAQ;sBAACkL,KAAK,EAAC,cAAc;sBAAAuC,QAAA,gBAC5BzK,OAAA;wBAAAyK,QAAA,EAAQ;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,+BAC/B;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eACX/K,OAAA,CAAC/C,OAAO;sBAAA2N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACV1J,MAAM,CAACqC,MAAM,GAAG,CAAC,gBAChB1D,OAAA,CAACzD,GAAG;sBAACqQ,SAAS,EAAC,IAAI;sBAAC5B,EAAE,EAAE;wBAAEC,CAAC,EAAE,CAAC;wBAAEoC,OAAO,EAAE;sBAAmB,CAAE;sBAAA5C,QAAA,eAC5DzK,OAAA,CAACvD,UAAU;wBAACiO,OAAO,EAAC,SAAS;wBAACM,EAAE,EAAE;0BAAEmC,UAAU,EAAE,MAAM;0BAAEvB,KAAK,EAAE;wBAAe,CAAE;wBAAAnB,QAAA,GAC7EpJ,MAAM,CAACqC,MAAM,EAAC,6BACjB;sBAAA;wBAAAkH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,GACJ,IAAI,EACP1J,MAAM,CAACkL,GAAG,CAAEpG,MAAM,iBACjBnG,OAAA,CAAChD,QAAQ;sBAEPkL,KAAK,EAAE/B,MAAM,CAAClE,SAAU;sBACxB6J,QAAQ,EAAE3F,MAAM,CAACL,aAAa,GAAG0C,UAAU,CAAC3G,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;sBACxEgJ,EAAE,EAAE;wBACF,gBAAgB,EAAE;0BAAEqC,OAAO,EAAE;wBAAgB,CAAC;wBAC9C,sBAAsB,EAAE;0BAAEA,OAAO,EAAE;wBAAgB,CAAC;wBACpDA,OAAO,EAAE9L,YAAY,IACd4E,MAAM,CAACjB,SAAS,KAAK3D,YAAY,CAAC2D,SAAS,IAC3CK,MAAM,CAACY,MAAM,CAAChB,YAAY,CAAC,KAAKI,MAAM,CAAChE,YAAY,CAAC4D,YAAY,CAAC,IACjEI,MAAM,CAACY,MAAM,CAACf,OAAO,CAAC,KAAKG,MAAM,CAAChE,YAAY,CAAC6D,OAAO,CAAC,GACvD,yBAAyB,GAAG;sBACrC,CAAE;sBAAAqF,QAAA,eAEFzK,OAAA,CAACzD,GAAG;wBAACyO,EAAE,EAAE;0BAAEiB,OAAO,EAAE,MAAM;0BAAEuC,aAAa,EAAE,QAAQ;0BAAEC,KAAK,EAAE;wBAAO,CAAE;wBAAAhE,QAAA,gBACnEzK,OAAA,CAACzD,GAAG;0BAACyO,EAAE,EAAE;4BAAEiB,OAAO,EAAE,MAAM;4BAAEC,cAAc,EAAE,eAAe;4BAAEb,UAAU,EAAE,QAAQ;4BAAEoD,KAAK,EAAE;0BAAO,CAAE;0BAAAhE,QAAA,gBACjGzK,OAAA,CAACvD,UAAU;4BAACiO,OAAO,EAAC,OAAO;4BAACM,EAAE,EAAE;8BAAEmC,UAAU,EAAE;4BAAO,CAAE;4BAAA1C,QAAA,GACpDxH,eAAe,CAACkD,MAAM,CAAClE,SAAS,CAAC,EAAC,KAAG,EAACkE,MAAM,CAACjB,SAAS,IAAI,KAAK;0BAAA;4BAAA0F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtD,CAAC,EACZxJ,YAAY,IACZ4E,MAAM,CAACjB,SAAS,KAAK3D,YAAY,CAAC2D,SAAS,IAC3CK,MAAM,CAACY,MAAM,CAAChB,YAAY,CAAC,KAAKI,MAAM,CAAChE,YAAY,CAAC4D,YAAY,CAAC,IACjEI,MAAM,CAACY,MAAM,CAACf,OAAO,CAAC,KAAKG,MAAM,CAAChE,YAAY,CAAC6D,OAAO,CAAC,iBACtDpF,OAAA,CAAC1C,IAAI;4BACH0O,IAAI,EAAC,OAAO;4BACZP,KAAK,EAAC,aAAa;4BACnBG,KAAK,EAAC,SAAS;4BACflB,OAAO,EAAC,UAAU;4BAClBM,EAAE,EAAE;8BAAEuC,MAAM,EAAE,EAAE;8BAAEmB,QAAQ,EAAE;4BAAS;0BAAE;4BAAA9D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxC,CACF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACN/K,OAAA,CAACzD,GAAG;0BAACyO,EAAE,EAAE;4BAAEiB,OAAO,EAAE,MAAM;4BAAEC,cAAc,EAAE,eAAe;4BAAEuC,KAAK,EAAE;0BAAO,CAAE;0BAAAhE,QAAA,gBAC3EzK,OAAA,CAACvD,UAAU;4BAACiO,OAAO,EAAC,SAAS;4BAAAD,QAAA,GAC1BtE,MAAM,CAAChB,YAAY,IAAI,KAAK,EAAC,KAAG,EAACgB,MAAM,CAACf,OAAO,IAAI,KAAK;0BAAA;4BAAAwF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/C,CAAC,eACb/K,OAAA,CAACvD,UAAU;4BAACiO,OAAO,EAAC,SAAS;4BAACM,EAAE,EAAE;8BAAEmC,UAAU,EAAE,MAAM;8BAAEvB,KAAK,EAAEzF,MAAM,CAACL,aAAa,GAAG0C,UAAU,CAAC3G,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;4BAAe,CAAE;4BAAAyI,QAAA,GAC5JtE,MAAM,CAACL,aAAa,IAAI,CAAC,EAAC,gBAC7B;0BAAA;4BAAA8E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GAvCD5E,MAAM,CAAClE,SAAS;sBAAA2I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAwCb,CACX,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACT/K,OAAA,CAAC5C,cAAc;oBAAAqN,QAAA,EACZvI,UAAU,CAACD,SAAS,IAAI;kBAAsD;oBAAA2I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAEb1J,MAAM,CAACqC,MAAM,KAAK,CAAC,IAAI,CAAC7C,aAAa,iBACpCb,OAAA,CAAC9C,KAAK;kBAACkP,QAAQ,EAAC,SAAS;kBAACpB,EAAE,EAAE;oBAAEwC,EAAE,EAAE,CAAC;oBAAEkB,QAAQ,EAAE;kBAAS,CAAE;kBAAAjE,QAAA,EAAC;gBAE7D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP/K,OAAA,CAAC9C,KAAK;YAACkP,QAAQ,EAAC,MAAM;YAACpB,EAAE,EAAE;cAAEwC,EAAE,EAAE;YAAE,CAAE;YAAA/C,QAAA,eACnCzK,OAAA,CAACvD,UAAU;cAACiO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBzK,OAAA;gBAAAyK,QAAA,EAAQ;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,iGACvB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,EAGA,CAAClK,aAAa,IAAIgB,QAAQ,CAACI,SAAS,iBACnCjC,OAAA,CAACzD,GAAG;UAACyO,EAAE,EAAE;YAAEwC,EAAE,EAAE,CAAC;YAAEvC,CAAC,EAAE,CAAC;YAAEoC,OAAO,EAAE,kBAAkB;YAAEC,YAAY,EAAE,CAAC;YAAEqB,MAAM,EAAE;UAAoB,CAAE;UAAAlE,QAAA,gBAClGzK,OAAA,CAACvD,UAAU;YAACiO,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAF,QAAA,EAAC;UAE7C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ,CAAC,MAAM;YACN,MAAM5E,MAAM,GAAG9E,MAAM,CAAC6F,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAAC5D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;YACnE,IAAIkE,MAAM,EAAE;cACV,oBACEnG,OAAA,CAACpD,IAAI;gBAACuO,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAX,QAAA,gBACzBzK,OAAA,CAACpD,IAAI;kBAAC0O,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAAC6B,EAAE,EAAE,CAAE;kBAAA3C,QAAA,gBACvBzK,OAAA,CAACvD,UAAU;oBAACiO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBzK,OAAA;sBAAAyK,QAAA,EAAQ;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC9H,eAAe,CAACkD,MAAM,CAAClE,SAAS,CAAC;kBAAA;oBAAA2I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACb/K,OAAA,CAACvD,UAAU;oBAACiO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBzK,OAAA;sBAAAyK,QAAA,EAAQ;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5E,MAAM,CAACjB,SAAS,IAAI,KAAK;kBAAA;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACb/K,OAAA,CAACvD,UAAU;oBAACiO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBzK,OAAA;sBAAAyK,QAAA,EAAQ;oBAAW;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5E,MAAM,CAAChB,YAAY,IAAI,KAAK,EAAC,KAAG,EAACgB,MAAM,CAACf,OAAO,IAAI,KAAK;kBAAA;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACP/K,OAAA,CAACpD,IAAI;kBAAC0O,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAAC6B,EAAE,EAAE,CAAE;kBAAA3C,QAAA,gBACvBzK,OAAA,CAACvD,UAAU;oBAACiO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBzK,OAAA;sBAAAyK,QAAA,EAAQ;oBAAa;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5E,MAAM,CAACyI,YAAY,IAAI,CAAC,EAAC,IAC3D;kBAAA;oBAAAhE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb/K,OAAA,CAACvD,UAAU;oBAACiO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBzK,OAAA;sBAAAyK,QAAA,EAAQ;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5E,MAAM,CAACL,aAAa,IAAI,CAAC,EAAC,IAC7D;kBAAA;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb/K,OAAA,CAACvD,UAAU;oBAACiO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBzK,OAAA;sBAAAyK,QAAA,EAAQ;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5E,MAAM,CAACC,YAAY,IAAI,KAAK;kBAAA;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAEX;YACA,oBACE/K,OAAA,CAACvD,UAAU;cAACiO,OAAO,EAAC,OAAO;cAACkB,KAAK,EAAC,OAAO;cAAAnB,QAAA,EAAC;YAE1C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAEjB,CAAC,EAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,EAEA1J,MAAM,CAACqC,MAAM,KAAK,CAAC,IAAI,CAAC7C,aAAa,iBACpCb,OAAA,CAAC9C,KAAK;UAACkP,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,EAAC;QAEzC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAM8D,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,IAAIf,YAAY,GAAG,SAAS;IAC5B,IAAIgB,UAAU,GAAG,IAAI;IAErB,IAAIjN,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;MACzC6L,YAAY,GAAG,cAAc;IAC/B,CAAC,MAAM,IAAIjM,QAAQ,CAACI,SAAS,EAAE;MAC7B6L,YAAY,GAAG7K,eAAe,CAACpB,QAAQ,CAACI,SAAS,CAAC;MAClD;MACA6M,UAAU,GAAGzN,MAAM,CAAC6F,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAAC5D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;IACnE;;IAEA;IACA,MAAM8H,kBAAkB,GAAGH,2BAA2B,CAACpB,UAAU,CAAC3G,QAAQ,CAACG,YAAY,CAAC,EAAET,YAAY,CAACmH,aAAa,CAAC;IAErH,oBACE1I,OAAA,CAACzD,GAAG;MAAAkO,QAAA,gBACFzK,OAAA,CAACvD,UAAU;QAACiO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb/K,OAAA,CAACxD,KAAK;QAACwO,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBzK,OAAA,CAACvD,UAAU;UAACiO,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGb/K,OAAA,CAACb,eAAe;UACd4H,IAAI,EAAExF,YAAa;UACnB2L,OAAO,EAAE,IAAK;UACdhE,KAAK,EAAC;QAAmB;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAGF/K,OAAA,CAACzD,GAAG;UAACyO,EAAE,EAAE;YAAEwC,EAAE,EAAE,CAAC;YAAEvC,CAAC,EAAE,CAAC;YAAEoC,OAAO,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAA7C,QAAA,gBAC5DzK,OAAA,CAACvD,UAAU;YAACiO,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEmC,UAAU,EAAE;YAAO,CAAE;YAAA1C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/K,OAAA,CAACpD,IAAI;YAACuO,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBACzBzK,OAAA,CAACpD,IAAI;cAAC0O,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAA3C,QAAA,gBACvBzK,OAAA,CAACvD,UAAU;gBAACiO,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBzK,OAAA;kBAAAyK,QAAA,EAAQ;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClJ,QAAQ,CAACG,YAAY,EAAC,IACxD;cAAA;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb/K,OAAA,CAACvD,UAAU;gBAACiO,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBzK,OAAA;kBAAAyK,QAAA,EAAQ;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAChB,kBAAkB;cAAA;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACP/K,OAAA,CAACpD,IAAI;cAAC0O,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAA3C,QAAA,gBACvBzK,OAAA,CAACvD,UAAU;gBAACiO,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBzK,OAAA;kBAAAyK,QAAA,EAAQ;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC+C,YAAY;cAAA;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,EACZ+D,UAAU,iBACT9O,OAAA,CAACvD,UAAU;gBAACiO,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBzK,OAAA;kBAAAyK,QAAA,EAAQ;gBAAqB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC+D,UAAU,CAAChJ,aAAa,EAAC,IACnE;cAAA;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAEL+D,UAAU,IAAItG,UAAU,CAAC3G,QAAQ,CAACG,YAAY,CAAC,GAAGwG,UAAU,CAACsG,UAAU,CAAChJ,aAAa,CAAC,IAAI,CAAC8C,iBAAiB,iBAC3G5I,OAAA,CAAC9C,KAAK;UAACkP,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,gBACtCzK,OAAA;YAAAyK,QAAA,EAAQ;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,qBAAiB,EAAClJ,QAAQ,CAACG,YAAY,EAAC,4CAA0C,EAAC8M,UAAU,CAAChJ,aAAa,EAAC,gDAE1I;QAAA;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,eAED/K,OAAA,CAAC9C,KAAK;UAACkP,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,GAAC,8EAEpC,EAAC5I,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,gFAAgF;QAAA;UAAA2I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3I,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMgE,cAAc,GAAIC,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAOxE,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOoD,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOX,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAO4B,WAAW,CAAC,CAAC;MAAE;MACxB;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;;EAED;EACA,MAAMI,4BAA4B,GAAGA,CAAA,KAAM;IACzCtM,wBAAwB,CAAC,KAAK,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAID;EACA,MAAMqM,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAItM,eAAe,EAAE;MACnBpC,QAAQ,CAAC,mCAAmCJ,UAAU,IAAIwC,eAAe,CAACb,OAAO,EAAE,CAAC;IACtF;IACAkN,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,wBAAwB,GAAGA,CAAA,KAAM;IACrCF,4BAA4B,CAAC,CAAC;IAC9B;IACAzN,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBR,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMkO,iCAAiC,GAAGA,CAAA,KAAM;IAC9C7M,6BAA6B,CAAC,KAAK,CAAC;IACpCE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM4M,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAI,CAAC9N,YAAY,IAAI,CAACiB,gBAAgB,EAAE;IAExC,IAAI;MACF9B,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAM1B,WAAW,CAACsQ,0BAA0B,CAAClP,UAAU,EAAEmB,YAAY,CAACQ,OAAO,EAAES,gBAAgB,CAACP,SAAS,CAAC;;MAE1G;MACA,MAAM2F,WAAW,GAAG,MAAM5I,WAAW,CAACuQ,WAAW,CAACnP,UAAU,EAAEmB,YAAY,CAACQ,OAAO,CAAC;MACnFP,eAAe,CAACoG,WAAW,CAAC;;MAE5B;MACA9F,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXI,SAAS,EAAEO,gBAAgB,CAACP;MAC9B,CAAC,CAAC;MAEF5B,SAAS,CAAC,4BAA4BkB,YAAY,CAACQ,OAAO,6CAA6CS,gBAAgB,CAACP,SAAS,EAAE,CAAC;MACpImN,iCAAiC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOxL,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iEAAiE,EAAEA,KAAK,CAAC;MACvFtD,OAAO,CAAC,kEAAkE,IAAIsD,KAAK,CAACqB,MAAM,IAAIrB,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACvI,CAAC,SAAS;MACRtD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8O,uBAAuB,GAAGA,CAAA,KAAM;IACpCJ,iCAAiC,CAAC,CAAC;IACnC;IACAtN,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXI,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,oBACEjC,OAAA,CAACzD,GAAG;IAAAkO,QAAA,gBAEFzK,OAAA,CAACxD,KAAK;MAACwO,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,gBACzBzK,OAAA,CAACvD,UAAU;QAACiO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/K,OAAA,CAACpD,IAAI;QAACuO,SAAS;QAACC,OAAO,EAAE,CAAE;QAACC,UAAU,EAAC,QAAQ;QAAAZ,QAAA,gBAC7CzK,OAAA,CAACpD,IAAI;UAAC0O,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAd,QAAA,eACfzK,OAAA,CAACtD,SAAS;YACR8O,SAAS;YACTC,KAAK,EAAC,SAAS;YACff,OAAO,EAAC,UAAU;YAClBxC,KAAK,EAAEzG,WAAY;YACnBiK,QAAQ,EAAG1D,CAAC,IAAKtG,cAAc,CAACsG,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;YAChDyD,WAAW,EAAC;UAAyC;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP/K,OAAA,CAACpD,IAAI;UAAC0O,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAd,QAAA,eACfzK,OAAA,CAACrD,MAAM;YACL6O,SAAS;YACTd,OAAO,EAAC,WAAW;YACnBkB,KAAK,EAAC,SAAS;YACfC,OAAO,EAAEjF,oBAAqB;YAC9BkF,QAAQ,EAAEnL,WAAW,IAAI,CAACc,WAAW,CAACoF,IAAI,CAAC,CAAE;YAC7CkF,SAAS,EAAEpL,WAAW,gBAAGX,OAAA,CAAC7C,gBAAgB;cAAC6O,IAAI,EAAE;YAAG;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG/K,OAAA,CAAC1B,UAAU;cAAAsM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAN,QAAA,EAC1E;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGP9J,iBAAiB,IAAIF,aAAa,CAAC2C,MAAM,GAAG,CAAC,iBAC5C1D,OAAA,CAACxD,KAAK;MAACwO,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,gBACzBzK,OAAA,CAACvD,UAAU;QAACiO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/K,OAAA,CAAClC,cAAc;QAAA2M,QAAA,eACbzK,OAAA,CAACrC,KAAK;UAACqO,IAAI,EAAC,OAAO;UAAAvB,QAAA,gBACjBzK,OAAA,CAACjC,SAAS;YAAA0M,QAAA,eACRzK,OAAA,CAAChC,QAAQ;cAACgN,EAAE,EAAE;gBAAEqC,OAAO,EAAE;cAAU,CAAE;cAAA5C,QAAA,gBACnCzK,OAAA,CAACnC,SAAS;gBAAA4M,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B/K,OAAA,CAACnC,SAAS;gBAAA4M,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC/K,OAAA,CAACnC,SAAS;gBAAA4M,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC/K,OAAA,CAACnC,SAAS;gBAAA4M,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC/K,OAAA,CAACnC,SAAS;gBAAA4M,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpC/K,OAAA,CAACnC,SAAS;gBAAA4M,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5B/K,OAAA,CAACnC,SAAS;gBAAA4M,QAAA,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ/K,OAAA,CAACpC,SAAS;YAAA6M,QAAA,EACP1J,aAAa,CAACwL,GAAG,CAAExF,IAAI,iBACtB/G,OAAA,CAAChC,QAAQ;cAAAyM,QAAA,gBACPzK,OAAA,CAACnC,SAAS;gBAAA4M,QAAA,EAAE1D,IAAI,CAAChF;cAAO;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrC/K,OAAA,CAACnC,SAAS;gBAAA4M,QAAA,EAAE1D,IAAI,CAAC7B,SAAS,IAAI;cAAK;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChD/K,OAAA,CAACnC,SAAS;gBAAA4M,QAAA,GAAE1D,IAAI,CAAC5B,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC4B,IAAI,CAAC3B,OAAO,IAAI,KAAK;cAAA;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7E/K,OAAA,CAACnC,SAAS;gBAAA4M,QAAA,GAAC,MAAI,EAAC1D,IAAI,CAAC8F,mBAAmB,IAAI,KAAK,eAAC7M,OAAA;kBAAA4K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,OAAG,EAAChE,IAAI,CAAC+F,iBAAiB,IAAI,KAAK;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvG/K,OAAA,CAACnC,SAAS;gBAAA4M,QAAA,GAAE1D,IAAI,CAAC2B,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACtD/K,OAAA,CAACnC,SAAS;gBAAA4M,QAAA,eACRzK,OAAA,CAAC1C,IAAI;kBACHmO,KAAK,EAAE1E,IAAI,CAACI,mBAAmB,IAAI,KAAM;kBACzC6E,IAAI,EAAC,OAAO;kBACZJ,KAAK,EAAEjM,kBAAkB,CAACoH,IAAI,CAACI,mBAAmB,CAAE;kBACpDuD,OAAO,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ/K,OAAA,CAACnC,SAAS;gBAAA4M,QAAA,eACRzK,OAAA,CAACrD,MAAM;kBACLqP,IAAI,EAAC,OAAO;kBACZtB,OAAO,EAAC,WAAW;kBACnBkB,KAAK,EAAC,SAAS;kBACfC,OAAO,EAAEA,CAAA,KAAMvE,gBAAgB,CAACP,IAAI,CAAE;kBACtC+E,QAAQ,EAAEpM,gBAAgB,CAACqH,IAAI,CAAE;kBAAA0D,QAAA,EAClC;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GAxBChE,IAAI,CAAChF,OAAO;cAAA6I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CACR,EAGAxJ,YAAY,iBACXvB,OAAA,CAACxD,KAAK;MAACwO,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,gBAClBzK,OAAA,CAACvD,UAAU;QAACiO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb/K,OAAA,CAACzD,GAAG;QAACyO,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEoC,OAAO,EAAE,SAAS;UAAEC,YAAY,EAAE,CAAC;UAAEpC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBAC5DzK,OAAA,CAACvD,UAAU;UAACiO,OAAO,EAAC,WAAW;UAACC,YAAY;UAACK,EAAE,EAAE;YAAEmC,UAAU,EAAE,MAAM;YAAEvB,KAAK,EAAE;UAAe,CAAE;UAAAnB,QAAA,GAAC,oBAC5E,EAAClJ,YAAY,CAACQ,OAAO;QAAA;UAAA6I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACb/K,OAAA,CAACpD,IAAI;UAACuO,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAX,QAAA,gBACzBzK,OAAA,CAACpD,IAAI;YAAC0O,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6B,EAAE,EAAE,CAAE;YAAA3C,QAAA,gBACvBzK,OAAA,CAACvD,UAAU;cAACiO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACzK,OAAA;gBAAAyK,QAAA,EAAQ;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxJ,YAAY,CAAC2D,SAAS,IAAI,KAAK;YAAA;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACtG/K,OAAA,CAACvD,UAAU;cAACiO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACzK,OAAA;gBAAAyK,QAAA,EAAQ;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxJ,YAAY,CAAC4D,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC5D,YAAY,CAAC6D,OAAO,IAAI,KAAK;YAAA;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC5I/K,OAAA,CAACvD,UAAU;cAACiO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACzK,OAAA;gBAAAyK,QAAA,EAAQ;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxJ,YAAY,CAACmH,aAAa,IAAI,KAAK,EAAC,IAAE;YAAA;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5G,CAAC,eACP/K,OAAA,CAACpD,IAAI;YAAC0O,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6B,EAAE,EAAE,CAAE;YAAA3C,QAAA,gBACvBzK,OAAA,CAACvD,UAAU;cAACiO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACzK,OAAA;gBAAAyK,QAAA,EAAQ;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxJ,YAAY,CAACsL,mBAAmB,IAAI,KAAK;YAAA;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC1H/K,OAAA,CAACvD,UAAU;cAACiO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACzK,OAAA;gBAAAyK,QAAA,EAAQ;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxJ,YAAY,CAACuL,iBAAiB,IAAI,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACtH/K,OAAA,CAACvD,UAAU;cAACiO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBzK,OAAA;gBAAAyK,QAAA,EAAQ;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvB/K,OAAA,CAAC1C,IAAI;gBACHmO,KAAK,EAAElK,YAAY,CAAC4F,mBAAmB,IAAI,KAAM;gBACjD6E,IAAI,EAAC,OAAO;gBACZJ,KAAK,EAAEjM,kBAAkB,CAAC4B,YAAY,CAAC4F,mBAAmB,CAAE;gBAC5DuD,OAAO,EAAC,UAAU;gBAClBM,EAAE,EAAE;kBAAE0B,EAAE,EAAE;gBAAE;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN/K,OAAA,CAACpD,IAAI;QAACuO,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAX,QAAA,gBAEzBzK,OAAA,CAACpD,IAAI;UAAC0O,IAAI;UAACC,EAAE,EAAE,EAAG;UAAC6B,EAAE,EAAE,CAAE;UAAA3C,QAAA,gBACvBzK,OAAA,CAACvD,UAAU;YAACiO,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEmC,UAAU,EAAE;YAAO,CAAE;YAAA1C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/K,OAAA,CAACtD,SAAS;YACRsP,IAAI,EAAC,OAAO;YACZR,SAAS;YACTC,KAAK,EAAC,cAAc;YACpBf,OAAO,EAAC,UAAU;YAClBzC,IAAI,EAAC,cAAc;YACnBwF,IAAI,EAAC,QAAQ;YACbvF,KAAK,EAAErG,QAAQ,CAACG,YAAa;YAC7B0J,QAAQ,EAAE3D,gBAAiB;YAC3BnE,KAAK,EAAE,CAAC,CAAC1B,UAAU,CAACF,YAAa;YACjC0L,UAAU,EAAExL,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;YACjE2L,mBAAmB,EAAE;cACnB3C,EAAE,EAAE;gBAAEY,KAAK,EAAExJ,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACFgJ,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EACD3I,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,iBACpDhC,OAAA,CAAC9C,KAAK;YAACkP,QAAQ,EAAC,SAAS;YAACpB,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,EACrCrI,YAAY,CAACJ;UAAY;YAAA4I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAGP/K,OAAA,CAACpD,IAAI;UAAC0O,IAAI;UAACC,EAAE,EAAE,EAAG;UAAC6B,EAAE,EAAE,CAAE;UAAA3C,QAAA,gBACvBzK,OAAA,CAACvD,UAAU;YAACiO,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEmC,UAAU,EAAE;YAAO,CAAE;YAAA1C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZlK,aAAa,gBACZb,OAAA,CAACzD,GAAG;YAACyO,EAAE,EAAE;cAAEiB,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA1B,QAAA,eAC5DzK,OAAA,CAAC7C,gBAAgB;cAAAyN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,gBAEN/K,OAAA,CAACzD,GAAG;YAAAkO,QAAA,gBACFzK,OAAA,CAACvD,UAAU;cAACiO,OAAO,EAAC,WAAW;cAACC,YAAY;cAACK,EAAE,EAAE;gBAAEY,KAAK,EAAE,cAAc;gBAAEuB,UAAU,EAAE,MAAM;gBAAEjC,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,EACnGlJ,YAAY,GAAG,4CAA4C,GAAG;YAAsB;cAAAqJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,eAEb/K,OAAA,CAACnD,WAAW;cAAC2O,SAAS;cAACQ,IAAI,EAAC,OAAO;cAACpI,KAAK,EAAE,CAAC,CAAC1B,UAAU,CAACD,SAAU;cAAAwI,QAAA,gBAChEzK,OAAA,CAAClD,UAAU;gBAACwR,EAAE,EAAC,qBAAqB;gBAAA7D,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClE/K,OAAA,CAACjD,MAAM;gBACLwR,OAAO,EAAC,qBAAqB;gBAC7BD,EAAE,EAAC,eAAe;gBAClBrG,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAErG,QAAQ,CAACI,SAAU;gBAC1BwJ,KAAK,EAAC,kBAAkB;gBACxBC,QAAQ,EAAE3D,gBAAiB;gBAAA0C,QAAA,gBAE3BzK,OAAA,CAAChD,QAAQ;kBAACkL,KAAK,EAAC,cAAc;kBAAAuC,QAAA,gBAC5BzK,OAAA;oBAAAyK,QAAA,EAAQ;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,+BAC/B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACX/K,OAAA,CAAC/C,OAAO;kBAAA2N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACV1J,MAAM,CAACqC,MAAM,GAAG,CAAC,gBAChB1D,OAAA,CAACzD,GAAG;kBAACqQ,SAAS,EAAC,IAAI;kBAAC5B,EAAE,EAAE;oBAAEC,CAAC,EAAE,CAAC;oBAAEoC,OAAO,EAAE;kBAAmB,CAAE;kBAAA5C,QAAA,eAC5DzK,OAAA,CAACvD,UAAU;oBAACiO,OAAO,EAAC,SAAS;oBAACM,EAAE,EAAE;sBAAEmC,UAAU,EAAE,MAAM;sBAAEvB,KAAK,EAAE;oBAAe,CAAE;oBAAAnB,QAAA,GAC7EpJ,MAAM,CAACqC,MAAM,EAAC,6BACjB;kBAAA;oBAAAkH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,GACJ,IAAI,EACP1J,MAAM,CAACkL,GAAG,CAAEpG,MAAM,iBACjBnG,OAAA,CAAChD,QAAQ;kBAEPkL,KAAK,EAAE/B,MAAM,CAAClE,SAAU;kBACxB6J,QAAQ,EAAE3F,MAAM,CAACL,aAAa,GAAG0C,UAAU,CAAC3G,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;kBACxEgJ,EAAE,EAAE;oBACF,gBAAgB,EAAE;sBAAEqC,OAAO,EAAE;oBAAgB,CAAC;oBAC9C,sBAAsB,EAAE;sBAAEA,OAAO,EAAE;oBAAgB,CAAC;oBACpDA,OAAO,EAAE9L,YAAY,IACd4E,MAAM,CAACjB,SAAS,KAAK3D,YAAY,CAAC2D,SAAS,IAC3CK,MAAM,CAACY,MAAM,CAAChB,YAAY,CAAC,KAAKI,MAAM,CAAChE,YAAY,CAAC4D,YAAY,CAAC,IACjEI,MAAM,CAACY,MAAM,CAACf,OAAO,CAAC,KAAKG,MAAM,CAAChE,YAAY,CAAC6D,OAAO,CAAC,GACvD,yBAAyB,GAAG;kBACrC,CAAE;kBAAAqF,QAAA,eAEFzK,OAAA,CAACzD,GAAG;oBAACyO,EAAE,EAAE;sBAAEiB,OAAO,EAAE,MAAM;sBAAEuC,aAAa,EAAE,QAAQ;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBAAAhE,QAAA,gBACnEzK,OAAA,CAACzD,GAAG;sBAACyO,EAAE,EAAE;wBAAEiB,OAAO,EAAE,MAAM;wBAAEC,cAAc,EAAE,eAAe;wBAAEb,UAAU,EAAE,QAAQ;wBAAEoD,KAAK,EAAE;sBAAO,CAAE;sBAAAhE,QAAA,gBACjGzK,OAAA,CAACvD,UAAU;wBAACiO,OAAO,EAAC,OAAO;wBAACM,EAAE,EAAE;0BAAEmC,UAAU,EAAE;wBAAO,CAAE;wBAAA1C,QAAA,GACpDxH,eAAe,CAACkD,MAAM,CAAClE,SAAS,CAAC,EAAC,KAAG,EAACkE,MAAM,CAACjB,SAAS,IAAI,KAAK;sBAAA;wBAAA0F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC,EACZxJ,YAAY,IACZ4E,MAAM,CAACjB,SAAS,KAAK3D,YAAY,CAAC2D,SAAS,IAC3CK,MAAM,CAACY,MAAM,CAAChB,YAAY,CAAC,KAAKI,MAAM,CAAChE,YAAY,CAAC4D,YAAY,CAAC,IACjEI,MAAM,CAACY,MAAM,CAACf,OAAO,CAAC,KAAKG,MAAM,CAAChE,YAAY,CAAC6D,OAAO,CAAC,iBACtDpF,OAAA,CAAC1C,IAAI;wBACH0O,IAAI,EAAC,OAAO;wBACZP,KAAK,EAAC,aAAa;wBACnBG,KAAK,EAAC,SAAS;wBACflB,OAAO,EAAC,UAAU;wBAClBM,EAAE,EAAE;0BAAEuC,MAAM,EAAE,EAAE;0BAAEmB,QAAQ,EAAE;wBAAS;sBAAE;wBAAA9D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CACF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACN/K,OAAA,CAACzD,GAAG;sBAACyO,EAAE,EAAE;wBAAEiB,OAAO,EAAE,MAAM;wBAAEC,cAAc,EAAE,eAAe;wBAAEuC,KAAK,EAAE;sBAAO,CAAE;sBAAAhE,QAAA,gBAC3EzK,OAAA,CAACvD,UAAU;wBAACiO,OAAO,EAAC,SAAS;wBAAAD,QAAA,GAC1BtE,MAAM,CAAChB,YAAY,IAAI,KAAK,EAAC,KAAG,EAACgB,MAAM,CAACf,OAAO,IAAI,KAAK;sBAAA;wBAAAwF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC,eACb/K,OAAA,CAACvD,UAAU;wBAACiO,OAAO,EAAC,SAAS;wBAACM,EAAE,EAAE;0BAAEmC,UAAU,EAAE,MAAM;0BAAEvB,KAAK,EAAEzF,MAAM,CAACL,aAAa,GAAG0C,UAAU,CAAC3G,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;wBAAe,CAAE;wBAAAyI,QAAA,GAC5JtE,MAAM,CAACL,aAAa,IAAI,CAAC,EAAC,gBAC7B;sBAAA;wBAAA8E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAvCD5E,MAAM,CAAClE,SAAS;kBAAA2I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwCb,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACT/K,OAAA,CAAC5C,cAAc;gBAAAqN,QAAA,EACZvI,UAAU,CAACD,SAAS,IAAI;cAAsD;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAEb1J,MAAM,CAACqC,MAAM,KAAK,CAAC,IAAI,CAAC7C,aAAa,iBACpCb,OAAA,CAAC9C,KAAK;cAACkP,QAAQ,EAAC,SAAS;cAACpB,EAAE,EAAE;gBAAEwC,EAAE,EAAE,CAAC;gBAAEkB,QAAQ,EAAE;cAAS,CAAE;cAAAjE,QAAA,EAAC;YAE7D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGA,CAAClK,aAAa,IAAIgB,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,CAAC,MAAM;YACvF,MAAMkE,MAAM,GAAG9E,MAAM,CAAC6F,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAAC5D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;YACnE,IAAIkE,MAAM,EAAE;cACV,oBACEnG,OAAA,CAACzD,GAAG;gBAACyO,EAAE,EAAE;kBAAEwC,EAAE,EAAE,CAAC;kBAAEvC,CAAC,EAAE,CAAC;kBAAEoC,OAAO,EAAE,SAAS;kBAAEC,YAAY,EAAE;gBAAE,CAAE;gBAAA7C,QAAA,gBAC5DzK,OAAA,CAACvD,UAAU;kBAACiO,OAAO,EAAC,OAAO;kBAAAD,QAAA,gBAACzK,OAAA;oBAAAyK,QAAA,EAAQ;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC9H,eAAe,CAACkD,MAAM,CAAClE,SAAS,CAAC;gBAAA;kBAAA2I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACrG/K,OAAA,CAACvD,UAAU;kBAACiO,OAAO,EAAC,OAAO;kBAAAD,QAAA,gBAACzK,OAAA;oBAAAyK,QAAA,EAAQ;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC5E,MAAM,CAACL,aAAa,IAAI,CAAC,EAAC,IAAE;gBAAA;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtG/K,OAAA,CAACvD,UAAU;kBAACiO,OAAO,EAAC,OAAO;kBAAAD,QAAA,gBACzBzK,OAAA;oBAAAyK,QAAA,EAAQ;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvB/K,OAAA,CAAC1C,IAAI;oBACHmO,KAAK,EAAEtF,MAAM,CAACC,YAAY,IAAI,KAAM;oBACpC4F,IAAI,EAAC,OAAO;oBACZJ,KAAK,EAAEhM,iBAAiB,CAACuG,MAAM,CAACC,YAAY,CAAE;oBAC9CsE,OAAO,EAAC,UAAU;oBAClBM,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAEV;YACA,OAAO,IAAI;UACb,CAAC,EAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP/K,OAAA,CAACzD,GAAG;QAACyO,EAAE,EAAE;UAAEwC,EAAE,EAAE,CAAC;UAAEvB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAzB,QAAA,gBACnEzK,OAAA,CAACrD,MAAM;UACL+N,OAAO,EAAC,UAAU;UAClBkB,KAAK,EAAC,WAAW;UACjBC,OAAO,EAAEA,CAAA,KAAM;YACbrK,eAAe,CAAC,IAAI,CAAC;YACrBM,WAAW,CAAC;cACVC,OAAO,EAAE,EAAE;cACXC,YAAY,EAAE,EAAE;cAChBC,SAAS,EAAE;YACb,CAAC,CAAC;UACJ,CAAE;UACF8J,SAAS,eAAE/L,OAAA,CAACtB,UAAU;YAAAkM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Be,QAAQ,EAAErL,OAAQ;UAAAgK,QAAA,EACnB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET/K,OAAA,CAACrD,MAAM;UACL+N,OAAO,EAAC,WAAW;UACnBkB,KAAK,EAAC,SAAS;UACfC,OAAO,EAAE/B,YAAa;UACtB2F,OAAO,eAAEzP,OAAA,CAACxB,QAAQ;YAAAoM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBe,QAAQ,EAAErL,OAAO,IAAI,CAACoB,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,SAAU;UAAAwI,QAAA,EAElEhK,OAAO,gBAAGT,OAAA,CAAC7C,gBAAgB;YAAC6O,IAAI,EAAE;UAAG;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGD/K,OAAA,CAACzC,MAAM;MAACmS,IAAI,EAAE5G,iBAAkB;MAAC6G,OAAO,EAAEA,CAAA,KAAM5G,oBAAoB,CAAC,KAAK,CAAE;MAAC6G,QAAQ,EAAC,IAAI;MAACpE,SAAS;MAAAf,QAAA,gBAClGzK,OAAA,CAACxC,WAAW;QAACwN,EAAE,EAAE;UAAEqC,OAAO,EAAE;QAAgB,CAAE;QAAA5C,QAAA,eAC5CzK,OAAA,CAACzD,GAAG;UAACyO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAEwE,GAAG,EAAE;UAAE,CAAE;UAAApF,QAAA,gBACzDzK,OAAA,CAACpB,WAAW;YAACgN,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B/K,OAAA,CAACvD,UAAU;YAACiO,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAEzB,kBAAkB,CAACE;UAAK;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd/K,OAAA,CAACvC,aAAa;QAAAgN,QAAA,eACZzK,OAAA,CAACvD,UAAU;UAACiO,OAAO,EAAC,OAAO;UAACM,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,EACvCzB,kBAAkB,CAAChF;QAAO;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChB/K,OAAA,CAACtC,aAAa;QAAA+M,QAAA,gBACZzK,OAAA,CAACrD,MAAM;UAACkP,OAAO,EAAEA,CAAA,KAAM9C,oBAAoB,CAAC,KAAK,CAAE;UAAC6C,KAAK,EAAC,WAAW;UAAClB,OAAO,EAAC,UAAU;UAAAD,QAAA,EAAC;QAEzF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/K,OAAA,CAACrD,MAAM;UACLkP,OAAO,EAAEA,CAAA,KAAM;YACb9C,oBAAoB,CAAC,KAAK,CAAC;YAC3BC,kBAAkB,CAACG,SAAS,CAAC,CAAC;UAChC,CAAE;UACFyC,KAAK,EAAC,SAAS;UACflB,OAAO,EAAC,WAAW;UACnBoF,SAAS;UAAArF,QAAA,EACV;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT/K,OAAA,CAACzC,MAAM;MAACmS,IAAI,EAAEhN,qBAAsB;MAACiN,OAAO,EAAEV,4BAA6B;MAACW,QAAQ,EAAC,IAAI;MAACpE,SAAS;MAAAf,QAAA,gBACjGzK,OAAA,CAACxC,WAAW;QAACwN,EAAE,EAAE;UAAEqC,OAAO,EAAE;QAAgB,CAAE;QAAA5C,QAAA,eAC5CzK,OAAA,CAACzD,GAAG;UAACyO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAEwE,GAAG,EAAE;UAAE,CAAE;UAAApF,QAAA,gBACzDzK,OAAA,CAACpB,WAAW;YAACgN,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B/K,OAAA,CAACvD,UAAU;YAACiO,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd/K,OAAA,CAACvC,aAAa;QAAAgN,QAAA,EACX7H,eAAe,iBACd5C,OAAA,CAACzD,GAAG;UAACyO,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,gBACjBzK,OAAA,CAACvD,UAAU;YAACiO,OAAO,EAAC,OAAO;YAAC0D,SAAS;YAAA3D,QAAA,GAAC,UAC5B,eAAAzK,OAAA;cAAAyK,QAAA,EAAS7H,eAAe,CAACb;YAAO;cAAA6I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,4BAAqB,EAACnI,eAAe,CAACwE,eAAe,IAAI,CAAC,EAAC,KAC/G;UAAA;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/K,OAAA,CAACvD,UAAU;YAACiO,OAAO,EAAC,OAAO;YAAC0D,SAAS;YAAA3D,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/K,OAAA,CAACvD,UAAU;YAACiO,OAAO,EAAC,OAAO;YAACkC,SAAS,EAAC,IAAI;YAAAnC,QAAA,gBACxCzK,OAAA;cAAAyK,QAAA,EAAI;YAAsC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C/K,OAAA;cAAAyK,QAAA,EAAI;YAAyB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC/K,OAAA;cAAAyK,QAAA,EAAI;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChB/K,OAAA,CAACtC,aAAa;QAACsN,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEiB,cAAc,EAAE;QAAgB,CAAE;QAAAzB,QAAA,gBAC3DzK,OAAA,CAACrD,MAAM;UAACkP,OAAO,EAAEoD,4BAA6B;UAACrD,KAAK,EAAC,WAAW;UAAAnB,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/K,OAAA,CAACzD,GAAG;UAAAkO,QAAA,gBACFzK,OAAA,CAACrD,MAAM;YAACkP,OAAO,EAAEsD,wBAAyB;YAACvD,KAAK,EAAC,SAAS;YAACZ,EAAE,EAAE;cAAE+E,EAAE,EAAE;YAAE,CAAE;YAAAtF,QAAA,EAAC;UAE1E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/K,OAAA,CAACrD,MAAM;YAACkP,OAAO,EAAEqD,gBAAiB;YAACxE,OAAO,EAAC,WAAW;YAACkB,KAAK,EAAC,SAAS;YAAAnB,QAAA,EAAC;UAEvE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT/K,OAAA,CAACd,sBAAsB;MACrBwQ,IAAI,EAAEpN,0BAA2B;MACjCqN,OAAO,EAAEP,iCAAkC;MAC3CrI,IAAI,EAAExF,YAAa;MACnB4E,MAAM,EAAE3D,gBAAiB;MACzBwN,YAAY,EAAEX,2BAA4B;MAC1CY,mBAAmB,EAAET;IAAwB;MAAA5E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eAGF/K,OAAA,CAACzC,MAAM;MACLmS,IAAI,EAAE5M,qBAAsB;MAC5B6M,OAAO,EAAEA,CAAA,KAAM5M,wBAAwB,CAAC,KAAK,CAAE;MAC/C6M,QAAQ,EAAC,IAAI;MACbpE,SAAS;MAAAf,QAAA,gBAETzK,OAAA,CAACxC,WAAW;QAAAiN,QAAA,eACVzK,OAAA,CAACzD,GAAG;UAACyO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAEwE,GAAG,EAAE;UAAE,CAAE;UAAApF,QAAA,gBACzDzK,OAAA,CAAClB,QAAQ;YAAC8M,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5B/K,OAAA,CAACvD,UAAU;YAACiO,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd/K,OAAA,CAACvC,aAAa;QAAAgN,QAAA,eACZzK,OAAA,CAACb,eAAe;UAAC4H,IAAI,EAAExF;QAAa;UAAAqJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAChB/K,OAAA,CAACtC,aAAa;QAAA+M,QAAA,eACZzK,OAAA,CAACrD,MAAM;UAACkP,OAAO,EAAEA,CAAA,KAAM9I,wBAAwB,CAAC,KAAK,CAAE;UAAC6I,KAAK,EAAC,SAAS;UAAAnB,QAAA,EAAC;QAExE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACxK,EAAA,CA1iEIJ,kBAAkB;EAAA,QACLpB,WAAW;AAAA;AAAAmR,EAAA,GADxB/P,kBAAkB;AA4iExB,eAAeA,kBAAkB;AAAC,IAAA+P,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}