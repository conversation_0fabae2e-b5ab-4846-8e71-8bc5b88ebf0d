{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"më pak se një sekondë\",\n    other: \"më pak se {{count}} sekonda\"\n  },\n  xSeconds: {\n    one: \"1 sekondë\",\n    other: \"{{count}} sekonda\"\n  },\n  halfAMinute: \"gjysëm minuti\",\n  lessThanXMinutes: {\n    one: \"më pak se një minute\",\n    other: \"më pak se {{count}} minuta\"\n  },\n  xMinutes: {\n    one: \"1 minutë\",\n    other: \"{{count}} minuta\"\n  },\n  aboutXHours: {\n    one: \"rreth 1 orë\",\n    other: \"rreth {{count}} orë\"\n  },\n  xHours: {\n    one: \"1 orë\",\n    other: \"{{count}} orë\"\n  },\n  xDays: {\n    one: \"1 ditë\",\n    other: \"{{count}} ditë\"\n  },\n  aboutXWeeks: {\n    one: \"rreth 1 javë\",\n    other: \"rreth {{count}} javë\"\n  },\n  xWeeks: {\n    one: \"1 javë\",\n    other: \"{{count}} javë\"\n  },\n  aboutXMonths: {\n    one: \"rreth 1 muaj\",\n    other: \"rreth {{count}} muaj\"\n  },\n  xMonths: {\n    one: \"1 muaj\",\n    other: \"{{count}} muaj\"\n  },\n  aboutXYears: {\n    one: \"rreth 1 vit\",\n    other: \"rreth {{count}} vite\"\n  },\n  xYears: {\n    one: \"1 vit\",\n    other: \"{{count}} vite\"\n  },\n  overXYears: {\n    one: \"mbi 1 vit\",\n    other: \"mbi {{count}} vite\"\n  },\n  almostXYears: {\n    one: \"pothuajse 1 vit\",\n    other: \"pothuajse {{count}} vite\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"në \" + result;\n    } else {\n      return result + \" më parë\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/sq/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"më pak se një sekondë\",\n    other: \"më pak se {{count}} sekonda\",\n  },\n\n  xSeconds: {\n    one: \"1 sekondë\",\n    other: \"{{count}} sekonda\",\n  },\n\n  halfAMinute: \"gjysëm minuti\",\n\n  lessThanXMinutes: {\n    one: \"më pak se një minute\",\n    other: \"më pak se {{count}} minuta\",\n  },\n\n  xMinutes: {\n    one: \"1 minutë\",\n    other: \"{{count}} minuta\",\n  },\n\n  aboutXHours: {\n    one: \"rreth 1 orë\",\n    other: \"rreth {{count}} orë\",\n  },\n\n  xHours: {\n    one: \"1 orë\",\n    other: \"{{count}} orë\",\n  },\n\n  xDays: {\n    one: \"1 ditë\",\n    other: \"{{count}} ditë\",\n  },\n\n  aboutXWeeks: {\n    one: \"rreth 1 javë\",\n    other: \"rreth {{count}} javë\",\n  },\n\n  xWeeks: {\n    one: \"1 javë\",\n    other: \"{{count}} javë\",\n  },\n\n  aboutXMonths: {\n    one: \"rreth 1 muaj\",\n    other: \"rreth {{count}} muaj\",\n  },\n\n  xMonths: {\n    one: \"1 muaj\",\n    other: \"{{count}} muaj\",\n  },\n\n  aboutXYears: {\n    one: \"rreth 1 vit\",\n    other: \"rreth {{count}} vite\",\n  },\n\n  xYears: {\n    one: \"1 vit\",\n    other: \"{{count}} vite\",\n  },\n\n  overXYears: {\n    one: \"mbi 1 vit\",\n    other: \"mbi {{count}} vite\",\n  },\n\n  almostXYears: {\n    one: \"pothuajse 1 vit\",\n    other: \"pothuajse {{count}} vite\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"në \" + result;\n    } else {\n      return result + \" më parë\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,uBAAuB;IAC5BC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,eAAe;EAE5BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,EAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,UAAU;IAC5B;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}