{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"bir saniyədən az\",\n    other: \"{{count}} bir saniyədən az\"\n  },\n  xSeconds: {\n    one: \"1 saniyə\",\n    other: \"{{count}} saniyə\"\n  },\n  halfAMinute: \"yarım dəqiqə\",\n  lessThanXMinutes: {\n    one: \"bir dəqiqədən az\",\n    other: \"{{count}} bir dəqiqədən az\"\n  },\n  xMinutes: {\n    one: \"bir dəqiqə\",\n    other: \"{{count}} dəqiqə\"\n  },\n  aboutXHours: {\n    one: \"təxminən 1 saat\",\n    other: \"təxminən {{count}} saat\"\n  },\n  xHours: {\n    one: \"1 saat\",\n    other: \"{{count}} saat\"\n  },\n  xDays: {\n    one: \"1 gün\",\n    other: \"{{count}} gün\"\n  },\n  aboutXWeeks: {\n    one: \"təxminən 1 həftə\",\n    other: \"təxminən {{count}} həftə\"\n  },\n  xWeeks: {\n    one: \"1 həftə\",\n    other: \"{{count}} həftə\"\n  },\n  aboutXMonths: {\n    one: \"təxminən 1 ay\",\n    other: \"təxminən {{count}} ay\"\n  },\n  xMonths: {\n    one: \"1 ay\",\n    other: \"{{count}} ay\"\n  },\n  aboutXYears: {\n    one: \"təxminən 1 il\",\n    other: \"təxminən {{count}} il\"\n  },\n  xYears: {\n    one: \"1 il\",\n    other: \"{{count}} il\"\n  },\n  overXYears: {\n    one: \"1 ildən çox\",\n    other: \"{{count}} ildən çox\"\n  },\n  almostXYears: {\n    one: \"demək olar ki 1 il\",\n    other: \"demək olar ki {{count}} il\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" sonra\";\n    } else {\n      return result + \" əvvəl\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/az/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"bir saniyədən az\",\n    other: \"{{count}} bir saniyədən az\",\n  },\n\n  xSeconds: {\n    one: \"1 saniyə\",\n    other: \"{{count}} saniyə\",\n  },\n\n  halfAMinute: \"yarım dəqiqə\",\n\n  lessThanXMinutes: {\n    one: \"bir dəqiqədən az\",\n    other: \"{{count}} bir dəqiqədən az\",\n  },\n\n  xMinutes: {\n    one: \"bir dəqiqə\",\n    other: \"{{count}} dəqiqə\",\n  },\n\n  aboutXHours: {\n    one: \"təxminən 1 saat\",\n    other: \"təxminən {{count}} saat\",\n  },\n\n  xHours: {\n    one: \"1 saat\",\n    other: \"{{count}} saat\",\n  },\n\n  xDays: {\n    one: \"1 gün\",\n    other: \"{{count}} gün\",\n  },\n\n  aboutXWeeks: {\n    one: \"təxminən 1 həftə\",\n    other: \"təxminən {{count}} həftə\",\n  },\n\n  xWeeks: {\n    one: \"1 həftə\",\n    other: \"{{count}} həftə\",\n  },\n\n  aboutXMonths: {\n    one: \"təxminən 1 ay\",\n    other: \"təxminən {{count}} ay\",\n  },\n\n  xMonths: {\n    one: \"1 ay\",\n    other: \"{{count}} ay\",\n  },\n\n  aboutXYears: {\n    one: \"təxminən 1 il\",\n    other: \"təxminən {{count}} il\",\n  },\n\n  xYears: {\n    one: \"1 il\",\n    other: \"{{count}} il\",\n  },\n\n  overXYears: {\n    one: \"1 ildən çox\",\n    other: \"{{count}} ildən çox\",\n  },\n\n  almostXYears: {\n    one: \"demək olar ki 1 il\",\n    other: \"demək olar ki {{count}} il\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" sonra\";\n    } else {\n      return result + \" əvvəl\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,cAAc;EAE3BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,EAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM,GAAG,QAAQ;IAC1B,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,QAAQ;IAC1B;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}