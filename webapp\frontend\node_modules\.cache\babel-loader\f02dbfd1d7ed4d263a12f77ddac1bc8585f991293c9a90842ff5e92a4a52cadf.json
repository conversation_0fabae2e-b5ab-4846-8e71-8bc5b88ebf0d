{"ast": null, "code": "import { isSameWeek } from \"../../../isSameWeek.js\";\nconst weekdays = [\"недела\", \"понеделник\", \"вторник\", \"среда\", \"четврток\", \"петок\", \"сабота\"];\nfunction lastWeek(day) {\n  const weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'минатата \" + weekday + \" во' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'минатиот \" + weekday + \" во' p\";\n  }\n}\nfunction thisWeek(day) {\n  const weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'ова \" + weekday + \" вo' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'овој \" + weekday + \" вo' p\";\n  }\n}\nfunction nextWeek(day) {\n  const weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'следната \" + weekday + \" вo' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'следниот \" + weekday + \" вo' p\";\n  }\n}\nconst formatRelativeLocale = {\n  lastWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return lastWeek(day);\n    }\n  },\n  yesterday: \"'вчера во' p\",\n  today: \"'денес во' p\",\n  tomorrow: \"'утре во' p\",\n  nextWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return nextWeek(day);\n    }\n  },\n  other: \"P\"\n};\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n  return format;\n};", "map": {"version": 3, "names": ["isSameWeek", "weekdays", "lastWeek", "day", "weekday", "thisWeek", "nextWeek", "formatRelativeLocale", "date", "baseDate", "options", "getDay", "yesterday", "today", "tomorrow", "other", "formatRelative", "token", "format"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/mk/_lib/formatRelative.js"], "sourcesContent": ["import { isSameWeek } from \"../../../isSameWeek.js\";\n\nconst weekdays = [\n  \"недела\",\n  \"понеделник\",\n  \"вторник\",\n  \"среда\",\n  \"четврток\",\n  \"петок\",\n  \"сабота\",\n];\n\nfunction lastWeek(day) {\n  const weekday = weekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'минатата \" + weekday + \" во' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'минатиот \" + weekday + \" во' p\";\n  }\n}\n\nfunction thisWeek(day) {\n  const weekday = weekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'ова \" + weekday + \" вo' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'овој \" + weekday + \" вo' p\";\n  }\n}\n\nfunction nextWeek(day) {\n  const weekday = weekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'следната \" + weekday + \" вo' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'следниот \" + weekday + \" вo' p\";\n  }\n}\n\nconst formatRelativeLocale = {\n  lastWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return lastWeek(day);\n    }\n  },\n  yesterday: \"'вчера во' p\",\n  today: \"'денес во' p\",\n  tomorrow: \"'утре во' p\",\n  nextWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return nextWeek(day);\n    }\n  },\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n\n  return format;\n};\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,wBAAwB;AAEnD,MAAMC,QAAQ,GAAG,CACf,QAAQ,EACR,YAAY,EACZ,SAAS,EACT,OAAO,EACP,UAAU,EACV,OAAO,EACP,QAAQ,CACT;AAED,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,QAAQ,CAACE,GAAG,CAAC;EAE7B,QAAQA,GAAG;IACT,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,YAAY,GAAGC,OAAO,GAAG,QAAQ;IAC1C,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,YAAY,GAAGA,OAAO,GAAG,QAAQ;EAC5C;AACF;AAEA,SAASC,QAAQA,CAACF,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,QAAQ,CAACE,GAAG,CAAC;EAE7B,QAAQA,GAAG;IACT,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,OAAO,GAAGC,OAAO,GAAG,QAAQ;IACrC,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,QAAQ,GAAGA,OAAO,GAAG,QAAQ;EACxC;AACF;AAEA,SAASE,QAAQA,CAACH,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,QAAQ,CAACE,GAAG,CAAC;EAE7B,QAAQA,GAAG;IACT,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,YAAY,GAAGC,OAAO,GAAG,QAAQ;IAC1C,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,YAAY,GAAGA,OAAO,GAAG,QAAQ;EAC5C;AACF;AAEA,MAAMG,oBAAoB,GAAG;EAC3BL,QAAQ,EAAEA,CAACM,IAAI,EAAEC,QAAQ,EAAEC,OAAO,KAAK;IACrC,MAAMP,GAAG,GAAGK,IAAI,CAACG,MAAM,CAAC,CAAC;IACzB,IAAIX,UAAU,CAACQ,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MACvC,OAAOL,QAAQ,CAACF,GAAG,CAAC;IACtB,CAAC,MAAM;MACL,OAAOD,QAAQ,CAACC,GAAG,CAAC;IACtB;EACF,CAAC;EACDS,SAAS,EAAE,cAAc;EACzBC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,aAAa;EACvBR,QAAQ,EAAEA,CAACE,IAAI,EAAEC,QAAQ,EAAEC,OAAO,KAAK;IACrC,MAAMP,GAAG,GAAGK,IAAI,CAACG,MAAM,CAAC,CAAC;IACzB,IAAIX,UAAU,CAACQ,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MACvC,OAAOL,QAAQ,CAACF,GAAG,CAAC;IACtB,CAAC,MAAM;MACL,OAAOG,QAAQ,CAACH,GAAG,CAAC;IACtB;EACF,CAAC;EACDY,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAET,IAAI,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EAChE,MAAMQ,MAAM,GAAGX,oBAAoB,CAACU,KAAK,CAAC;EAE1C,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACV,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACxC;EAEA,OAAOQ,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}