{"ast": null, "code": "'use client';\n\nexport { appendOwnerState } from './appendOwnerState';\nexport { areArraysEqual } from './areArraysEqual';\nexport { ClassNameConfigurator } from './ClassNameConfigurator';\nexport { extractEventHandlers } from './extractEventHandlers';\nexport { isHostComponent } from './isHostComponent';\nexport { resolveComponentProps } from './resolveComponentProps';\nexport { useRootElementName } from './useRootElementName';\nexport { useSlotProps } from './useSlotProps';\nexport { mergeSlotProps } from './mergeSlotProps';\nexport { prepareForSlot } from './prepareForSlot';\nexport * from './PolymorphicComponent';\nexport * from './types';", "map": {"version": 3, "names": ["appendOwnerState", "areArraysEqual", "ClassNameConfigurator", "extractEventHandlers", "isHostComponent", "resolveComponentProps", "useRootElementName", "useSlotProps", "mergeSlotProps", "prepareForSlot"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/base/utils/index.js"], "sourcesContent": ["'use client';\n\nexport { appendOwnerState } from './appendOwnerState';\nexport { areArraysEqual } from './areArraysEqual';\nexport { ClassNameConfigurator } from './ClassNameConfigurator';\nexport { extractEventHandlers } from './extractEventHandlers';\nexport { isHostComponent } from './isHostComponent';\nexport { resolveComponentProps } from './resolveComponentProps';\nexport { useRootElementName } from './useRootElementName';\nexport { useSlotProps } from './useSlotProps';\nexport { mergeSlotProps } from './mergeSlotProps';\nexport { prepareForSlot } from './prepareForSlot';\nexport * from './PolymorphicComponent';\nexport * from './types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,cAAc,wBAAwB;AACtC,cAAc,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}