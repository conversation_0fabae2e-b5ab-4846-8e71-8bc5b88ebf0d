{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      let url = `/cavi/${cantiereIdNum}`;\n      if (tipoCavo !== null) {\n        url += `?tipo_cavo=${tipoCavo}`;\n      }\n      console.log(`Chiamata API: GET ${url}`);\n      console.log('Token:', localStorage.getItem('token'));\n      const response = await axiosInstance.get(url);\n      console.log(`Risposta API: ${url}`, response.data);\n      return response.data;\n    } catch (error) {\n      var _error$response, _error$response2;\n      console.error('Get cavi error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        data: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data,\n        url: `/cavi/${cantiereId}${tipoCavo !== null ? `?tipo_cavo=${tipoCavo}` : ''}`\n      });\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      const response = await axiosInstance.post(`/cavi/${cantiereId}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      const response = await axiosInstance.put(`/cavi/${cantiereId}/${cavoId}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina un cavo\n  deleteCavo: async (cantiereId, cavoId) => {\n    try {\n      const response = await axiosInstance.delete(`/cavi/${cantiereId}/${cavoId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati) => {\n    try {\n      const response = await axiosInstance.post(`/cavi/${cantiereId}/${cavoId}/metri-posati`, {\n        metri_posati: metriPosati\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina) => {\n    try {\n      const response = await axiosInstance.post(`/cavi/${cantiereId}/${cavoId}/bobina`, {\n        id_bobina: idBobina\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default caviService;", "map": {"version": 3, "names": ["axios", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "caviService", "get<PERSON><PERSON>", "cantiereId", "tipoCavo", "cantiereIdNum", "parseInt", "isNaN", "Error", "url", "console", "log", "response", "get", "data", "_error$response", "_error$response2", "message", "status", "createCavo", "cavoData", "post", "updateCavo", "cavoId", "put", "deleteCavo", "delete", "updateMetri<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_posati", "updateBobina", "idBobina", "id_bobina"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/caviService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      let url = `/cavi/${cantiereIdNum}`;\n      if (tipoCavo !== null) {\n        url += `?tipo_cavo=${tipoCavo}`;\n      }\n\n      console.log(`Chiamata API: GET ${url}`);\n      console.log('Token:', localStorage.getItem('token'));\n\n      const response = await axiosInstance.get(url);\n      console.log(`Risposta API: ${url}`, response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: error.response?.status,\n        data: error.response?.data,\n        url: `/cavi/${cantiereId}${tipoCavo !== null ? `?tipo_cavo=${tipoCavo}` : ''}`\n      });\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      const response = await axiosInstance.post(`/cavi/${cantiereId}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      const response = await axiosInstance.put(`/cavi/${cantiereId}/${cavoId}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina un cavo\n  deleteCavo: async (cantiereId, cavoId) => {\n    try {\n      const response = await axiosInstance.delete(`/cavi/${cantiereId}/${cavoId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati) => {\n    try {\n      const response = await axiosInstance.post(`/cavi/${cantiereId}/${cavoId}/metri-posati`, {\n        metri_posati: metriPosati\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina) => {\n    try {\n      const response = await axiosInstance.post(`/cavi/${cantiereId}/${cavoId}/bobina`, {\n        id_bobina: idBobina\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default caviService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,WAAW,GAAG;EAClB;EACAC,OAAO,EAAE,MAAAA,CAAOC,UAAU,EAAEC,QAAQ,GAAG,IAAI,KAAK;IAC9C,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,IAAIM,GAAG,GAAG,SAASJ,aAAa,EAAE;MAClC,IAAID,QAAQ,KAAK,IAAI,EAAE;QACrBK,GAAG,IAAI,cAAcL,QAAQ,EAAE;MACjC;MAEAM,OAAO,CAACC,GAAG,CAAC,qBAAqBF,GAAG,EAAE,CAAC;MACvCC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEhB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;MAEpD,MAAMgB,QAAQ,GAAG,MAAM1B,aAAa,CAAC2B,GAAG,CAACJ,GAAG,CAAC;MAC7CC,OAAO,CAACC,GAAG,CAAC,iBAAiBF,GAAG,EAAE,EAAEG,QAAQ,CAACE,IAAI,CAAC;MAClD,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MAAA,IAAAiB,eAAA,EAAAC,gBAAA;MACdN,OAAO,CAACZ,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCY,OAAO,CAACZ,KAAK,CAAC,gBAAgB,EAAE;QAC9BmB,OAAO,EAAEnB,KAAK,CAACmB,OAAO;QACtBC,MAAM,GAAAH,eAAA,GAAEjB,KAAK,CAACc,QAAQ,cAAAG,eAAA,uBAAdA,eAAA,CAAgBG,MAAM;QAC9BJ,IAAI,GAAAE,gBAAA,GAAElB,KAAK,CAACc,QAAQ,cAAAI,gBAAA,uBAAdA,gBAAA,CAAgBF,IAAI;QAC1BL,GAAG,EAAE,SAASN,UAAU,GAAGC,QAAQ,KAAK,IAAI,GAAG,cAAcA,QAAQ,EAAE,GAAG,EAAE;MAC9E,CAAC,CAAC;MACF,MAAMN,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACE,IAAI,GAAGhB,KAAK;IACpD;EACF,CAAC;EAED;EACAqB,UAAU,EAAE,MAAAA,CAAOhB,UAAU,EAAEiB,QAAQ,KAAK;IAC1C,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAM1B,aAAa,CAACmC,IAAI,CAAC,SAASlB,UAAU,EAAE,EAAEiB,QAAQ,CAAC;MAC1E,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdY,OAAO,CAACZ,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACE,IAAI,GAAGhB,KAAK;IACpD;EACF,CAAC;EAED;EACAwB,UAAU,EAAE,MAAAA,CAAOnB,UAAU,EAAEoB,MAAM,EAAEH,QAAQ,KAAK;IAClD,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAM1B,aAAa,CAACsC,GAAG,CAAC,SAASrB,UAAU,IAAIoB,MAAM,EAAE,EAAEH,QAAQ,CAAC;MACnF,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdY,OAAO,CAACZ,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACE,IAAI,GAAGhB,KAAK;IACpD;EACF,CAAC;EAED;EACA2B,UAAU,EAAE,MAAAA,CAAOtB,UAAU,EAAEoB,MAAM,KAAK;IACxC,IAAI;MACF,MAAMX,QAAQ,GAAG,MAAM1B,aAAa,CAACwC,MAAM,CAAC,SAASvB,UAAU,IAAIoB,MAAM,EAAE,CAAC;MAC5E,OAAOX,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdY,OAAO,CAACZ,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACE,IAAI,GAAGhB,KAAK;IACpD;EACF,CAAC;EAED;EACA6B,iBAAiB,EAAE,MAAAA,CAAOxB,UAAU,EAAEoB,MAAM,EAAEK,WAAW,KAAK;IAC5D,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAM1B,aAAa,CAACmC,IAAI,CAAC,SAASlB,UAAU,IAAIoB,MAAM,eAAe,EAAE;QACtFM,YAAY,EAAED;MAChB,CAAC,CAAC;MACF,OAAOhB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdY,OAAO,CAACZ,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACE,IAAI,GAAGhB,KAAK;IACpD;EACF,CAAC;EAED;EACAgC,YAAY,EAAE,MAAAA,CAAO3B,UAAU,EAAEoB,MAAM,EAAEQ,QAAQ,KAAK;IACpD,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAM1B,aAAa,CAACmC,IAAI,CAAC,SAASlB,UAAU,IAAIoB,MAAM,SAAS,EAAE;QAChFS,SAAS,EAAED;MACb,CAAC,CAAC;MACF,OAAOnB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdY,OAAO,CAACZ,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACE,IAAI,GAAGhB,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}