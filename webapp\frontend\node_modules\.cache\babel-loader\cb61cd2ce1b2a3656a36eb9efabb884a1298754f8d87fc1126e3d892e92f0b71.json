{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'el' eeee 'passat a la' LT\",\n  yesterday: \"'ahir a la' p\",\n  today: \"'avui a la' p\",\n  tomorrow: \"'demà a la' p\",\n  nextWeek: \"eeee 'a la' p\",\n  other: \"P\"\n};\nconst formatRelativeLocalePlural = {\n  lastWeek: \"'el' eeee 'passat a les' p\",\n  yesterday: \"'ahir a les' p\",\n  today: \"'avui a les' p\",\n  tomorrow: \"'demà a les' p\",\n  nextWeek: \"eeee 'a les' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, date, _baseDate, _options) => {\n  if (date.getHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  }\n  return formatRelativeLocale[token];\n};", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelativeLocalePlural", "formatRelative", "token", "date", "_baseDate", "_options", "getHours"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ca/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'el' eeee 'passat a la' LT\",\n  yesterday: \"'ahir a la' p\",\n  today: \"'avui a la' p\",\n  tomorrow: \"'demà a la' p\",\n  nextWeek: \"eeee 'a la' p\",\n  other: \"P\",\n};\n\nconst formatRelativeLocalePlural = {\n  lastWeek: \"'el' eeee 'passat a les' p\",\n  yesterday: \"'ahir a les' p\",\n  today: \"'avui a les' p\",\n  tomorrow: \"'demà a les' p\",\n  nextWeek: \"eeee 'a les' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, _baseDate, _options) => {\n  if (date.getHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  }\n  return formatRelativeLocale[token];\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,4BAA4B;EACtCC,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,0BAA0B,GAAG;EACjCN,QAAQ,EAAE,4BAA4B;EACtCC,SAAS,EAAE,gBAAgB;EAC3BC,KAAK,EAAE,gBAAgB;EACvBC,QAAQ,EAAE,gBAAgB;EAC1BC,QAAQ,EAAE,gBAAgB;EAC1BC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAME,cAAc,GAAGA,CAACC,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,KAAK;EAClE,IAAIF,IAAI,CAACG,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE;IACzB,OAAON,0BAA0B,CAACE,KAAK,CAAC;EAC1C;EACA,OAAOT,oBAAoB,CAACS,KAAK,CAAC;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}