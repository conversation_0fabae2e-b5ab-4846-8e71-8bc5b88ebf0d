{"ast": null, "code": "import React from'react';import ReactD<PERSON> from'react-dom/client';import{<PERSON><PERSON>er<PERSON>outer}from'react-router-dom';import App from'./App';import{AuthProvider}from'./context/AuthContext';import{GlobalProvider}from'./context/GlobalContext';import'./index.css';import{jsx as _jsx}from\"react/jsx-runtime\";const root=ReactDOM.createRoot(document.getElementById('root'));root.render(/*#__PURE__*/_jsx(React.StrictMode,{children:/*#__PURE__*/_jsx(BrowserRouter,{children:/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(GlobalProvider,{children:/*#__PURE__*/_jsx(App,{})})})})}));", "map": {"version": 3, "names": ["React", "ReactDOM", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "App", "<PERSON>th<PERSON><PERSON><PERSON>", "GlobalProvider", "jsx", "_jsx", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';\nimport App from './App';\nimport { AuthProvider } from './context/AuthContext';\nimport { GlobalProvider } from './context/GlobalContext';\nimport './index.css';\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <React.StrictMode>\n    <BrowserRouter>\n      <AuthProvider>\n        <GlobalProvider>\n          <App />\n        </GlobalProvider>\n      </AuthProvider>\n    </BrowserRouter>\n  </React.StrictMode>\n);\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,OAASC,aAAa,KAAQ,kBAAkB,CAChD,MAAO,CAAAC,GAAG,KAAM,OAAO,CACvB,OAASC,YAAY,KAAQ,uBAAuB,CACpD,OAASC,cAAc,KAAQ,yBAAyB,CACxD,MAAO,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAErB,KAAM,CAAAC,IAAI,CAAGP,QAAQ,CAACQ,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC,CACjEH,IAAI,CAACI,MAAM,cACTL,IAAA,CAACP,KAAK,CAACa,UAAU,EAAAC,QAAA,cACfP,IAAA,CAACL,aAAa,EAAAY,QAAA,cACZP,IAAA,CAACH,YAAY,EAAAU,QAAA,cACXP,IAAA,CAACF,cAAc,EAAAS,QAAA,cACbP,IAAA,CAACJ,GAAG,GAAE,CAAC,CACO,CAAC,CACL,CAAC,CACF,CAAC,CACA,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}