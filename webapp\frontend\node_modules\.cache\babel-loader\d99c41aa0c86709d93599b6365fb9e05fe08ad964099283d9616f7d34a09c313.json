{"ast": null, "code": "import React,{useState}from'react';import{useNavigate,useLocation}from'react-router-dom';import{AppBar,Toolbar,Box,Button,Menu,MenuItem,Typography,IconButton,Divider,Avatar,Tooltip,Snackbar,Alert}from'@mui/material';import{Home as HomeIcon,AdminPanelSettings as AdminIcon,Construction as ConstructionIcon,Cable as CableIcon,Description as ReportIcon,Logout as LogoutIcon,KeyboardArrowDown as ArrowDownIcon,FileUpload as FileUploadIcon,FileDownload as FileDownloadIcon}from'@mui/icons-material';import{useAuth}from'../context/AuthContext';import{useGlobalContext}from'../context/GlobalContext';import SelectedCantiereDisplay from'./common/SelectedCantiereDisplay';import ExcelPopup from'./cavi/ExcelPopup';import'./TopNavbar.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const TopNavbar=()=>{const navigate=useNavigate();const location=useLocation();const{user,logout,isImpersonating,impersonatedUser}=useAuth();const{setOpenEliminaCavoDialog,setOpenModificaCavoDialog,setOpenAggiungiCavoDialog}=useGlobalContext();// Stato per il popup Excel\nconst[excelPopupOpen,setExcelPopupOpen]=useState(false);const[excelOperationType,setExcelOperationType]=useState('');// Stato per gli snackbar\nconst[snackbar,setSnackbar]=useState({open:false,message:'',severity:'success'});// Recupera l'ID del cantiere dal localStorage\nconst cantiereId=parseInt(localStorage.getItem('selectedCantiereId'),10);// Stati per i menu a tendina\nconst[homeAnchorEl,setHomeAnchorEl]=useState(null);const[adminAnchorEl,setAdminAnchorEl]=useState(null);const[cantieriAnchorEl,setCantieriAnchorEl]=useState(null);const[caviAnchorEl,setCaviAnchorEl]=useState(null);const[posaAnchorEl,setPosaAnchorEl]=useState(null);const[parcoAnchorEl,setParcoAnchorEl]=useState(null);const[excelAnchorEl,setExcelAnchorEl]=useState(null);const[certificazioneAnchorEl,setCertificazioneAnchorEl]=useState(null);const[comandeAnchorEl,setComandeAnchorEl]=useState(null);// Recupera l'ID del cantiere selezionato dal localStorage\nconst selectedCantiereId=localStorage.getItem('selectedCantiereId');const selectedCantiereName=localStorage.getItem('selectedCantiereName');// Funzioni per aprire/chiudere i menu\nconst handleMenuOpen=(event,setAnchorEl)=>{setAnchorEl(event.currentTarget);};const handleMenuClose=setAnchorEl=>{setAnchorEl(null);};// Gestisce l'apertura del popup Excel\nconst handleOpenExcelPopup=operationType=>{setExcelOperationType(operationType);setExcelPopupOpen(true);handleMenuClose(setExcelAnchorEl);};// Gestisce la creazione diretta dei template senza popup\nconst handleCreateTemplateDirect=async templateType=>{try{handleMenuClose(setExcelAnchorEl);if(templateType==='cavi'){const excelService=await import('../services/excelService');await excelService.default.createCaviTemplate();setSnackbar({open:true,message:'Template Excel per cavi creato e download avviato! Controlla la cartella Download del tuo browser.',severity:'success'});}else if(templateType==='parco-bobine'){const excelService=await import('../services/excelService');await excelService.default.createParcoBobineTemplate();setSnackbar({open:true,message:'Template Excel per parco bobine creato e download avviato! Controlla la cartella Download del tuo browser.',severity:'success'});}}catch(error){console.error(`Errore nella creazione del template ${templateType}:`,error);setSnackbar({open:true,message:`Errore nella creazione del template ${templateType}: ${error.message||'Errore sconosciuto'}`,severity:'error'});}};// Gestisce la chiusura del popup Excel\nconst handleCloseExcelPopup=()=>{setExcelPopupOpen(false);};// Gestisce il successo delle operazioni Excel\nconst handleExcelSuccess=message=>{setSnackbar({open:true,message,severity:'success'});};// Gestisce gli errori delle operazioni Excel\nconst handleExcelError=message=>{setSnackbar({open:true,message,severity:'error'});};// Gestisce la chiusura dello snackbar\nconst handleCloseSnackbar=()=>{setSnackbar({...snackbar,open:false});};// Naviga a un percorso\nconst navigateTo=path=>{// Gestione speciale per il percorso Home\nif(path==='/dashboard'){// Se l'utente è un amministratore che sta impersonando un utente\nif(isImpersonating){navigate('/dashboard/admin');}// Se l'utente è un amministratore normale\nelse if((user===null||user===void 0?void 0:user.role)==='owner'){navigate('/dashboard/admin');}// Se l'utente è un utente standard\nelse if((user===null||user===void 0?void 0:user.role)==='user'){navigate('/dashboard/cantieri');}// Se l'utente è un utente cantiere\nelse if((user===null||user===void 0?void 0:user.role)==='cantieri_user'){// Reindirizza direttamente alla pagina di visualizzazione cavi\nnavigate('/dashboard/cavi/visualizza');}// Fallback per altri tipi di utenti\nelse{navigate(path);}}else{navigate(path);}// Chiudi tutti i menu\nhandleMenuClose(setHomeAnchorEl);handleMenuClose(setAdminAnchorEl);handleMenuClose(setCantieriAnchorEl);handleMenuClose(setCaviAnchorEl);handleMenuClose(setPosaAnchorEl);handleMenuClose(setParcoAnchorEl);handleMenuClose(setExcelAnchorEl);handleMenuClose(setCertificazioneAnchorEl);handleMenuClose(setComandeAnchorEl);};const handleLogout=()=>{logout();};// Verifica se un percorso è attivo\nconst isActive=path=>{return location.pathname===path;};// Verifica se un percorso è parte del percorso attivo (per i sottomenu)\nconst isPartOfActive=path=>{return location.pathname.startsWith(path);};return/*#__PURE__*/_jsxs(AppBar,{position:\"static\",color:\"default\",elevation:2,sx:{zIndex:1100,width:'100%',overflowX:'hidden'},className:\"excel-style-menu\",children:[/*#__PURE__*/_jsxs(Toolbar,{sx:{overflowX:'hidden',height:'60px'},children:[/*#__PURE__*/_jsx(Button,{color:\"inherit\",onClick:()=>navigateTo('/dashboard'),startIcon:/*#__PURE__*/_jsx(HomeIcon,{}),sx:{mr:1},className:isActive('/dashboard')?'active-button':'',children:isImpersonating?\"Torna al Menu Admin\":(user===null||user===void 0?void 0:user.role)==='owner'?\"Pannello Admin\":(user===null||user===void 0?void 0:user.role)==='user'?\"Lista Cantieri\":(user===null||user===void 0?void 0:user.role)==='cantieri_user'?\"Gestione Cavi\":\"Home\"}),/*#__PURE__*/_jsx(Divider,{orientation:\"vertical\",flexItem:true,sx:{mx:0.5}}),((user===null||user===void 0?void 0:user.role)!=='owner'||(user===null||user===void 0?void 0:user.role)==='owner'&&isImpersonating&&impersonatedUser)&&/*#__PURE__*/_jsxs(_Fragment,{children:[isImpersonating&&/*#__PURE__*/_jsx(Button,{color:\"inherit\",onClick:()=>navigateTo('/dashboard/cantieri'),sx:{mr:1},className:isActive('/dashboard/cantieri')?'active-button':'',children:isImpersonating&&impersonatedUser?`Cantieri di ${impersonatedUser.username}`:\"Lista Cantieri\"}),selectedCantiereId&&/*#__PURE__*/_jsxs(_Fragment,{children:[(user===null||user===void 0?void 0:user.role)!=='cantieri_user'&&/*#__PURE__*/_jsx(Button,{color:\"inherit\",onClick:()=>navigateTo('/dashboard/cavi/visualizza'),sx:{mr:1},className:isActive('/dashboard/cavi/visualizza')?'active-button':'',children:\"Visualizza Cavi\"}),/*#__PURE__*/_jsx(Button,{color:\"inherit\",\"aria-controls\":\"posa-menu\",\"aria-haspopup\":\"true\",onClick:e=>handleMenuOpen(e,setPosaAnchorEl),endIcon:/*#__PURE__*/_jsx(ArrowDownIcon,{}),sx:{mr:1},className:isPartOfActive('/dashboard/cavi/posa')?'active-button':'',children:\"Posa e Collegamenti\"}),/*#__PURE__*/_jsx(Button,{color:\"inherit\",\"aria-controls\":\"parco-menu\",\"aria-haspopup\":\"true\",onClick:()=>navigateTo('/dashboard/cavi/parco/visualizza'),sx:{mr:1},className:isPartOfActive('/dashboard/cavi/parco')?'active-button':'',children:\"Parco Cavi\"}),/*#__PURE__*/_jsx(Button,{color:\"inherit\",\"aria-controls\":\"excel-menu\",\"aria-haspopup\":\"true\",onClick:e=>handleMenuOpen(e,setExcelAnchorEl),endIcon:/*#__PURE__*/_jsx(ArrowDownIcon,{}),sx:{mr:1},className:isPartOfActive('/dashboard/cavi/excel')?'active-button':'',children:\"Gestione Excel\"}),/*#__PURE__*/_jsx(Button,{color:\"inherit\",onClick:()=>navigateTo(`/dashboard/cavi/${selectedCantiereId}/report`),sx:{mr:1},className:isPartOfActive('/dashboard/cavi/report')?'active-button':'',children:\"Report\"}),/*#__PURE__*/_jsx(Button,{color:\"inherit\",\"aria-controls\":\"certificazione-menu\",\"aria-haspopup\":\"true\",onClick:e=>handleMenuOpen(e,setCertificazioneAnchorEl),endIcon:/*#__PURE__*/_jsx(ArrowDownIcon,{}),sx:{mr:1},className:isPartOfActive('/dashboard/cavi/certificazione')?'active-button':'',children:\"Certificazione Cavi\"}),/*#__PURE__*/_jsx(Button,{color:\"inherit\",\"aria-controls\":\"comande-menu\",\"aria-haspopup\":\"true\",onClick:e=>handleMenuOpen(e,setComandeAnchorEl),endIcon:/*#__PURE__*/_jsx(ArrowDownIcon,{}),sx:{mr:1},className:isPartOfActive('/dashboard/cavi/comande')?'active-button':'',children:\"Gestione Comande\"}),/*#__PURE__*/_jsxs(Menu,{id:\"posa-menu\",anchorEl:posaAnchorEl,keepMounted:true,open:Boolean(posaAnchorEl),onClose:()=>handleMenuClose(setPosaAnchorEl),anchorOrigin:{vertical:'bottom',horizontal:'center'},transformOrigin:{vertical:'top',horizontal:'center'},className:\"excel-style-submenu\",elevation:3,sx:{mt:0.5},children:[/*#__PURE__*/_jsx(MenuItem,{onClick:()=>navigateTo('/dashboard/cavi/posa/inserisci-metri'),children:\"Inserisci metri posati\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>{// Apre il dialogo di modifica cavi invece di navigare alla pagina\nsetOpenModificaCavoDialog(true);// Chiude il menu\nhandleMenuClose(setPosaAnchorEl);// Naviga alla pagina visualizza cavi\nnavigateTo('/dashboard/cavi/visualizza');},children:\"Modifica cavo\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>{// Apre il dialogo di aggiunta cavi invece di navigare alla pagina\nsetOpenAggiungiCavoDialog(true);// Chiude il menu\nhandleMenuClose(setPosaAnchorEl);// Naviga alla pagina visualizza cavi\nnavigateTo('/dashboard/cavi/visualizza');},children:\"Aggiungi nuovo cavo\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>{// Apre il dialogo di eliminazione cavi invece di navigare alla pagina\nsetOpenEliminaCavoDialog(true);// Chiude il menu\nhandleMenuClose(setPosaAnchorEl);// Naviga alla pagina visualizza cavi\nnavigateTo('/dashboard/cavi/visualizza');},children:\"Elimina cavo\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>navigateTo('/dashboard/cavi/posa/modifica-bobina'),children:\"Modifica bobina cavo posato\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>navigateTo('/dashboard/cavi/posa/collegamenti'),children:\"Gestisci collegamenti cavo\"})]}),/*#__PURE__*/_jsx(Menu,{id:\"parco-menu\",anchorEl:parcoAnchorEl,keepMounted:true,open:Boolean(parcoAnchorEl),onClose:()=>handleMenuClose(setParcoAnchorEl),anchorOrigin:{vertical:'bottom',horizontal:'center'},transformOrigin:{vertical:'top',horizontal:'center'},className:\"excel-style-submenu\",elevation:3,sx:{mt:0.5},children:/*#__PURE__*/_jsx(MenuItem,{onClick:()=>navigateTo('/dashboard/cavi/parco/storico'),children:\"Visualizza Storico Utilizzo\"})}),/*#__PURE__*/_jsxs(Menu,{id:\"excel-menu\",anchorEl:excelAnchorEl,keepMounted:true,open:Boolean(excelAnchorEl),onClose:()=>handleMenuClose(setExcelAnchorEl),anchorOrigin:{vertical:'bottom',horizontal:'center'},transformOrigin:{vertical:'top',horizontal:'center'},className:\"excel-style-submenu\",elevation:3,sx:{mt:0.5},children:[/*#__PURE__*/_jsxs(MenuItem,{onClick:()=>handleOpenExcelPopup('importaCavi'),children:[/*#__PURE__*/_jsx(FileUploadIcon,{fontSize:\"small\",sx:{mr:1}}),\"Importa Cavi\"]}),/*#__PURE__*/_jsxs(MenuItem,{onClick:()=>handleOpenExcelPopup('importaParcoBobine'),children:[/*#__PURE__*/_jsx(FileUploadIcon,{fontSize:\"small\",sx:{mr:1}}),\"Importa Parco Bobine\"]}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsxs(MenuItem,{onClick:()=>handleCreateTemplateDirect('cavi'),children:[/*#__PURE__*/_jsx(FileDownloadIcon,{fontSize:\"small\",sx:{mr:1}}),\"Template Cavi\"]}),/*#__PURE__*/_jsxs(MenuItem,{onClick:()=>handleCreateTemplateDirect('parco-bobine'),children:[/*#__PURE__*/_jsx(FileDownloadIcon,{fontSize:\"small\",sx:{mr:1}}),\"Template Parco Bobine\"]}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsxs(MenuItem,{onClick:()=>handleOpenExcelPopup('esportaCavi'),children:[/*#__PURE__*/_jsx(FileDownloadIcon,{fontSize:\"small\",sx:{mr:1}}),\"Esporta Cavi\"]}),/*#__PURE__*/_jsxs(MenuItem,{onClick:()=>handleOpenExcelPopup('esportaParcoBobine'),children:[/*#__PURE__*/_jsx(FileDownloadIcon,{fontSize:\"small\",sx:{mr:1}}),\"Esporta Parco Bobine\"]})]}),/*#__PURE__*/_jsxs(Menu,{id:\"certificazione-menu\",anchorEl:certificazioneAnchorEl,keepMounted:true,open:Boolean(certificazioneAnchorEl),onClose:()=>handleMenuClose(setCertificazioneAnchorEl),anchorOrigin:{vertical:'bottom',horizontal:'center'},transformOrigin:{vertical:'top',horizontal:'center'},className:\"excel-style-submenu\",elevation:3,sx:{mt:0.5},children:[/*#__PURE__*/_jsx(MenuItem,{onClick:()=>navigateTo('/dashboard/cavi/certificazione/visualizza'),children:\"Visualizza certificazioni\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>navigateTo('/dashboard/cavi/certificazione/filtra'),children:\"Filtra per cavo\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>navigateTo('/dashboard/cavi/certificazione/crea'),children:\"Crea certificazione\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>navigateTo('/dashboard/cavi/certificazione/modifica'),children:\"Modifica certificazione\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>navigateTo('/dashboard/cavi/certificazione/elimina'),children:\"Elimina certificazione\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>navigateTo('/dashboard/cavi/certificazione/strumenti'),children:\"Gestione strumenti\"})]}),/*#__PURE__*/_jsxs(Menu,{id:\"comande-menu\",anchorEl:comandeAnchorEl,keepMounted:true,open:Boolean(comandeAnchorEl),onClose:()=>handleMenuClose(setComandeAnchorEl),anchorOrigin:{vertical:'bottom',horizontal:'center'},transformOrigin:{vertical:'top',horizontal:'center'},className:\"excel-style-submenu\",elevation:3,sx:{mt:0.5},children:[/*#__PURE__*/_jsx(MenuItem,{onClick:()=>navigateTo('/dashboard/cavi/comande/visualizza'),children:\"Visualizza comande\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>navigateTo('/dashboard/cavi/comande/crea'),children:\"Crea nuova comanda\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>navigateTo('/dashboard/cavi/comande/modifica'),children:\"Modifica comanda\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>navigateTo('/dashboard/cavi/comande/elimina'),children:\"Elimina comanda\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>navigateTo('/dashboard/cavi/comande/stampa'),children:\"Stampa comanda\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:()=>navigateTo('/dashboard/cavi/comande/assegna'),children:\"Assegna comanda a cavo\"})]})]})]}),/*#__PURE__*/_jsx(Box,{sx:{flexGrow:1}}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',height:'100%'},children:[/*#__PURE__*/_jsx(SelectedCantiereDisplay,{}),isImpersonating&&impersonatedUser&&/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"textSecondary\",sx:{mr:2.5,fontSize:'1rem'},children:[\"Accesso come: \",/*#__PURE__*/_jsx(\"b\",{children:impersonatedUser.username})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{mr:2.5,fontWeight:500,fontSize:'1rem'},children:(user===null||user===void 0?void 0:user.username)||''}),/*#__PURE__*/_jsx(Tooltip,{title:\"Logout\",children:/*#__PURE__*/_jsx(IconButton,{color:\"inherit\",onClick:handleLogout,edge:\"end\",sx:{'&:hover':{backgroundColor:'#e9ecef'},padding:'10px'},children:/*#__PURE__*/_jsx(LogoutIcon,{fontSize:\"medium\"})})})]})]}),/*#__PURE__*/_jsx(ExcelPopup,{open:excelPopupOpen,onClose:handleCloseExcelPopup,operationType:excelOperationType,cantiereId:cantiereId,onSuccess:handleExcelSuccess,onError:handleExcelError}),/*#__PURE__*/_jsx(Snackbar,{open:snackbar.open,autoHideDuration:6000,onClose:handleCloseSnackbar,anchorOrigin:{vertical:'bottom',horizontal:'center'},children:/*#__PURE__*/_jsx(Alert,{onClose:handleCloseSnackbar,severity:snackbar.severity,variant:\"filled\",sx:{width:'100%'},children:snackbar.message})})]});};export default TopNavbar;", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "AppBar", "<PERSON><PERSON><PERSON>", "Box", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Typography", "IconButton", "Divider", "Avatar", "<PERSON><PERSON><PERSON>", "Snackbar", "<PERSON><PERSON>", "Home", "HomeIcon", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "Logout", "LogoutIcon", "KeyboardArrowDown", "ArrowDownIcon", "FileUpload", "FileUploadIcon", "FileDownload", "FileDownloadIcon", "useAuth", "useGlobalContext", "SelectedCantiereDisplay", "ExcelPopup", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "TopNavbar", "navigate", "location", "user", "logout", "isImpersonating", "impersonated<PERSON><PERSON>", "setOpenEliminaCavoDialog", "setOpenModificaCavoDialog", "setOpenAggiungiCavoDialog", "excelPopupOpen", "setExcelPopupOpen", "excelOperationType", "setExcelOperationType", "snackbar", "setSnackbar", "open", "message", "severity", "cantiereId", "parseInt", "localStorage", "getItem", "homeAnchorEl", "setHomeAnchorEl", "adminAnchorEl", "setAdminAnchorEl", "cantieriAnchorEl", "setCantieriAnchorEl", "caviAnchorEl", "setCaviAnchorEl", "posaAnchorEl", "setPosaAnchorEl", "parcoAnchorEl", "setParcoAnchorEl", "excelAnchorEl", "setExcelAnchorEl", "certificazioneAnchorEl", "setCertificazioneAnchorEl", "comandeAnchorEl", "setComandeAnchorEl", "selectedCantiereId", "selectedCantiereName", "handleMenuOpen", "event", "setAnchorEl", "currentTarget", "handleMenuClose", "handleOpenExcelPopup", "operationType", "handleCreateTemplateDirect", "templateType", "excelService", "default", "createCaviTemplate", "createParcoBobineTemplate", "error", "console", "handleCloseExcelPopup", "handleExcelSuccess", "handleExcelError", "handleCloseSnackbar", "navigateTo", "path", "role", "handleLogout", "isActive", "pathname", "isPartOfActive", "startsWith", "position", "color", "elevation", "sx", "zIndex", "width", "overflowX", "className", "children", "height", "onClick", "startIcon", "mr", "orientation", "flexItem", "mx", "username", "e", "endIcon", "id", "anchorEl", "keepMounted", "Boolean", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "mt", "fontSize", "flexGrow", "display", "alignItems", "variant", "fontWeight", "title", "edge", "backgroundColor", "padding", "onSuccess", "onError", "autoHideDuration"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/TopNavbar.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  AppBar,\n  Toolbar,\n  Box,\n  Button,\n  Menu,\n  MenuItem,\n  Typography,\n  IconButton,\n  Divider,\n  Avatar,\n  Tooltip,\n  Snackbar,\n  Alert\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon,\n  Logout as LogoutIcon,\n  KeyboardArrowDown as ArrowDownIcon,\n  FileUpload as FileUploadIcon,\n  FileDownload as FileDownloadIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { useGlobalContext } from '../context/GlobalContext';\nimport SelectedCantiereDisplay from './common/SelectedCantiereDisplay';\nimport ExcelPopup from './cavi/ExcelPopup';\nimport './TopNavbar.css';\n\nconst TopNavbar = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, logout, isImpersonating, impersonatedUser } = useAuth();\n  const { setOpenEliminaCavoDialog, setOpenModificaCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();\n\n  // Stato per il popup Excel\n  const [excelPopupOpen, setExcelPopupOpen] = useState(false);\n  const [excelOperationType, setExcelOperationType] = useState('');\n\n  // Stato per gli snackbar\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n\n  // Stati per i menu a tendina\n  const [homeAnchorEl, setHomeAnchorEl] = useState(null);\n  const [adminAnchorEl, setAdminAnchorEl] = useState(null);\n  const [cantieriAnchorEl, setCantieriAnchorEl] = useState(null);\n  const [caviAnchorEl, setCaviAnchorEl] = useState(null);\n  const [posaAnchorEl, setPosaAnchorEl] = useState(null);\n  const [parcoAnchorEl, setParcoAnchorEl] = useState(null);\n  const [excelAnchorEl, setExcelAnchorEl] = useState(null);\n  const [certificazioneAnchorEl, setCertificazioneAnchorEl] = useState(null);\n  const [comandeAnchorEl, setComandeAnchorEl] = useState(null);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Funzioni per aprire/chiudere i menu\n  const handleMenuOpen = (event, setAnchorEl) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = (setAnchorEl) => {\n    setAnchorEl(null);\n  };\n\n  // Gestisce l'apertura del popup Excel\n  const handleOpenExcelPopup = (operationType) => {\n    setExcelOperationType(operationType);\n    setExcelPopupOpen(true);\n    handleMenuClose(setExcelAnchorEl);\n  };\n\n  // Gestisce la creazione diretta dei template senza popup\n  const handleCreateTemplateDirect = async (templateType) => {\n    try {\n      handleMenuClose(setExcelAnchorEl);\n\n      if (templateType === 'cavi') {\n        const excelService = await import('../services/excelService');\n        await excelService.default.createCaviTemplate();\n        setSnackbar({\n          open: true,\n          message: 'Template Excel per cavi creato e download avviato! Controlla la cartella Download del tuo browser.',\n          severity: 'success'\n        });\n      } else if (templateType === 'parco-bobine') {\n        const excelService = await import('../services/excelService');\n        await excelService.default.createParcoBobineTemplate();\n        setSnackbar({\n          open: true,\n          message: 'Template Excel per parco bobine creato e download avviato! Controlla la cartella Download del tuo browser.',\n          severity: 'success'\n        });\n      }\n    } catch (error) {\n      console.error(`Errore nella creazione del template ${templateType}:`, error);\n      setSnackbar({\n        open: true,\n        message: `Errore nella creazione del template ${templateType}: ${error.message || 'Errore sconosciuto'}`,\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce la chiusura del popup Excel\n  const handleCloseExcelPopup = () => {\n    setExcelPopupOpen(false);\n  };\n\n  // Gestisce il successo delle operazioni Excel\n  const handleExcelSuccess = (message) => {\n    setSnackbar({\n      open: true,\n      message,\n      severity: 'success'\n    });\n  };\n\n  // Gestisce gli errori delle operazioni Excel\n  const handleExcelError = (message) => {\n    setSnackbar({\n      open: true,\n      message,\n      severity: 'error'\n    });\n  };\n\n  // Gestisce la chiusura dello snackbar\n  const handleCloseSnackbar = () => {\n    setSnackbar({\n      ...snackbar,\n      open: false\n    });\n  };\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    // Gestione speciale per il percorso Home\n    if (path === '/dashboard') {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if (user?.role === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if (user?.role === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if (user?.role === 'cantieri_user') {\n        // Reindirizza direttamente alla pagina di visualizzazione cavi\n        navigate('/dashboard/cavi/visualizza');\n      }\n      // Fallback per altri tipi di utenti\n      else {\n        navigate(path);\n      }\n    } else {\n      navigate(path);\n    }\n\n    // Chiudi tutti i menu\n    handleMenuClose(setHomeAnchorEl);\n    handleMenuClose(setAdminAnchorEl);\n    handleMenuClose(setCantieriAnchorEl);\n    handleMenuClose(setCaviAnchorEl);\n    handleMenuClose(setPosaAnchorEl);\n    handleMenuClose(setParcoAnchorEl);\n    handleMenuClose(setExcelAnchorEl);\n    handleMenuClose(setCertificazioneAnchorEl);\n    handleMenuClose(setComandeAnchorEl);\n  };\n\n  const handleLogout = () => {\n    logout();\n  };\n\n  // Verifica se un percorso è attivo\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = (path) => {\n    return location.pathname.startsWith(path);\n  };\n\n  return (\n    <AppBar position=\"static\" color=\"default\" elevation={2} sx={{ zIndex: 1100, width: '100%', overflowX: 'hidden' }} className=\"excel-style-menu\">\n      <Toolbar sx={{ overflowX: 'hidden', height: '60px' }}>\n        {/* Logo/Home - Testo personalizzato in base al tipo di utente */}\n        <Button\n          color=\"inherit\"\n          onClick={() => navigateTo('/dashboard')}\n          startIcon={<HomeIcon />}\n          sx={{ mr: 1 }}\n          className={isActive('/dashboard') ? 'active-button' : ''}\n        >\n          {isImpersonating ? \"Torna al Menu Admin\" :\n           user?.role === 'owner' ? \"Pannello Admin\" :\n           user?.role === 'user' ? \"Lista Cantieri\" :\n           user?.role === 'cantieri_user' ? \"Gestione Cavi\" : \"Home\"}\n        </Button>\n        <Divider orientation=\"vertical\" flexItem sx={{ mx: 0.5 }} />\n\n        {/* Il menu Amministratore è stato rimosso perché ridondante con il pulsante Home per gli amministratori */}\n\n        {/* Menu per utenti standard e cantieri */}\n        {(user?.role !== 'owner' || (user?.role === 'owner' && isImpersonating && impersonatedUser)) && (\n          <>\n            {/* Pulsante Lista Cantieri solo per utenti che impersonano */}\n            {isImpersonating && (\n              <Button\n                color=\"inherit\"\n                onClick={() => navigateTo('/dashboard/cantieri')}\n                sx={{ mr: 1 }}\n                className={isActive('/dashboard/cantieri') ? 'active-button' : ''}\n              >\n                {isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"Lista Cantieri\"}\n              </Button>\n            )}\n\n            {/* Il cantiere selezionato è stato spostato nella parte destra della barra di navigazione */}\n\n            {/* Menu di gestione cavi - visibile solo se un cantiere è selezionato */}\n            {selectedCantiereId && (\n              <>\n                {/* Visualizza Cavi - nascosto per utenti cantiere perché ridondante con il tasto Home */}\n                {user?.role !== 'cantieri_user' && (\n                  <Button\n                    color=\"inherit\"\n                    onClick={() => navigateTo('/dashboard/cavi/visualizza')}\n                    sx={{ mr: 1 }}\n                    className={isActive('/dashboard/cavi/visualizza') ? 'active-button' : ''}\n                  >\n                    Visualizza Cavi\n                  </Button>\n                )}\n\n                {/* Posa e Collegamenti */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"posa-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setPosaAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/posa') ? 'active-button' : ''}\n                >\n                  Posa e Collegamenti\n                </Button>\n\n                {/* Parco Cavi */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"parco-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={() => navigateTo('/dashboard/cavi/parco/visualizza')}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/parco') ? 'active-button' : ''}\n                >\n                  Parco Cavi\n                </Button>\n\n                {/* Gestione Excel */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"excel-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setExcelAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/excel') ? 'active-button' : ''}\n                >\n                  Gestione Excel\n                </Button>\n\n                {/* Report */}\n                <Button\n                  color=\"inherit\"\n                  onClick={() => navigateTo(`/dashboard/cavi/${selectedCantiereId}/report`)}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/report') ? 'active-button' : ''}\n                >\n                  Report\n                </Button>\n\n                {/* Certificazione Cavi */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"certificazione-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setCertificazioneAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/certificazione') ? 'active-button' : ''}\n                >\n                  Certificazione Cavi\n                </Button>\n\n                {/* Gestione Comande */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"comande-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setComandeAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/comande') ? 'active-button' : ''}\n                >\n                  Gestione Comande\n                </Button>\n\n                {/* Sottomenu Posa e Collegamenti */}\n                <Menu\n                  id=\"posa-menu\"\n                  anchorEl={posaAnchorEl}\n                  keepMounted\n                  open={Boolean(posaAnchorEl)}\n                  onClose={() => handleMenuClose(setPosaAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/inserisci-metri')}>Inserisci metri posati</MenuItem>\n                  <MenuItem onClick={() => {\n                    // Apre il dialogo di modifica cavi invece di navigare alla pagina\n                    setOpenModificaCavoDialog(true);\n                    // Chiude il menu\n                    handleMenuClose(setPosaAnchorEl);\n                    // Naviga alla pagina visualizza cavi\n                    navigateTo('/dashboard/cavi/visualizza');\n                  }}>Modifica cavo</MenuItem>\n                  <MenuItem onClick={() => {\n                    // Apre il dialogo di aggiunta cavi invece di navigare alla pagina\n                    setOpenAggiungiCavoDialog(true);\n                    // Chiude il menu\n                    handleMenuClose(setPosaAnchorEl);\n                    // Naviga alla pagina visualizza cavi\n                    navigateTo('/dashboard/cavi/visualizza');\n                  }}>Aggiungi nuovo cavo</MenuItem>\n                  <MenuItem onClick={() => {\n                    // Apre il dialogo di eliminazione cavi invece di navigare alla pagina\n                    setOpenEliminaCavoDialog(true);\n                    // Chiude il menu\n                    handleMenuClose(setPosaAnchorEl);\n                    // Naviga alla pagina visualizza cavi\n                    navigateTo('/dashboard/cavi/visualizza');\n                  }}>Elimina cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/modifica-bobina')}>Modifica bobina cavo posato</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/collegamenti')}>Gestisci collegamenti cavo</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Parco Cavi */}\n                <Menu\n                  id=\"parco-menu\"\n                  anchorEl={parcoAnchorEl}\n                  keepMounted\n                  open={Boolean(parcoAnchorEl)}\n                  onClose={() => handleMenuClose(setParcoAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/storico')}>Visualizza Storico Utilizzo</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Gestione Excel */}\n                <Menu\n                  id=\"excel-menu\"\n                  anchorEl={excelAnchorEl}\n                  keepMounted\n                  open={Boolean(excelAnchorEl)}\n                  onClose={() => handleMenuClose(setExcelAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => handleOpenExcelPopup('importaCavi')}>\n                    <FileUploadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Importa Cavi\n                  </MenuItem>\n                  <MenuItem onClick={() => handleOpenExcelPopup('importaParcoBobine')}>\n                    <FileUploadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Importa Parco Bobine\n                  </MenuItem>\n                  <Divider />\n                  <MenuItem onClick={() => handleCreateTemplateDirect('cavi')}>\n                    <FileDownloadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Template Cavi\n                  </MenuItem>\n                  <MenuItem onClick={() => handleCreateTemplateDirect('parco-bobine')}>\n                    <FileDownloadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Template Parco Bobine\n                  </MenuItem>\n                  <Divider />\n                  <MenuItem onClick={() => handleOpenExcelPopup('esportaCavi')}>\n                    <FileDownloadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Esporta Cavi\n                  </MenuItem>\n                  <MenuItem onClick={() => handleOpenExcelPopup('esportaParcoBobine')}>\n                    <FileDownloadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Esporta Parco Bobine\n                  </MenuItem>\n                </Menu>\n\n\n\n                {/* Sottomenu Certificazione Cavi */}\n                <Menu\n                  id=\"certificazione-menu\"\n                  anchorEl={certificazioneAnchorEl}\n                  keepMounted\n                  open={Boolean(certificazioneAnchorEl)}\n                  onClose={() => handleMenuClose(setCertificazioneAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/visualizza')}>Visualizza certificazioni</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/filtra')}>Filtra per cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/crea')}>Crea certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/modifica')}>Modifica certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/elimina')}>Elimina certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/strumenti')}>Gestione strumenti</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Gestione Comande */}\n                <Menu\n                  id=\"comande-menu\"\n                  anchorEl={comandeAnchorEl}\n                  keepMounted\n                  open={Boolean(comandeAnchorEl)}\n                  onClose={() => handleMenuClose(setComandeAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/visualizza')}>Visualizza comande</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/crea')}>Crea nuova comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/modifica')}>Modifica comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/elimina')}>Elimina comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/stampa')}>Stampa comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/assegna')}>Assegna comanda a cavo</MenuItem>\n                </Menu>\n              </>\n            )}\n          </>\n        )}\n\n        {/* Spacer */}\n        <Box sx={{ flexGrow: 1 }} />\n\n        {/* Informazioni utente e logout */}\n        <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>\n          {/* Mostra il cantiere selezionato */}\n          <SelectedCantiereDisplay />\n\n          {isImpersonating && impersonatedUser && (\n            <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mr: 2.5, fontSize: '1rem' }}>\n              Accesso come: <b>{impersonatedUser.username}</b>\n            </Typography>\n          )}\n          <Typography variant=\"body2\" sx={{ mr: 2.5, fontWeight: 500, fontSize: '1rem' }}>\n            {user?.username || ''}\n          </Typography>\n          <Tooltip title=\"Logout\">\n            <IconButton\n              color=\"inherit\"\n              onClick={handleLogout}\n              edge=\"end\"\n              sx={{ '&:hover': { backgroundColor: '#e9ecef' }, padding: '10px' }}\n            >\n              <LogoutIcon fontSize=\"medium\" />\n            </IconButton>\n          </Tooltip>\n        </Box>\n      </Toolbar>\n\n      {/* Excel Popup */}\n      <ExcelPopup\n        open={excelPopupOpen}\n        onClose={handleCloseExcelPopup}\n        operationType={excelOperationType}\n        cantiereId={cantiereId}\n        onSuccess={handleExcelSuccess}\n        onError={handleExcelError}\n      />\n\n      {/* Snackbar per messaggi di successo/errore */}\n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={6000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert\n          onClose={handleCloseSnackbar}\n          severity={snackbar.severity}\n          variant=\"filled\"\n          sx={{ width: '100%' }}\n        >\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n    </AppBar>\n  );\n};\n\nexport default TopNavbar;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OACEC,MAAM,CACNC,OAAO,CACPC,GAAG,CACHC,MAAM,CACNC,IAAI,CACJC,QAAQ,CACRC,UAAU,CACVC,UAAU,CACVC,OAAO,CACPC,MAAM,CACNC,OAAO,CACPC,QAAQ,CACRC,KAAK,KACA,eAAe,CACtB,OACEC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,kBAAkB,GAAI,CAAAC,SAAS,CAC/BC,YAAY,GAAI,CAAAC,gBAAgB,CAChCC,KAAK,GAAI,CAAAC,SAAS,CAClBC,WAAW,GAAI,CAAAC,UAAU,CACzBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,iBAAiB,GAAI,CAAAC,aAAa,CAClCC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,YAAY,GAAI,CAAAC,gBAAgB,KAC3B,qBAAqB,CAC5B,OAASC,OAAO,KAAQ,wBAAwB,CAChD,OAASC,gBAAgB,KAAQ,0BAA0B,CAC3D,MAAO,CAAAC,uBAAuB,KAAM,kCAAkC,CACtE,MAAO,CAAAC,UAAU,KAAM,mBAAmB,CAC1C,MAAO,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEzB,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAAAC,QAAQ,CAAG5C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA6C,QAAQ,CAAG5C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAE6C,IAAI,CAAEC,MAAM,CAAEC,eAAe,CAAEC,gBAAiB,CAAC,CAAGhB,OAAO,CAAC,CAAC,CACrE,KAAM,CAAEiB,wBAAwB,CAAEC,yBAAyB,CAAEC,yBAA0B,CAAC,CAAGlB,gBAAgB,CAAC,CAAC,CAE7G;AACA,KAAM,CAACmB,cAAc,CAAEC,iBAAiB,CAAC,CAAGvD,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAACwD,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGzD,QAAQ,CAAC,EAAE,CAAC,CAEhE;AACA,KAAM,CAAC0D,QAAQ,CAAEC,WAAW,CAAC,CAAG3D,QAAQ,CAAC,CACvC4D,IAAI,CAAE,KAAK,CACXC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,SACZ,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,UAAU,CAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAE,EAAE,CAAC,CAE3E;AACA,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGpE,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACqE,aAAa,CAAEC,gBAAgB,CAAC,CAAGtE,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAACuE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGxE,QAAQ,CAAC,IAAI,CAAC,CAC9D,KAAM,CAACyE,YAAY,CAAEC,eAAe,CAAC,CAAG1E,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAAC2E,YAAY,CAAEC,eAAe,CAAC,CAAG5E,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAAC6E,aAAa,CAAEC,gBAAgB,CAAC,CAAG9E,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAAC+E,aAAa,CAAEC,gBAAgB,CAAC,CAAGhF,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAACiF,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGlF,QAAQ,CAAC,IAAI,CAAC,CAC1E,KAAM,CAACmF,eAAe,CAAEC,kBAAkB,CAAC,CAAGpF,QAAQ,CAAC,IAAI,CAAC,CAE5D;AACA,KAAM,CAAAqF,kBAAkB,CAAGpB,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CACrE,KAAM,CAAAoB,oBAAoB,CAAGrB,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAEzE;AACA,KAAM,CAAAqB,cAAc,CAAGA,CAACC,KAAK,CAAEC,WAAW,GAAK,CAC7CA,WAAW,CAACD,KAAK,CAACE,aAAa,CAAC,CAClC,CAAC,CAED,KAAM,CAAAC,eAAe,CAAIF,WAAW,EAAK,CACvCA,WAAW,CAAC,IAAI,CAAC,CACnB,CAAC,CAED;AACA,KAAM,CAAAG,oBAAoB,CAAIC,aAAa,EAAK,CAC9CpC,qBAAqB,CAACoC,aAAa,CAAC,CACpCtC,iBAAiB,CAAC,IAAI,CAAC,CACvBoC,eAAe,CAACX,gBAAgB,CAAC,CACnC,CAAC,CAED;AACA,KAAM,CAAAc,0BAA0B,CAAG,KAAO,CAAAC,YAAY,EAAK,CACzD,GAAI,CACFJ,eAAe,CAACX,gBAAgB,CAAC,CAEjC,GAAIe,YAAY,GAAK,MAAM,CAAE,CAC3B,KAAM,CAAAC,YAAY,CAAG,KAAM,OAAM,CAAC,0BAA0B,CAAC,CAC7D,KAAM,CAAAA,YAAY,CAACC,OAAO,CAACC,kBAAkB,CAAC,CAAC,CAC/CvC,WAAW,CAAC,CACVC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,oGAAoG,CAC7GC,QAAQ,CAAE,SACZ,CAAC,CAAC,CACJ,CAAC,IAAM,IAAIiC,YAAY,GAAK,cAAc,CAAE,CAC1C,KAAM,CAAAC,YAAY,CAAG,KAAM,OAAM,CAAC,0BAA0B,CAAC,CAC7D,KAAM,CAAAA,YAAY,CAACC,OAAO,CAACE,yBAAyB,CAAC,CAAC,CACtDxC,WAAW,CAAC,CACVC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,4GAA4G,CACrHC,QAAQ,CAAE,SACZ,CAAC,CAAC,CACJ,CACF,CAAE,MAAOsC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uCAAuCL,YAAY,GAAG,CAAEK,KAAK,CAAC,CAC5EzC,WAAW,CAAC,CACVC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,uCAAuCkC,YAAY,KAAKK,KAAK,CAACvC,OAAO,EAAI,oBAAoB,EAAE,CACxGC,QAAQ,CAAE,OACZ,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAwC,qBAAqB,CAAGA,CAAA,GAAM,CAClC/C,iBAAiB,CAAC,KAAK,CAAC,CAC1B,CAAC,CAED;AACA,KAAM,CAAAgD,kBAAkB,CAAI1C,OAAO,EAAK,CACtCF,WAAW,CAAC,CACVC,IAAI,CAAE,IAAI,CACVC,OAAO,CACPC,QAAQ,CAAE,SACZ,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAA0C,gBAAgB,CAAI3C,OAAO,EAAK,CACpCF,WAAW,CAAC,CACVC,IAAI,CAAE,IAAI,CACVC,OAAO,CACPC,QAAQ,CAAE,OACZ,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAA2C,mBAAmB,CAAGA,CAAA,GAAM,CAChC9C,WAAW,CAAC,CACV,GAAGD,QAAQ,CACXE,IAAI,CAAE,KACR,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAA8C,UAAU,CAAIC,IAAI,EAAK,CAC3B;AACA,GAAIA,IAAI,GAAK,YAAY,CAAE,CACzB;AACA,GAAI1D,eAAe,CAAE,CACnBJ,QAAQ,CAAC,kBAAkB,CAAC,CAC9B,CACA;AAAA,IACK,IAAI,CAAAE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE6D,IAAI,IAAK,OAAO,CAAE,CAC/B/D,QAAQ,CAAC,kBAAkB,CAAC,CAC9B,CACA;AAAA,IACK,IAAI,CAAAE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE6D,IAAI,IAAK,MAAM,CAAE,CAC9B/D,QAAQ,CAAC,qBAAqB,CAAC,CACjC,CACA;AAAA,IACK,IAAI,CAAAE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE6D,IAAI,IAAK,eAAe,CAAE,CACvC;AACA/D,QAAQ,CAAC,4BAA4B,CAAC,CACxC,CACA;AAAA,IACK,CACHA,QAAQ,CAAC8D,IAAI,CAAC,CAChB,CACF,CAAC,IAAM,CACL9D,QAAQ,CAAC8D,IAAI,CAAC,CAChB,CAEA;AACAhB,eAAe,CAACvB,eAAe,CAAC,CAChCuB,eAAe,CAACrB,gBAAgB,CAAC,CACjCqB,eAAe,CAACnB,mBAAmB,CAAC,CACpCmB,eAAe,CAACjB,eAAe,CAAC,CAChCiB,eAAe,CAACf,eAAe,CAAC,CAChCe,eAAe,CAACb,gBAAgB,CAAC,CACjCa,eAAe,CAACX,gBAAgB,CAAC,CACjCW,eAAe,CAACT,yBAAyB,CAAC,CAC1CS,eAAe,CAACP,kBAAkB,CAAC,CACrC,CAAC,CAED,KAAM,CAAAyB,YAAY,CAAGA,CAAA,GAAM,CACzB7D,MAAM,CAAC,CAAC,CACV,CAAC,CAED;AACA,KAAM,CAAA8D,QAAQ,CAAIH,IAAI,EAAK,CACzB,MAAO,CAAA7D,QAAQ,CAACiE,QAAQ,GAAKJ,IAAI,CACnC,CAAC,CAED;AACA,KAAM,CAAAK,cAAc,CAAIL,IAAI,EAAK,CAC/B,MAAO,CAAA7D,QAAQ,CAACiE,QAAQ,CAACE,UAAU,CAACN,IAAI,CAAC,CAC3C,CAAC,CAED,mBACElE,KAAA,CAACtC,MAAM,EAAC+G,QAAQ,CAAC,QAAQ,CAACC,KAAK,CAAC,SAAS,CAACC,SAAS,CAAE,CAAE,CAACC,EAAE,CAAE,CAAEC,MAAM,CAAE,IAAI,CAAEC,KAAK,CAAE,MAAM,CAAEC,SAAS,CAAE,QAAS,CAAE,CAACC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC5IjF,KAAA,CAACrC,OAAO,EAACiH,EAAE,CAAE,CAAEG,SAAS,CAAE,QAAQ,CAAEG,MAAM,CAAE,MAAO,CAAE,CAAAD,QAAA,eAEnDnF,IAAA,CAACjC,MAAM,EACL6G,KAAK,CAAC,SAAS,CACfS,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,YAAY,CAAE,CACxCmB,SAAS,cAAEtF,IAAA,CAACtB,QAAQ,GAAE,CAAE,CACxBoG,EAAE,CAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CACdL,SAAS,CAAEX,QAAQ,CAAC,YAAY,CAAC,CAAG,eAAe,CAAG,EAAG,CAAAY,QAAA,CAExDzE,eAAe,CAAG,qBAAqB,CACvC,CAAAF,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE6D,IAAI,IAAK,OAAO,CAAG,gBAAgB,CACzC,CAAA7D,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE6D,IAAI,IAAK,MAAM,CAAG,gBAAgB,CACxC,CAAA7D,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE6D,IAAI,IAAK,eAAe,CAAG,eAAe,CAAG,MAAM,CACpD,CAAC,cACTrE,IAAA,CAAC5B,OAAO,EAACoH,WAAW,CAAC,UAAU,CAACC,QAAQ,MAACX,EAAE,CAAE,CAAEY,EAAE,CAAE,GAAI,CAAE,CAAE,CAAC,CAK3D,CAAC,CAAAlF,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE6D,IAAI,IAAK,OAAO,EAAK,CAAA7D,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE6D,IAAI,IAAK,OAAO,EAAI3D,eAAe,EAAIC,gBAAiB,gBACzFT,KAAA,CAAAE,SAAA,EAAA+E,QAAA,EAEGzE,eAAe,eACdV,IAAA,CAACjC,MAAM,EACL6G,KAAK,CAAC,SAAS,CACfS,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,qBAAqB,CAAE,CACjDW,EAAE,CAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CACdL,SAAS,CAAEX,QAAQ,CAAC,qBAAqB,CAAC,CAAG,eAAe,CAAG,EAAG,CAAAY,QAAA,CAEjEzE,eAAe,EAAIC,gBAAgB,CAAG,eAAeA,gBAAgB,CAACgF,QAAQ,EAAE,CAAG,gBAAgB,CAC9F,CACT,CAKA7C,kBAAkB,eACjB5C,KAAA,CAAAE,SAAA,EAAA+E,QAAA,EAEG,CAAA3E,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE6D,IAAI,IAAK,eAAe,eAC7BrE,IAAA,CAACjC,MAAM,EACL6G,KAAK,CAAC,SAAS,CACfS,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,4BAA4B,CAAE,CACxDW,EAAE,CAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CACdL,SAAS,CAAEX,QAAQ,CAAC,4BAA4B,CAAC,CAAG,eAAe,CAAG,EAAG,CAAAY,QAAA,CAC1E,iBAED,CAAQ,CACT,cAGDnF,IAAA,CAACjC,MAAM,EACL6G,KAAK,CAAC,SAAS,CACf,gBAAc,WAAW,CACzB,gBAAc,MAAM,CACpBS,OAAO,CAAGO,CAAC,EAAK5C,cAAc,CAAC4C,CAAC,CAAEvD,eAAe,CAAE,CACnDwD,OAAO,cAAE7F,IAAA,CAACV,aAAa,GAAE,CAAE,CAC3BwF,EAAE,CAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CACdL,SAAS,CAAET,cAAc,CAAC,sBAAsB,CAAC,CAAG,eAAe,CAAG,EAAG,CAAAU,QAAA,CAC1E,qBAED,CAAQ,CAAC,cAGTnF,IAAA,CAACjC,MAAM,EACL6G,KAAK,CAAC,SAAS,CACf,gBAAc,YAAY,CAC1B,gBAAc,MAAM,CACpBS,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,kCAAkC,CAAE,CAC9DW,EAAE,CAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CACdL,SAAS,CAAET,cAAc,CAAC,uBAAuB,CAAC,CAAG,eAAe,CAAG,EAAG,CAAAU,QAAA,CAC3E,YAED,CAAQ,CAAC,cAGTnF,IAAA,CAACjC,MAAM,EACL6G,KAAK,CAAC,SAAS,CACf,gBAAc,YAAY,CAC1B,gBAAc,MAAM,CACpBS,OAAO,CAAGO,CAAC,EAAK5C,cAAc,CAAC4C,CAAC,CAAEnD,gBAAgB,CAAE,CACpDoD,OAAO,cAAE7F,IAAA,CAACV,aAAa,GAAE,CAAE,CAC3BwF,EAAE,CAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CACdL,SAAS,CAAET,cAAc,CAAC,uBAAuB,CAAC,CAAG,eAAe,CAAG,EAAG,CAAAU,QAAA,CAC3E,gBAED,CAAQ,CAAC,cAGTnF,IAAA,CAACjC,MAAM,EACL6G,KAAK,CAAC,SAAS,CACfS,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,mBAAmBrB,kBAAkB,SAAS,CAAE,CAC1EgC,EAAE,CAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CACdL,SAAS,CAAET,cAAc,CAAC,wBAAwB,CAAC,CAAG,eAAe,CAAG,EAAG,CAAAU,QAAA,CAC5E,QAED,CAAQ,CAAC,cAGTnF,IAAA,CAACjC,MAAM,EACL6G,KAAK,CAAC,SAAS,CACf,gBAAc,qBAAqB,CACnC,gBAAc,MAAM,CACpBS,OAAO,CAAGO,CAAC,EAAK5C,cAAc,CAAC4C,CAAC,CAAEjD,yBAAyB,CAAE,CAC7DkD,OAAO,cAAE7F,IAAA,CAACV,aAAa,GAAE,CAAE,CAC3BwF,EAAE,CAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CACdL,SAAS,CAAET,cAAc,CAAC,gCAAgC,CAAC,CAAG,eAAe,CAAG,EAAG,CAAAU,QAAA,CACpF,qBAED,CAAQ,CAAC,cAGTnF,IAAA,CAACjC,MAAM,EACL6G,KAAK,CAAC,SAAS,CACf,gBAAc,cAAc,CAC5B,gBAAc,MAAM,CACpBS,OAAO,CAAGO,CAAC,EAAK5C,cAAc,CAAC4C,CAAC,CAAE/C,kBAAkB,CAAE,CACtDgD,OAAO,cAAE7F,IAAA,CAACV,aAAa,GAAE,CAAE,CAC3BwF,EAAE,CAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CACdL,SAAS,CAAET,cAAc,CAAC,yBAAyB,CAAC,CAAG,eAAe,CAAG,EAAG,CAAAU,QAAA,CAC7E,kBAED,CAAQ,CAAC,cAGTjF,KAAA,CAAClC,IAAI,EACH8H,EAAE,CAAC,WAAW,CACdC,QAAQ,CAAE3D,YAAa,CACvB4D,WAAW,MACX3E,IAAI,CAAE4E,OAAO,CAAC7D,YAAY,CAAE,CAC5B8D,OAAO,CAAEA,CAAA,GAAM9C,eAAe,CAACf,eAAe,CAAE,CAChD8D,YAAY,CAAE,CACZC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,QACd,CAAE,CACFC,eAAe,CAAE,CACfF,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,QACd,CAAE,CACFnB,SAAS,CAAC,qBAAqB,CAC/BL,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CAAEyB,EAAE,CAAE,GAAI,CAAE,CAAApB,QAAA,eAEhBnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,sCAAsC,CAAE,CAAAgB,QAAA,CAAC,wBAAsB,CAAU,CAAC,cAC9GnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAM,CACvB;AACAxE,yBAAyB,CAAC,IAAI,CAAC,CAC/B;AACAuC,eAAe,CAACf,eAAe,CAAC,CAChC;AACA8B,UAAU,CAAC,4BAA4B,CAAC,CAC1C,CAAE,CAAAgB,QAAA,CAAC,eAAa,CAAU,CAAC,cAC3BnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAM,CACvB;AACAvE,yBAAyB,CAAC,IAAI,CAAC,CAC/B;AACAsC,eAAe,CAACf,eAAe,CAAC,CAChC;AACA8B,UAAU,CAAC,4BAA4B,CAAC,CAC1C,CAAE,CAAAgB,QAAA,CAAC,qBAAmB,CAAU,CAAC,cACjCnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAM,CACvB;AACAzE,wBAAwB,CAAC,IAAI,CAAC,CAC9B;AACAwC,eAAe,CAACf,eAAe,CAAC,CAChC;AACA8B,UAAU,CAAC,4BAA4B,CAAC,CAC1C,CAAE,CAAAgB,QAAA,CAAC,cAAY,CAAU,CAAC,cAC1BnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,sCAAsC,CAAE,CAAAgB,QAAA,CAAC,6BAA2B,CAAU,CAAC,cACnHnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,mCAAmC,CAAE,CAAAgB,QAAA,CAAC,4BAA0B,CAAU,CAAC,EAC3G,CAAC,cAGPnF,IAAA,CAAChC,IAAI,EACH8H,EAAE,CAAC,YAAY,CACfC,QAAQ,CAAEzD,aAAc,CACxB0D,WAAW,MACX3E,IAAI,CAAE4E,OAAO,CAAC3D,aAAa,CAAE,CAC7B4D,OAAO,CAAEA,CAAA,GAAM9C,eAAe,CAACb,gBAAgB,CAAE,CACjD4D,YAAY,CAAE,CACZC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,QACd,CAAE,CACFC,eAAe,CAAE,CACfF,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,QACd,CAAE,CACFnB,SAAS,CAAC,qBAAqB,CAC/BL,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CAAEyB,EAAE,CAAE,GAAI,CAAE,CAAApB,QAAA,cAEhBnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,+BAA+B,CAAE,CAAAgB,QAAA,CAAC,6BAA2B,CAAU,CAAC,CACxG,CAAC,cAGPjF,KAAA,CAAClC,IAAI,EACH8H,EAAE,CAAC,YAAY,CACfC,QAAQ,CAAEvD,aAAc,CACxBwD,WAAW,MACX3E,IAAI,CAAE4E,OAAO,CAACzD,aAAa,CAAE,CAC7B0D,OAAO,CAAEA,CAAA,GAAM9C,eAAe,CAACX,gBAAgB,CAAE,CACjD0D,YAAY,CAAE,CACZC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,QACd,CAAE,CACFC,eAAe,CAAE,CACfF,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,QACd,CAAE,CACFnB,SAAS,CAAC,qBAAqB,CAC/BL,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CAAEyB,EAAE,CAAE,GAAI,CAAE,CAAApB,QAAA,eAEhBjF,KAAA,CAACjC,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMhC,oBAAoB,CAAC,aAAa,CAAE,CAAA8B,QAAA,eAC3DnF,IAAA,CAACR,cAAc,EAACgH,QAAQ,CAAC,OAAO,CAAC1B,EAAE,CAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,eAEpD,EAAU,CAAC,cACXrF,KAAA,CAACjC,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMhC,oBAAoB,CAAC,oBAAoB,CAAE,CAAA8B,QAAA,eAClEnF,IAAA,CAACR,cAAc,EAACgH,QAAQ,CAAC,OAAO,CAAC1B,EAAE,CAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,uBAEpD,EAAU,CAAC,cACXvF,IAAA,CAAC5B,OAAO,GAAE,CAAC,cACX8B,KAAA,CAACjC,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAM9B,0BAA0B,CAAC,MAAM,CAAE,CAAA4B,QAAA,eAC1DnF,IAAA,CAACN,gBAAgB,EAAC8G,QAAQ,CAAC,OAAO,CAAC1B,EAAE,CAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,gBAEtD,EAAU,CAAC,cACXrF,KAAA,CAACjC,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAM9B,0BAA0B,CAAC,cAAc,CAAE,CAAA4B,QAAA,eAClEnF,IAAA,CAACN,gBAAgB,EAAC8G,QAAQ,CAAC,OAAO,CAAC1B,EAAE,CAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,wBAEtD,EAAU,CAAC,cACXvF,IAAA,CAAC5B,OAAO,GAAE,CAAC,cACX8B,KAAA,CAACjC,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMhC,oBAAoB,CAAC,aAAa,CAAE,CAAA8B,QAAA,eAC3DnF,IAAA,CAACN,gBAAgB,EAAC8G,QAAQ,CAAC,OAAO,CAAC1B,EAAE,CAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,eAEtD,EAAU,CAAC,cACXrF,KAAA,CAACjC,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMhC,oBAAoB,CAAC,oBAAoB,CAAE,CAAA8B,QAAA,eAClEnF,IAAA,CAACN,gBAAgB,EAAC8G,QAAQ,CAAC,OAAO,CAAC1B,EAAE,CAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,uBAEtD,EAAU,CAAC,EACP,CAAC,cAKPrF,KAAA,CAAClC,IAAI,EACH8H,EAAE,CAAC,qBAAqB,CACxBC,QAAQ,CAAErD,sBAAuB,CACjCsD,WAAW,MACX3E,IAAI,CAAE4E,OAAO,CAACvD,sBAAsB,CAAE,CACtCwD,OAAO,CAAEA,CAAA,GAAM9C,eAAe,CAACT,yBAAyB,CAAE,CAC1DwD,YAAY,CAAE,CACZC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,QACd,CAAE,CACFC,eAAe,CAAE,CACfF,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,QACd,CAAE,CACFnB,SAAS,CAAC,qBAAqB,CAC/BL,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CAAEyB,EAAE,CAAE,GAAI,CAAE,CAAApB,QAAA,eAEhBnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,2CAA2C,CAAE,CAAAgB,QAAA,CAAC,2BAAyB,CAAU,CAAC,cACtHnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,uCAAuC,CAAE,CAAAgB,QAAA,CAAC,iBAAe,CAAU,CAAC,cACxGnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,qCAAqC,CAAE,CAAAgB,QAAA,CAAC,qBAAmB,CAAU,CAAC,cAC1GnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,yCAAyC,CAAE,CAAAgB,QAAA,CAAC,yBAAuB,CAAU,CAAC,cAClHnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,wCAAwC,CAAE,CAAAgB,QAAA,CAAC,wBAAsB,CAAU,CAAC,cAChHnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,0CAA0C,CAAE,CAAAgB,QAAA,CAAC,oBAAkB,CAAU,CAAC,EAC1G,CAAC,cAGPjF,KAAA,CAAClC,IAAI,EACH8H,EAAE,CAAC,cAAc,CACjBC,QAAQ,CAAEnD,eAAgB,CAC1BoD,WAAW,MACX3E,IAAI,CAAE4E,OAAO,CAACrD,eAAe,CAAE,CAC/BsD,OAAO,CAAEA,CAAA,GAAM9C,eAAe,CAACP,kBAAkB,CAAE,CACnDsD,YAAY,CAAE,CACZC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,QACd,CAAE,CACFC,eAAe,CAAE,CACfF,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,QACd,CAAE,CACFnB,SAAS,CAAC,qBAAqB,CAC/BL,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CAAEyB,EAAE,CAAE,GAAI,CAAE,CAAApB,QAAA,eAEhBnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,oCAAoC,CAAE,CAAAgB,QAAA,CAAC,oBAAkB,CAAU,CAAC,cACxGnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,8BAA8B,CAAE,CAAAgB,QAAA,CAAC,oBAAkB,CAAU,CAAC,cAClGnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,kCAAkC,CAAE,CAAAgB,QAAA,CAAC,kBAAgB,CAAU,CAAC,cACpGnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,iCAAiC,CAAE,CAAAgB,QAAA,CAAC,iBAAe,CAAU,CAAC,cAClGnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,gCAAgC,CAAE,CAAAgB,QAAA,CAAC,gBAAc,CAAU,CAAC,cAChGnF,IAAA,CAAC/B,QAAQ,EAACoH,OAAO,CAAEA,CAAA,GAAMlB,UAAU,CAAC,iCAAiC,CAAE,CAAAgB,QAAA,CAAC,wBAAsB,CAAU,CAAC,EACrG,CAAC,EACP,CACH,EACD,CACH,cAGDnF,IAAA,CAAClC,GAAG,EAACgH,EAAE,CAAE,CAAE2B,QAAQ,CAAE,CAAE,CAAE,CAAE,CAAC,cAG5BvG,KAAA,CAACpC,GAAG,EAACgH,EAAE,CAAE,CAAE4B,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEvB,MAAM,CAAE,MAAO,CAAE,CAAAD,QAAA,eAEjEnF,IAAA,CAACH,uBAAuB,GAAE,CAAC,CAE1Ba,eAAe,EAAIC,gBAAgB,eAClCT,KAAA,CAAChC,UAAU,EAAC0I,OAAO,CAAC,OAAO,CAAChC,KAAK,CAAC,eAAe,CAACE,EAAE,CAAE,CAAES,EAAE,CAAE,GAAG,CAAEiB,QAAQ,CAAE,MAAO,CAAE,CAAArB,QAAA,EAAC,gBACrE,cAAAnF,IAAA,MAAAmF,QAAA,CAAIxE,gBAAgB,CAACgF,QAAQ,CAAI,CAAC,EACtC,CACb,cACD3F,IAAA,CAAC9B,UAAU,EAAC0I,OAAO,CAAC,OAAO,CAAC9B,EAAE,CAAE,CAAES,EAAE,CAAE,GAAG,CAAEsB,UAAU,CAAE,GAAG,CAAEL,QAAQ,CAAE,MAAO,CAAE,CAAArB,QAAA,CAC5E,CAAA3E,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEmF,QAAQ,GAAI,EAAE,CACX,CAAC,cACb3F,IAAA,CAAC1B,OAAO,EAACwI,KAAK,CAAC,QAAQ,CAAA3B,QAAA,cACrBnF,IAAA,CAAC7B,UAAU,EACTyG,KAAK,CAAC,SAAS,CACfS,OAAO,CAAEf,YAAa,CACtByC,IAAI,CAAC,KAAK,CACVjC,EAAE,CAAE,CAAE,SAAS,CAAE,CAAEkC,eAAe,CAAE,SAAU,CAAC,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAA9B,QAAA,cAEnEnF,IAAA,CAACZ,UAAU,EAACoH,QAAQ,CAAC,QAAQ,CAAE,CAAC,CACtB,CAAC,CACN,CAAC,EACP,CAAC,EACC,CAAC,cAGVxG,IAAA,CAACF,UAAU,EACTuB,IAAI,CAAEN,cAAe,CACrBmF,OAAO,CAAEnC,qBAAsB,CAC/BT,aAAa,CAAErC,kBAAmB,CAClCO,UAAU,CAAEA,UAAW,CACvB0F,SAAS,CAAElD,kBAAmB,CAC9BmD,OAAO,CAAElD,gBAAiB,CAC3B,CAAC,cAGFjE,IAAA,CAACzB,QAAQ,EACP8C,IAAI,CAAEF,QAAQ,CAACE,IAAK,CACpB+F,gBAAgB,CAAE,IAAK,CACvBlB,OAAO,CAAEhC,mBAAoB,CAC7BiC,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAlB,QAAA,cAE3DnF,IAAA,CAACxB,KAAK,EACJ0H,OAAO,CAAEhC,mBAAoB,CAC7B3C,QAAQ,CAAEJ,QAAQ,CAACI,QAAS,CAC5BqF,OAAO,CAAC,QAAQ,CAChB9B,EAAE,CAAE,CAAEE,KAAK,CAAE,MAAO,CAAE,CAAAG,QAAA,CAErBhE,QAAQ,CAACG,OAAO,CACZ,CAAC,CACA,CAAC,EACL,CAAC,CAEb,CAAC,CAED,cAAe,CAAAjB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}