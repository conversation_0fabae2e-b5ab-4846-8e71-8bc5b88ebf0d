{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"P\", \"M\"],\n  abbreviated: [\"PK\", \"MK\"],\n  wide: [\"Para Krishtit\", \"<PERSON>bas Krishtit\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"4-mujori I\", \"4-mujori II\", \"4-mujori III\", \"4-mujori IV\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"J\", \"S\", \"M\", \"P\", \"M\", \"Q\", \"K\", \"G\", \"S\", \"T\", \"N\", \"D\"],\n  abbreviated: [\"<PERSON>\", \"Shk\", \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>r\", \"<PERSON>\", \"<PERSON>ht\", \"Te<PERSON>\", \"Nën\", \"Dhj\"],\n  wide: [\"Janar\", \"Shkurt\", \"Mars\", \"Prill\", \"Maj\", \"Qershor\", \"Korrik\", \"Gusht\", \"Shtator\", \"Tetor\", \"Nëntor\", \"Dhjetor\"]\n};\nconst dayValues = {\n  narrow: [\"D\", \"H\", \"M\", \"M\", \"E\", \"P\", \"S\"],\n  short: [\"Di\", \"Hë\", \"Ma\", \"Më\", \"En\", \"Pr\", \"Sh\"],\n  abbreviated: [\"Die\", \"Hën\", \"Mar\", \"Mër\", \"Enj\", \"Pre\", \"Sht\"],\n  wide: [\"Dielë\", \"Hënë\", \"Martë\", \"Mërkurë\", \"Enjte\", \"Premte\", \"Shtunë\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"p\",\n    pm: \"m\",\n    midnight: \"m\",\n    noon: \"d\",\n    morning: \"mëngjes\",\n    afternoon: \"dite\",\n    evening: \"mbrëmje\",\n    night: \"natë\"\n  },\n  abbreviated: {\n    am: \"PD\",\n    pm: \"MD\",\n    midnight: \"mesnëtë\",\n    noon: \"drek\",\n    morning: \"mëngjes\",\n    afternoon: \"mbasdite\",\n    evening: \"mbrëmje\",\n    night: \"natë\"\n  },\n  wide: {\n    am: \"p.d.\",\n    pm: \"m.d.\",\n    midnight: \"mesnëtë\",\n    noon: \"drek\",\n    morning: \"mëngjes\",\n    afternoon: \"mbasdite\",\n    evening: \"mbrëmje\",\n    night: \"natë\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"p\",\n    pm: \"m\",\n    midnight: \"m\",\n    noon: \"d\",\n    morning: \"në mëngjes\",\n    afternoon: \"në mbasdite\",\n    evening: \"në mbrëmje\",\n    night: \"në mesnatë\"\n  },\n  abbreviated: {\n    am: \"PD\",\n    pm: \"MD\",\n    midnight: \"mesnatë\",\n    noon: \"drek\",\n    morning: \"në mëngjes\",\n    afternoon: \"në mbasdite\",\n    evening: \"në mbrëmje\",\n    night: \"në mesnatë\"\n  },\n  wide: {\n    am: \"p.d.\",\n    pm: \"m.d.\",\n    midnight: \"mesnatë\",\n    noon: \"drek\",\n    morning: \"në mëngjes\",\n    afternoon: \"në mbasdite\",\n    evening: \"në mbrëmje\",\n    night: \"në mesnatë\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  if (options?.unit === \"hour\") return String(number);\n  if (number === 1) return number + \"-rë\";\n  if (number === 4) return number + \"t\";\n  return number + \"-të\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/sq/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"P\", \"M\"],\n  abbreviated: [\"PK\", \"MK\"],\n  wide: [\"Para Krishtit\", \"<PERSON>bas <PERSON>htit\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"4-mujori I\", \"4-mujori II\", \"4-mujori III\", \"4-mujori IV\"],\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"J\", \"S\", \"M\", \"P\", \"M\", \"Q\", \"K\", \"G\", \"S\", \"T\", \"N\", \"D\"],\n  abbreviated: [\n    \"<PERSON>\",\n    \"Shk\",\n    \"<PERSON>\",\n    \"<PERSON><PERSON>\",\n    \"<PERSON>\",\n    \"<PERSON><PERSON>\",\n    \"<PERSON><PERSON>\",\n    \"<PERSON>\",\n    \"<PERSON>ht\",\n    \"<PERSON>t\",\n    \"Nën\",\n    \"Dhj\",\n  ],\n\n  wide: [\n    \"Janar\",\n    \"Shkurt\",\n    \"Mars\",\n    \"Prill\",\n    \"Maj\",\n    \"Qershor\",\n    \"Korrik\",\n    \"Gusht\",\n    \"Shtator\",\n    \"Tetor\",\n    \"Nëntor\",\n    \"Dhjetor\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"D\", \"H\", \"M\", \"M\", \"E\", \"P\", \"S\"],\n  short: [\"Di\", \"Hë\", \"Ma\", \"Më\", \"En\", \"Pr\", \"Sh\"],\n  abbreviated: [\"Die\", \"Hën\", \"Mar\", \"Mër\", \"Enj\", \"Pre\", \"Sht\"],\n  wide: [\"Dielë\", \"Hënë\", \"Martë\", \"Mërkurë\", \"Enjte\", \"Premte\", \"Shtunë\"],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"p\",\n    pm: \"m\",\n    midnight: \"m\",\n    noon: \"d\",\n    morning: \"mëngjes\",\n    afternoon: \"dite\",\n    evening: \"mbrëmje\",\n    night: \"natë\",\n  },\n  abbreviated: {\n    am: \"PD\",\n    pm: \"MD\",\n    midnight: \"mesnëtë\",\n    noon: \"drek\",\n    morning: \"mëngjes\",\n    afternoon: \"mbasdite\",\n    evening: \"mbrëmje\",\n    night: \"natë\",\n  },\n  wide: {\n    am: \"p.d.\",\n    pm: \"m.d.\",\n    midnight: \"mesnëtë\",\n    noon: \"drek\",\n    morning: \"mëngjes\",\n    afternoon: \"mbasdite\",\n    evening: \"mbrëmje\",\n    night: \"natë\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"p\",\n    pm: \"m\",\n    midnight: \"m\",\n    noon: \"d\",\n    morning: \"në mëngjes\",\n    afternoon: \"në mbasdite\",\n    evening: \"në mbrëmje\",\n    night: \"në mesnatë\",\n  },\n  abbreviated: {\n    am: \"PD\",\n    pm: \"MD\",\n    midnight: \"mesnatë\",\n    noon: \"drek\",\n    morning: \"në mëngjes\",\n    afternoon: \"në mbasdite\",\n    evening: \"në mbrëmje\",\n    night: \"në mesnatë\",\n  },\n  wide: {\n    am: \"p.d.\",\n    pm: \"m.d.\",\n    midnight: \"mesnatë\",\n    noon: \"drek\",\n    morning: \"në mëngjes\",\n    afternoon: \"në mbasdite\",\n    evening: \"në mbrëmje\",\n    night: \"në mesnatë\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n\n  if (options?.unit === \"hour\") return String(number);\n\n  if (number === 1) return number + \"-rë\";\n  if (number === 4) return number + \"t\";\n\n  return number + \"-të\";\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EAClBC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACzBC,IAAI,EAAE,CAAC,eAAe,EAAE,eAAe;AACzC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,aAAa;AACnE,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,OAAO,EACP,QAAQ,EACR,MAAM,EACN,OAAO,EACP,KAAK,EACL,SAAS,EACT,QAAQ,EACR,OAAO,EACP,SAAS,EACT,OAAO,EACP,QAAQ,EACR,SAAS;AAEb,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ;AACzE,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,YAAY;IACrBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,YAAY;IACrBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,YAAY;IACrBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,OAAO,KAAK;EAC9C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAElC,IAAIC,OAAO,EAAEG,IAAI,KAAK,MAAM,EAAE,OAAOC,MAAM,CAACH,MAAM,CAAC;EAEnD,IAAIA,MAAM,KAAK,CAAC,EAAE,OAAOA,MAAM,GAAG,KAAK;EACvC,IAAIA,MAAM,KAAK,CAAC,EAAE,OAAOA,MAAM,GAAG,GAAG;EAErC,OAAOA,MAAM,GAAG,KAAK;AACvB,CAAC;AAED,OAAO,MAAMI,QAAQ,GAAG;EACtBP,aAAa;EAEbQ,GAAG,EAAE3B,eAAe,CAAC;IACnB4B,MAAM,EAAE3B,SAAS;IACjB4B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE9B,eAAe,CAAC;IACvB4B,MAAM,EAAEvB,aAAa;IACrBwB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAEhC,eAAe,CAAC;IACrB4B,MAAM,EAAEtB,WAAW;IACnBuB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAEjC,eAAe,CAAC;IACnB4B,MAAM,EAAErB,SAAS;IACjBsB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAElC,eAAe,CAAC;IACzB4B,MAAM,EAAEnB,eAAe;IACvBoB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEjB,yBAAyB;IAC3CkB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}