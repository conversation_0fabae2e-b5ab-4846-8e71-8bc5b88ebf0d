{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\nconst eraValues = {\n  narrow: [\"AC\", \"DC\"],\n  abbreviated: [\"AC\", \"DC\"],\n  wide: [\"antes de cristo\", \"despois de cristo\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"T1\", \"T2\", \"T3\", \"T4\"],\n  wide: [\"1º trimestre\", \"2º trimestre\", \"3º trimestre\", \"4º trimestre\"]\n};\nconst monthValues = {\n  narrow: [\"e\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"],\n  abbreviated: [\"xan\", \"feb\", \"mar\", \"abr\", \"mai\", \"xun\", \"xul\", \"ago\", \"set\", \"out\", \"nov\", \"dec\"],\n  wide: [\"xaneiro\", \"febreiro\", \"marzo\", \"abril\", \"maio\", \"xuño\", \"xullo\", \"agosto\", \"setembro\", \"outubro\", \"novembro\", \"decembro\"]\n};\nconst dayValues = {\n  narrow: [\"d\", \"l\", \"m\", \"m\", \"j\", \"v\", \"s\"],\n  short: [\"do\", \"lu\", \"ma\", \"me\", \"xo\", \"ve\", \"sa\"],\n  abbreviated: [\"dom\", \"lun\", \"mar\", \"mer\", \"xov\", \"ven\", \"sab\"],\n  wide: [\"domingo\", \"luns\", \"martes\", \"mércores\", \"xoves\", \"venres\", \"sábado\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"mn\",\n    noon: \"md\",\n    morning: \"mañá\",\n    afternoon: \"tarde\",\n    evening: \"tarde\",\n    night: \"noite\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"medianoite\",\n    noon: \"mediodía\",\n    morning: \"mañá\",\n    afternoon: \"tarde\",\n    evening: \"tardiña\",\n    night: \"noite\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"medianoite\",\n    noon: \"mediodía\",\n    morning: \"mañá\",\n    afternoon: \"tarde\",\n    evening: \"tardiña\",\n    night: \"noite\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"mn\",\n    noon: \"md\",\n    morning: \"da mañá\",\n    afternoon: \"da tarde\",\n    evening: \"da tardiña\",\n    night: \"da noite\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"medianoite\",\n    noon: \"mediodía\",\n    morning: \"da mañá\",\n    afternoon: \"da tarde\",\n    evening: \"da tardiña\",\n    night: \"da noite\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"medianoite\",\n    noon: \"mediodía\",\n    morning: \"da mañá\",\n    afternoon: \"da tarde\",\n    evening: \"da tardiña\",\n    night: \"da noite\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \"º\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/gl/_lib/localize.mjs"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\nconst eraValues = {\n  narrow: [\"AC\", \"DC\"],\n  abbreviated: [\"AC\", \"DC\"],\n  wide: [\"antes de cristo\", \"despois de cristo\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"T1\", \"T2\", \"T3\", \"T4\"],\n  wide: [\"1º trimestre\", \"2º trimestre\", \"3º trimestre\", \"4º trimestre\"],\n};\n\nconst monthValues = {\n  narrow: [\"e\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"],\n  abbreviated: [\n    \"xan\",\n    \"feb\",\n    \"mar\",\n    \"abr\",\n    \"mai\",\n    \"xun\",\n    \"xul\",\n    \"ago\",\n    \"set\",\n    \"out\",\n    \"nov\",\n    \"dec\",\n  ],\n\n  wide: [\n    \"xaneiro\",\n    \"febreiro\",\n    \"marzo\",\n    \"abril\",\n    \"maio\",\n    \"xuño\",\n    \"xullo\",\n    \"agosto\",\n    \"setembro\",\n    \"outubro\",\n    \"novembro\",\n    \"decembro\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"d\", \"l\", \"m\", \"m\", \"j\", \"v\", \"s\"],\n  short: [\"do\", \"lu\", \"ma\", \"me\", \"xo\", \"ve\", \"sa\"],\n  abbreviated: [\"dom\", \"lun\", \"mar\", \"mer\", \"xov\", \"ven\", \"sab\"],\n  wide: [\"domingo\", \"luns\", \"martes\", \"mércores\", \"xoves\", \"venres\", \"sábado\"],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"mn\",\n    noon: \"md\",\n    morning: \"mañá\",\n    afternoon: \"tarde\",\n    evening: \"tarde\",\n    night: \"noite\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"medianoite\",\n    noon: \"mediodía\",\n    morning: \"mañá\",\n    afternoon: \"tarde\",\n    evening: \"tardiña\",\n    night: \"noite\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"medianoite\",\n    noon: \"mediodía\",\n    morning: \"mañá\",\n    afternoon: \"tarde\",\n    evening: \"tardiña\",\n    night: \"noite\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"mn\",\n    noon: \"md\",\n    morning: \"da mañá\",\n    afternoon: \"da tarde\",\n    evening: \"da tardiña\",\n    night: \"da noite\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"medianoite\",\n    noon: \"mediodía\",\n    morning: \"da mañá\",\n    afternoon: \"da tarde\",\n    evening: \"da tardiña\",\n    night: \"da noite\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"medianoite\",\n    noon: \"mediodía\",\n    morning: \"da mañá\",\n    afternoon: \"da tarde\",\n    evening: \"da tardiña\",\n    night: \"da noite\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \"º\";\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gCAAgC;AAEhE,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACpBC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACzBC,IAAI,EAAE,CAAC,iBAAiB,EAAE,mBAAmB;AAC/C,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc;AACvE,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,SAAS,EACT,UAAU,EACV,OAAO,EACP,OAAO,EACP,MAAM,EACN,MAAM,EACN,OAAO,EACP,QAAQ,EACR,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU;AAEd,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ;AAC7E,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,YAAY;IACrBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAClC,OAAOE,MAAM,GAAG,GAAG;AACrB,CAAC;AAED,OAAO,MAAME,QAAQ,GAAG;EACtBL,aAAa;EAEbM,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE/B,eAAe,CAAC;IACnB0B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAEhC,eAAe,CAAC;IACzB0B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEf,yBAAyB;IAC3CgB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}