{"ast": null, "code": "// Source: https://www.unicode.org/cldr/charts/32/summary/gu.html\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"હમણાં\",\n    // CLDR #1461\n    other: \"​આશરે {{count}} સેકંડ\"\n  },\n  xSeconds: {\n    one: \"1 સેકંડ\",\n    other: \"{{count}} સેકંડ\"\n  },\n  halfAMinute: \"અડધી મિનિટ\",\n  lessThanXMinutes: {\n    one: \"આ મિનિટ\",\n    // CLDR #1448\n    other: \"​આશરે {{count}} મિનિટ\"\n  },\n  xMinutes: {\n    one: \"1 મિનિટ\",\n    other: \"{{count}} મિનિટ\"\n  },\n  aboutXHours: {\n    one: \"​આશરે 1 કલાક\",\n    other: \"​આશરે {{count}} કલાક\"\n  },\n  xHours: {\n    one: \"1 કલાક\",\n    other: \"{{count}} કલાક\"\n  },\n  xDays: {\n    one: \"1 દિવસ\",\n    other: \"{{count}} દિવસ\"\n  },\n  aboutXWeeks: {\n    one: \"આશરે 1 અઠવાડિયું\",\n    other: \"આશરે {{count}} અઠવાડિયા\"\n  },\n  xWeeks: {\n    one: \"1 અઠવાડિયું\",\n    other: \"{{count}} અઠવાડિયા\"\n  },\n  aboutXMonths: {\n    one: \"આશરે 1 મહિનો\",\n    other: \"આશરે {{count}} મહિના\"\n  },\n  xMonths: {\n    one: \"1 મહિનો\",\n    other: \"{{count}} મહિના\"\n  },\n  aboutXYears: {\n    one: \"આશરે 1 વર્ષ\",\n    other: \"આશરે {{count}} વર્ષ\"\n  },\n  xYears: {\n    one: \"1 વર્ષ\",\n    other: \"{{count}} વર્ષ\"\n  },\n  overXYears: {\n    one: \"1 વર્ષથી વધુ\",\n    other: \"{{count}} વર્ષથી વધુ\"\n  },\n  almostXYears: {\n    one: \"લગભગ 1 વર્ષ\",\n    other: \"લગભગ {{count}} વર્ષ\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"માં\";\n    } else {\n      return result + \" પહેલાં\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/gu/_lib/formatDistance.js"], "sourcesContent": ["// Source: https://www.unicode.org/cldr/charts/32/summary/gu.html\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"હમણાં\", // CLDR #1461\n    other: \"​આશરે {{count}} સેકંડ\",\n  },\n\n  xSeconds: {\n    one: \"1 સેકંડ\",\n    other: \"{{count}} સેકંડ\",\n  },\n\n  halfAMinute: \"અડધી મિનિટ\",\n\n  lessThanXMinutes: {\n    one: \"આ મિનિટ\", // CLDR #1448\n    other: \"​આશરે {{count}} મિનિટ\",\n  },\n\n  xMinutes: {\n    one: \"1 મિનિટ\",\n    other: \"{{count}} મિનિટ\",\n  },\n\n  aboutXHours: {\n    one: \"​આશરે 1 કલાક\",\n    other: \"​આશરે {{count}} કલાક\",\n  },\n\n  xHours: {\n    one: \"1 કલાક\",\n    other: \"{{count}} કલાક\",\n  },\n\n  xDays: {\n    one: \"1 દિવસ\",\n    other: \"{{count}} દિવસ\",\n  },\n\n  aboutXWeeks: {\n    one: \"આશરે 1 અઠવાડિયું\",\n    other: \"આશરે {{count}} અઠવાડિયા\",\n  },\n\n  xWeeks: {\n    one: \"1 અઠવાડિયું\",\n    other: \"{{count}} અઠવાડિયા\",\n  },\n\n  aboutXMonths: {\n    one: \"આશરે 1 મહિનો\",\n    other: \"આશરે {{count}} મહિના\",\n  },\n\n  xMonths: {\n    one: \"1 મહિનો\",\n    other: \"{{count}} મહિના\",\n  },\n\n  aboutXYears: {\n    one: \"આશરે 1 વર્ષ\",\n    other: \"આશરે {{count}} વર્ષ\",\n  },\n\n  xYears: {\n    one: \"1 વર્ષ\",\n    other: \"{{count}} વર્ષ\",\n  },\n\n  overXYears: {\n    one: \"1 વર્ષથી વધુ\",\n    other: \"{{count}} વર્ષથી વધુ\",\n  },\n\n  almostXYears: {\n    one: \"લગભગ 1 વર્ષ\",\n    other: \"લગભગ {{count}} વર્ષ\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"માં\";\n    } else {\n      return result + \" પહેલાં\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA;AACA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,OAAO;IAAE;IACdC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,YAAY;EAEzBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,SAAS;IAAE;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,EAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM,GAAG,KAAK;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,SAAS;IAC3B;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}