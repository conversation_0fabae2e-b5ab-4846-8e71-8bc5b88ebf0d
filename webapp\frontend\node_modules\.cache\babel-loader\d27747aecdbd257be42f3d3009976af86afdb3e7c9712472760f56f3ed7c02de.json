{"ast": null, "code": "'use client';\n\nexport { default } from './DialogContentText';\nexport { default as dialogContentTextClasses } from './dialogContentTextClasses';\nexport * from './dialogContentTextClasses';", "map": {"version": 3, "names": ["default", "dialogContentTextClasses"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/DialogContentText/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './DialogContentText';\nexport { default as dialogContentTextClasses } from './dialogContentTextClasses';\nexport * from './dialogContentTextClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,qBAAqB;AAC7C,SAASA,OAAO,IAAIC,wBAAwB,QAAQ,4BAA4B;AAChF,cAAc,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}