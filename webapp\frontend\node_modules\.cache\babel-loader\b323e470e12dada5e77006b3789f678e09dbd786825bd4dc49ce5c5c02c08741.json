{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeListRivoluzionato.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Alert, CircularProgress, Tooltip, Grid, List, ListItem, ListItemText, Tabs, Tab, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Visibility as ViewIcon, Assignment as AssignIcon, Refresh as RefreshIcon, Person as PersonIcon, Email as EmailIcon, Phone as PhoneIcon, ExpandMore as ExpandMoreIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ComandeListRivoluzionato = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  // Stati principali\n  const [activeTab, setActiveTab] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Stati comande\n  const [comande, setComande] = useState([]);\n  const [statistiche, setStatistiche] = useState(null);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Carica dati al mount\n  useEffect(() => {\n    if (cantiereId) {\n      loadComande();\n      loadStatistiche();\n      loadResponsabili();\n    }\n  }, [cantiereId]);\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const data = await comandeService.getComande(cantiereId);\n      setComande(data.comande || []);\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n  const loadComandePerResponsabili = async responsabiliList => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    setOpenResponsabileDialog(true);\n  };\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n  const handleDeleteResponsabile = async idResponsabile => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n  const getStatoColor = stato => {\n    const colors = {\n      'CREATA': 'default',\n      'IN_CORSO': 'primary',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      sx: {\n        fontWeight: 500,\n        color: 'text.primary'\n      },\n      children: \"Gestione Comande\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        borderBottom: 1,\n        borderColor: 'divider',\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: activeTab,\n        onChange: (e, newValue) => setActiveTab(newValue),\n        sx: {\n          '& .MuiTab-root': {\n            textTransform: 'none',\n            fontWeight: 500,\n            fontSize: '1rem'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Responsabili\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Tutte le Comande\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this), activeTab === 0 && /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          sx: {\n            fontWeight: 500,\n            color: 'text.primary'\n          },\n          children: \"Responsabili del Cantiere\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 26\n          }, this),\n          onClick: () => handleOpenResponsabileDialog('create'),\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3,\n            py: 1\n          },\n          children: \"Inserisci Responsabile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this), loadingResponsabili ? /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"center\",\n        py: 4,\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        children: responsabili.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 0,\n          sx: {\n            p: 6,\n            textAlign: 'center',\n            backgroundColor: 'grey.50',\n            border: '1px dashed',\n            borderColor: 'grey.300'\n          },\n          children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n            sx: {\n              fontSize: 48,\n              color: 'grey.400',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"text.secondary\",\n            gutterBottom: true,\n            children: \"Nessun responsabile configurato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 3\n            },\n            children: \"Aggiungi il primo responsabile per iniziare a gestire le comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 32\n            }, this),\n            onClick: () => handleOpenResponsabileDialog('create'),\n            sx: {\n              textTransform: 'none'\n            },\n            children: \"Inserisci Primo Responsabile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 17\n        }, this) : responsabili.map(responsabile => /*#__PURE__*/_jsxDEV(Accordion, {\n          sx: {\n            mb: 2,\n            '&:before': {\n              display: 'none'\n            },\n            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n            border: '1px solid',\n            borderColor: 'grey.200'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 35\n            }, this),\n            sx: {\n              '&:hover': {\n                backgroundColor: 'grey.50'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\",\n              width: \"100%\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                  color: \"primary\",\n                  sx: {\n                    fontSize: 28\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      fontWeight: 500\n                    },\n                    children: responsabile.nome_responsabile\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    gap: 3,\n                    mt: 0.5,\n                    children: [responsabile.email && /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 0.5,\n                      children: [/*#__PURE__*/_jsxDEV(EmailIcon, {\n                        fontSize: \"small\",\n                        color: \"action\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 365,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: responsabile.email\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 366,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 364,\n                      columnNumber: 33\n                    }, this), responsabile.telefono && /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 0.5,\n                      children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                        fontSize: \"small\",\n                        color: \"action\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 373,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: responsabile.telefono\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 374,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 1,\n                onClick: e => e.stopPropagation(),\n                children: [/*#__PURE__*/_jsxDEV(Chip, {\n                  icon: /*#__PURE__*/_jsxDEV(AssignIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 35\n                  }, this),\n                  label: `${(comandePerResponsabile[responsabile.id_responsabile] || []).length} comande`,\n                  size: \"small\",\n                  color: \"primary\",\n                  variant: \"outlined\",\n                  sx: {\n                    fontWeight: 500\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Modifica responsabile\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => handleOpenResponsabileDialog('edit', responsabile),\n                    sx: {\n                      '&:hover': {\n                        backgroundColor: 'primary.light',\n                        color: 'white'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Elimina responsabile\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => handleDeleteResponsabile(responsabile.id_responsabile),\n                    sx: {\n                      '&:hover': {\n                        backgroundColor: 'error.light',\n                        color: 'white'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            sx: {\n              pt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              sx: {\n                fontWeight: 500,\n                mb: 2\n              },\n              children: \"Comande Assegnate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 23\n            }, this), !Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) || comandePerResponsabile[responsabile.id_responsabile].length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 3,\n                textAlign: 'center',\n                backgroundColor: 'grey.50',\n                borderRadius: 1,\n                border: '1px dashed',\n                borderColor: 'grey.300'\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Nessuna comanda assegnata a questo responsabile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 27\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 25\n            }, this) : /*#__PURE__*/_jsxDEV(List, {\n              dense: true,\n              children: Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) && comandePerResponsabile[responsabile.id_responsabile].map(comanda => /*#__PURE__*/_jsxDEV(ListItem, {\n                divider: true,\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"bold\",\n                      children: comanda.codice_comanda\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 37\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: getTipoComandaLabel(comanda.tipo_comanda),\n                      size: \"small\",\n                      variant: \"outlined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 37\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: comanda.stato || 'CREATA',\n                      size: \"small\",\n                      color: getStatoColor(comanda.stato)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 459,\n                      columnNumber: 37\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 35\n                  }, this),\n                  secondary: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"textSecondary\",\n                    children: [comanda.descrizione || 'Nessuna descrizione', comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 35\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 31\n                }, this)\n              }, comanda.codice_comanda, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 29\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 21\n          }, this)]\n        }, responsabile.id_responsabile, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 19\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 9\n    }, this), activeTab === 1 && /*#__PURE__*/_jsxDEV(Box, {\n      children: [statistiche && /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        sx: {\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n              border: '1px solid',\n              borderColor: 'grey.200'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                textAlign: 'center',\n                py: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 500,\n                  mb: 1\n                },\n                children: \"Totale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'text.primary'\n                },\n                children: statistiche.totale_comande\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n              border: '1px solid',\n              borderColor: 'grey.200'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                textAlign: 'center',\n                py: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 500,\n                  mb: 1\n                },\n                children: \"Create\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'primary.main'\n                },\n                children: statistiche.comande_create\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n              border: '1px solid',\n              borderColor: 'grey.200'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                textAlign: 'center',\n                py: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 500,\n                  mb: 1\n                },\n                children: \"In Corso\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'warning.main'\n                },\n                children: statistiche.comande_in_corso\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n              border: '1px solid',\n              borderColor: 'grey.200'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                textAlign: 'center',\n                py: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 500,\n                  mb: 1\n                },\n                children: \"Completate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'success.main'\n                },\n                children: statistiche.comande_completate\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          sx: {\n            fontWeight: 500,\n            color: 'text.primary'\n          },\n          children: \"Tutte le Comande\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 28\n            }, this),\n            onClick: () => setOpenCreaConCavi(true),\n            sx: {\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            },\n            children: \"Nuova Comanda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 28\n            }, this),\n            onClick: () => {\n              loadComande();\n              loadStatistiche();\n            },\n            sx: {\n              textTransform: 'none',\n              fontWeight: 500\n            },\n            children: \"Aggiorna\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        sx: {\n          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                backgroundColor: 'grey.50'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"Codice\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"Tipo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"Responsabile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 586,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"Priorit\\xE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"Data Creazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: comande.length === 0 ? /*#__PURE__*/_jsxDEV(TableRow, {\n              children: /*#__PURE__*/_jsxDEV(TableCell, {\n                colSpan: 7,\n                align: \"center\",\n                sx: {\n                  py: 8\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n                    sx: {\n                      fontSize: 48,\n                      color: 'grey.400',\n                      mb: 2\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    color: \"text.secondary\",\n                    gutterBottom: true,\n                    children: \"Nessuna comanda trovata\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    sx: {\n                      mb: 3\n                    },\n                    children: \"Crea la prima comanda per iniziare\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"contained\",\n                    startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 606,\n                      columnNumber: 38\n                    }, this),\n                    onClick: () => setOpenCreaConCavi(true),\n                    sx: {\n                      textTransform: 'none'\n                    },\n                    children: \"Crea Prima Comanda\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 19\n            }, this) : comande.map(comanda => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: \"bold\",\n                  children: comanda.codice_comanda\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: getTipoComandaLabel(comanda.tipo_comanda),\n                  size: \"small\",\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 624,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: comanda.responsabile || 'Non assegnato'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: comanda.stato || 'CREATA',\n                  size: \"small\",\n                  color: getStatoColor(comanda.stato)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: comanda.priorita || 'NORMALE',\n                  size: \"small\",\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: comanda.data_creazione ? new Date(comanda.data_creazione).toLocaleDateString() : '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  gap: 0.5,\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Visualizza\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      children: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 658,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 657,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 656,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Modifica\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 663,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 662,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 661,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Elimina\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 668,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 655,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 23\n              }, this)]\n            }, comanda.codice_comanda, true, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openResponsabileDialog,\n      onClose: handleCloseResponsabileDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 693,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 692,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nome Responsabile\",\n            value: formDataResponsabile.nome_responsabile,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              nome_responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true,\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 699,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: formDataResponsabile.email,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              email: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Email per notifiche (opzionale se inserisci telefono)\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Telefono\",\n            value: formDataResponsabile.telefono,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              telefono: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Numero per SMS (opzionale se inserisci email)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 698,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 697,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseResponsabileDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 734,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitResponsabile,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 733,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 683,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n      cantiereId: cantiereId,\n      open: openCreaConCavi,\n      onClose: () => setOpenCreaConCavi(false),\n      onSuccess: () => {\n        loadComande();\n        loadStatistiche();\n        loadResponsabili();\n        setOpenCreaConCavi(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 755,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 250,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeListRivoluzionato, \"otAKg4rsewQ13eQYRszZfMJdYOU=\");\n_c = ComandeListRivoluzionato;\nexport default ComandeListRivoluzionato;\nvar _c;\n$RefreshReg$(_c, \"ComandeListRivoluzionato\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON><PERSON>", "Grid", "List", "ListItem", "ListItemText", "Tabs", "Tab", "Accordion", "AccordionSummary", "AccordionDetails", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Visibility", "ViewIcon", "Assignment", "AssignIcon", "Refresh", "RefreshIcon", "Person", "PersonIcon", "Email", "EmailIcon", "Phone", "PhoneIcon", "ExpandMore", "ExpandMoreIcon", "comandeService", "responsabiliService", "CreaComandaConCavi", "jsxDEV", "_jsxDEV", "ComandeListRivoluzionato", "cantiereId", "cantiereName", "_s", "activeTab", "setActiveTab", "loading", "setLoading", "error", "setError", "comande", "setComande", "statistiche", "setStatistiche", "openCreaConCavi", "setOpenCreaConCavi", "responsabili", "setResponsabili", "loadingResponsabili", "setLoadingResponsabili", "comandePerResponsabile", "setComandePerResponsabile", "openResponsabileDialog", "setOpenResponsabileDialog", "dialogModeResponsabile", "setDialogModeResponsabile", "selectedResponsabile", "setSelectedResponsabile", "formDataResponsabile", "setFormDataResponsabile", "nome_responsabile", "email", "telefono", "loadComande", "loadStatistiche", "loadResponsabili", "data", "getComande", "err", "console", "stats", "getStatisticheComande", "getResponsabiliCantiere", "loadComandePerResponsabili", "_err$response", "_err$response$data", "errorMessage", "response", "detail", "message", "responsabiliList", "comandeMap", "responsabile", "getComandeByResponsabile", "Array", "isArray", "id_responsabile", "handleOpenResponsabileDialog", "mode", "handleCloseResponsabileDialog", "handleSubmitResponsabile", "trim", "createResponsabile", "updateResponsabile", "handleDeleteResponsabile", "idResponsabile", "window", "confirm", "deleteResponsabile", "getTipoComandaLabel", "tipo", "labels", "getStatoColor", "stato", "colors", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "gutterBottom", "sx", "fontWeight", "color", "severity", "mb", "borderBottom", "borderColor", "value", "onChange", "e", "newValue", "textTransform", "fontSize", "label", "startIcon", "onClick", "px", "py", "length", "elevation", "p", "textAlign", "backgroundColor", "border", "map", "boxShadow", "expandIcon", "width", "gap", "mt", "stopPropagation", "icon", "size", "title", "pt", "borderRadius", "dense", "comanda", "divider", "primary", "codice_comanda", "tipo_comanda", "secondary", "descrizione", "data_creazione", "Date", "toLocaleDateString", "container", "spacing", "item", "xs", "sm", "md", "totale_comande", "comande_create", "comande_in_corso", "comande_completate", "component", "colSpan", "align", "priorita", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "pb", "target", "margin", "required", "type", "helperText", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeListRivoluzionato.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Alert,\n  CircularProgress,\n  Tooltip,\n  Grid,\n  List,\n  ListItem,\n  ListItemText,\n  Tabs,\n  Tab,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Visibility as ViewIcon,\n  Assignment as AssignIcon,\n  Refresh as RefreshIcon,\n  Person as PersonIcon,\n  Email as EmailIcon,\n  Phone as PhoneIcon,\n  ExpandMore as ExpandMoreIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\n\nconst ComandeListRivoluzionato = ({ cantiereId, cantiereName }) => {\n  // Stati principali\n  const [activeTab, setActiveTab] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Stati comande\n  const [comande, setComande] = useState([]);\n  const [statistiche, setStatistiche] = useState(null);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Carica dati al mount\n  useEffect(() => {\n    if (cantiereId) {\n      loadComande();\n      loadStatistiche();\n      loadResponsabili();\n    }\n  }, [cantiereId]);\n\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const data = await comandeService.getComande(cantiereId);\n      setComande(data.comande || []);\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = err.response?.data?.detail || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  const loadComandePerResponsabili = async (responsabiliList) => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    \n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    \n    setOpenResponsabileDialog(true);\n  };\n\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      \n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      \n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n\n  const handleDeleteResponsabile = async (idResponsabile) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n\n  const getStatoColor = (stato) => {\n    const colors = {\n      'CREATA': 'default',\n      'IN_CORSO': 'primary',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom sx={{ fontWeight: 500, color: 'text.primary' }}>\n        Gestione Comande\n      </Typography>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Tabs per navigazione */}\n      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>\n        <Tabs\n          value={activeTab}\n          onChange={(e, newValue) => setActiveTab(newValue)}\n          sx={{\n            '& .MuiTab-root': {\n              textTransform: 'none',\n              fontWeight: 500,\n              fontSize: '1rem'\n            }\n          }}\n        >\n          <Tab label=\"Responsabili\" />\n          <Tab label=\"Tutte le Comande\" />\n        </Tabs>\n      </Box>\n\n      {/* Tab 0: Responsabili */}\n      {activeTab === 0 && (\n        <Box>\n          {/* Toolbar Responsabili */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n              Responsabili del Cantiere\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={() => handleOpenResponsabileDialog('create')}\n              sx={{\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1\n              }}\n            >\n              Inserisci Responsabile\n            </Button>\n          </Box>\n\n          {loadingResponsabili ? (\n            <Box display=\"flex\" justifyContent=\"center\" py={4}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              {responsabili.length === 0 ? (\n                <Paper\n                  elevation={0}\n                  sx={{\n                    p: 6,\n                    textAlign: 'center',\n                    backgroundColor: 'grey.50',\n                    border: '1px dashed',\n                    borderColor: 'grey.300'\n                  }}\n                >\n                  <PersonIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />\n                  <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                    Nessun responsabile configurato\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                    Aggiungi il primo responsabile per iniziare a gestire le comande\n                  </Typography>\n                  <Button\n                    variant=\"contained\"\n                    startIcon={<AddIcon />}\n                    onClick={() => handleOpenResponsabileDialog('create')}\n                    sx={{ textTransform: 'none' }}\n                  >\n                    Inserisci Primo Responsabile\n                  </Button>\n                </Paper>\n              ) : (\n                responsabili.map((responsabile) => (\n                  <Accordion\n                    key={responsabile.id_responsabile}\n                    sx={{\n                      mb: 2,\n                      '&:before': { display: 'none' },\n                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                      border: '1px solid',\n                      borderColor: 'grey.200'\n                    }}\n                  >\n                    <AccordionSummary\n                      expandIcon={<ExpandMoreIcon />}\n                      sx={{\n                        '&:hover': {\n                          backgroundColor: 'grey.50'\n                        }\n                      }}\n                    >\n                      <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\" width=\"100%\">\n                        <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                          <PersonIcon color=\"primary\" sx={{ fontSize: 28 }} />\n                          <Box>\n                            <Typography variant=\"h6\" sx={{ fontWeight: 500 }}>\n                              {responsabile.nome_responsabile}\n                            </Typography>\n                            <Box display=\"flex\" gap={3} mt={0.5}>\n                              {responsabile.email && (\n                                <Box display=\"flex\" alignItems=\"center\" gap={0.5}>\n                                  <EmailIcon fontSize=\"small\" color=\"action\" />\n                                  <Typography variant=\"body2\" color=\"text.secondary\">\n                                    {responsabile.email}\n                                  </Typography>\n                                </Box>\n                              )}\n                              {responsabile.telefono && (\n                                <Box display=\"flex\" alignItems=\"center\" gap={0.5}>\n                                  <PhoneIcon fontSize=\"small\" color=\"action\" />\n                                  <Typography variant=\"body2\" color=\"text.secondary\">\n                                    {responsabile.telefono}\n                                  </Typography>\n                                </Box>\n                              )}\n                            </Box>\n                          </Box>\n                        </Box>\n\n                        <Box display=\"flex\" alignItems=\"center\" gap={1} onClick={(e) => e.stopPropagation()}>\n                          <Chip\n                            icon={<AssignIcon />}\n                            label={`${(comandePerResponsabile[responsabile.id_responsabile] || []).length} comande`}\n                            size=\"small\"\n                            color=\"primary\"\n                            variant=\"outlined\"\n                            sx={{ fontWeight: 500 }}\n                          />\n                          <Tooltip title=\"Modifica responsabile\">\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleOpenResponsabileDialog('edit', responsabile)}\n                              sx={{\n                                '&:hover': {\n                                  backgroundColor: 'primary.light',\n                                  color: 'white'\n                                }\n                              }}\n                            >\n                              <EditIcon fontSize=\"small\" />\n                            </IconButton>\n                          </Tooltip>\n                          <Tooltip title=\"Elimina responsabile\">\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleDeleteResponsabile(responsabile.id_responsabile)}\n                              sx={{\n                                '&:hover': {\n                                  backgroundColor: 'error.light',\n                                  color: 'white'\n                                }\n                              }}\n                            >\n                              <DeleteIcon fontSize=\"small\" />\n                            </IconButton>\n                          </Tooltip>\n                        </Box>\n                      </Box>\n                    </AccordionSummary>\n\n                    <AccordionDetails sx={{ pt: 2 }}>\n                      <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>\n                        Comande Assegnate\n                      </Typography>\n\n                      {(!Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) || comandePerResponsabile[responsabile.id_responsabile].length === 0) ? (\n                        <Box\n                          sx={{\n                            p: 3,\n                            textAlign: 'center',\n                            backgroundColor: 'grey.50',\n                            borderRadius: 1,\n                            border: '1px dashed',\n                            borderColor: 'grey.300'\n                          }}\n                        >\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Nessuna comanda assegnata a questo responsabile\n                          </Typography>\n                        </Box>\n                      ) : (\n                        <List dense>\n                          {Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) && comandePerResponsabile[responsabile.id_responsabile].map((comanda) => (\n                            <ListItem key={comanda.codice_comanda} divider>\n                              <ListItemText\n                                primary={\n                                  <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                                    <Typography variant=\"body2\" fontWeight=\"bold\">\n                                      {comanda.codice_comanda}\n                                    </Typography>\n                                    <Chip\n                                      label={getTipoComandaLabel(comanda.tipo_comanda)}\n                                      size=\"small\"\n                                      variant=\"outlined\"\n                                    />\n                                    <Chip\n                                      label={comanda.stato || 'CREATA'}\n                                      size=\"small\"\n                                      color={getStatoColor(comanda.stato)}\n                                    />\n                                  </Box>\n                                }\n                                secondary={\n                                  <Typography variant=\"body2\" color=\"textSecondary\">\n                                    {comanda.descrizione || 'Nessuna descrizione'}\n                                    {comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`}\n                                  </Typography>\n                                }\n                              />\n                            </ListItem>\n                          ))}\n                        </List>\n                      )}\n                    </AccordionDetails>\n                  </Accordion>\n                ))\n              )}\n            </Box>\n          )}\n        </Box>\n      )}\n\n      {/* Tab 1: Tutte le Comande */}\n      {activeTab === 1 && (\n        <Box>\n          {/* Statistiche */}\n          {statistiche && (\n            <Grid container spacing={3} sx={{ mb: 4 }}>\n              <Grid item xs={12} sm={6} md={3}>\n                <Card sx={{ boxShadow: '0 1px 3px rgba(0,0,0,0.1)', border: '1px solid', borderColor: 'grey.200' }}>\n                  <CardContent sx={{ textAlign: 'center', py: 3 }}>\n                    <Typography color=\"text.secondary\" variant=\"body2\" sx={{ fontWeight: 500, mb: 1 }}>\n                      Totale\n                    </Typography>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'text.primary' }}>\n                      {statistiche.totale_comande}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n              <Grid item xs={12} sm={6} md={3}>\n                <Card sx={{ boxShadow: '0 1px 3px rgba(0,0,0,0.1)', border: '1px solid', borderColor: 'grey.200' }}>\n                  <CardContent sx={{ textAlign: 'center', py: 3 }}>\n                    <Typography color=\"text.secondary\" variant=\"body2\" sx={{ fontWeight: 500, mb: 1 }}>\n                      Create\n                    </Typography>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'primary.main' }}>\n                      {statistiche.comande_create}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n              <Grid item xs={12} sm={6} md={3}>\n                <Card sx={{ boxShadow: '0 1px 3px rgba(0,0,0,0.1)', border: '1px solid', borderColor: 'grey.200' }}>\n                  <CardContent sx={{ textAlign: 'center', py: 3 }}>\n                    <Typography color=\"text.secondary\" variant=\"body2\" sx={{ fontWeight: 500, mb: 1 }}>\n                      In Corso\n                    </Typography>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'warning.main' }}>\n                      {statistiche.comande_in_corso}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n              <Grid item xs={12} sm={6} md={3}>\n                <Card sx={{ boxShadow: '0 1px 3px rgba(0,0,0,0.1)', border: '1px solid', borderColor: 'grey.200' }}>\n                  <CardContent sx={{ textAlign: 'center', py: 3 }}>\n                    <Typography color=\"text.secondary\" variant=\"body2\" sx={{ fontWeight: 500, mb: 1 }}>\n                      Completate\n                    </Typography>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'success.main' }}>\n                      {statistiche.comande_completate}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            </Grid>\n          )}\n\n          {/* Toolbar Comande */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n              Tutte le Comande\n            </Typography>\n            <Box display=\"flex\" gap={2}>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => setOpenCreaConCavi(true)}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500,\n                  px: 3\n                }}\n              >\n                Nuova Comanda\n              </Button>\n              <Button\n                variant=\"outlined\"\n                startIcon={<RefreshIcon />}\n                onClick={() => {\n                  loadComande();\n                  loadStatistiche();\n                }}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500\n                }}\n              >\n                Aggiorna\n              </Button>\n            </Box>\n          </Box>\n\n          {/* Tabella Comande */}\n          <TableContainer component={Paper} sx={{ boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>\n            <Table>\n              <TableHead>\n                <TableRow sx={{ backgroundColor: 'grey.50' }}>\n                  <TableCell sx={{ fontWeight: 600 }}>Codice</TableCell>\n                  <TableCell sx={{ fontWeight: 600 }}>Tipo</TableCell>\n                  <TableCell sx={{ fontWeight: 600 }}>Responsabile</TableCell>\n                  <TableCell sx={{ fontWeight: 600 }}>Stato</TableCell>\n                  <TableCell sx={{ fontWeight: 600 }}>Priorità</TableCell>\n                  <TableCell sx={{ fontWeight: 600 }}>Data Creazione</TableCell>\n                  <TableCell sx={{ fontWeight: 600 }}>Azioni</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {comande.length === 0 ? (\n                  <TableRow>\n                    <TableCell colSpan={7} align=\"center\" sx={{ py: 8 }}>\n                      <Box>\n                        <AssignIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />\n                        <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                          Nessuna comanda trovata\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                          Crea la prima comanda per iniziare\n                        </Typography>\n                        <Button\n                          variant=\"contained\"\n                          startIcon={<AddIcon />}\n                          onClick={() => setOpenCreaConCavi(true)}\n                          sx={{ textTransform: 'none' }}\n                        >\n                          Crea Prima Comanda\n                        </Button>\n                      </Box>\n                    </TableCell>\n                  </TableRow>\n                ) : (\n                  comande.map((comanda) => (\n                    <TableRow key={comanda.codice_comanda}>\n                      <TableCell>\n                        <Typography variant=\"body2\" fontWeight=\"bold\">\n                          {comanda.codice_comanda}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={getTipoComandaLabel(comanda.tipo_comanda)}\n                          size=\"small\"\n                          variant=\"outlined\"\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {comanda.responsabile || 'Non assegnato'}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={comanda.stato || 'CREATA'}\n                          size=\"small\"\n                          color={getStatoColor(comanda.stato)}\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={comanda.priorita || 'NORMALE'}\n                          size=\"small\"\n                          variant=\"outlined\"\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {comanda.data_creazione ? new Date(comanda.data_creazione).toLocaleDateString() : '-'}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Box display=\"flex\" gap={0.5}>\n                          <Tooltip title=\"Visualizza\">\n                            <IconButton size=\"small\">\n                              <ViewIcon />\n                            </IconButton>\n                          </Tooltip>\n                          <Tooltip title=\"Modifica\">\n                            <IconButton size=\"small\">\n                              <EditIcon />\n                            </IconButton>\n                          </Tooltip>\n                          <Tooltip title=\"Elimina\">\n                            <IconButton size=\"small\" color=\"error\">\n                              <DeleteIcon />\n                            </IconButton>\n                          </Tooltip>\n                        </Box>\n                      </TableCell>\n                    </TableRow>\n                  ))\n                )}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </Box>\n      )}\n\n      {/* Dialog per creazione/modifica responsabile */}\n      <Dialog\n        open={openResponsabileDialog}\n        onClose={handleCloseResponsabileDialog}\n        maxWidth=\"sm\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Nome Responsabile\"\n              value={formDataResponsabile.nome_responsabile}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, nome_responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n              variant=\"outlined\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Email\"\n              type=\"email\"\n              value={formDataResponsabile.email}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, email: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Email per notifiche (opzionale se inserisci telefono)\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Telefono\"\n              value={formDataResponsabile.telefono}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, telefono: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Numero per SMS (opzionale se inserisci email)\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseResponsabileDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSubmitResponsabile}\n            variant=\"contained\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            {dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog CreaComandaConCavi */}\n      <CreaComandaConCavi\n        cantiereId={cantiereId}\n        open={openCreaConCavi}\n        onClose={() => setOpenCreaConCavi(false)}\n        onSuccess={() => {\n          loadComande();\n          loadStatistiche();\n          loadResponsabili();\n          setOpenCreaConCavi(false);\n        }}\n      />\n    </Box>\n  );\n};\n\nexport default ComandeListRivoluzionato;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,IAAI,EACJC,GAAG,EACHC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,QACX,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,QAAQ,EACtBC,UAAU,IAAIC,UAAU,EACxBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,kBAAkB,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACjE;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACgE,OAAO,EAAEC,UAAU,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkE,KAAK,EAAEC,QAAQ,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM,CAACoE,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsE,WAAW,EAAEC,cAAc,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACwE,eAAe,EAAEC,kBAAkB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAAC0E,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC4E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC8E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG/E,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACgF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACkF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGnF,QAAQ,CAAC,QAAQ,CAAC;EAC9E,MAAM,CAACoF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACsF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvF,QAAQ,CAAC;IAC/DwF,iBAAiB,EAAE,EAAE;IACrBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACAzF,SAAS,CAAC,MAAM;IACd,IAAI0D,UAAU,EAAE;MACdgC,WAAW,CAAC,CAAC;MACbC,eAAe,CAAC,CAAC;MACjBC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAClC,UAAU,CAAC,CAAC;EAEhB,MAAMgC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF1B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM6B,IAAI,GAAG,MAAMzC,cAAc,CAAC0C,UAAU,CAACpC,UAAU,CAAC;MACxDU,UAAU,CAACyB,IAAI,CAAC1B,OAAO,IAAI,EAAE,CAAC;MAC9BD,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAO6B,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,uCAAuC,EAAE8B,GAAG,CAAC;MAC3D7B,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMM,KAAK,GAAG,MAAM7C,cAAc,CAAC8C,qBAAqB,CAACxC,UAAU,CAAC;MACpEY,cAAc,CAAC2B,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,2CAA2C,EAAE8B,GAAG,CAAC;IACjE;EACF,CAAC;EAED,MAAMH,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFhB,sBAAsB,CAAC,IAAI,CAAC;MAC5BV,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM2B,IAAI,GAAG,MAAMxC,mBAAmB,CAAC8C,uBAAuB,CAACzC,UAAU,CAAC;MAC1EgB,eAAe,CAACmB,IAAI,IAAI,EAAE,CAAC;MAC3B,MAAMO,0BAA0B,CAACP,IAAI,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOE,GAAG,EAAE;MAAA,IAAAM,aAAA,EAAAC,kBAAA;MACZN,OAAO,CAAC/B,KAAK,CAAC,0CAA0C,EAAE8B,GAAG,CAAC;MAC9D,MAAMQ,YAAY,GAAG,EAAAF,aAAA,GAAAN,GAAG,CAACS,QAAQ,cAAAH,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcR,IAAI,cAAAS,kBAAA,uBAAlBA,kBAAA,CAAoBG,MAAM,KAAIV,GAAG,CAACW,OAAO,IAAI,yCAAyC;MAC3GxC,QAAQ,CAAC,4CAA4CqC,YAAY,EAAE,CAAC;IACtE,CAAC,SAAS;MACR3B,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMwB,0BAA0B,GAAG,MAAOO,gBAAgB,IAAK;IAC7D,IAAI;MACF,MAAMC,UAAU,GAAG,CAAC,CAAC;MACrB,KAAK,MAAMC,YAAY,IAAIF,gBAAgB,EAAE;QAC3C,IAAI;UACF,MAAMH,QAAQ,GAAG,MAAMpD,cAAc,CAAC0D,wBAAwB,CAACpD,UAAU,EAAEmD,YAAY,CAACtB,iBAAiB,CAAC;UAC1G;UACA,IAAIpB,OAAO,GAAG,EAAE;UAChB,IAAIqC,QAAQ,IAAIO,KAAK,CAACC,OAAO,CAACR,QAAQ,CAAC,EAAE;YACvCrC,OAAO,GAAGqC,QAAQ;UACpB,CAAC,MAAM,IAAIA,QAAQ,IAAIA,QAAQ,CAACrC,OAAO,IAAI4C,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACrC,OAAO,CAAC,EAAE;YAC1EA,OAAO,GAAGqC,QAAQ,CAACrC,OAAO;UAC5B,CAAC,MAAM,IAAIqC,QAAQ,IAAIA,QAAQ,CAACX,IAAI,IAAIkB,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACX,IAAI,CAAC,EAAE;YACpE1B,OAAO,GAAGqC,QAAQ,CAACX,IAAI;UACzB;UACAe,UAAU,CAACC,YAAY,CAACI,eAAe,CAAC,GAAG9C,OAAO;QACpD,CAAC,CAAC,OAAO4B,GAAG,EAAE;UACZC,OAAO,CAAC/B,KAAK,CAAC,sCAAsC4C,YAAY,CAACtB,iBAAiB,GAAG,EAAEQ,GAAG,CAAC;UAC3Fa,UAAU,CAACC,YAAY,CAACI,eAAe,CAAC,GAAG,EAAE;QAC/C;MACF;MACAnC,yBAAyB,CAAC8B,UAAU,CAAC;IACvC,CAAC,CAAC,OAAOb,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,uCAAuC,EAAE8B,GAAG,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMmB,4BAA4B,GAAGA,CAACC,IAAI,EAAEN,YAAY,GAAG,IAAI,KAAK;IAClE3B,yBAAyB,CAACiC,IAAI,CAAC;IAC/B/B,uBAAuB,CAACyB,YAAY,CAAC;IAErC,IAAIM,IAAI,KAAK,MAAM,IAAIN,YAAY,EAAE;MACnCvB,uBAAuB,CAAC;QACtBC,iBAAiB,EAAEsB,YAAY,CAACtB,iBAAiB,IAAI,EAAE;QACvDC,KAAK,EAAEqB,YAAY,CAACrB,KAAK,IAAI,EAAE;QAC/BC,QAAQ,EAAEoB,YAAY,CAACpB,QAAQ,IAAI;MACrC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,uBAAuB,CAAC;QACtBC,iBAAiB,EAAE,EAAE;QACrBC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEAT,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMoC,6BAA6B,GAAGA,CAAA,KAAM;IAC1CpC,yBAAyB,CAAC,KAAK,CAAC;IAChCI,uBAAuB,CAAC,IAAI,CAAC;IAC7BlB,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAMmD,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFnD,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI,CAACmB,oBAAoB,CAACE,iBAAiB,CAAC+B,IAAI,CAAC,CAAC,EAAE;QAClDpD,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA,IAAI,CAACmB,oBAAoB,CAACG,KAAK,IAAI,CAACH,oBAAoB,CAACI,QAAQ,EAAE;QACjEvB,QAAQ,CAAC,yDAAyD,CAAC;QACnE;MACF;MAEA,IAAIe,sBAAsB,KAAK,QAAQ,EAAE;QACvC,MAAM5B,mBAAmB,CAACkE,kBAAkB,CAAC7D,UAAU,EAAE2B,oBAAoB,CAAC;MAChF,CAAC,MAAM,IAAIJ,sBAAsB,KAAK,MAAM,EAAE;QAC5C,MAAM5B,mBAAmB,CAACmE,kBAAkB,CAACrC,oBAAoB,CAAC8B,eAAe,EAAE5B,oBAAoB,CAAC;MAC1G;MAEA+B,6BAA6B,CAAC,CAAC;MAC/B,MAAMxB,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,yBAAyB,EAAE8B,GAAG,CAAC;MAC7C7B,QAAQ,CAAC6B,GAAG,CAACU,MAAM,IAAI,yCAAyC,CAAC;IACnE;EACF,CAAC;EAED,MAAMgB,wBAAwB,GAAG,MAAOC,cAAc,IAAK;IACzD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACzE;IACF;IAEA,IAAI;MACF,MAAMvE,mBAAmB,CAACwE,kBAAkB,CAACH,cAAc,CAAC;MAC5D,MAAM9B,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,4BAA4B,EAAE8B,GAAG,CAAC;MAChD7B,QAAQ,CAAC,4CAA4C,CAAC;IACxD;EACF,CAAC;EAED,MAAM4D,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,MAAM,GAAG;MACb,MAAM,EAAE,MAAM;MACd,uBAAuB,EAAE,gBAAgB;MACzC,qBAAqB,EAAE,cAAc;MACrC,gBAAgB,EAAE;IACpB,CAAC;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;EAED,MAAME,aAAa,GAAIC,KAAK,IAAK;IAC/B,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC,CAAC;EAED,IAAInE,OAAO,EAAE;IACX,oBACEP,OAAA,CAACvD,GAAG;MAACmI,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/EhF,OAAA,CAACnC,gBAAgB;QAAAoH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACEpF,OAAA,CAACvD,GAAG;IAAAuI,QAAA,gBACFhF,OAAA,CAACpD,UAAU;MAACyI,OAAO,EAAC,IAAI;MAACC,YAAY;MAACC,EAAE,EAAE;QAAEC,UAAU,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAe,CAAE;MAAAT,QAAA,EAAC;IAEtF;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZ3E,KAAK,iBACJT,OAAA,CAACpC,KAAK;MAAC8H,QAAQ,EAAC,OAAO;MAACH,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,EACnCvE;IAAK;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGDpF,OAAA,CAACvD,GAAG;MAAC8I,EAAE,EAAE;QAAEK,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,SAAS;QAAEF,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,eAC1DhF,OAAA,CAAC7B,IAAI;QACH2H,KAAK,EAAEzF,SAAU;QACjB0F,QAAQ,EAAEA,CAACC,CAAC,EAAEC,QAAQ,KAAK3F,YAAY,CAAC2F,QAAQ,CAAE;QAClDV,EAAE,EAAE;UACF,gBAAgB,EAAE;YAChBW,aAAa,EAAE,MAAM;YACrBV,UAAU,EAAE,GAAG;YACfW,QAAQ,EAAE;UACZ;QACF,CAAE;QAAAnB,QAAA,gBAEFhF,OAAA,CAAC5B,GAAG;UAACgI,KAAK,EAAC;QAAc;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5BpF,OAAA,CAAC5B,GAAG;UAACgI,KAAK,EAAC;QAAkB;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGL/E,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACvD,GAAG;MAAAuI,QAAA,gBAEFhF,OAAA,CAACvD,GAAG;QAACmI,OAAO,EAAC,MAAM;QAACC,cAAc,EAAC,eAAe;QAACC,UAAU,EAAC,QAAQ;QAACa,EAAE,EAAE,CAAE;QAAAX,QAAA,gBAC3EhF,OAAA,CAACpD,UAAU;UAACyI,OAAO,EAAC,IAAI;UAACE,EAAE,EAAE;YAAEC,UAAU,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAe,CAAE;UAAAT,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpF,OAAA,CAACnD,MAAM;UACLwI,OAAO,EAAC,WAAW;UACnBgB,SAAS,eAAErG,OAAA,CAACvB,OAAO;YAAAwG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBkB,OAAO,EAAEA,CAAA,KAAM5C,4BAA4B,CAAC,QAAQ,CAAE;UACtD6B,EAAE,EAAE;YACFW,aAAa,EAAE,MAAM;YACrBV,UAAU,EAAE,GAAG;YACfe,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE;UACN,CAAE;UAAAxB,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELjE,mBAAmB,gBAClBnB,OAAA,CAACvD,GAAG;QAACmI,OAAO,EAAC,MAAM;QAACC,cAAc,EAAC,QAAQ;QAAC2B,EAAE,EAAE,CAAE;QAAAxB,QAAA,eAChDhF,OAAA,CAACnC,gBAAgB;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,gBAENpF,OAAA,CAACvD,GAAG;QAAAuI,QAAA,EACD/D,YAAY,CAACwF,MAAM,KAAK,CAAC,gBACxBzG,OAAA,CAAC5C,KAAK;UACJsJ,SAAS,EAAE,CAAE;UACbnB,EAAE,EAAE;YACFoB,CAAC,EAAE,CAAC;YACJC,SAAS,EAAE,QAAQ;YACnBC,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,YAAY;YACpBjB,WAAW,EAAE;UACf,CAAE;UAAAb,QAAA,gBAEFhF,OAAA,CAACX,UAAU;YAACkG,EAAE,EAAE;cAAEY,QAAQ,EAAE,EAAE;cAAEV,KAAK,EAAE,UAAU;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DpF,OAAA,CAACpD,UAAU;YAACyI,OAAO,EAAC,IAAI;YAACI,KAAK,EAAC,gBAAgB;YAACH,YAAY;YAAAN,QAAA,EAAC;UAE7D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbpF,OAAA,CAACpD,UAAU;YAACyI,OAAO,EAAC,OAAO;YAACI,KAAK,EAAC,gBAAgB;YAACF,EAAE,EAAE;cAAEI,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbpF,OAAA,CAACnD,MAAM;YACLwI,OAAO,EAAC,WAAW;YACnBgB,SAAS,eAAErG,OAAA,CAACvB,OAAO;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBkB,OAAO,EAAEA,CAAA,KAAM5C,4BAA4B,CAAC,QAAQ,CAAE;YACtD6B,EAAE,EAAE;cAAEW,aAAa,EAAE;YAAO,CAAE;YAAAlB,QAAA,EAC/B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,GAERnE,YAAY,CAAC8F,GAAG,CAAE1D,YAAY,iBAC5BrD,OAAA,CAAC3B,SAAS;UAERkH,EAAE,EAAE;YACFI,EAAE,EAAE,CAAC;YACL,UAAU,EAAE;cAAEf,OAAO,EAAE;YAAO,CAAC;YAC/BoC,SAAS,EAAE,2BAA2B;YACtCF,MAAM,EAAE,WAAW;YACnBjB,WAAW,EAAE;UACf,CAAE;UAAAb,QAAA,gBAEFhF,OAAA,CAAC1B,gBAAgB;YACf2I,UAAU,eAAEjH,OAAA,CAACL,cAAc;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC/BG,EAAE,EAAE;cACF,SAAS,EAAE;gBACTsB,eAAe,EAAE;cACnB;YACF,CAAE;YAAA7B,QAAA,eAEFhF,OAAA,CAACvD,GAAG;cAACmI,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAACD,cAAc,EAAC,eAAe;cAACqC,KAAK,EAAC,MAAM;cAAAlC,QAAA,gBACjFhF,OAAA,CAACvD,GAAG;gBAACmI,OAAO,EAAC,MAAM;gBAACE,UAAU,EAAC,QAAQ;gBAACqC,GAAG,EAAE,CAAE;gBAAAnC,QAAA,gBAC7ChF,OAAA,CAACX,UAAU;kBAACoG,KAAK,EAAC,SAAS;kBAACF,EAAE,EAAE;oBAAEY,QAAQ,EAAE;kBAAG;gBAAE;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpDpF,OAAA,CAACvD,GAAG;kBAAAuI,QAAA,gBACFhF,OAAA,CAACpD,UAAU;oBAACyI,OAAO,EAAC,IAAI;oBAACE,EAAE,EAAE;sBAAEC,UAAU,EAAE;oBAAI,CAAE;oBAAAR,QAAA,EAC9C3B,YAAY,CAACtB;kBAAiB;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACbpF,OAAA,CAACvD,GAAG;oBAACmI,OAAO,EAAC,MAAM;oBAACuC,GAAG,EAAE,CAAE;oBAACC,EAAE,EAAE,GAAI;oBAAApC,QAAA,GACjC3B,YAAY,CAACrB,KAAK,iBACjBhC,OAAA,CAACvD,GAAG;sBAACmI,OAAO,EAAC,MAAM;sBAACE,UAAU,EAAC,QAAQ;sBAACqC,GAAG,EAAE,GAAI;sBAAAnC,QAAA,gBAC/ChF,OAAA,CAACT,SAAS;wBAAC4G,QAAQ,EAAC,OAAO;wBAACV,KAAK,EAAC;sBAAQ;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7CpF,OAAA,CAACpD,UAAU;wBAACyI,OAAO,EAAC,OAAO;wBAACI,KAAK,EAAC,gBAAgB;wBAAAT,QAAA,EAC/C3B,YAAY,CAACrB;sBAAK;wBAAAiD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CACN,EACA/B,YAAY,CAACpB,QAAQ,iBACpBjC,OAAA,CAACvD,GAAG;sBAACmI,OAAO,EAAC,MAAM;sBAACE,UAAU,EAAC,QAAQ;sBAACqC,GAAG,EAAE,GAAI;sBAAAnC,QAAA,gBAC/ChF,OAAA,CAACP,SAAS;wBAAC0G,QAAQ,EAAC,OAAO;wBAACV,KAAK,EAAC;sBAAQ;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7CpF,OAAA,CAACpD,UAAU;wBAACyI,OAAO,EAAC,OAAO;wBAACI,KAAK,EAAC,gBAAgB;wBAAAT,QAAA,EAC/C3B,YAAY,CAACpB;sBAAQ;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENpF,OAAA,CAACvD,GAAG;gBAACmI,OAAO,EAAC,MAAM;gBAACE,UAAU,EAAC,QAAQ;gBAACqC,GAAG,EAAE,CAAE;gBAACb,OAAO,EAAGN,CAAC,IAAKA,CAAC,CAACqB,eAAe,CAAC,CAAE;gBAAArC,QAAA,gBAClFhF,OAAA,CAAC3C,IAAI;kBACHiK,IAAI,eAAEtH,OAAA,CAACf,UAAU;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACrBgB,KAAK,EAAE,GAAG,CAAC/E,sBAAsB,CAACgC,YAAY,CAACI,eAAe,CAAC,IAAI,EAAE,EAAEgD,MAAM,UAAW;kBACxFc,IAAI,EAAC,OAAO;kBACZ9B,KAAK,EAAC,SAAS;kBACfJ,OAAO,EAAC,UAAU;kBAClBE,EAAE,EAAE;oBAAEC,UAAU,EAAE;kBAAI;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACFpF,OAAA,CAAClC,OAAO;kBAAC0J,KAAK,EAAC,uBAAuB;kBAAAxC,QAAA,eACpChF,OAAA,CAAC1C,UAAU;oBACTiK,IAAI,EAAC,OAAO;oBACZjB,OAAO,EAAEA,CAAA,KAAM5C,4BAA4B,CAAC,MAAM,EAAEL,YAAY,CAAE;oBAClEkC,EAAE,EAAE;sBACF,SAAS,EAAE;wBACTsB,eAAe,EAAE,eAAe;wBAChCpB,KAAK,EAAE;sBACT;oBACF,CAAE;oBAAAT,QAAA,eAEFhF,OAAA,CAACrB,QAAQ;sBAACwH,QAAQ,EAAC;oBAAO;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACVpF,OAAA,CAAClC,OAAO;kBAAC0J,KAAK,EAAC,sBAAsB;kBAAAxC,QAAA,eACnChF,OAAA,CAAC1C,UAAU;oBACTiK,IAAI,EAAC,OAAO;oBACZjB,OAAO,EAAEA,CAAA,KAAMrC,wBAAwB,CAACZ,YAAY,CAACI,eAAe,CAAE;oBACtE8B,EAAE,EAAE;sBACF,SAAS,EAAE;wBACTsB,eAAe,EAAE,aAAa;wBAC9BpB,KAAK,EAAE;sBACT;oBACF,CAAE;oBAAAT,QAAA,eAEFhF,OAAA,CAACnB,UAAU;sBAACsH,QAAQ,EAAC;oBAAO;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eAEnBpF,OAAA,CAACzB,gBAAgB;YAACgH,EAAE,EAAE;cAAEkC,EAAE,EAAE;YAAE,CAAE;YAAAzC,QAAA,gBAC9BhF,OAAA,CAACpD,UAAU;cAACyI,OAAO,EAAC,WAAW;cAACC,YAAY;cAACC,EAAE,EAAE;gBAAEC,UAAU,EAAE,GAAG;gBAAEG,EAAE,EAAE;cAAE,CAAE;cAAAX,QAAA,EAAC;YAE7E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAEX,CAAC7B,KAAK,CAACC,OAAO,CAACnC,sBAAsB,CAACgC,YAAY,CAACI,eAAe,CAAC,CAAC,IAAIpC,sBAAsB,CAACgC,YAAY,CAACI,eAAe,CAAC,CAACgD,MAAM,KAAK,CAAC,gBACzIzG,OAAA,CAACvD,GAAG;cACF8I,EAAE,EAAE;gBACFoB,CAAC,EAAE,CAAC;gBACJC,SAAS,EAAE,QAAQ;gBACnBC,eAAe,EAAE,SAAS;gBAC1Ba,YAAY,EAAE,CAAC;gBACfZ,MAAM,EAAE,YAAY;gBACpBjB,WAAW,EAAE;cACf,CAAE;cAAAb,QAAA,eAEFhF,OAAA,CAACpD,UAAU;gBAACyI,OAAO,EAAC,OAAO;gBAACI,KAAK,EAAC,gBAAgB;gBAAAT,QAAA,EAAC;cAEnD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,gBAENpF,OAAA,CAAChC,IAAI;cAAC2J,KAAK;cAAA3C,QAAA,EACRzB,KAAK,CAACC,OAAO,CAACnC,sBAAsB,CAACgC,YAAY,CAACI,eAAe,CAAC,CAAC,IAAIpC,sBAAsB,CAACgC,YAAY,CAACI,eAAe,CAAC,CAACsD,GAAG,CAAEa,OAAO,iBACvI5H,OAAA,CAAC/B,QAAQ;gBAA8B4J,OAAO;gBAAA7C,QAAA,eAC5ChF,OAAA,CAAC9B,YAAY;kBACX4J,OAAO,eACL9H,OAAA,CAACvD,GAAG;oBAACmI,OAAO,EAAC,MAAM;oBAACE,UAAU,EAAC,QAAQ;oBAACqC,GAAG,EAAE,CAAE;oBAAAnC,QAAA,gBAC7ChF,OAAA,CAACpD,UAAU;sBAACyI,OAAO,EAAC,OAAO;sBAACG,UAAU,EAAC,MAAM;sBAAAR,QAAA,EAC1C4C,OAAO,CAACG;oBAAc;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC,eACbpF,OAAA,CAAC3C,IAAI;sBACH+I,KAAK,EAAE9B,mBAAmB,CAACsD,OAAO,CAACI,YAAY,CAAE;sBACjDT,IAAI,EAAC,OAAO;sBACZlC,OAAO,EAAC;oBAAU;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC,eACFpF,OAAA,CAAC3C,IAAI;sBACH+I,KAAK,EAAEwB,OAAO,CAAClD,KAAK,IAAI,QAAS;sBACjC6C,IAAI,EAAC,OAAO;sBACZ9B,KAAK,EAAEhB,aAAa,CAACmD,OAAO,CAAClD,KAAK;oBAAE;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN;kBACD6C,SAAS,eACPjI,OAAA,CAACpD,UAAU;oBAACyI,OAAO,EAAC,OAAO;oBAACI,KAAK,EAAC,eAAe;oBAAAT,QAAA,GAC9C4C,OAAO,CAACM,WAAW,IAAI,qBAAqB,EAC5CN,OAAO,CAACO,cAAc,IAAI,cAAc,IAAIC,IAAI,CAACR,OAAO,CAACO,cAAc,CAAC,CAACE,kBAAkB,CAAC,CAAC,EAAE;kBAAA;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC,GAzBWwC,OAAO,CAACG,cAAc;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0B3B,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACe,CAAC;QAAA,GA3Id/B,YAAY,CAACI,eAAe;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4IxB,CACZ;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGA/E,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACvD,GAAG;MAAAuI,QAAA,GAEDnE,WAAW,iBACVb,OAAA,CAACjC,IAAI;QAACuK,SAAS;QAACC,OAAO,EAAE,CAAE;QAAChD,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,gBACxChF,OAAA,CAACjC,IAAI;UAACyK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA3D,QAAA,eAC9BhF,OAAA,CAACtD,IAAI;YAAC6I,EAAE,EAAE;cAAEyB,SAAS,EAAE,2BAA2B;cAAEF,MAAM,EAAE,WAAW;cAAEjB,WAAW,EAAE;YAAW,CAAE;YAAAb,QAAA,eACjGhF,OAAA,CAACrD,WAAW;cAAC4I,EAAE,EAAE;gBAAEqB,SAAS,EAAE,QAAQ;gBAAEJ,EAAE,EAAE;cAAE,CAAE;cAAAxB,QAAA,gBAC9ChF,OAAA,CAACpD,UAAU;gBAAC6I,KAAK,EAAC,gBAAgB;gBAACJ,OAAO,EAAC,OAAO;gBAACE,EAAE,EAAE;kBAAEC,UAAU,EAAE,GAAG;kBAAEG,EAAE,EAAE;gBAAE,CAAE;gBAAAX,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbpF,OAAA,CAACpD,UAAU;gBAACyI,OAAO,EAAC,IAAI;gBAACE,EAAE,EAAE;kBAAEC,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAe,CAAE;gBAAAT,QAAA,EACrEnE,WAAW,CAAC+H;cAAc;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPpF,OAAA,CAACjC,IAAI;UAACyK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA3D,QAAA,eAC9BhF,OAAA,CAACtD,IAAI;YAAC6I,EAAE,EAAE;cAAEyB,SAAS,EAAE,2BAA2B;cAAEF,MAAM,EAAE,WAAW;cAAEjB,WAAW,EAAE;YAAW,CAAE;YAAAb,QAAA,eACjGhF,OAAA,CAACrD,WAAW;cAAC4I,EAAE,EAAE;gBAAEqB,SAAS,EAAE,QAAQ;gBAAEJ,EAAE,EAAE;cAAE,CAAE;cAAAxB,QAAA,gBAC9ChF,OAAA,CAACpD,UAAU;gBAAC6I,KAAK,EAAC,gBAAgB;gBAACJ,OAAO,EAAC,OAAO;gBAACE,EAAE,EAAE;kBAAEC,UAAU,EAAE,GAAG;kBAAEG,EAAE,EAAE;gBAAE,CAAE;gBAAAX,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbpF,OAAA,CAACpD,UAAU;gBAACyI,OAAO,EAAC,IAAI;gBAACE,EAAE,EAAE;kBAAEC,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAe,CAAE;gBAAAT,QAAA,EACrEnE,WAAW,CAACgI;cAAc;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPpF,OAAA,CAACjC,IAAI;UAACyK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA3D,QAAA,eAC9BhF,OAAA,CAACtD,IAAI;YAAC6I,EAAE,EAAE;cAAEyB,SAAS,EAAE,2BAA2B;cAAEF,MAAM,EAAE,WAAW;cAAEjB,WAAW,EAAE;YAAW,CAAE;YAAAb,QAAA,eACjGhF,OAAA,CAACrD,WAAW;cAAC4I,EAAE,EAAE;gBAAEqB,SAAS,EAAE,QAAQ;gBAAEJ,EAAE,EAAE;cAAE,CAAE;cAAAxB,QAAA,gBAC9ChF,OAAA,CAACpD,UAAU;gBAAC6I,KAAK,EAAC,gBAAgB;gBAACJ,OAAO,EAAC,OAAO;gBAACE,EAAE,EAAE;kBAAEC,UAAU,EAAE,GAAG;kBAAEG,EAAE,EAAE;gBAAE,CAAE;gBAAAX,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbpF,OAAA,CAACpD,UAAU;gBAACyI,OAAO,EAAC,IAAI;gBAACE,EAAE,EAAE;kBAAEC,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAe,CAAE;gBAAAT,QAAA,EACrEnE,WAAW,CAACiI;cAAgB;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPpF,OAAA,CAACjC,IAAI;UAACyK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA3D,QAAA,eAC9BhF,OAAA,CAACtD,IAAI;YAAC6I,EAAE,EAAE;cAAEyB,SAAS,EAAE,2BAA2B;cAAEF,MAAM,EAAE,WAAW;cAAEjB,WAAW,EAAE;YAAW,CAAE;YAAAb,QAAA,eACjGhF,OAAA,CAACrD,WAAW;cAAC4I,EAAE,EAAE;gBAAEqB,SAAS,EAAE,QAAQ;gBAAEJ,EAAE,EAAE;cAAE,CAAE;cAAAxB,QAAA,gBAC9ChF,OAAA,CAACpD,UAAU;gBAAC6I,KAAK,EAAC,gBAAgB;gBAACJ,OAAO,EAAC,OAAO;gBAACE,EAAE,EAAE;kBAAEC,UAAU,EAAE,GAAG;kBAAEG,EAAE,EAAE;gBAAE,CAAE;gBAAAX,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbpF,OAAA,CAACpD,UAAU;gBAACyI,OAAO,EAAC,IAAI;gBAACE,EAAE,EAAE;kBAAEC,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAe,CAAE;gBAAAT,QAAA,EACrEnE,WAAW,CAACkI;cAAkB;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP,eAGDpF,OAAA,CAACvD,GAAG;QAACmI,OAAO,EAAC,MAAM;QAACC,cAAc,EAAC,eAAe;QAACC,UAAU,EAAC,QAAQ;QAACa,EAAE,EAAE,CAAE;QAAAX,QAAA,gBAC3EhF,OAAA,CAACpD,UAAU;UAACyI,OAAO,EAAC,IAAI;UAACE,EAAE,EAAE;YAAEC,UAAU,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAe,CAAE;UAAAT,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpF,OAAA,CAACvD,GAAG;UAACmI,OAAO,EAAC,MAAM;UAACuC,GAAG,EAAE,CAAE;UAAAnC,QAAA,gBACzBhF,OAAA,CAACnD,MAAM;YACLwI,OAAO,EAAC,WAAW;YACnBgB,SAAS,eAAErG,OAAA,CAACvB,OAAO;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBkB,OAAO,EAAEA,CAAA,KAAMtF,kBAAkB,CAAC,IAAI,CAAE;YACxCuE,EAAE,EAAE;cACFW,aAAa,EAAE,MAAM;cACrBV,UAAU,EAAE,GAAG;cACfe,EAAE,EAAE;YACN,CAAE;YAAAvB,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpF,OAAA,CAACnD,MAAM;YACLwI,OAAO,EAAC,UAAU;YAClBgB,SAAS,eAAErG,OAAA,CAACb,WAAW;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BkB,OAAO,EAAEA,CAAA,KAAM;cACbpE,WAAW,CAAC,CAAC;cACbC,eAAe,CAAC,CAAC;YACnB,CAAE;YACFoD,EAAE,EAAE;cACFW,aAAa,EAAE,MAAM;cACrBV,UAAU,EAAE;YACd,CAAE;YAAAR,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpF,OAAA,CAAC/C,cAAc;QAAC+L,SAAS,EAAE5L,KAAM;QAACmI,EAAE,EAAE;UAAEyB,SAAS,EAAE;QAA4B,CAAE;QAAAhC,QAAA,eAC/EhF,OAAA,CAAClD,KAAK;UAAAkI,QAAA,gBACJhF,OAAA,CAAC9C,SAAS;YAAA8H,QAAA,eACRhF,OAAA,CAAC7C,QAAQ;cAACoI,EAAE,EAAE;gBAAEsB,eAAe,EAAE;cAAU,CAAE;cAAA7B,QAAA,gBAC3ChF,OAAA,CAAChD,SAAS;gBAACuI,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAI,CAAE;gBAAAR,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACtDpF,OAAA,CAAChD,SAAS;gBAACuI,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAI,CAAE;gBAAAR,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpDpF,OAAA,CAAChD,SAAS;gBAACuI,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAI,CAAE;gBAAAR,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5DpF,OAAA,CAAChD,SAAS;gBAACuI,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAI,CAAE;gBAAAR,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACrDpF,OAAA,CAAChD,SAAS;gBAACuI,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAI,CAAE;gBAAAR,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACxDpF,OAAA,CAAChD,SAAS;gBAACuI,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAI,CAAE;gBAAAR,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9DpF,OAAA,CAAChD,SAAS;gBAACuI,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAI,CAAE;gBAAAR,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZpF,OAAA,CAACjD,SAAS;YAAAiI,QAAA,EACPrE,OAAO,CAAC8F,MAAM,KAAK,CAAC,gBACnBzG,OAAA,CAAC7C,QAAQ;cAAA6H,QAAA,eACPhF,OAAA,CAAChD,SAAS;gBAACiM,OAAO,EAAE,CAAE;gBAACC,KAAK,EAAC,QAAQ;gBAAC3D,EAAE,EAAE;kBAAEiB,EAAE,EAAE;gBAAE,CAAE;gBAAAxB,QAAA,eAClDhF,OAAA,CAACvD,GAAG;kBAAAuI,QAAA,gBACFhF,OAAA,CAACf,UAAU;oBAACsG,EAAE,EAAE;sBAAEY,QAAQ,EAAE,EAAE;sBAAEV,KAAK,EAAE,UAAU;sBAAEE,EAAE,EAAE;oBAAE;kBAAE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9DpF,OAAA,CAACpD,UAAU;oBAACyI,OAAO,EAAC,IAAI;oBAACI,KAAK,EAAC,gBAAgB;oBAACH,YAAY;oBAAAN,QAAA,EAAC;kBAE7D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpF,OAAA,CAACpD,UAAU;oBAACyI,OAAO,EAAC,OAAO;oBAACI,KAAK,EAAC,gBAAgB;oBAACF,EAAE,EAAE;sBAAEI,EAAE,EAAE;oBAAE,CAAE;oBAAAX,QAAA,EAAC;kBAElE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpF,OAAA,CAACnD,MAAM;oBACLwI,OAAO,EAAC,WAAW;oBACnBgB,SAAS,eAAErG,OAAA,CAACvB,OAAO;sBAAAwG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACvBkB,OAAO,EAAEA,CAAA,KAAMtF,kBAAkB,CAAC,IAAI,CAAE;oBACxCuE,EAAE,EAAE;sBAAEW,aAAa,EAAE;oBAAO,CAAE;oBAAAlB,QAAA,EAC/B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,GAEXzE,OAAO,CAACoG,GAAG,CAAEa,OAAO,iBAClB5H,OAAA,CAAC7C,QAAQ;cAAA6H,QAAA,gBACPhF,OAAA,CAAChD,SAAS;gBAAAgI,QAAA,eACRhF,OAAA,CAACpD,UAAU;kBAACyI,OAAO,EAAC,OAAO;kBAACG,UAAU,EAAC,MAAM;kBAAAR,QAAA,EAC1C4C,OAAO,CAACG;gBAAc;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZpF,OAAA,CAAChD,SAAS;gBAAAgI,QAAA,eACRhF,OAAA,CAAC3C,IAAI;kBACH+I,KAAK,EAAE9B,mBAAmB,CAACsD,OAAO,CAACI,YAAY,CAAE;kBACjDT,IAAI,EAAC,OAAO;kBACZlC,OAAO,EAAC;gBAAU;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZpF,OAAA,CAAChD,SAAS;gBAAAgI,QAAA,eACRhF,OAAA,CAACpD,UAAU;kBAACyI,OAAO,EAAC,OAAO;kBAAAL,QAAA,EACxB4C,OAAO,CAACvE,YAAY,IAAI;gBAAe;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZpF,OAAA,CAAChD,SAAS;gBAAAgI,QAAA,eACRhF,OAAA,CAAC3C,IAAI;kBACH+I,KAAK,EAAEwB,OAAO,CAAClD,KAAK,IAAI,QAAS;kBACjC6C,IAAI,EAAC,OAAO;kBACZ9B,KAAK,EAAEhB,aAAa,CAACmD,OAAO,CAAClD,KAAK;gBAAE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZpF,OAAA,CAAChD,SAAS;gBAAAgI,QAAA,eACRhF,OAAA,CAAC3C,IAAI;kBACH+I,KAAK,EAAEwB,OAAO,CAACuB,QAAQ,IAAI,SAAU;kBACrC5B,IAAI,EAAC,OAAO;kBACZlC,OAAO,EAAC;gBAAU;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZpF,OAAA,CAAChD,SAAS;gBAAAgI,QAAA,eACRhF,OAAA,CAACpD,UAAU;kBAACyI,OAAO,EAAC,OAAO;kBAAAL,QAAA,EACxB4C,OAAO,CAACO,cAAc,GAAG,IAAIC,IAAI,CAACR,OAAO,CAACO,cAAc,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;gBAAG;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZpF,OAAA,CAAChD,SAAS;gBAAAgI,QAAA,eACRhF,OAAA,CAACvD,GAAG;kBAACmI,OAAO,EAAC,MAAM;kBAACuC,GAAG,EAAE,GAAI;kBAAAnC,QAAA,gBAC3BhF,OAAA,CAAClC,OAAO;oBAAC0J,KAAK,EAAC,YAAY;oBAAAxC,QAAA,eACzBhF,OAAA,CAAC1C,UAAU;sBAACiK,IAAI,EAAC,OAAO;sBAAAvC,QAAA,eACtBhF,OAAA,CAACjB,QAAQ;wBAAAkG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVpF,OAAA,CAAClC,OAAO;oBAAC0J,KAAK,EAAC,UAAU;oBAAAxC,QAAA,eACvBhF,OAAA,CAAC1C,UAAU;sBAACiK,IAAI,EAAC,OAAO;sBAAAvC,QAAA,eACtBhF,OAAA,CAACrB,QAAQ;wBAAAsG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVpF,OAAA,CAAClC,OAAO;oBAAC0J,KAAK,EAAC,SAAS;oBAAAxC,QAAA,eACtBhF,OAAA,CAAC1C,UAAU;sBAACiK,IAAI,EAAC,OAAO;sBAAC9B,KAAK,EAAC,OAAO;sBAAAT,QAAA,eACpChF,OAAA,CAACnB,UAAU;wBAAAoG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA,GAvDCwC,OAAO,CAACG,cAAc;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwD3B,CACX;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACN,eAGDpF,OAAA,CAACzC,MAAM;MACL6L,IAAI,EAAE7H,sBAAuB;MAC7B8H,OAAO,EAAEzF,6BAA8B;MACvC0F,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACVjE,EAAE,EAAE;UAAEmC,YAAY,EAAE;QAAE;MACxB,CAAE;MAAA1C,QAAA,gBAEFhF,OAAA,CAACxC,WAAW;QAAC+H,EAAE,EAAE;UAAEkE,EAAE,EAAE;QAAE,CAAE;QAAAzE,QAAA,eACzBhF,OAAA,CAACpD,UAAU;UAACyI,OAAO,EAAC,IAAI;UAACE,EAAE,EAAE;YAAEC,UAAU,EAAE;UAAI,CAAE;UAAAR,QAAA,EAC9CvD,sBAAsB,KAAK,QAAQ,GAAG,wBAAwB,GAAG;QAAuB;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdpF,OAAA,CAACvC,aAAa;QAAAuH,QAAA,eACZhF,OAAA,CAACvD,GAAG;UAAC8I,EAAE,EAAE;YAAEkC,EAAE,EAAE;UAAE,CAAE;UAAAzC,QAAA,gBACjBhF,OAAA,CAACrC,SAAS;YACR4L,SAAS;YACTnD,KAAK,EAAC,mBAAmB;YACzBN,KAAK,EAAEjE,oBAAoB,CAACE,iBAAkB;YAC9CgE,QAAQ,EAAGC,CAAC,IAAKlE,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEE,iBAAiB,EAAEiE,CAAC,CAAC0D,MAAM,CAAC5D;YAAM,CAAC,CAAE;YACzG6D,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRvE,OAAO,EAAC,UAAU;YAClBE,EAAE,EAAE;cAAEI,EAAE,EAAE;YAAE;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFpF,OAAA,CAACrC,SAAS;YACR4L,SAAS;YACTnD,KAAK,EAAC,OAAO;YACbyD,IAAI,EAAC,OAAO;YACZ/D,KAAK,EAAEjE,oBAAoB,CAACG,KAAM;YAClC+D,QAAQ,EAAGC,CAAC,IAAKlE,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEG,KAAK,EAAEgE,CAAC,CAAC0D,MAAM,CAAC5D;YAAM,CAAC,CAAE;YAC7F6D,MAAM,EAAC,QAAQ;YACftE,OAAO,EAAC,UAAU;YAClByE,UAAU,EAAC,uDAAuD;YAClEvE,EAAE,EAAE;cAAEI,EAAE,EAAE;YAAE;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFpF,OAAA,CAACrC,SAAS;YACR4L,SAAS;YACTnD,KAAK,EAAC,UAAU;YAChBN,KAAK,EAAEjE,oBAAoB,CAACI,QAAS;YACrC8D,QAAQ,EAAGC,CAAC,IAAKlE,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEI,QAAQ,EAAE+D,CAAC,CAAC0D,MAAM,CAAC5D;YAAM,CAAC,CAAE;YAChG6D,MAAM,EAAC,QAAQ;YACftE,OAAO,EAAC,UAAU;YAClByE,UAAU,EAAC;UAA+C;YAAA7E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBpF,OAAA,CAACtC,aAAa;QAAC6H,EAAE,EAAE;UAAEoB,CAAC,EAAE,CAAC;UAAEc,EAAE,EAAE;QAAE,CAAE;QAAAzC,QAAA,gBACjChF,OAAA,CAACnD,MAAM;UACLyJ,OAAO,EAAE1C,6BAA8B;UACvC2B,EAAE,EAAE;YAAEW,aAAa,EAAE;UAAO,CAAE;UAAAlB,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpF,OAAA,CAACnD,MAAM;UACLyJ,OAAO,EAAEzC,wBAAyB;UAClCwB,OAAO,EAAC,WAAW;UACnBE,EAAE,EAAE;YACFW,aAAa,EAAE,MAAM;YACrBV,UAAU,EAAE,GAAG;YACfe,EAAE,EAAE;UACN,CAAE;UAAAvB,QAAA,EAEDvD,sBAAsB,KAAK,QAAQ,GAAG,MAAM,GAAG;QAAO;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTpF,OAAA,CAACF,kBAAkB;MACjBI,UAAU,EAAEA,UAAW;MACvBkJ,IAAI,EAAErI,eAAgB;MACtBsI,OAAO,EAAEA,CAAA,KAAMrI,kBAAkB,CAAC,KAAK,CAAE;MACzC+I,SAAS,EAAEA,CAAA,KAAM;QACf7H,WAAW,CAAC,CAAC;QACbC,eAAe,CAAC,CAAC;QACjBC,gBAAgB,CAAC,CAAC;QAClBpB,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IAAE;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAChF,EAAA,CA7sBIH,wBAAwB;AAAA+J,EAAA,GAAxB/J,wBAAwB;AA+sB9B,eAAeA,wBAAwB;AAAC,IAAA+J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}