{"ast": null, "code": "var baseAssignValue = require('./_baseAssignValue'),\n  baseForOwn = require('./_baseForOwn'),\n  baseIteratee = require('./_baseIteratee');\n\n/**\n * Creates an object with the same keys as `object` and values generated\n * by running each own enumerable string keyed property of `object` thru\n * `iteratee`. The iteratee is invoked with three arguments:\n * (value, key, object).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns the new mapped object.\n * @see _.mapKeys\n * @example\n *\n * var users = {\n *   'fred':    { 'user': 'fred',    'age': 40 },\n *   'pebbles': { 'user': 'pebbles', 'age': 1 }\n * };\n *\n * _.mapValues(users, function(o) { return o.age; });\n * // => { 'fred': 40, 'pebbles': 1 } (iteration order is not guaranteed)\n *\n * // The `_.property` iteratee shorthand.\n * _.mapValues(users, 'age');\n * // => { 'fred': 40, 'pebbles': 1 } (iteration order is not guaranteed)\n */\nfunction mapValues(object, iteratee) {\n  var result = {};\n  iteratee = baseIteratee(iteratee, 3);\n  baseForOwn(object, function (value, key, object) {\n    baseAssignValue(result, key, iteratee(value, key, object));\n  });\n  return result;\n}\nmodule.exports = mapValues;", "map": {"version": 3, "names": ["baseAssignValue", "require", "baseForOwn", "baseIteratee", "mapValues", "object", "iteratee", "result", "value", "key", "module", "exports"], "sources": ["C:/CMS/webapp/frontend/node_modules/lodash/mapValues.js"], "sourcesContent": ["var baseAssignValue = require('./_baseAssignValue'),\n    baseForOwn = require('./_baseForOwn'),\n    baseIteratee = require('./_baseIteratee');\n\n/**\n * Creates an object with the same keys as `object` and values generated\n * by running each own enumerable string keyed property of `object` thru\n * `iteratee`. The iteratee is invoked with three arguments:\n * (value, key, object).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns the new mapped object.\n * @see _.mapKeys\n * @example\n *\n * var users = {\n *   'fred':    { 'user': 'fred',    'age': 40 },\n *   'pebbles': { 'user': 'pebbles', 'age': 1 }\n * };\n *\n * _.mapValues(users, function(o) { return o.age; });\n * // => { 'fred': 40, 'pebbles': 1 } (iteration order is not guaranteed)\n *\n * // The `_.property` iteratee shorthand.\n * _.mapValues(users, 'age');\n * // => { 'fred': 40, 'pebbles': 1 } (iteration order is not guaranteed)\n */\nfunction mapValues(object, iteratee) {\n  var result = {};\n  iteratee = baseIteratee(iteratee, 3);\n\n  baseForOwn(object, function(value, key, object) {\n    baseAssignValue(result, key, iteratee(value, key, object));\n  });\n  return result;\n}\n\nmodule.exports = mapValues;\n"], "mappings": "AAAA,IAAIA,eAAe,GAAGC,OAAO,CAAC,oBAAoB,CAAC;EAC/CC,UAAU,GAAGD,OAAO,CAAC,eAAe,CAAC;EACrCE,YAAY,GAAGF,OAAO,CAAC,iBAAiB,CAAC;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,SAASA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EACnC,IAAIC,MAAM,GAAG,CAAC,CAAC;EACfD,QAAQ,GAAGH,YAAY,CAACG,QAAQ,EAAE,CAAC,CAAC;EAEpCJ,UAAU,CAACG,MAAM,EAAE,UAASG,KAAK,EAAEC,GAAG,EAAEJ,MAAM,EAAE;IAC9CL,eAAe,CAACO,MAAM,EAAEE,GAAG,EAAEH,QAAQ,CAACE,KAAK,EAAEC,GAAG,EAAEJ,MAAM,CAAC,CAAC;EAC5D,CAAC,CAAC;EACF,OAAOE,MAAM;AACf;AAEAG,MAAM,CAACC,OAAO,GAAGP,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}