{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\nconst eraValues = {\n  narrow: [\"до н.э.\", \"н.э.\"],\n  abbreviated: [\"до н. э.\", \"н. э.\"],\n  wide: [\"до нашей эры\", \"нашей эры\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-й кв.\", \"2-й кв.\", \"3-й кв.\", \"4-й кв.\"],\n  wide: [\"1-й квартал\", \"2-й квартал\", \"3-й квартал\", \"4-й квартал\"]\n};\nconst monthValues = {\n  narrow: [\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"],\n  abbreviated: [\"янв.\", \"фев.\", \"март\", \"апр.\", \"май\", \"июнь\", \"июль\", \"авг.\", \"сент.\", \"окт.\", \"нояб.\", \"дек.\"],\n  wide: [\"январь\", \"февраль\", \"март\", \"апрель\", \"май\", \"июнь\", \"июль\", \"август\", \"сентябрь\", \"октябрь\", \"ноябрь\", \"декабрь\"]\n};\nconst formattingMonthValues = {\n  narrow: [\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"],\n  abbreviated: [\"янв.\", \"фев.\", \"мар.\", \"апр.\", \"мая\", \"июн.\", \"июл.\", \"авг.\", \"сент.\", \"окт.\", \"нояб.\", \"дек.\"],\n  wide: [\"января\", \"февраля\", \"марта\", \"апреля\", \"мая\", \"июня\", \"июля\", \"августа\", \"сентября\", \"октября\", \"ноября\", \"декабря\"]\n};\nconst dayValues = {\n  narrow: [\"В\", \"П\", \"В\", \"С\", \"Ч\", \"П\", \"С\"],\n  short: [\"вс\", \"пн\", \"вт\", \"ср\", \"чт\", \"пт\", \"сб\"],\n  abbreviated: [\"вск\", \"пнд\", \"втр\", \"срд\", \"чтв\", \"птн\", \"суб\"],\n  wide: [\"воскресенье\", \"понедельник\", \"вторник\", \"среда\", \"четверг\", \"пятница\", \"суббота\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полн.\",\n    noon: \"полд.\",\n    morning: \"утро\",\n    afternoon: \"день\",\n    evening: \"веч.\",\n    night: \"ночь\"\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полн.\",\n    noon: \"полд.\",\n    morning: \"утро\",\n    afternoon: \"день\",\n    evening: \"веч.\",\n    night: \"ночь\"\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полночь\",\n    noon: \"полдень\",\n    morning: \"утро\",\n    afternoon: \"день\",\n    evening: \"вечер\",\n    night: \"ночь\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полн.\",\n    noon: \"полд.\",\n    morning: \"утра\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночи\"\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полн.\",\n    noon: \"полд.\",\n    morning: \"утра\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночи\"\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полночь\",\n    noon: \"полдень\",\n    morning: \"утра\",\n    afternoon: \"дня\",\n    evening: \"вечера\",\n    night: \"ночи\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = options?.unit;\n  let suffix;\n  if (unit === \"date\") {\n    suffix = \"-е\";\n  } else if (unit === \"week\" || unit === \"minute\" || unit === \"second\") {\n    suffix = \"-я\";\n  } else {\n    suffix = \"-й\";\n  }\n  return number + suffix;\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "formattingMonthValues", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "suffix", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ru/_lib/localize.mjs"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\nconst eraValues = {\n  narrow: [\"до н.э.\", \"н.э.\"],\n  abbreviated: [\"до н. э.\", \"н. э.\"],\n  wide: [\"до нашей эры\", \"нашей эры\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-й кв.\", \"2-й кв.\", \"3-й кв.\", \"4-й кв.\"],\n  wide: [\"1-й квартал\", \"2-й квартал\", \"3-й квартал\", \"4-й квартал\"],\n};\n\nconst monthValues = {\n  narrow: [\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"],\n  abbreviated: [\n    \"янв.\",\n    \"фев.\",\n    \"март\",\n    \"апр.\",\n    \"май\",\n    \"июнь\",\n    \"июль\",\n    \"авг.\",\n    \"сент.\",\n    \"окт.\",\n    \"нояб.\",\n    \"дек.\",\n  ],\n\n  wide: [\n    \"январь\",\n    \"февраль\",\n    \"март\",\n    \"апрель\",\n    \"май\",\n    \"июнь\",\n    \"июль\",\n    \"август\",\n    \"сентябрь\",\n    \"октябрь\",\n    \"ноябрь\",\n    \"декабрь\",\n  ],\n};\n\nconst formattingMonthValues = {\n  narrow: [\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"],\n  abbreviated: [\n    \"янв.\",\n    \"фев.\",\n    \"мар.\",\n    \"апр.\",\n    \"мая\",\n    \"июн.\",\n    \"июл.\",\n    \"авг.\",\n    \"сент.\",\n    \"окт.\",\n    \"нояб.\",\n    \"дек.\",\n  ],\n\n  wide: [\n    \"января\",\n    \"февраля\",\n    \"марта\",\n    \"апреля\",\n    \"мая\",\n    \"июня\",\n    \"июля\",\n    \"августа\",\n    \"сентября\",\n    \"октября\",\n    \"ноября\",\n    \"декабря\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"В\", \"П\", \"В\", \"С\", \"Ч\", \"П\", \"С\"],\n  short: [\"вс\", \"пн\", \"вт\", \"ср\", \"чт\", \"пт\", \"сб\"],\n  abbreviated: [\"вск\", \"пнд\", \"втр\", \"срд\", \"чтв\", \"птн\", \"суб\"],\n  wide: [\n    \"воскресенье\",\n    \"понедельник\",\n    \"вторник\",\n    \"среда\",\n    \"четверг\",\n    \"пятница\",\n    \"суббота\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полн.\",\n    noon: \"полд.\",\n    morning: \"утро\",\n    afternoon: \"день\",\n    evening: \"веч.\",\n    night: \"ночь\",\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полн.\",\n    noon: \"полд.\",\n    morning: \"утро\",\n    afternoon: \"день\",\n    evening: \"веч.\",\n    night: \"ночь\",\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полночь\",\n    noon: \"полдень\",\n    morning: \"утро\",\n    afternoon: \"день\",\n    evening: \"вечер\",\n    night: \"ночь\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полн.\",\n    noon: \"полд.\",\n    morning: \"утра\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночи\",\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полн.\",\n    noon: \"полд.\",\n    morning: \"утра\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночи\",\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"полночь\",\n    noon: \"полдень\",\n    morning: \"утра\",\n    afternoon: \"дня\",\n    evening: \"вечера\",\n    night: \"ночи\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = options?.unit;\n\n  let suffix;\n  if (unit === \"date\") {\n    suffix = \"-е\";\n  } else if (unit === \"week\" || unit === \"minute\" || unit === \"second\") {\n    suffix = \"-я\";\n  } else {\n    suffix = \"-й\";\n  }\n\n  return number + suffix;\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gCAAgC;AAEhE,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;EAC3BC,WAAW,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;EAClCC,IAAI,EAAE,CAAC,cAAc,EAAE,WAAW;AACpC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EACzDC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,CACP;EAEDC,IAAI,EAAE,CACJ,QAAQ,EACR,SAAS,EACT,MAAM,EACN,QAAQ,EACR,KAAK,EACL,MAAM,EACN,MAAM,EACN,QAAQ,EACR,UAAU,EACV,SAAS,EACT,QAAQ,EACR,SAAS;AAEb,CAAC;AAED,MAAMG,qBAAqB,GAAG;EAC5BL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,CACP;EAEDC,IAAI,EAAE,CACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,QAAQ,EACR,KAAK,EACL,MAAM,EACN,MAAM,EACN,SAAS,EACT,UAAU,EACV,SAAS,EACT,QAAQ,EACR,SAAS;AAEb,CAAC;AAED,MAAMI,SAAS,GAAG;EAChBN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDN,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CACJ,aAAa,EACb,aAAa,EACb,SAAS,EACT,OAAO,EACP,SAAS,EACT,SAAS,EACT,SAAS;AAEb,CAAC;AAED,MAAMM,eAAe,GAAG;EACtBR,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChCjB,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,OAAO,KAAK;EAC9C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAClC,MAAMI,IAAI,GAAGH,OAAO,EAAEG,IAAI;EAE1B,IAAIC,MAAM;EACV,IAAID,IAAI,KAAK,MAAM,EAAE;IACnBC,MAAM,GAAG,IAAI;EACf,CAAC,MAAM,IAAID,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ,EAAE;IACpEC,MAAM,GAAG,IAAI;EACf,CAAC,MAAM;IACLA,MAAM,GAAG,IAAI;EACf;EAEA,OAAOH,MAAM,GAAGG,MAAM;AACxB,CAAC;AAED,OAAO,MAAMC,QAAQ,GAAG;EACtBP,aAAa;EAEbQ,GAAG,EAAE5B,eAAe,CAAC;IACnB6B,MAAM,EAAE5B,SAAS;IACjB6B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE/B,eAAe,CAAC;IACvB6B,MAAM,EAAExB,aAAa;IACrByB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAEjC,eAAe,CAAC;IACrB6B,MAAM,EAAEvB,WAAW;IACnBwB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAE3B,qBAAqB;IACvC4B,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EAEFC,GAAG,EAAEpC,eAAe,CAAC;IACnB6B,MAAM,EAAErB,SAAS;IACjBsB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFO,SAAS,EAAErC,eAAe,CAAC;IACzB6B,MAAM,EAAEnB,eAAe;IACvBoB,YAAY,EAAE,KAAK;IACnBI,gBAAgB,EAAEf,yBAAyB;IAC3CgB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}