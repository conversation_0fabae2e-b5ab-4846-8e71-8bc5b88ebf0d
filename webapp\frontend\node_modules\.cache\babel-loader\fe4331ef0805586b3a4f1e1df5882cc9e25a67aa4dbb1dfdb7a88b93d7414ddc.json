{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeListRivoluzionato.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport { Box, Typography, Button, Paper, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Alert, CircularProgress, List, ListItem, ListItemText, Stack, MenuItem, Divider } from '@mui/material';\nimport { Add as AddIcon, Assignment as AssignIcon, Person as PersonIcon, CheckCircle as CheckCircleIcon, Verified as VerifiedIcon, People as PeopleIcon, Construction as ConstructionIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport ResponsabiliListPopup from './ResponsabiliListPopup';\nimport ComandeListTable from './ComandeListTable';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ComandeListRivoluzionato = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  // Hook per gestire i parametri URL\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchingComanda, setSearchingComanda] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [allComande, setAllComande] = useState([]);\n  const [loadingComande, setLoadingComande] = useState(false);\n\n  // Stati per popup responsabili\n  const [openResponsabiliPopup, setOpenResponsabiliPopup] = useState(false);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [dialogModeComanda, setDialogModeComanda] = useState('view'); // 'view', 'edit'\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    note_capo_cantiere: ''\n  });\n  const loadComande = async () => {\n    try {\n      setLoadingComande(true);\n      console.log('🔄 Caricamento comande per cantiere:', cantiereId);\n      const comandeData = await comandeService.getComande(cantiereId);\n      console.log('📋 Dati comande ricevuti:', comandeData);\n      console.log('📋 Tipo dati:', typeof comandeData, 'Array?', Array.isArray(comandeData));\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(comandeData)) {\n        comandeArray = comandeData;\n      } else if (comandeData && Array.isArray(comandeData.comande)) {\n        comandeArray = comandeData.comande;\n      } else if (comandeData && Array.isArray(comandeData.data)) {\n        comandeArray = comandeData.data;\n      }\n      console.log('📋 Array comande finale:', comandeArray, 'Lunghezza:', comandeArray.length);\n      setAllComande(comandeArray);\n    } catch (err) {\n      console.error('Errore nel caricamento comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoadingComande(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      console.log('🔄 Caricamento statistiche per cantiere:', cantiereId);\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      console.log('📊 Statistiche ricevute:', stats);\n      setStatistiche(stats);\n    } catch (err) {\n      var _err$response;\n      console.error('❌ Errore nel caricamento delle statistiche:', err);\n      console.error('❌ Dettagli errore:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n    }\n  };\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail) || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    const initializeData = async () => {\n      if (cantiereId) {\n        setLoading(true);\n        try {\n          await Promise.all([loadResponsabili(), loadComande(), loadStatistiche()]);\n        } catch (err) {\n          console.error('Errore nel caricamento iniziale:', err);\n        } finally {\n          setLoading(false);\n        }\n      }\n    };\n    initializeData();\n  }, [cantiereId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Gestione parametro URL per aprire comanda specifica\n  useEffect(() => {\n    const comandaParam = searchParams.get('comanda');\n    console.log('🔍 Controllo parametro URL comanda:', comandaParam);\n    console.log('📊 Stato dati:', {\n      responsabili: responsabili.length,\n      comandePerResponsabile: Object.keys(comandePerResponsabile).length,\n      loading,\n      loadingResponsabili\n    });\n\n    // Imposta lo stato di ricerca\n    if (comandaParam && comandaParam !== searchingComanda) {\n      setSearchingComanda(comandaParam);\n    }\n    if (comandaParam && responsabili.length > 0 && Object.keys(comandePerResponsabile).length > 0) {\n      console.log('🔎 Ricerca comanda tra i responsabili...');\n\n      // Cerca la comanda tra tutti i responsabili\n      let comandaTrovata = null;\n      for (const responsabile of responsabili) {\n        const comandeResp = comandePerResponsabile[responsabile.id_responsabile] || [];\n        console.log(`📋 Responsabile ${responsabile.nome_responsabile}: ${comandeResp.length} comande`);\n        comandaTrovata = comandeResp.find(c => c.codice_comanda === comandaParam);\n        if (comandaTrovata) {\n          console.log('✅ Comanda trovata:', comandaTrovata);\n          break;\n        }\n      }\n      if (comandaTrovata) {\n        console.log('🎯 Apertura automatica comanda da URL:', comandaParam);\n        setSearchingComanda(null); // Rimuovi lo stato di ricerca\n        handleOpenComandaDialog('view', comandaTrovata);\n        // Rimuovi il parametro dall'URL per evitare riaperture\n        setTimeout(() => {\n          setSearchParams(prev => {\n            const newParams = new URLSearchParams(prev);\n            newParams.delete('comanda');\n            return newParams;\n          });\n        }, 100);\n      } else {\n        console.warn('⚠️ Comanda non trovata:', comandaParam);\n        console.log('📋 Comande disponibili:');\n        responsabili.forEach(resp => {\n          const comande = comandePerResponsabile[resp.id_responsabile] || [];\n          comande.forEach(cmd => {\n            console.log(`  - ${cmd.codice_comanda} (${resp.nome_responsabile})`);\n          });\n        });\n\n        // Se non troviamo la comanda, potrebbe essere che i dati non sono completi\n        // Riprova dopo un breve delay\n        if (!loading && !loadingResponsabili) {\n          console.log('🔄 Tentativo di ricaricamento dati...');\n          setTimeout(() => {\n            loadResponsabili();\n          }, 500);\n        }\n      }\n    } else if (comandaParam) {\n      console.log('⏳ Dati non ancora caricati, attendo...');\n\n      // Se abbiamo un parametro ma i dati non sono caricati, forza il caricamento\n      if (!loading && !loadingResponsabili && responsabili.length === 0) {\n        console.log('🚀 Forzatura caricamento responsabili...');\n        loadResponsabili();\n      }\n    }\n  }, [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadComandePerResponsabili = async responsabiliList => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    setOpenResponsabileDialog(true);\n  };\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n  const handleDeleteResponsabile = async idResponsabile => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = (mode, comanda = null) => {\n    setDialogModeComanda(mode);\n    setSelectedComanda(comanda);\n    if (mode === 'edit' && comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n    setOpenComandaDialog(true);\n  };\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      note_capo_cantiere: ''\n    });\n  };\n  const handleSubmitComanda = async () => {\n    try {\n      if (dialogModeComanda === 'edit') {\n        await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n        handleCloseComandaDialog();\n        await loadResponsabili(); // Ricarica per aggiornare le comande\n        await loadComande();\n        await loadStatistiche();\n      }\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n  const handleDeleteComanda = async codiceComanda => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione',\n      'TESTING': 'Testing/Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n  const getStatoColor = stato => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n  if (loading || loadingComande) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 600,\n          color: 'primary.main'\n        },\n        children: cantiereName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 438,\n      columnNumber: 9\n    }, this), searchingComanda && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 2\n      },\n      children: [\"\\uD83D\\uDD0D Ricerca comanda \", searchingComanda, \" in corso...\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 444,\n      columnNumber: 9\n    }, this), statistiche && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3,\n        bgcolor: 'grey.50'\n      },\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 4,\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        flexWrap: \"wrap\",\n        children: [/*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n            color: \"primary\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.responsabili_attivi || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Responsabili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n            color: \"info\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.totale_comande || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Totale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n            color: \"warning\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_in_corso || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"In Corso\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(VerifiedIcon, {\n            color: \"success\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_completate || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Completate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: 32,\n              height: 32,\n              borderRadius: '50%',\n              bgcolor: statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.8 ? 'success.main' : statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.5 ? 'warning.main' : 'error.main',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              fontWeight: \"bold\",\n              color: \"white\",\n              children: [statistiche.totale_comande > 0 ? Math.round(statistiche.comande_completate / statistiche.totale_comande * 100) : 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"medium\",\n              sx: {\n                lineHeight: 1\n              },\n              children: \"Completamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [statistiche.comande_create || 0, \" create\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          mb: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 500,\n              color: 'text.primary'\n            },\n            children: \"Gestione Responsabili e Comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            gap: 2,\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 28\n              }, this),\n              onClick: () => setOpenResponsabiliPopup(true),\n              sx: {\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1,\n                backgroundColor: '#f5f7fa',\n                color: '#2196f3',\n                border: '1px solid #2196f3',\n                '&:hover': {\n                  backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                  borderColor: '#1976d2'\n                }\n              },\n              children: \"Lista Responsabili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 28\n              }, this),\n              onClick: () => handleOpenResponsabileDialog('create'),\n              sx: {\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1,\n                backgroundColor: '#2196f3',\n                color: 'white',\n                '&:hover': {\n                  backgroundColor: '#1976d2'\n                }\n              },\n              children: \"Crea Responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this), loadingComande ? /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          py: 4,\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 13\n        }, this) : allComande.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 0,\n          sx: {\n            p: 6,\n            textAlign: 'center',\n            backgroundColor: 'grey.50',\n            border: '1px dashed',\n            borderColor: 'grey.300'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n            sx: {\n              fontSize: 48,\n              color: 'grey.400',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"text.secondary\",\n            gutterBottom: true,\n            children: \"Nessuna comanda disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 3\n            },\n            children: \"Crea la prima comanda per iniziare a gestire i lavori\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 28\n            }, this),\n            onClick: () => setOpenCreaConCavi(true),\n            sx: {\n              textTransform: 'none',\n              backgroundColor: '#2196f3',\n              color: 'white',\n              '&:hover': {\n                backgroundColor: '#1976d2'\n              }\n            },\n            children: \"Crea Prima Comanda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(ComandeListTable, {\n          comande: allComande,\n          onViewComanda: handleOpenComandaDialog.bind(null, 'view'),\n          onEditComanda: handleOpenComandaDialog.bind(null, 'edit'),\n          onDeleteComanda: handleDeleteComanda,\n          loading: loadingComande\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 536,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openResponsabileDialog,\n      onClose: handleCloseResponsabileDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 645,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nome Responsabile\",\n            value: formDataResponsabile.nome_responsabile,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              nome_responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true,\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: formDataResponsabile.email,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              email: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Email per notifiche (opzionale se inserisci telefono)\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Telefono\",\n            value: formDataResponsabile.telefono,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              telefono: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Numero per SMS (opzionale se inserisci email)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 650,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseResponsabileDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 687,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitResponsabile,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 693,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 686,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 636,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openComandaDialog,\n      onClose: handleCloseComandaDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeComanda === 'view' ? 'Dettagli Comanda' : 'Modifica Comanda'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 717,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: dialogModeComanda === 'view' && selectedComanda ? /*#__PURE__*/_jsxDEV(List, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Codice Comanda\",\n                secondary: selectedComanda.codice_comanda\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Tipo\",\n                secondary: getTipoComandaLabel(selectedComanda.tipo_comanda)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 739,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Stato\",\n                secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: selectedComanda.stato,\n                  color: getStatoColor(selectedComanda.stato),\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 740,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Descrizione\",\n                secondary: selectedComanda.descrizione || 'Nessuna descrizione'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Responsabile\",\n                secondary: selectedComanda.responsabile || 'Non assegnato'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 760,\n              columnNumber: 17\n            }, this), selectedComanda.note_capo_cantiere && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Note Capo Cantiere\",\n                  secondary: selectedComanda.note_capo_cantiere\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Data Creazione\",\n                secondary: new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 779,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 778,\n              columnNumber: 17\n            }, this), selectedComanda.data_scadenza && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 786,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Data Scadenza\",\n                  secondary: new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 788,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 787,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 795,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Cavi Assegnati\",\n                secondary: selectedComanda.numero_cavi_assegnati || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 797,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 796,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Completamento\",\n                secondary: `${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 804,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 803,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Tipo Comanda\",\n              value: formDataComanda.tipo_comanda,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                tipo_comanda: e.target.value\n              }),\n              margin: \"normal\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"POSA\",\n                children: \"Posa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 821,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_PARTENZA\",\n                children: \"Collegamento Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_ARRIVO\",\n                children: \"Collegamento Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 823,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"CERTIFICAZIONE\",\n                children: \"Certificazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 824,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"TESTING\",\n                children: \"Testing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 812,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Descrizione\",\n              value: formDataComanda.descrizione,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                descrizione: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 3,\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Responsabile\",\n              value: formDataComanda.responsabile,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                responsabile: e.target.value\n              }),\n              margin: \"normal\",\n              required: true,\n              helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 841,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note Capo Cantiere\",\n              value: formDataComanda.note_capo_cantiere,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                note_capo_cantiere: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 2,\n              helperText: \"Istruzioni specifiche per il responsabile\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Data Scadenza\",\n              type: \"date\",\n              value: formDataComanda.data_scadenza,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                data_scadenza: e.target.value\n              }),\n              margin: \"normal\",\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 723,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 722,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseComandaDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: dialogModeComanda === 'view' ? 'Chiudi' : 'Annulla'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 880,\n          columnNumber: 11\n        }, this), dialogModeComanda === 'edit' && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitComanda,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: \"Salva Modifiche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 887,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 879,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 708,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n      cantiereId: cantiereId,\n      open: openCreaConCavi,\n      onClose: () => setOpenCreaConCavi(false),\n      onSuccess: (response, successMessage) => {\n        console.log('🎉 Comanda creata, aggiornamento interfaccia...');\n\n        // Mostra messaggio di successo se fornito\n        if (successMessage) {\n          // Potresti aggiungere qui un toast/snackbar per mostrare il messaggio\n          console.log('📢 Successo:', successMessage);\n        }\n\n        // Ricarica tutti i dati per aggiornare l'interfaccia\n        loadComande();\n        loadStatistiche();\n        loadResponsabili();\n        setOpenCreaConCavi(false);\n        console.log('✅ Interfaccia aggiornata');\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 903,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ResponsabiliListPopup, {\n      open: openResponsabiliPopup,\n      onClose: () => setOpenResponsabiliPopup(false),\n      responsabili: responsabili,\n      comandePerResponsabile: comandePerResponsabile,\n      onEditResponsabile: responsabile => {\n        setOpenResponsabiliPopup(false);\n        handleOpenResponsabileDialog('edit', responsabile);\n      },\n      onDeleteResponsabile: async idResponsabile => {\n        await handleDeleteResponsabile(idResponsabile);\n        setOpenResponsabiliPopup(false);\n      },\n      loading: loadingResponsabili,\n      error: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 927,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 429,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeListRivoluzionato, \"xi6+DixhOaRxNivspx16VcpqUsg=\", false, function () {\n  return [useSearchParams];\n});\n_c = ComandeListRivoluzionato;\nexport default ComandeListRivoluzionato;\nvar _c;\n$RefreshReg$(_c, \"ComandeListRivoluzionato\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSearchParams", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "<PERSON><PERSON>", "CircularProgress", "List", "ListItem", "ListItemText", "<PERSON><PERSON>", "MenuItem", "Divider", "Add", "AddIcon", "Assignment", "AssignIcon", "Person", "PersonIcon", "CheckCircle", "CheckCircleIcon", "Verified", "VerifiedIcon", "People", "PeopleIcon", "Construction", "ConstructionIcon", "comandeService", "responsabiliService", "CreaComandaConCavi", "ResponsabiliListPopup", "ComandeListTable", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ComandeListRivoluzionato", "cantiereId", "cantiereName", "_s", "searchParams", "setSearchParams", "loading", "setLoading", "error", "setError", "searchingComanda", "setSearchingComanda", "statistiche", "setStatistiche", "allComande", "setAllComande", "loadingComande", "setLoadingComande", "openResponsabiliPopup", "setOpenResponsabiliPopup", "openCreaConCavi", "setOpenCreaConCavi", "responsabili", "setResponsabili", "loadingResponsabili", "setLoadingResponsabili", "comandePerResponsabile", "setComandePerResponsabile", "openResponsabileDialog", "setOpenResponsabileDialog", "dialogModeResponsabile", "setDialogModeResponsabile", "selectedResponsabile", "setSelectedResponsabile", "formDataResponsabile", "setFormDataResponsabile", "nome_responsabile", "email", "telefono", "openComandaDialog", "setOpenComandaDialog", "dialogModeComanda", "setDialogModeComanda", "selectedComanda", "setSelectedComanda", "formDataComanda", "setFormDataComanda", "tipo_comanda", "descrizione", "responsabile", "data_scadenza", "note_capo_cantiere", "loadComande", "console", "log", "comandeData", "getComande", "Array", "isArray", "comandeArray", "comande", "data", "length", "err", "loadStatistiche", "stats", "getStatisticheComande", "_err$response", "response", "message", "loadResponsabili", "getResponsabiliCantiere", "loadComandePerResponsabili", "_err$response2", "_err$response2$data", "errorMessage", "detail", "initializeData", "Promise", "all", "comandaParam", "get", "Object", "keys", "comandaTrovata", "comandeResp", "id_responsabile", "find", "c", "codice_comanda", "handleOpenComandaDialog", "setTimeout", "prev", "newParams", "URLSearchParams", "delete", "warn", "for<PERSON>ach", "resp", "cmd", "responsabiliList", "comandeMap", "getComandeByResponsabile", "handleOpenResponsabileDialog", "mode", "handleCloseResponsabileDialog", "handleSubmitResponsabile", "trim", "createResponsabile", "updateResponsabile", "handleDeleteResponsabile", "idResponsabile", "window", "confirm", "deleteResponsabile", "comanda", "handleCloseComandaDialog", "handleSubmitComanda", "updateComanda", "handleDeleteComanda", "codiceComanda", "deleteComanda", "getTipoComandaLabel", "tipo", "labels", "getStatoColor", "stato", "colors", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "variant", "fontWeight", "color", "severity", "bgcolor", "direction", "spacing", "flexWrap", "fontSize", "lineHeight", "responsabili_attivi", "totale_comande", "comande_in_corso", "comande_completate", "width", "height", "borderRadius", "Math", "round", "comande_create", "gap", "startIcon", "onClick", "textTransform", "px", "py", "backgroundColor", "border", "borderColor", "elevation", "textAlign", "gutterBottom", "onViewComanda", "bind", "onEditComanda", "onDeleteComanda", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "pb", "pt", "label", "value", "onChange", "e", "target", "margin", "required", "type", "helperText", "primary", "secondary", "size", "Date", "data_creazione", "toLocaleDateString", "numero_cavi_assegnati", "percentuale_completamento", "toFixed", "select", "multiline", "rows", "InputLabelProps", "shrink", "onSuccess", "successMessage", "onEditResponsabile", "onDeleteResponsabile", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeListRivoluzionato.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Alert,\n  CircularProgress,\n  List,\n  ListItem,\n  ListItemText,\n  Stack,\n  MenuItem,\n  Divider\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Assignment as AssignIcon,\n  Person as PersonIcon,\n  CheckCircle as CheckCircleIcon,\n  Verified as VerifiedIcon,\n  People as PeopleIcon,\n  Construction as ConstructionIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport ResponsabiliListPopup from './ResponsabiliListPopup';\nimport ComandeListTable from './ComandeListTable';\n\nconst ComandeListRivoluzionato = ({ cantiereId, cantiereName }) => {\n  // Hook per gestire i parametri URL\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchingComanda, setSearchingComanda] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [allComande, setAllComande] = useState([]);\n  const [loadingComande, setLoadingComande] = useState(false);\n\n  // Stati per popup responsabili\n  const [openResponsabiliPopup, setOpenResponsabiliPopup] = useState(false);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [dialogModeComanda, setDialogModeComanda] = useState('view'); // 'view', 'edit'\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    note_capo_cantiere: ''\n  });\n\n  const loadComande = async () => {\n    try {\n      setLoadingComande(true);\n      console.log('🔄 Caricamento comande per cantiere:', cantiereId);\n      const comandeData = await comandeService.getComande(cantiereId);\n      console.log('📋 Dati comande ricevuti:', comandeData);\n      console.log('📋 Tipo dati:', typeof comandeData, 'Array?', Array.isArray(comandeData));\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(comandeData)) {\n        comandeArray = comandeData;\n      } else if (comandeData && Array.isArray(comandeData.comande)) {\n        comandeArray = comandeData.comande;\n      } else if (comandeData && Array.isArray(comandeData.data)) {\n        comandeArray = comandeData.data;\n      }\n\n      console.log('📋 Array comande finale:', comandeArray, 'Lunghezza:', comandeArray.length);\n      setAllComande(comandeArray);\n    } catch (err) {\n      console.error('Errore nel caricamento comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoadingComande(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      console.log('🔄 Caricamento statistiche per cantiere:', cantiereId);\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      console.log('📊 Statistiche ricevute:', stats);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('❌ Errore nel caricamento delle statistiche:', err);\n      console.error('❌ Dettagli errore:', err.response?.data || err.message);\n    }\n  };\n\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = err.response?.data?.detail || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    const initializeData = async () => {\n      if (cantiereId) {\n        setLoading(true);\n        try {\n          await Promise.all([\n            loadResponsabili(),\n            loadComande(),\n            loadStatistiche()\n          ]);\n        } catch (err) {\n          console.error('Errore nel caricamento iniziale:', err);\n        } finally {\n          setLoading(false);\n        }\n      }\n    };\n\n    initializeData();\n  }, [cantiereId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Gestione parametro URL per aprire comanda specifica\n  useEffect(() => {\n    const comandaParam = searchParams.get('comanda');\n    console.log('🔍 Controllo parametro URL comanda:', comandaParam);\n    console.log('📊 Stato dati:', {\n      responsabili: responsabili.length,\n      comandePerResponsabile: Object.keys(comandePerResponsabile).length,\n      loading,\n      loadingResponsabili\n    });\n\n    // Imposta lo stato di ricerca\n    if (comandaParam && comandaParam !== searchingComanda) {\n      setSearchingComanda(comandaParam);\n    }\n\n    if (comandaParam && responsabili.length > 0 && Object.keys(comandePerResponsabile).length > 0) {\n      console.log('🔎 Ricerca comanda tra i responsabili...');\n\n      // Cerca la comanda tra tutti i responsabili\n      let comandaTrovata = null;\n\n      for (const responsabile of responsabili) {\n        const comandeResp = comandePerResponsabile[responsabile.id_responsabile] || [];\n        console.log(`📋 Responsabile ${responsabile.nome_responsabile}: ${comandeResp.length} comande`);\n        comandaTrovata = comandeResp.find(c => c.codice_comanda === comandaParam);\n        if (comandaTrovata) {\n          console.log('✅ Comanda trovata:', comandaTrovata);\n          break;\n        }\n      }\n\n      if (comandaTrovata) {\n        console.log('🎯 Apertura automatica comanda da URL:', comandaParam);\n        setSearchingComanda(null); // Rimuovi lo stato di ricerca\n        handleOpenComandaDialog('view', comandaTrovata);\n        // Rimuovi il parametro dall'URL per evitare riaperture\n        setTimeout(() => {\n          setSearchParams(prev => {\n            const newParams = new URLSearchParams(prev);\n            newParams.delete('comanda');\n            return newParams;\n          });\n        }, 100);\n      } else {\n        console.warn('⚠️ Comanda non trovata:', comandaParam);\n        console.log('📋 Comande disponibili:');\n        responsabili.forEach(resp => {\n          const comande = comandePerResponsabile[resp.id_responsabile] || [];\n          comande.forEach(cmd => {\n            console.log(`  - ${cmd.codice_comanda} (${resp.nome_responsabile})`);\n          });\n        });\n\n        // Se non troviamo la comanda, potrebbe essere che i dati non sono completi\n        // Riprova dopo un breve delay\n        if (!loading && !loadingResponsabili) {\n          console.log('🔄 Tentativo di ricaricamento dati...');\n          setTimeout(() => {\n            loadResponsabili();\n          }, 500);\n        }\n      }\n    } else if (comandaParam) {\n      console.log('⏳ Dati non ancora caricati, attendo...');\n\n      // Se abbiamo un parametro ma i dati non sono caricati, forza il caricamento\n      if (!loading && !loadingResponsabili && responsabili.length === 0) {\n        console.log('🚀 Forzatura caricamento responsabili...');\n        loadResponsabili();\n      }\n    }\n  }, [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadComandePerResponsabili = async (responsabiliList) => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    \n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    \n    setOpenResponsabileDialog(true);\n  };\n\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      \n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      \n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n\n  const handleDeleteResponsabile = async (idResponsabile) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = (mode, comanda = null) => {\n    setDialogModeComanda(mode);\n    setSelectedComanda(comanda);\n\n    if (mode === 'edit' && comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n\n    setOpenComandaDialog(true);\n  };\n\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      note_capo_cantiere: ''\n    });\n  };\n\n  const handleSubmitComanda = async () => {\n    try {\n      if (dialogModeComanda === 'edit') {\n        await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n        handleCloseComandaDialog();\n        await loadResponsabili(); // Ricarica per aggiornare le comande\n        await loadComande();\n        await loadStatistiche();\n      }\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n\n  const handleDeleteComanda = async (codiceComanda) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione',\n      'TESTING': 'Testing/Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n\n  const getStatoColor = (stato) => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n\n\n\n  if (loading || loadingComande) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'primary.main' }}>\n          {cantiereName}\n        </Typography>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {searchingComanda && (\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          🔍 Ricerca comanda {searchingComanda} in corso...\n        </Alert>\n      )}\n\n      {/* Statistiche in stile Visualizza Cavi */}\n      {statistiche && (\n        <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n          <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n            {/* Responsabili */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <PersonIcon color=\"primary\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.responsabili_attivi || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Responsabili\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Totale Comande */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <AssignIcon color=\"info\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.totale_comande || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Totale\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* In Corso */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <CheckCircleIcon color=\"warning\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_in_corso || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  In Corso\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Completate */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <VerifiedIcon color=\"success\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_completate || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Completate\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Percentuale completamento */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <Box sx={{\n                width: 32,\n                height: 32,\n                borderRadius: '50%',\n                bgcolor: (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.8 ? 'success.main' :\n                         (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.5 ? 'warning.main' : 'error.main',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}>\n                <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n                  {statistiche.totale_comande > 0 ? Math.round((statistiche.comande_completate / statistiche.totale_comande) * 100) : 0}%\n                </Typography>\n              </Box>\n              <Box>\n                <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n                  Completamento\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {statistiche.comande_create || 0} create\n                </Typography>\n              </Box>\n            </Stack>\n          </Stack>\n        </Paper>\n      )}\n\n      {/* Sezione Comande - Elemento Principale */}\n      <Box>\n        <Box>\n          {/* Toolbar Comande */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n              Gestione Responsabili e Comande\n            </Typography>\n            <Box display=\"flex\" gap={2}>\n              <Button\n                variant=\"outlined\"\n                startIcon={<PeopleIcon />}\n                onClick={() => setOpenResponsabiliPopup(true)}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500,\n                  px: 3,\n                  py: 1,\n                  backgroundColor: '#f5f7fa',\n                  color: '#2196f3',\n                  border: '1px solid #2196f3',\n                  '&:hover': {\n                    backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                    borderColor: '#1976d2'\n                  }\n                }}\n              >\n                Lista Responsabili\n              </Button>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => handleOpenResponsabileDialog('create')}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500,\n                  px: 3,\n                  py: 1,\n                  backgroundColor: '#2196f3',\n                  color: 'white',\n                  '&:hover': {\n                    backgroundColor: '#1976d2'\n                  }\n                }}\n              >\n                Crea Responsabile\n              </Button>\n            </Box>\n          </Box>\n\n          {/* Lista Comande in stile tabella */}\n          {loadingComande ? (\n            <Box display=\"flex\" justifyContent=\"center\" py={4}>\n              <CircularProgress />\n            </Box>\n          ) : allComande.length === 0 ? (\n            <Paper\n              elevation={0}\n              sx={{\n                p: 6,\n                textAlign: 'center',\n                backgroundColor: 'grey.50',\n                border: '1px dashed',\n                borderColor: 'grey.300'\n              }}\n            >\n              <AssignIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />\n              <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                Nessuna comanda disponibile\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                Crea la prima comanda per iniziare a gestire i lavori\n              </Typography>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => setOpenCreaConCavi(true)}\n                sx={{\n                  textTransform: 'none',\n                  backgroundColor: '#2196f3',\n                  color: 'white',\n                  '&:hover': {\n                    backgroundColor: '#1976d2'\n                  }\n                }}\n              >\n                Crea Prima Comanda\n              </Button>\n            </Paper>\n          ) : (\n            <ComandeListTable\n              comande={allComande}\n              onViewComanda={handleOpenComandaDialog.bind(null, 'view')}\n              onEditComanda={handleOpenComandaDialog.bind(null, 'edit')}\n              onDeleteComanda={handleDeleteComanda}\n              loading={loadingComande}\n            />\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per creazione/modifica responsabile */}\n      <Dialog\n        open={openResponsabileDialog}\n        onClose={handleCloseResponsabileDialog}\n        maxWidth=\"sm\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Nome Responsabile\"\n              value={formDataResponsabile.nome_responsabile}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, nome_responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n              variant=\"outlined\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Email\"\n              type=\"email\"\n              value={formDataResponsabile.email}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, email: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Email per notifiche (opzionale se inserisci telefono)\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Telefono\"\n              value={formDataResponsabile.telefono}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, telefono: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Numero per SMS (opzionale se inserisci email)\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseResponsabileDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSubmitResponsabile}\n            variant=\"contained\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            {dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per visualizzazione/modifica comanda */}\n      <Dialog\n        open={openComandaDialog}\n        onClose={handleCloseComandaDialog}\n        maxWidth=\"md\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeComanda === 'view' ? 'Dettagli Comanda' : 'Modifica Comanda'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            {dialogModeComanda === 'view' && selectedComanda ? (\n              <List>\n                <ListItem>\n                  <ListItemText\n                    primary=\"Codice Comanda\"\n                    secondary={selectedComanda.codice_comanda}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Tipo\"\n                    secondary={getTipoComandaLabel(selectedComanda.tipo_comanda)}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Stato\"\n                    secondary={\n                      <Chip\n                        label={selectedComanda.stato}\n                        color={getStatoColor(selectedComanda.stato)}\n                        size=\"small\"\n                      />\n                    }\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Descrizione\"\n                    secondary={selectedComanda.descrizione || 'Nessuna descrizione'}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Responsabile\"\n                    secondary={selectedComanda.responsabile || 'Non assegnato'}\n                  />\n                </ListItem>\n                {selectedComanda.note_capo_cantiere && (\n                  <>\n                    <Divider />\n                    <ListItem>\n                      <ListItemText\n                        primary=\"Note Capo Cantiere\"\n                        secondary={selectedComanda.note_capo_cantiere}\n                      />\n                    </ListItem>\n                  </>\n                )}\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Data Creazione\"\n                    secondary={new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')}\n                  />\n                </ListItem>\n                {selectedComanda.data_scadenza && (\n                  <>\n                    <Divider />\n                    <ListItem>\n                      <ListItemText\n                        primary=\"Data Scadenza\"\n                        secondary={new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')}\n                      />\n                    </ListItem>\n                  </>\n                )}\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Cavi Assegnati\"\n                    secondary={selectedComanda.numero_cavi_assegnati || 0}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Completamento\"\n                    secondary={`${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`}\n                  />\n                </ListItem>\n              </List>\n            ) : (\n              <>\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Tipo Comanda\"\n                  value={formDataComanda.tipo_comanda}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, tipo_comanda: e.target.value })}\n                  margin=\"normal\"\n                  sx={{ mb: 2 }}\n                >\n                  <MenuItem value=\"POSA\">Posa</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_PARTENZA\">Collegamento Partenza</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_ARRIVO\">Collegamento Arrivo</MenuItem>\n                  <MenuItem value=\"CERTIFICAZIONE\">Certificazione</MenuItem>\n                  <MenuItem value=\"TESTING\">Testing</MenuItem>\n                </TextField>\n\n\n\n                <TextField\n                  fullWidth\n                  label=\"Descrizione\"\n                  value={formDataComanda.descrizione}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, descrizione: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={3}\n                  sx={{ mb: 2 }}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Responsabile\"\n                  value={formDataComanda.responsabile}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, responsabile: e.target.value })}\n                  margin=\"normal\"\n                  required\n                  helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n                  sx={{ mb: 2 }}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Note Capo Cantiere\"\n                  value={formDataComanda.note_capo_cantiere}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, note_capo_cantiere: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={2}\n                  helperText=\"Istruzioni specifiche per il responsabile\"\n                  sx={{ mb: 2 }}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Data Scadenza\"\n                  type=\"date\"\n                  value={formDataComanda.data_scadenza}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, data_scadenza: e.target.value })}\n                  margin=\"normal\"\n                  InputLabelProps={{\n                    shrink: true,\n                  }}\n                />\n              </>\n            )}\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseComandaDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            {dialogModeComanda === 'view' ? 'Chiudi' : 'Annulla'}\n          </Button>\n          {dialogModeComanda === 'edit' && (\n            <Button\n              onClick={handleSubmitComanda}\n              variant=\"contained\"\n              sx={{\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3\n              }}\n            >\n              Salva Modifiche\n            </Button>\n          )}\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog CreaComandaConCavi */}\n      <CreaComandaConCavi\n        cantiereId={cantiereId}\n        open={openCreaConCavi}\n        onClose={() => setOpenCreaConCavi(false)}\n        onSuccess={(response, successMessage) => {\n          console.log('🎉 Comanda creata, aggiornamento interfaccia...');\n\n          // Mostra messaggio di successo se fornito\n          if (successMessage) {\n            // Potresti aggiungere qui un toast/snackbar per mostrare il messaggio\n            console.log('📢 Successo:', successMessage);\n          }\n\n          // Ricarica tutti i dati per aggiornare l'interfaccia\n          loadComande();\n          loadStatistiche();\n          loadResponsabili();\n          setOpenCreaConCavi(false);\n\n          console.log('✅ Interfaccia aggiornata');\n        }}\n      />\n\n      {/* Popup Lista Responsabili */}\n      <ResponsabiliListPopup\n        open={openResponsabiliPopup}\n        onClose={() => setOpenResponsabiliPopup(false)}\n        responsabili={responsabili}\n        comandePerResponsabile={comandePerResponsabile}\n        onEditResponsabile={(responsabile) => {\n          setOpenResponsabiliPopup(false);\n          handleOpenResponsabileDialog('edit', responsabile);\n        }}\n        onDeleteResponsabile={async (idResponsabile) => {\n          await handleDeleteResponsabile(idResponsabile);\n          setOpenResponsabiliPopup(false);\n        }}\n        loading={loadingResponsabili}\n        error={error}\n      />\n    </Box>\n  );\n};\n\nexport default ComandeListRivoluzionato;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,KAAK,EACLC,QAAQ,EACRC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,UAAU,IAAIC,UAAU,EACxBC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,YAAY,IAAIC,gBAAgB,QAC3B,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACjE;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG/C,eAAe,CAAC,CAAC;;EAEzD;EACA,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoD,KAAK,EAAEC,QAAQ,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4D,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAAC8D,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACgE,eAAe,EAAEC,kBAAkB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACwE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC0E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG3E,QAAQ,CAAC,QAAQ,CAAC;EAC9E,MAAM,CAAC4E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC8E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/E,QAAQ,CAAC;IAC/DgF,iBAAiB,EAAE,EAAE;IACrBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACqF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtF,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACpE,MAAM,CAACuF,eAAe,EAAEC,kBAAkB,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyF,eAAe,EAAEC,kBAAkB,CAAC,GAAG1F,QAAQ,CAAC;IACrD2F,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFnC,iBAAiB,CAAC,IAAI,CAAC;MACvBoC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAErD,UAAU,CAAC;MAC/D,MAAMsD,WAAW,GAAG,MAAMhE,cAAc,CAACiE,UAAU,CAACvD,UAAU,CAAC;MAC/DoD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEC,WAAW,CAAC;MACrDF,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,OAAOC,WAAW,EAAE,QAAQ,EAAEE,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,CAAC;;MAEtF;MACA,IAAII,YAAY,GAAG,EAAE;MACrB,IAAIF,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,EAAE;QAC9BI,YAAY,GAAGJ,WAAW;MAC5B,CAAC,MAAM,IAAIA,WAAW,IAAIE,KAAK,CAACC,OAAO,CAACH,WAAW,CAACK,OAAO,CAAC,EAAE;QAC5DD,YAAY,GAAGJ,WAAW,CAACK,OAAO;MACpC,CAAC,MAAM,IAAIL,WAAW,IAAIE,KAAK,CAACC,OAAO,CAACH,WAAW,CAACM,IAAI,CAAC,EAAE;QACzDF,YAAY,GAAGJ,WAAW,CAACM,IAAI;MACjC;MAEAR,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEK,YAAY,EAAE,YAAY,EAAEA,YAAY,CAACG,MAAM,CAAC;MACxF/C,aAAa,CAAC4C,YAAY,CAAC;IAC7B,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZV,OAAO,CAAC7C,KAAK,CAAC,iCAAiC,EAAEuD,GAAG,CAAC;MACrDtD,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRQ,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAM+C,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFX,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAErD,UAAU,CAAC;MACnE,MAAMgE,KAAK,GAAG,MAAM1E,cAAc,CAAC2E,qBAAqB,CAACjE,UAAU,CAAC;MACpEoD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEW,KAAK,CAAC;MAC9CpD,cAAc,CAACoD,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOF,GAAG,EAAE;MAAA,IAAAI,aAAA;MACZd,OAAO,CAAC7C,KAAK,CAAC,6CAA6C,EAAEuD,GAAG,CAAC;MACjEV,OAAO,CAAC7C,KAAK,CAAC,oBAAoB,EAAE,EAAA2D,aAAA,GAAAJ,GAAG,CAACK,QAAQ,cAAAD,aAAA,uBAAZA,aAAA,CAAcN,IAAI,KAAIE,GAAG,CAACM,OAAO,CAAC;IACxE;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF7C,sBAAsB,CAAC,IAAI,CAAC;MAC5BhB,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMoD,IAAI,GAAG,MAAMrE,mBAAmB,CAAC+E,uBAAuB,CAACtE,UAAU,CAAC;MAC1EsB,eAAe,CAACsC,IAAI,IAAI,EAAE,CAAC;MAC3B,MAAMW,0BAA0B,CAACX,IAAI,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOE,GAAG,EAAE;MAAA,IAAAU,cAAA,EAAAC,mBAAA;MACZrB,OAAO,CAAC7C,KAAK,CAAC,0CAA0C,EAAEuD,GAAG,CAAC;MAC9D,MAAMY,YAAY,GAAG,EAAAF,cAAA,GAAAV,GAAG,CAACK,QAAQ,cAAAK,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcZ,IAAI,cAAAa,mBAAA,uBAAlBA,mBAAA,CAAoBE,MAAM,KAAIb,GAAG,CAACM,OAAO,IAAI,yCAAyC;MAC3G5D,QAAQ,CAAC,4CAA4CkE,YAAY,EAAE,CAAC;IACtE,CAAC,SAAS;MACRlD,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAID;EACApE,SAAS,CAAC,MAAM;IACd,MAAMwH,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI5E,UAAU,EAAE;QACdM,UAAU,CAAC,IAAI,CAAC;QAChB,IAAI;UACF,MAAMuE,OAAO,CAACC,GAAG,CAAC,CAChBT,gBAAgB,CAAC,CAAC,EAClBlB,WAAW,CAAC,CAAC,EACbY,eAAe,CAAC,CAAC,CAClB,CAAC;QACJ,CAAC,CAAC,OAAOD,GAAG,EAAE;UACZV,OAAO,CAAC7C,KAAK,CAAC,kCAAkC,EAAEuD,GAAG,CAAC;QACxD,CAAC,SAAS;UACRxD,UAAU,CAAC,KAAK,CAAC;QACnB;MACF;IACF,CAAC;IAEDsE,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC5E,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElB;EACA5C,SAAS,CAAC,MAAM;IACd,MAAM2H,YAAY,GAAG5E,YAAY,CAAC6E,GAAG,CAAC,SAAS,CAAC;IAChD5B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE0B,YAAY,CAAC;IAChE3B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;MAC5BhC,YAAY,EAAEA,YAAY,CAACwC,MAAM;MACjCpC,sBAAsB,EAAEwD,MAAM,CAACC,IAAI,CAACzD,sBAAsB,CAAC,CAACoC,MAAM;MAClExD,OAAO;MACPkB;IACF,CAAC,CAAC;;IAEF;IACA,IAAIwD,YAAY,IAAIA,YAAY,KAAKtE,gBAAgB,EAAE;MACrDC,mBAAmB,CAACqE,YAAY,CAAC;IACnC;IAEA,IAAIA,YAAY,IAAI1D,YAAY,CAACwC,MAAM,GAAG,CAAC,IAAIoB,MAAM,CAACC,IAAI,CAACzD,sBAAsB,CAAC,CAACoC,MAAM,GAAG,CAAC,EAAE;MAC7FT,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;MAEvD;MACA,IAAI8B,cAAc,GAAG,IAAI;MAEzB,KAAK,MAAMnC,YAAY,IAAI3B,YAAY,EAAE;QACvC,MAAM+D,WAAW,GAAG3D,sBAAsB,CAACuB,YAAY,CAACqC,eAAe,CAAC,IAAI,EAAE;QAC9EjC,OAAO,CAACC,GAAG,CAAC,mBAAmBL,YAAY,CAACb,iBAAiB,KAAKiD,WAAW,CAACvB,MAAM,UAAU,CAAC;QAC/FsB,cAAc,GAAGC,WAAW,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAKT,YAAY,CAAC;QACzE,IAAII,cAAc,EAAE;UAClB/B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE8B,cAAc,CAAC;UACjD;QACF;MACF;MAEA,IAAIA,cAAc,EAAE;QAClB/B,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE0B,YAAY,CAAC;QACnErE,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3B+E,uBAAuB,CAAC,MAAM,EAAEN,cAAc,CAAC;QAC/C;QACAO,UAAU,CAAC,MAAM;UACftF,eAAe,CAACuF,IAAI,IAAI;YACtB,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACF,IAAI,CAAC;YAC3CC,SAAS,CAACE,MAAM,CAAC,SAAS,CAAC;YAC3B,OAAOF,SAAS;UAClB,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLxC,OAAO,CAAC2C,IAAI,CAAC,yBAAyB,EAAEhB,YAAY,CAAC;QACrD3B,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;QACtChC,YAAY,CAAC2E,OAAO,CAACC,IAAI,IAAI;UAC3B,MAAMtC,OAAO,GAAGlC,sBAAsB,CAACwE,IAAI,CAACZ,eAAe,CAAC,IAAI,EAAE;UAClE1B,OAAO,CAACqC,OAAO,CAACE,GAAG,IAAI;YACrB9C,OAAO,CAACC,GAAG,CAAC,OAAO6C,GAAG,CAACV,cAAc,KAAKS,IAAI,CAAC9D,iBAAiB,GAAG,CAAC;UACtE,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACA;QACA,IAAI,CAAC9B,OAAO,IAAI,CAACkB,mBAAmB,EAAE;UACpC6B,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;UACpDqC,UAAU,CAAC,MAAM;YACfrB,gBAAgB,CAAC,CAAC;UACpB,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF,CAAC,MAAM,IAAIU,YAAY,EAAE;MACvB3B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,IAAI,CAAChD,OAAO,IAAI,CAACkB,mBAAmB,IAAIF,YAAY,CAACwC,MAAM,KAAK,CAAC,EAAE;QACjET,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvDgB,gBAAgB,CAAC,CAAC;MACpB;IACF;EACF,CAAC,EAAE,CAAClE,YAAY,EAAEkB,YAAY,EAAEI,sBAAsB,EAAEpB,OAAO,EAAEkB,mBAAmB,CAAC,CAAC,CAAC,CAAC;;EAExF,MAAMgD,0BAA0B,GAAG,MAAO4B,gBAAgB,IAAK;IAC7D,IAAI;MACF,MAAMC,UAAU,GAAG,CAAC,CAAC;MACrB,KAAK,MAAMpD,YAAY,IAAImD,gBAAgB,EAAE;QAC3C,IAAI;UACF,MAAMhC,QAAQ,GAAG,MAAM7E,cAAc,CAAC+G,wBAAwB,CAACrG,UAAU,EAAEgD,YAAY,CAACb,iBAAiB,CAAC;UAC1G;UACA,IAAIwB,OAAO,GAAG,EAAE;UAChB,IAAIQ,QAAQ,IAAIX,KAAK,CAACC,OAAO,CAACU,QAAQ,CAAC,EAAE;YACvCR,OAAO,GAAGQ,QAAQ;UACpB,CAAC,MAAM,IAAIA,QAAQ,IAAIA,QAAQ,CAACR,OAAO,IAAIH,KAAK,CAACC,OAAO,CAACU,QAAQ,CAACR,OAAO,CAAC,EAAE;YAC1EA,OAAO,GAAGQ,QAAQ,CAACR,OAAO;UAC5B,CAAC,MAAM,IAAIQ,QAAQ,IAAIA,QAAQ,CAACP,IAAI,IAAIJ,KAAK,CAACC,OAAO,CAACU,QAAQ,CAACP,IAAI,CAAC,EAAE;YACpED,OAAO,GAAGQ,QAAQ,CAACP,IAAI;UACzB;UACAwC,UAAU,CAACpD,YAAY,CAACqC,eAAe,CAAC,GAAG1B,OAAO;QACpD,CAAC,CAAC,OAAOG,GAAG,EAAE;UACZV,OAAO,CAAC7C,KAAK,CAAC,sCAAsCyC,YAAY,CAACb,iBAAiB,GAAG,EAAE2B,GAAG,CAAC;UAC3FsC,UAAU,CAACpD,YAAY,CAACqC,eAAe,CAAC,GAAG,EAAE;QAC/C;MACF;MACA3D,yBAAyB,CAAC0E,UAAU,CAAC;IACvC,CAAC,CAAC,OAAOtC,GAAG,EAAE;MACZV,OAAO,CAAC7C,KAAK,CAAC,uCAAuC,EAAEuD,GAAG,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMwC,4BAA4B,GAAGA,CAACC,IAAI,EAAEvD,YAAY,GAAG,IAAI,KAAK;IAClElB,yBAAyB,CAACyE,IAAI,CAAC;IAC/BvE,uBAAuB,CAACgB,YAAY,CAAC;IAErC,IAAIuD,IAAI,KAAK,MAAM,IAAIvD,YAAY,EAAE;MACnCd,uBAAuB,CAAC;QACtBC,iBAAiB,EAAEa,YAAY,CAACb,iBAAiB,IAAI,EAAE;QACvDC,KAAK,EAAEY,YAAY,CAACZ,KAAK,IAAI,EAAE;QAC/BC,QAAQ,EAAEW,YAAY,CAACX,QAAQ,IAAI;MACrC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,uBAAuB,CAAC;QACtBC,iBAAiB,EAAE,EAAE;QACrBC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEAT,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAM4E,6BAA6B,GAAGA,CAAA,KAAM;IAC1C5E,yBAAyB,CAAC,KAAK,CAAC;IAChCI,uBAAuB,CAAC,IAAI,CAAC;IAC7BxB,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAMiG,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFjG,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI,CAACyB,oBAAoB,CAACE,iBAAiB,CAACuE,IAAI,CAAC,CAAC,EAAE;QAClDlG,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA,IAAI,CAACyB,oBAAoB,CAACG,KAAK,IAAI,CAACH,oBAAoB,CAACI,QAAQ,EAAE;QACjE7B,QAAQ,CAAC,yDAAyD,CAAC;QACnE;MACF;MAEA,IAAIqB,sBAAsB,KAAK,QAAQ,EAAE;QACvC,MAAMtC,mBAAmB,CAACoH,kBAAkB,CAAC3G,UAAU,EAAEiC,oBAAoB,CAAC;MAChF,CAAC,MAAM,IAAIJ,sBAAsB,KAAK,MAAM,EAAE;QAC5C,MAAMtC,mBAAmB,CAACqH,kBAAkB,CAAC7E,oBAAoB,CAACsD,eAAe,EAAEpD,oBAAoB,CAAC;MAC1G;MAEAuE,6BAA6B,CAAC,CAAC;MAC/B,MAAMnC,gBAAgB,CAAC,CAAC;MACxB,MAAMlB,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOW,GAAG,EAAE;MACZV,OAAO,CAAC7C,KAAK,CAAC,yBAAyB,EAAEuD,GAAG,CAAC;MAC7CtD,QAAQ,CAACsD,GAAG,CAACa,MAAM,IAAI,yCAAyC,CAAC;IACnE;EACF,CAAC;EAED,MAAMkC,wBAAwB,GAAG,MAAOC,cAAc,IAAK;IACzD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACzE;IACF;IAEA,IAAI;MACF,MAAMzH,mBAAmB,CAAC0H,kBAAkB,CAACH,cAAc,CAAC;MAC5D,MAAMzC,gBAAgB,CAAC,CAAC;MACxB,MAAMlB,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOW,GAAG,EAAE;MACZV,OAAO,CAAC7C,KAAK,CAAC,4BAA4B,EAAEuD,GAAG,CAAC;MAChDtD,QAAQ,CAAC,4CAA4C,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMiF,uBAAuB,GAAGA,CAACc,IAAI,EAAEW,OAAO,GAAG,IAAI,KAAK;IACxDzE,oBAAoB,CAAC8D,IAAI,CAAC;IAC1B5D,kBAAkB,CAACuE,OAAO,CAAC;IAE3B,IAAIX,IAAI,KAAK,MAAM,IAAIW,OAAO,EAAE;MAC9BrE,kBAAkB,CAAC;QACjBC,YAAY,EAAEoE,OAAO,CAACpE,YAAY;QAClCC,WAAW,EAAEmE,OAAO,CAACnE,WAAW,IAAI,EAAE;QACtCC,YAAY,EAAEkE,OAAO,CAAClE,YAAY,IAAI,EAAE;QACxCC,aAAa,EAAEiE,OAAO,CAACjE,aAAa,IAAI,EAAE;QAC1CC,kBAAkB,EAAEgE,OAAO,CAAChE,kBAAkB,IAAI;MACpD,CAAC,CAAC;IACJ;IAEAX,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAM4E,wBAAwB,GAAGA,CAAA,KAAM;IACrC5E,oBAAoB,CAAC,KAAK,CAAC;IAC3BI,kBAAkB,CAAC,IAAI,CAAC;IACxBE,kBAAkB,CAAC;MACjBC,YAAY,EAAE,MAAM;MACpBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMkE,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,IAAI5E,iBAAiB,KAAK,MAAM,EAAE;QAChC,MAAMlD,cAAc,CAAC+H,aAAa,CAAC3E,eAAe,CAAC8C,cAAc,EAAE5C,eAAe,CAAC;QACnFuE,wBAAwB,CAAC,CAAC;QAC1B,MAAM9C,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAMlB,WAAW,CAAC,CAAC;QACnB,MAAMY,eAAe,CAAC,CAAC;MACzB;IACF,CAAC,CAAC,OAAOD,GAAG,EAAE;MACZV,OAAO,CAAC7C,KAAK,CAAC,yBAAyB,EAAEuD,GAAG,CAAC;MAC7CtD,QAAQ,CAAC,sCAAsC,CAAC;IAClD;EACF,CAAC;EAED,MAAM8G,mBAAmB,GAAG,MAAOC,aAAa,IAAK;IACnD,IAAI,CAACR,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACpE;IACF;IAEA,IAAI;MACF,MAAM1H,cAAc,CAACkI,aAAa,CAACD,aAAa,CAAC;MACjD,MAAMlD,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAC1B,MAAMlB,WAAW,CAAC,CAAC;MACnB,MAAMY,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOD,GAAG,EAAE;MACZV,OAAO,CAAC7C,KAAK,CAAC,4BAA4B,EAAEuD,GAAG,CAAC;MAChDtD,QAAQ,CAAC,yCAAyC,CAAC;IACrD;EACF,CAAC;EAED,MAAMiH,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,MAAM,GAAG;MACb,MAAM,EAAE,MAAM;MACd,uBAAuB,EAAE,gBAAgB;MACzC,qBAAqB,EAAE,cAAc;MACrC,gBAAgB,EAAE,gBAAgB;MAClC,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;EAED,MAAME,aAAa,GAAIC,KAAK,IAAK;IAC/B,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC,CAAC;EAID,IAAIxH,OAAO,IAAIU,cAAc,EAAE;IAC7B,oBACEnB,OAAA,CAACtC,GAAG;MAACyK,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/EvI,OAAA,CAAC3B,gBAAgB;QAAAmK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACE3I,OAAA,CAACtC,GAAG;IAACkL,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAN,QAAA,gBAEhBvI,OAAA,CAACtC,GAAG;MAACoL,EAAE,EAAE,CAAE;MAAAP,QAAA,eACTvI,OAAA,CAACrC,UAAU;QAACoL,OAAO,EAAC,IAAI;QAACH,EAAE,EAAE;UAAEI,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAe,CAAE;QAAAV,QAAA,EACrElI;MAAY;QAAAmI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAELhI,KAAK,iBACJX,OAAA,CAAC5B,KAAK;MAAC8K,QAAQ,EAAC,OAAO;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EACnC5H;IAAK;MAAA6H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEA9H,gBAAgB,iBACfb,OAAA,CAAC5B,KAAK;MAAC8K,QAAQ,EAAC,MAAM;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,GAAC,+BACjB,EAAC1H,gBAAgB,EAAC,cACvC;IAAA;MAAA2H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,EAGA5H,WAAW,iBACVf,OAAA,CAACnC,KAAK;MAAC+K,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEK,OAAO,EAAE;MAAU,CAAE;MAAAZ,QAAA,eAC7CvI,OAAA,CAACvB,KAAK;QAAC2K,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAAChB,UAAU,EAAC,QAAQ;QAACD,cAAc,EAAC,eAAe;QAACkB,QAAQ,EAAC,MAAM;QAAAf,QAAA,gBAEnGvI,OAAA,CAACvB,KAAK;UAAC2K,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDvI,OAAA,CAACf,UAAU;YAACgK,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/C3I,OAAA,CAACtC,GAAG;YAAA6K,QAAA,gBACFvI,OAAA,CAACrC,UAAU;cAACoL,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DxH,WAAW,CAAC0I,mBAAmB,IAAI;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACb3I,OAAA,CAACrC,UAAU;cAACoL,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGR3I,OAAA,CAACvB,KAAK;UAAC2K,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDvI,OAAA,CAACjB,UAAU;YAACkK,KAAK,EAAC,MAAM;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5C3I,OAAA,CAACtC,GAAG;YAAA6K,QAAA,gBACFvI,OAAA,CAACrC,UAAU;cAACoL,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DxH,WAAW,CAAC2I,cAAc,IAAI;YAAC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACb3I,OAAA,CAACrC,UAAU;cAACoL,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGR3I,OAAA,CAACvB,KAAK;UAAC2K,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDvI,OAAA,CAACb,eAAe;YAAC8J,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpD3I,OAAA,CAACtC,GAAG;YAAA6K,QAAA,gBACFvI,OAAA,CAACrC,UAAU;cAACoL,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DxH,WAAW,CAAC4I,gBAAgB,IAAI;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACb3I,OAAA,CAACrC,UAAU;cAACoL,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGR3I,OAAA,CAACvB,KAAK;UAAC2K,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDvI,OAAA,CAACX,YAAY;YAAC4J,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjD3I,OAAA,CAACtC,GAAG;YAAA6K,QAAA,gBACFvI,OAAA,CAACrC,UAAU;cAACoL,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DxH,WAAW,CAAC6I,kBAAkB,IAAI;YAAC;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACb3I,OAAA,CAACrC,UAAU;cAACoL,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGR3I,OAAA,CAACvB,KAAK;UAAC2K,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDvI,OAAA,CAACtC,GAAG;YAACkL,EAAE,EAAE;cACPiB,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVC,YAAY,EAAE,KAAK;cACnBZ,OAAO,EAAGpI,WAAW,CAAC6I,kBAAkB,IAAI7I,WAAW,CAAC2I,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAC3F3I,WAAW,CAAC6I,kBAAkB,IAAI7I,WAAW,CAAC2I,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAAG,YAAY;cACpHvB,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,eACAvI,OAAA,CAACrC,UAAU;cAACoL,OAAO,EAAC,SAAS;cAACC,UAAU,EAAC,MAAM;cAACC,KAAK,EAAC,OAAO;cAAAV,QAAA,GAC1DxH,WAAW,CAAC2I,cAAc,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAAElJ,WAAW,CAAC6I,kBAAkB,GAAG7I,WAAW,CAAC2I,cAAc,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GACxH;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN3I,OAAA,CAACtC,GAAG;YAAA6K,QAAA,gBACFvI,OAAA,CAACrC,UAAU;cAACoL,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,QAAQ;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3I,OAAA,CAACrC,UAAU;cAACoL,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,GACjDxH,WAAW,CAACmJ,cAAc,IAAI,CAAC,EAAC,SACnC;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAGD3I,OAAA,CAACtC,GAAG;MAAA6K,QAAA,eACFvI,OAAA,CAACtC,GAAG;QAAA6K,QAAA,gBAEFvI,OAAA,CAACtC,GAAG;UAACyK,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAACC,UAAU,EAAC,QAAQ;UAACS,EAAE,EAAE,CAAE;UAAAP,QAAA,gBAC3EvI,OAAA,CAACrC,UAAU;YAACoL,OAAO,EAAC,IAAI;YAACH,EAAE,EAAE;cAAEI,UAAU,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAe,CAAE;YAAAV,QAAA,EAAC;UAEzE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3I,OAAA,CAACtC,GAAG;YAACyK,OAAO,EAAC,MAAM;YAACgC,GAAG,EAAE,CAAE;YAAA5B,QAAA,gBACzBvI,OAAA,CAACpC,MAAM;cACLmL,OAAO,EAAC,UAAU;cAClBqB,SAAS,eAAEpK,OAAA,CAACT,UAAU;gBAAAiJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1B0B,OAAO,EAAEA,CAAA,KAAM/I,wBAAwB,CAAC,IAAI,CAAE;cAC9CsH,EAAE,EAAE;gBACF0B,aAAa,EAAE,MAAM;gBACrBtB,UAAU,EAAE,GAAG;gBACfuB,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLC,eAAe,EAAE,SAAS;gBAC1BxB,KAAK,EAAE,SAAS;gBAChByB,MAAM,EAAE,mBAAmB;gBAC3B,SAAS,EAAE;kBACTD,eAAe,EAAE,yBAAyB;kBAC1CE,WAAW,EAAE;gBACf;cACF,CAAE;cAAApC,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3I,OAAA,CAACpC,MAAM;cACLmL,OAAO,EAAC,WAAW;cACnBqB,SAAS,eAAEpK,OAAA,CAACnB,OAAO;gBAAA2J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvB0B,OAAO,EAAEA,CAAA,KAAM3D,4BAA4B,CAAC,QAAQ,CAAE;cACtDkC,EAAE,EAAE;gBACF0B,aAAa,EAAE,MAAM;gBACrBtB,UAAU,EAAE,GAAG;gBACfuB,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLC,eAAe,EAAE,SAAS;gBAC1BxB,KAAK,EAAE,OAAO;gBACd,SAAS,EAAE;kBACTwB,eAAe,EAAE;gBACnB;cACF,CAAE;cAAAlC,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLxH,cAAc,gBACbnB,OAAA,CAACtC,GAAG;UAACyK,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,QAAQ;UAACoC,EAAE,EAAE,CAAE;UAAAjC,QAAA,eAChDvI,OAAA,CAAC3B,gBAAgB;YAAAmK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJ1H,UAAU,CAACgD,MAAM,KAAK,CAAC,gBACzBjE,OAAA,CAACnC,KAAK;UACJ+M,SAAS,EAAE,CAAE;UACbhC,EAAE,EAAE;YACFC,CAAC,EAAE,CAAC;YACJgC,SAAS,EAAE,QAAQ;YACnBJ,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,YAAY;YACpBC,WAAW,EAAE;UACf,CAAE;UAAApC,QAAA,gBAEFvI,OAAA,CAACjB,UAAU;YAAC6J,EAAE,EAAE;cAAEW,QAAQ,EAAE,EAAE;cAAEN,KAAK,EAAE,UAAU;cAAEH,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9D3I,OAAA,CAACrC,UAAU;YAACoL,OAAO,EAAC,IAAI;YAACE,KAAK,EAAC,gBAAgB;YAAC6B,YAAY;YAAAvC,QAAA,EAAC;UAE7D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3I,OAAA,CAACrC,UAAU;YAACoL,OAAO,EAAC,OAAO;YAACE,KAAK,EAAC,gBAAgB;YAACL,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3I,OAAA,CAACpC,MAAM;YACLmL,OAAO,EAAC,WAAW;YACnBqB,SAAS,eAAEpK,OAAA,CAACnB,OAAO;cAAA2J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvB0B,OAAO,EAAEA,CAAA,KAAM7I,kBAAkB,CAAC,IAAI,CAAE;YACxCoH,EAAE,EAAE;cACF0B,aAAa,EAAE,MAAM;cACrBG,eAAe,EAAE,SAAS;cAC1BxB,KAAK,EAAE,OAAO;cACd,SAAS,EAAE;gBACTwB,eAAe,EAAE;cACnB;YACF,CAAE;YAAAlC,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,gBAER3I,OAAA,CAACF,gBAAgB;UACfiE,OAAO,EAAE9C,UAAW;UACpB8J,aAAa,EAAElF,uBAAuB,CAACmF,IAAI,CAAC,IAAI,EAAE,MAAM,CAAE;UAC1DC,aAAa,EAAEpF,uBAAuB,CAACmF,IAAI,CAAC,IAAI,EAAE,MAAM,CAAE;UAC1DE,eAAe,EAAExD,mBAAoB;UACrCjH,OAAO,EAAEU;QAAe;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3I,OAAA,CAACjC,MAAM;MACLoN,IAAI,EAAEpJ,sBAAuB;MAC7BqJ,OAAO,EAAExE,6BAA8B;MACvCyE,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACV3C,EAAE,EAAE;UAAEmB,YAAY,EAAE;QAAE;MACxB,CAAE;MAAAxB,QAAA,gBAEFvI,OAAA,CAAChC,WAAW;QAAC4K,EAAE,EAAE;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAjD,QAAA,eACzBvI,OAAA,CAACrC,UAAU;UAACoL,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAC9CtG,sBAAsB,KAAK,QAAQ,GAAG,wBAAwB,GAAG;QAAuB;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACd3I,OAAA,CAAC/B,aAAa;QAAAsK,QAAA,eACZvI,OAAA,CAACtC,GAAG;UAACkL,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,gBACjBvI,OAAA,CAAC7B,SAAS;YACRmN,SAAS;YACTI,KAAK,EAAC,mBAAmB;YACzBC,KAAK,EAAEtJ,oBAAoB,CAACE,iBAAkB;YAC9CqJ,QAAQ,EAAGC,CAAC,IAAKvJ,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEE,iBAAiB,EAAEsJ,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACzGI,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRjD,OAAO,EAAC,UAAU;YAClBH,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEF3I,OAAA,CAAC7B,SAAS;YACRmN,SAAS;YACTI,KAAK,EAAC,OAAO;YACbO,IAAI,EAAC,OAAO;YACZN,KAAK,EAAEtJ,oBAAoB,CAACG,KAAM;YAClCoJ,QAAQ,EAAGC,CAAC,IAAKvJ,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEG,KAAK,EAAEqJ,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC7FI,MAAM,EAAC,QAAQ;YACfhD,OAAO,EAAC,UAAU;YAClBmD,UAAU,EAAC,uDAAuD;YAClEtD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEF3I,OAAA,CAAC7B,SAAS;YACRmN,SAAS;YACTI,KAAK,EAAC,UAAU;YAChBC,KAAK,EAAEtJ,oBAAoB,CAACI,QAAS;YACrCmJ,QAAQ,EAAGC,CAAC,IAAKvJ,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEI,QAAQ,EAAEoJ,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAChGI,MAAM,EAAC,QAAQ;YACfhD,OAAO,EAAC,UAAU;YAClBmD,UAAU,EAAC;UAA+C;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChB3I,OAAA,CAAC9B,aAAa;QAAC0K,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAlD,QAAA,gBACjCvI,OAAA,CAACpC,MAAM;UACLyM,OAAO,EAAEzD,6BAA8B;UACvCgC,EAAE,EAAE;YAAE0B,aAAa,EAAE;UAAO,CAAE;UAAA/B,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3I,OAAA,CAACpC,MAAM;UACLyM,OAAO,EAAExD,wBAAyB;UAClCkC,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACF0B,aAAa,EAAE,MAAM;YACrBtB,UAAU,EAAE,GAAG;YACfuB,EAAE,EAAE;UACN,CAAE;UAAAhC,QAAA,EAEDtG,sBAAsB,KAAK,QAAQ,GAAG,MAAM,GAAG;QAAO;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT3I,OAAA,CAACjC,MAAM;MACLoN,IAAI,EAAEzI,iBAAkB;MACxB0I,OAAO,EAAE7D,wBAAyB;MAClC8D,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACV3C,EAAE,EAAE;UAAEmB,YAAY,EAAE;QAAE;MACxB,CAAE;MAAAxB,QAAA,gBAEFvI,OAAA,CAAChC,WAAW;QAAC4K,EAAE,EAAE;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAjD,QAAA,eACzBvI,OAAA,CAACrC,UAAU;UAACoL,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAC9C3F,iBAAiB,KAAK,MAAM,GAAG,kBAAkB,GAAG;QAAkB;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACd3I,OAAA,CAAC/B,aAAa;QAAAsK,QAAA,eACZvI,OAAA,CAACtC,GAAG;UAACkL,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,EAChB3F,iBAAiB,KAAK,MAAM,IAAIE,eAAe,gBAC9C9C,OAAA,CAAC1B,IAAI;YAAAiK,QAAA,gBACHvI,OAAA,CAACzB,QAAQ;cAAAgK,QAAA,eACPvI,OAAA,CAACxB,YAAY;gBACX2N,OAAO,EAAC,gBAAgB;gBACxBC,SAAS,EAAEtJ,eAAe,CAAC8C;cAAe;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACX3I,OAAA,CAACrB,OAAO;cAAA6J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX3I,OAAA,CAACzB,QAAQ;cAAAgK,QAAA,eACPvI,OAAA,CAACxB,YAAY;gBACX2N,OAAO,EAAC,MAAM;gBACdC,SAAS,EAAEvE,mBAAmB,CAAC/E,eAAe,CAACI,YAAY;cAAE;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACX3I,OAAA,CAACrB,OAAO;cAAA6J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX3I,OAAA,CAACzB,QAAQ;cAAAgK,QAAA,eACPvI,OAAA,CAACxB,YAAY;gBACX2N,OAAO,EAAC,OAAO;gBACfC,SAAS,eACPpM,OAAA,CAAClC,IAAI;kBACH4N,KAAK,EAAE5I,eAAe,CAACmF,KAAM;kBAC7BgB,KAAK,EAAEjB,aAAa,CAAClF,eAAe,CAACmF,KAAK,CAAE;kBAC5CoE,IAAI,EAAC;gBAAO;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACX3I,OAAA,CAACrB,OAAO;cAAA6J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX3I,OAAA,CAACzB,QAAQ;cAAAgK,QAAA,eACPvI,OAAA,CAACxB,YAAY;gBACX2N,OAAO,EAAC,aAAa;gBACrBC,SAAS,EAAEtJ,eAAe,CAACK,WAAW,IAAI;cAAsB;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACX3I,OAAA,CAACrB,OAAO;cAAA6J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX3I,OAAA,CAACzB,QAAQ;cAAAgK,QAAA,eACPvI,OAAA,CAACxB,YAAY;gBACX2N,OAAO,EAAC,cAAc;gBACtBC,SAAS,EAAEtJ,eAAe,CAACM,YAAY,IAAI;cAAgB;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EACV7F,eAAe,CAACQ,kBAAkB,iBACjCtD,OAAA,CAAAE,SAAA;cAAAqI,QAAA,gBACEvI,OAAA,CAACrB,OAAO;gBAAA6J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX3I,OAAA,CAACzB,QAAQ;gBAAAgK,QAAA,eACPvI,OAAA,CAACxB,YAAY;kBACX2N,OAAO,EAAC,oBAAoB;kBAC5BC,SAAS,EAAEtJ,eAAe,CAACQ;gBAAmB;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA,eACX,CACH,eACD3I,OAAA,CAACrB,OAAO;cAAA6J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX3I,OAAA,CAACzB,QAAQ;cAAAgK,QAAA,eACPvI,OAAA,CAACxB,YAAY;gBACX2N,OAAO,EAAC,gBAAgB;gBACxBC,SAAS,EAAE,IAAIE,IAAI,CAACxJ,eAAe,CAACyJ,cAAc,CAAC,CAACC,kBAAkB,CAAC,OAAO;cAAE;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EACV7F,eAAe,CAACO,aAAa,iBAC5BrD,OAAA,CAAAE,SAAA;cAAAqI,QAAA,gBACEvI,OAAA,CAACrB,OAAO;gBAAA6J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX3I,OAAA,CAACzB,QAAQ;gBAAAgK,QAAA,eACPvI,OAAA,CAACxB,YAAY;kBACX2N,OAAO,EAAC,eAAe;kBACvBC,SAAS,EAAE,IAAIE,IAAI,CAACxJ,eAAe,CAACO,aAAa,CAAC,CAACmJ,kBAAkB,CAAC,OAAO;gBAAE;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA,eACX,CACH,eACD3I,OAAA,CAACrB,OAAO;cAAA6J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX3I,OAAA,CAACzB,QAAQ;cAAAgK,QAAA,eACPvI,OAAA,CAACxB,YAAY;gBACX2N,OAAO,EAAC,gBAAgB;gBACxBC,SAAS,EAAEtJ,eAAe,CAAC2J,qBAAqB,IAAI;cAAE;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACX3I,OAAA,CAACrB,OAAO;cAAA6J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX3I,OAAA,CAACzB,QAAQ;cAAAgK,QAAA,eACPvI,OAAA,CAACxB,YAAY;gBACX2N,OAAO,EAAC,eAAe;gBACvBC,SAAS,EAAE,GAAG,CAACtJ,eAAe,CAAC4J,yBAAyB,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;cAAI;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,gBAEP3I,OAAA,CAAAE,SAAA;YAAAqI,QAAA,gBACEvI,OAAA,CAAC7B,SAAS;cACRmN,SAAS;cACTsB,MAAM;cACNlB,KAAK,EAAC,cAAc;cACpBC,KAAK,EAAE3I,eAAe,CAACE,YAAa;cACpC0I,QAAQ,EAAGC,CAAC,IAAK5I,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEE,YAAY,EAAE2I,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC1FI,MAAM,EAAC,QAAQ;cACfnD,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,gBAEdvI,OAAA,CAACtB,QAAQ;gBAACiN,KAAK,EAAC,MAAM;gBAAApD,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtC3I,OAAA,CAACtB,QAAQ;gBAACiN,KAAK,EAAC,uBAAuB;gBAAApD,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxE3I,OAAA,CAACtB,QAAQ;gBAACiN,KAAK,EAAC,qBAAqB;gBAAApD,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpE3I,OAAA,CAACtB,QAAQ;gBAACiN,KAAK,EAAC,gBAAgB;gBAAApD,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC1D3I,OAAA,CAACtB,QAAQ;gBAACiN,KAAK,EAAC,SAAS;gBAAApD,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAIZ3I,OAAA,CAAC7B,SAAS;cACRmN,SAAS;cACTI,KAAK,EAAC,aAAa;cACnBC,KAAK,EAAE3I,eAAe,CAACG,WAAY;cACnCyI,QAAQ,EAAGC,CAAC,IAAK5I,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEG,WAAW,EAAE0I,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACzFI,MAAM,EAAC,QAAQ;cACfc,SAAS;cACTC,IAAI,EAAE,CAAE;cACRlE,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEF3I,OAAA,CAAC7B,SAAS;cACRmN,SAAS;cACTI,KAAK,EAAC,cAAc;cACpBC,KAAK,EAAE3I,eAAe,CAACI,YAAa;cACpCwI,QAAQ,EAAGC,CAAC,IAAK5I,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEI,YAAY,EAAEyI,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC1FI,MAAM,EAAC,QAAQ;cACfC,QAAQ;cACRE,UAAU,EAAC,0CAAuC;cAClDtD,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEF3I,OAAA,CAAC7B,SAAS;cACRmN,SAAS;cACTI,KAAK,EAAC,oBAAoB;cAC1BC,KAAK,EAAE3I,eAAe,CAACM,kBAAmB;cAC1CsI,QAAQ,EAAGC,CAAC,IAAK5I,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEM,kBAAkB,EAAEuI,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAChGI,MAAM,EAAC,QAAQ;cACfc,SAAS;cACTC,IAAI,EAAE,CAAE;cACRZ,UAAU,EAAC,2CAA2C;cACtDtD,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEF3I,OAAA,CAAC7B,SAAS;cACRmN,SAAS;cACTI,KAAK,EAAC,eAAe;cACrBO,IAAI,EAAC,MAAM;cACXN,KAAK,EAAE3I,eAAe,CAACK,aAAc;cACrCuI,QAAQ,EAAGC,CAAC,IAAK5I,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEK,aAAa,EAAEwI,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC3FI,MAAM,EAAC,QAAQ;cACfgB,eAAe,EAAE;gBACfC,MAAM,EAAE;cACV;YAAE;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,eACF;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChB3I,OAAA,CAAC9B,aAAa;QAAC0K,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAlD,QAAA,gBACjCvI,OAAA,CAACpC,MAAM;UACLyM,OAAO,EAAE9C,wBAAyB;UAClCqB,EAAE,EAAE;YAAE0B,aAAa,EAAE;UAAO,CAAE;UAAA/B,QAAA,EAE7B3F,iBAAiB,KAAK,MAAM,GAAG,QAAQ,GAAG;QAAS;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,EACR/F,iBAAiB,KAAK,MAAM,iBAC3B5C,OAAA,CAACpC,MAAM;UACLyM,OAAO,EAAE7C,mBAAoB;UAC7BuB,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACF0B,aAAa,EAAE,MAAM;YACrBtB,UAAU,EAAE,GAAG;YACfuB,EAAE,EAAE;UACN,CAAE;UAAAhC,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT3I,OAAA,CAACJ,kBAAkB;MACjBQ,UAAU,EAAEA,UAAW;MACvB+K,IAAI,EAAE5J,eAAgB;MACtB6J,OAAO,EAAEA,CAAA,KAAM5J,kBAAkB,CAAC,KAAK,CAAE;MACzCyL,SAAS,EAAEA,CAAC1I,QAAQ,EAAE2I,cAAc,KAAK;QACvC1J,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;;QAE9D;QACA,IAAIyJ,cAAc,EAAE;UAClB;UACA1J,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEyJ,cAAc,CAAC;QAC7C;;QAEA;QACA3J,WAAW,CAAC,CAAC;QACbY,eAAe,CAAC,CAAC;QACjBM,gBAAgB,CAAC,CAAC;QAClBjD,kBAAkB,CAAC,KAAK,CAAC;QAEzBgC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACzC;IAAE;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGF3I,OAAA,CAACH,qBAAqB;MACpBsL,IAAI,EAAE9J,qBAAsB;MAC5B+J,OAAO,EAAEA,CAAA,KAAM9J,wBAAwB,CAAC,KAAK,CAAE;MAC/CG,YAAY,EAAEA,YAAa;MAC3BI,sBAAsB,EAAEA,sBAAuB;MAC/CsL,kBAAkB,EAAG/J,YAAY,IAAK;QACpC9B,wBAAwB,CAAC,KAAK,CAAC;QAC/BoF,4BAA4B,CAAC,MAAM,EAAEtD,YAAY,CAAC;MACpD,CAAE;MACFgK,oBAAoB,EAAE,MAAOlG,cAAc,IAAK;QAC9C,MAAMD,wBAAwB,CAACC,cAAc,CAAC;QAC9C5F,wBAAwB,CAAC,KAAK,CAAC;MACjC,CAAE;MACFb,OAAO,EAAEkB,mBAAoB;MAC7BhB,KAAK,EAAEA;IAAM;MAAA6H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACrI,EAAA,CA34BIH,wBAAwB;EAAA,QAEY1C,eAAe;AAAA;AAAA4P,EAAA,GAFnDlN,wBAAwB;AA64B9B,eAAeA,wBAAwB;AAAC,IAAAkN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}