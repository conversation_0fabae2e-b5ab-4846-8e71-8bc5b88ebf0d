{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nimport axiosInstance from './axiosConfig';\nconst API_URL = config.API_URL;\nconst cantieriService = {\n  // Ottiene la lista di tutti i cantieri dell'utente corrente\n  getMyCantieri: async () => {\n    try {\n      const response = await axiosInstance.get('/cantieri');\n      return response.data;\n    } catch (error) {\n      console.error('Get cantieri error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea un nuovo cantiere\n  createCantiere: async cantiereData => {\n    try {\n      const response = await axiosInstance.post('/cantieri', cantiereData);\n      return response.data;\n    } catch (error) {\n      console.error('Create cantiere error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina un cantiere\n  deleteCantiere: async cantiereId => {\n    try {\n      const response = await axiosInstance.delete(`/cantieri/${cantiereId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete cantiere error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i dettagli di un cantiere specifico\n  getCantiere: async cantiereId => {\n    try {\n      const response = await axiosInstance.get(`/cantieri/${cantiereId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cantiere error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i cantieri di un utente specifico (per amministratori che impersonano utenti)\n  getUserCantieri: async userId => {\n    try {\n      const response = await axiosInstance.get(`/cantieri/user/${userId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get user cantieri error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Verifica la password del cantiere e la restituisce se corretta\n  verifyCantierePassword: async (cantiereId, passwordAttuale) => {\n    try {\n      const response = await axiosInstance.post(`/cantieri/${cantiereId}/verify-password`, {\n        password_attuale: passwordAttuale\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Verify cantiere password error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Visualizza direttamente la password del cantiere (per recupero password dimenticata)\n  viewCantierePasswordDirect: async cantiereId => {\n    try {\n      const response = await axiosInstance.get(`/cantieri/${cantiereId}/view-password`);\n      return response.data;\n    } catch (error) {\n      console.error('View cantiere password direct error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Cambia la password del cantiere\n  changeCantierePassword: async (cantiereId, passwordAttuale, passwordNuova, confermaPassword) => {\n    try {\n      const response = await axiosInstance.post(`/cantieri/${cantiereId}/change-password`, {\n        password_attuale: passwordAttuale,\n        password_nuova: passwordNuova,\n        conferma_password: confermaPassword\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Change cantiere password error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna i dati del cantiere (nome, descrizione)\n  updateCantiere: async (cantiereId, cantiereData) => {\n    try {\n      const response = await axiosInstance.put(`/cantieri/${cantiereId}`, cantiereData);\n      return response.data;\n    } catch (error) {\n      console.error('Update cantiere error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default cantieriService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "cantieriService", "getMyCantieri", "response", "get", "data", "error", "console", "createCantiere", "cantiereData", "post", "deleteCantiere", "cantiereId", "delete", "getCantiere", "getUserCantieri", "userId", "verifyCantierePassword", "passwordAttuale", "password_attuale", "viewCantierePasswordDirect", "changeCantierePassword", "passwordNuova", "confermaPassword", "password_nuova", "conferma_password", "updateCantiere", "put"], "sources": ["C:/CMS/webapp/frontend/src/services/cantieriService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\nimport axiosInstance from './axiosConfig';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\nconst cantieriService = {\r\n  // Ottiene la lista di tutti i cantieri dell'utente corrente\r\n  getMyCantieri: async () => {\r\n    try {\r\n      const response = await axiosInstance.get('/cantieri');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get cantieri error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea un nuovo cantiere\r\n  createCantiere: async (cantiereData) => {\r\n    try {\r\n      const response = await axiosInstance.post('/cantieri', cantiereData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Create cantiere error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Elimina un cantiere\r\n  deleteCantiere: async (cantiereId) => {\r\n    try {\r\n      const response = await axiosInstance.delete(`/cantieri/${cantiereId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Delete cantiere error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene i dettagli di un cantiere specifico\r\n  getCantiere: async (cantiereId) => {\r\n    try {\r\n      const response = await axiosInstance.get(`/cantieri/${cantiereId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get cantiere error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene i cantieri di un utente specifico (per amministratori che impersonano utenti)\r\n  getUserCantieri: async (userId) => {\r\n    try {\r\n      const response = await axiosInstance.get(`/cantieri/user/${userId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get user cantieri error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Verifica la password del cantiere e la restituisce se corretta\r\n  verifyCantierePassword: async (cantiereId, passwordAttuale) => {\r\n    try {\r\n      const response = await axiosInstance.post(`/cantieri/${cantiereId}/verify-password`, {\r\n        password_attuale: passwordAttuale\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Verify cantiere password error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Visualizza direttamente la password del cantiere (per recupero password dimenticata)\r\n  viewCantierePasswordDirect: async (cantiereId) => {\r\n    try {\r\n      const response = await axiosInstance.get(`/cantieri/${cantiereId}/view-password`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('View cantiere password direct error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Cambia la password del cantiere\r\n  changeCantierePassword: async (cantiereId, passwordAttuale, passwordNuova, confermaPassword) => {\r\n    try {\r\n      const response = await axiosInstance.post(`/cantieri/${cantiereId}/change-password`, {\r\n        password_attuale: passwordAttuale,\r\n        password_nuova: passwordNuova,\r\n        conferma_password: confermaPassword\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Change cantiere password error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Aggiorna i dati del cantiere (nome, descrizione)\r\n  updateCantiere: async (cantiereId, cantiereData) => {\r\n    try {\r\n      const response = await axiosInstance.put(`/cantieri/${cantiereId}`, cantiereData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Update cantiere error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default cantieriService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,aAAa,MAAM,eAAe;AAEzC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO;AAE9B,MAAMC,eAAe,GAAG;EACtB;EACAC,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACK,GAAG,CAAC,WAAW,CAAC;MACrD,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAE,cAAc,EAAE,MAAOC,YAAY,IAAK;IACtC,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMJ,aAAa,CAACW,IAAI,CAAC,WAAW,EAAED,YAAY,CAAC;MACpE,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAK,cAAc,EAAE,MAAOC,UAAU,IAAK;IACpC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMJ,aAAa,CAACc,MAAM,CAAC,aAAaD,UAAU,EAAE,CAAC;MACtE,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAQ,WAAW,EAAE,MAAOF,UAAU,IAAK;IACjC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMJ,aAAa,CAACK,GAAG,CAAC,aAAaQ,UAAU,EAAE,CAAC;MACnE,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAS,eAAe,EAAE,MAAOC,MAAM,IAAK;IACjC,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMJ,aAAa,CAACK,GAAG,CAAC,kBAAkBY,MAAM,EAAE,CAAC;MACpE,OAAOb,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAW,sBAAsB,EAAE,MAAAA,CAAOL,UAAU,EAAEM,eAAe,KAAK;IAC7D,IAAI;MACF,MAAMf,QAAQ,GAAG,MAAMJ,aAAa,CAACW,IAAI,CAAC,aAAaE,UAAU,kBAAkB,EAAE;QACnFO,gBAAgB,EAAED;MACpB,CAAC,CAAC;MACF,OAAOf,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAc,0BAA0B,EAAE,MAAOR,UAAU,IAAK;IAChD,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMJ,aAAa,CAACK,GAAG,CAAC,aAAaQ,UAAU,gBAAgB,CAAC;MACjF,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAe,sBAAsB,EAAE,MAAAA,CAAOT,UAAU,EAAEM,eAAe,EAAEI,aAAa,EAAEC,gBAAgB,KAAK;IAC9F,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMJ,aAAa,CAACW,IAAI,CAAC,aAAaE,UAAU,kBAAkB,EAAE;QACnFO,gBAAgB,EAAED,eAAe;QACjCM,cAAc,EAAEF,aAAa;QAC7BG,iBAAiB,EAAEF;MACrB,CAAC,CAAC;MACF,OAAOpB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAoB,cAAc,EAAE,MAAAA,CAAOd,UAAU,EAAEH,YAAY,KAAK;IAClD,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMJ,aAAa,CAAC4B,GAAG,CAAC,aAAaf,UAAU,EAAE,EAAEH,YAAY,CAAC;MACjF,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}