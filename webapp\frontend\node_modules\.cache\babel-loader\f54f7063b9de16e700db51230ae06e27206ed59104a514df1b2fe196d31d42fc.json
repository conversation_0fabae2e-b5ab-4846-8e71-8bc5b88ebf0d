{"ast": null, "code": "import { isSameWeek } from \"../../../isSameWeek.js\";\nfunction checkWeek(date, baseDate, options) {\n  const baseFormat = \"eeee p\";\n  if (isSameWeek(date, baseDate, options)) {\n    return baseFormat; // in same week\n  } else if (date.getTime() > baseDate.getTime()) {\n    return \"'下个'\" + baseFormat; // in next week\n  }\n  return \"'上个'\" + baseFormat; // in last week\n}\nconst formatRelativeLocale = {\n  lastWeek: checkWeek,\n  // days before yesterday, maybe in this week or last week\n  yesterday: \"'昨天' p\",\n  today: \"'今天' p\",\n  tomorrow: \"'明天' p\",\n  nextWeek: checkWeek,\n  // days after tomorrow, maybe in this week or next week\n  other: \"PP p\"\n};\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n  return format;\n};", "map": {"version": 3, "names": ["isSameWeek", "checkWeek", "date", "baseDate", "options", "baseFormat", "getTime", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "format"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/zh-CN/_lib/formatRelative.js"], "sourcesContent": ["import { isSameWeek } from \"../../../isSameWeek.js\";\n\nfunction checkWeek(date, baseDate, options) {\n  const baseFormat = \"eeee p\";\n\n  if (isSameWeek(date, baseDate, options)) {\n    return baseFormat; // in same week\n  } else if (date.getTime() > baseDate.getTime()) {\n    return \"'下个'\" + baseFormat; // in next week\n  }\n  return \"'上个'\" + baseFormat; // in last week\n}\n\nconst formatRelativeLocale = {\n  lastWeek: checkWeek, // days before yesterday, maybe in this week or last week\n  yesterday: \"'昨天' p\",\n  today: \"'今天' p\",\n  tomorrow: \"'明天' p\",\n  nextWeek: checkWeek, // days after tomorrow, maybe in this week or next week\n  other: \"PP p\",\n};\n\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n\n  return format;\n};\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,wBAAwB;AAEnD,SAASC,SAASA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC1C,MAAMC,UAAU,GAAG,QAAQ;EAE3B,IAAIL,UAAU,CAACE,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;IACvC,OAAOC,UAAU,CAAC,CAAC;EACrB,CAAC,MAAM,IAAIH,IAAI,CAACI,OAAO,CAAC,CAAC,GAAGH,QAAQ,CAACG,OAAO,CAAC,CAAC,EAAE;IAC9C,OAAO,MAAM,GAAGD,UAAU,CAAC,CAAC;EAC9B;EACA,OAAO,MAAM,GAAGA,UAAU,CAAC,CAAC;AAC9B;AAEA,MAAME,oBAAoB,GAAG;EAC3BC,QAAQ,EAAEP,SAAS;EAAE;EACrBQ,SAAS,EAAE,QAAQ;EACnBC,KAAK,EAAE,QAAQ;EACfC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAEX,SAAS;EAAE;EACrBY,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEb,IAAI,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EAChE,MAAMY,MAAM,GAAGT,oBAAoB,CAACQ,KAAK,CAAC;EAE1C,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACd,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACxC;EAEA,OAAOY,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}