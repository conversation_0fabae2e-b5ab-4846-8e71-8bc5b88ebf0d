{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\charts\\\\ProgressChart.js\";\nimport React from 'react';\nimport { <PERSON><PERSON><PERSON>, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line } from 'recharts';\nimport { Box, Typography, Grid, Paper } from '@mui/material';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst COLORS = {\n  primary: '#2c3e50',\n  secondary: '#34495e',\n  success: '#3498db',\n  warning: '#5d6d7e',\n  info: '#85929e',\n  error: '#566573',\n  light: '#ecf0f1',\n  dark: '#2c3e50',\n  accent: '#7fb3d3'\n};\nconst ProgressChart = ({\n  data\n}) => {\n  var _data$posa_recente;\n  if (!data) return null;\n\n  // Dati per il grafico a torta dell'avanzamento\n  const progressData = [{\n    name: '<PERSON><PERSON>',\n    value: data.metri_posati,\n    color: COLORS.success\n  }, {\n    name: '<PERSON><PERSON>i',\n    value: data.metri_da_posare,\n    color: COLORS.warning\n  }];\n\n  // Dati per il grafico a torta dei cavi\n  const caviData = [{\n    name: 'Cavi Posati',\n    value: data.cavi_posati,\n    color: COLORS.success\n  }, {\n    name: 'Cavi Rimanenti',\n    value: data.cavi_rimanenti,\n    color: COLORS.warning\n  }];\n\n  // Dati per il grafico a barre delle metriche principali\n  const metricsData = [{\n    name: 'Metri',\n    Totali: data.metri_totali,\n    Posati: data.metri_posati,\n    Rimanenti: data.metri_da_posare\n  }, {\n    name: 'Cavi',\n    Totali: data.totale_cavi,\n    Posati: data.cavi_posati,\n    Rimanenti: data.cavi_rimanenti\n  }];\n\n  // Dati per il grafico temporale della posa recente\n  const posaTrendData = ((_data$posa_recente = data.posa_recente) === null || _data$posa_recente === void 0 ? void 0 : _data$posa_recente.map(posa => ({\n    data: posa.data,\n    metri: posa.metri\n  }))) || [];\n  const CustomTooltip = ({\n    active,\n    payload,\n    label\n  }) => {\n    if (active && payload && payload.length) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1,\n          border: '1px solid #ccc'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: `${label}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), payload.map((entry, index) => /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          style: {\n            color: entry.color\n          },\n          children: `${entry.name}: ${entry.value}`\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const renderCustomizedLabel = ({\n    cx,\n    cy,\n    midAngle,\n    innerRadius,\n    outerRadius,\n    percent\n  }) => {\n    if (percent < 0.05) return null; // Non mostrare etichette per fette troppo piccole\n\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n    return /*#__PURE__*/_jsxDEV(\"text\", {\n      x: x,\n      y: y,\n      fill: \"white\",\n      textAnchor: x > cx ? 'start' : 'end',\n      dominantBaseline: \"central\",\n      fontSize: \"12\",\n      fontWeight: \"bold\",\n      children: `${(percent * 100).toFixed(0)}%`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      border: '1px solid #e0e0e0',\n      borderRadius: 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"subtitle1\",\n      sx: {\n        fontWeight: 500,\n        mb: 2,\n        color: '#2c3e50'\n      },\n      children: \"Analisi Avanzamento\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            border: '1px solid #e0e0e0',\n            borderRadius: 1,\n            overflow: 'hidden',\n            height: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: '#f8f9fa',\n              p: 1.5,\n              borderBottom: '1px solid #e0e0e0'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 600,\n                color: '#2c3e50'\n              },\n              children: \"Distribuzione Avanzamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2,\n              height: 'calc(100% - 50px)',\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: 180,\n              children: /*#__PURE__*/_jsxDEV(PieChart, {\n                children: [/*#__PURE__*/_jsxDEV(Pie, {\n                  data: progressData,\n                  cx: \"50%\",\n                  cy: \"50%\",\n                  innerRadius: 35,\n                  outerRadius: 65,\n                  paddingAngle: 2,\n                  dataKey: \"value\",\n                  stroke: \"none\",\n                  children: progressData.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                    fill: entry.color\n                  }, `cell-${index}`, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  content: ({\n                    active,\n                    payload\n                  }) => {\n                    if (active && payload && payload.length) {\n                      return /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          bgcolor: 'white',\n                          p: 1,\n                          border: '1px solid #e0e0e0',\n                          borderRadius: 1,\n                          fontSize: '12px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          sx: {\n                            fontWeight: 600\n                          },\n                          children: [payload[0].name, \": \", payload[0].value, \"m\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 175,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 168,\n                        columnNumber: 27\n                      }, this);\n                    }\n                    return null;\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), data.media_giornaliera && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            border: '1px solid #e0e0e0',\n            borderRadius: 1,\n            overflow: 'hidden'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: '#f8f9fa',\n              p: 1.5,\n              borderBottom: '1px solid #e0e0e0'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 600,\n                color: '#2c3e50'\n              },\n              children: \"Performance e Previsioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 3,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      fontWeight: 600,\n                      color: COLORS.primary\n                    },\n                    children: [data.media_giornaliera, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Media Giornaliera\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this), data.giorni_stimati && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 3,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      sx: {\n                        fontWeight: 600,\n                        color: COLORS.warning\n                      },\n                      children: data.giorni_stimati\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 223,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        color: '#666'\n                      },\n                      children: \"Giorni Stimati\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 226,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 600,\n                        color: COLORS.info\n                      },\n                      children: data.data_completamento\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        color: '#666'\n                      },\n                      children: \"Data Completamento Prevista\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n};\n_c = ProgressChart;\nexport default ProgressChart;\nvar _c;\n$RefreshReg$(_c, \"ProgressChart\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "Line<PERSON>hart", "Line", "Box", "Typography", "Grid", "Paper", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "COLORS", "primary", "secondary", "success", "warning", "info", "error", "light", "dark", "accent", "ProgressChart", "data", "_data$posa_recente", "progressData", "name", "value", "metri_posati", "color", "metri_da_posare", "caviData", "cavi_posati", "cavi_rimanenti", "metricsData", "Totali", "metri_totali", "Posati", "<PERSON><PERSON><PERSON><PERSON>", "totale_cavi", "posaTrendData", "posa_recente", "map", "posa", "metri", "CustomTooltip", "active", "payload", "label", "length", "sx", "p", "border", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "entry", "index", "style", "renderCustomizedLabel", "cx", "cy", "midAngle", "innerRadius", "outerRadius", "percent", "RADIAN", "Math", "PI", "radius", "x", "cos", "y", "sin", "fill", "textAnchor", "dominantBaseline", "fontSize", "fontWeight", "toFixed", "borderRadius", "mb", "container", "spacing", "item", "xs", "md", "overflow", "height", "bgcolor", "borderBottom", "display", "alignItems", "width", "paddingAngle", "dataKey", "stroke", "content", "media_giornaliera", "textAlign", "giorni_stimati", "data_completamento", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/charts/ProgressChart.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  Pie,\n  Cell,\n  BarChart,\n  Bar,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Toolt<PERSON>,\n  Legend,\n  ResponsiveContainer,\n  LineChart,\n  Line\n} from 'recharts';\nimport { Box, Typography, Grid, Paper } from '@mui/material';\n\nconst COLORS = {\n  primary: '#2c3e50',\n  secondary: '#34495e',\n  success: '#3498db',\n  warning: '#5d6d7e',\n  info: '#85929e',\n  error: '#566573',\n  light: '#ecf0f1',\n  dark: '#2c3e50',\n  accent: '#7fb3d3'\n};\n\nconst ProgressChart = ({ data }) => {\n  if (!data) return null;\n\n  // Dati per il grafico a torta dell'avanzamento\n  const progressData = [\n    {\n      name: 'Metri Posati',\n      value: data.metri_posati,\n      color: COLORS.success\n    },\n    {\n      name: 'Metri Rimanenti',\n      value: data.metri_da_posare,\n      color: COLORS.warning\n    }\n  ];\n\n  // Dati per il grafico a torta dei cavi\n  const caviData = [\n    {\n      name: '<PERSON><PERSON>',\n      value: data.cavi_posati,\n      color: COLORS.success\n    },\n    {\n      name: '<PERSON><PERSON>',\n      value: data.cavi_rimanenti,\n      color: COLORS.warning\n    }\n  ];\n\n  // Dati per il grafico a barre delle metriche principali\n  const metricsData = [\n    {\n      name: 'Metri',\n      Totali: data.metri_totali,\n      Posati: data.metri_posati,\n      Rimanenti: data.metri_da_posare\n    },\n    {\n      name: 'Cavi',\n      Totali: data.totale_cavi,\n      Posati: data.cavi_posati,\n      Rimanenti: data.cavi_rimanenti\n    }\n  ];\n\n  // Dati per il grafico temporale della posa recente\n  const posaTrendData = data.posa_recente?.map(posa => ({\n    data: posa.data,\n    metri: posa.metri\n  })) || [];\n\n  const CustomTooltip = ({ active, payload, label }) => {\n    if (active && payload && payload.length) {\n      return (\n        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>\n          <Typography variant=\"body2\">{`${label}`}</Typography>\n          {payload.map((entry, index) => (\n            <Typography key={index} variant=\"body2\" style={{ color: entry.color }}>\n              {`${entry.name}: ${entry.value}`}\n            </Typography>\n          ))}\n        </Paper>\n      );\n    }\n    return null;\n  };\n\n  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {\n    if (percent < 0.05) return null; // Non mostrare etichette per fette troppo piccole\n\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n\n    return (\n      <text\n        x={x}\n        y={y}\n        fill=\"white\"\n        textAnchor={x > cx ? 'start' : 'end'}\n        dominantBaseline=\"central\"\n        fontSize=\"12\"\n        fontWeight=\"bold\"\n      >\n        {`${(percent * 100).toFixed(0)}%`}\n      </text>\n    );\n  };\n\n  return (\n    <Paper sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n      <Typography variant=\"subtitle1\" sx={{ fontWeight: 500, mb: 2, color: '#2c3e50' }}>\n        Analisi Avanzamento\n      </Typography>\n\n      <Grid container spacing={2}>\n        {/* Grafici di Distribuzione */}\n        <Grid item xs={12} md={6}>\n          <Box sx={{\n            border: '1px solid #e0e0e0',\n            borderRadius: 1,\n            overflow: 'hidden',\n            height: '100%'\n          }}>\n            <Box sx={{\n              bgcolor: '#f8f9fa',\n              p: 1.5,\n              borderBottom: '1px solid #e0e0e0'\n            }}>\n              <Typography variant=\"subtitle2\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                Distribuzione Avanzamento\n              </Typography>\n            </Box>\n            <Box sx={{ p: 2, height: 'calc(100% - 50px)', display: 'flex', alignItems: 'center' }}>\n              <ResponsiveContainer width=\"100%\" height={180}>\n                <PieChart>\n                  <Pie\n                    data={progressData}\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    innerRadius={35}\n                    outerRadius={65}\n                    paddingAngle={2}\n                    dataKey=\"value\"\n                    stroke=\"none\"\n                  >\n                    {progressData.map((entry, index) => (\n                      <Cell key={`cell-${index}`} fill={entry.color} />\n                    ))}\n                  </Pie>\n                  <Tooltip\n                    content={({ active, payload }) => {\n                      if (active && payload && payload.length) {\n                        return (\n                          <Box sx={{\n                            bgcolor: 'white',\n                            p: 1,\n                            border: '1px solid #e0e0e0',\n                            borderRadius: 1,\n                            fontSize: '12px'\n                          }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 600 }}>\n                              {payload[0].name}: {payload[0].value}m\n                            </Typography>\n                          </Box>\n                        );\n                      }\n                      return null;\n                    }}\n                  />\n                </PieChart>\n              </ResponsiveContainer>\n            </Box>\n          </Box>\n        </Grid>\n\n        {/* Performance e Trend (solo se ci sono dati) */}\n        {data.media_giornaliera && (\n          <Grid item xs={12}>\n            <Box sx={{\n              border: '1px solid #e0e0e0',\n              borderRadius: 1,\n              overflow: 'hidden'\n            }}>\n              <Box sx={{\n                bgcolor: '#f8f9fa',\n                p: 1.5,\n                borderBottom: '1px solid #e0e0e0'\n              }}>\n                <Typography variant=\"subtitle2\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                  Performance e Previsioni\n                </Typography>\n              </Box>\n              <Box sx={{ p: 2 }}>\n                <Grid container spacing={3}>\n                  <Grid item xs={12} md={3}>\n                    <Box sx={{ textAlign: 'center' }}>\n                      <Typography variant=\"h6\" sx={{ fontWeight: 600, color: COLORS.primary }}>\n                        {data.media_giornaliera}m\n                      </Typography>\n                      <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                        Media Giornaliera\n                      </Typography>\n                    </Box>\n                  </Grid>\n                  {data.giorni_stimati && (\n                    <>\n                      <Grid item xs={12} md={3}>\n                        <Box sx={{ textAlign: 'center' }}>\n                          <Typography variant=\"h6\" sx={{ fontWeight: 600, color: COLORS.warning }}>\n                            {data.giorni_stimati}\n                          </Typography>\n                          <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                            Giorni Stimati\n                          </Typography>\n                        </Box>\n                      </Grid>\n                      <Grid item xs={12} md={6}>\n                        <Box sx={{ textAlign: 'center' }}>\n                          <Typography variant=\"body2\" sx={{ fontWeight: 600, color: COLORS.info }}>\n                            {data.data_completamento}\n                          </Typography>\n                          <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                            Data Completamento Prevista\n                          </Typography>\n                        </Box>\n                      </Grid>\n                    </>\n                  )}\n                </Grid>\n              </Box>\n            </Box>\n          </Grid>\n        )}\n      </Grid>\n    </Paper>\n  );\n};\n\nexport default ProgressChart;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,EACnBC,SAAS,EACTC,IAAI,QACC,UAAU;AACjB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7D,MAAMC,MAAM,GAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAA,IAAAC,kBAAA;EAClC,IAAI,CAACD,IAAI,EAAE,OAAO,IAAI;;EAEtB;EACA,MAAME,YAAY,GAAG,CACnB;IACEC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAEJ,IAAI,CAACK,YAAY;IACxBC,KAAK,EAAEjB,MAAM,CAACG;EAChB,CAAC,EACD;IACEW,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAEJ,IAAI,CAACO,eAAe;IAC3BD,KAAK,EAAEjB,MAAM,CAACI;EAChB,CAAC,CACF;;EAED;EACA,MAAMe,QAAQ,GAAG,CACf;IACEL,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAEJ,IAAI,CAACS,WAAW;IACvBH,KAAK,EAAEjB,MAAM,CAACG;EAChB,CAAC,EACD;IACEW,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAEJ,IAAI,CAACU,cAAc;IAC1BJ,KAAK,EAAEjB,MAAM,CAACI;EAChB,CAAC,CACF;;EAED;EACA,MAAMkB,WAAW,GAAG,CAClB;IACER,IAAI,EAAE,OAAO;IACbS,MAAM,EAAEZ,IAAI,CAACa,YAAY;IACzBC,MAAM,EAAEd,IAAI,CAACK,YAAY;IACzBU,SAAS,EAAEf,IAAI,CAACO;EAClB,CAAC,EACD;IACEJ,IAAI,EAAE,MAAM;IACZS,MAAM,EAAEZ,IAAI,CAACgB,WAAW;IACxBF,MAAM,EAAEd,IAAI,CAACS,WAAW;IACxBM,SAAS,EAAEf,IAAI,CAACU;EAClB,CAAC,CACF;;EAED;EACA,MAAMO,aAAa,GAAG,EAAAhB,kBAAA,GAAAD,IAAI,CAACkB,YAAY,cAAAjB,kBAAA,uBAAjBA,kBAAA,CAAmBkB,GAAG,CAACC,IAAI,KAAK;IACpDpB,IAAI,EAAEoB,IAAI,CAACpB,IAAI;IACfqB,KAAK,EAAED,IAAI,CAACC;EACd,CAAC,CAAC,CAAC,KAAI,EAAE;EAET,MAAMC,aAAa,GAAGA,CAAC;IAAEC,MAAM;IAAEC,OAAO;IAAEC;EAAM,CAAC,KAAK;IACpD,IAAIF,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAACE,MAAM,EAAE;MACvC,oBACExC,OAAA,CAACF,KAAK;QAAC2C,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAiB,CAAE;QAAAC,QAAA,gBAC5C5C,OAAA,CAACJ,UAAU;UAACiD,OAAO,EAAC,OAAO;UAAAD,QAAA,EAAE,GAAGL,KAAK;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,EACpDX,OAAO,CAACL,GAAG,CAAC,CAACiB,KAAK,EAAEC,KAAK,kBACxBnD,OAAA,CAACJ,UAAU;UAAaiD,OAAO,EAAC,OAAO;UAACO,KAAK,EAAE;YAAEhC,KAAK,EAAE8B,KAAK,CAAC9B;UAAM,CAAE;UAAAwB,QAAA,EACnE,GAAGM,KAAK,CAACjC,IAAI,KAAKiC,KAAK,CAAChC,KAAK;QAAE,GADjBiC,KAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAEZ;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMI,qBAAqB,GAAGA,CAAC;IAAEC,EAAE;IAAEC,EAAE;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,WAAW;IAAEC;EAAQ,CAAC,KAAK;IACzF,IAAIA,OAAO,GAAG,IAAI,EAAE,OAAO,IAAI,CAAC,CAAC;;IAEjC,MAAMC,MAAM,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IAC5B,MAAMC,MAAM,GAAGN,WAAW,GAAG,CAACC,WAAW,GAAGD,WAAW,IAAI,GAAG;IAC9D,MAAMO,CAAC,GAAGV,EAAE,GAAGS,MAAM,GAAGF,IAAI,CAACI,GAAG,CAAC,CAACT,QAAQ,GAAGI,MAAM,CAAC;IACpD,MAAMM,CAAC,GAAGX,EAAE,GAAGQ,MAAM,GAAGF,IAAI,CAACM,GAAG,CAAC,CAACX,QAAQ,GAAGI,MAAM,CAAC;IAEpD,oBACE5D,OAAA;MACEgE,CAAC,EAAEA,CAAE;MACLE,CAAC,EAAEA,CAAE;MACLE,IAAI,EAAC,OAAO;MACZC,UAAU,EAAEL,CAAC,GAAGV,EAAE,GAAG,OAAO,GAAG,KAAM;MACrCgB,gBAAgB,EAAC,SAAS;MAC1BC,QAAQ,EAAC,IAAI;MACbC,UAAU,EAAC,MAAM;MAAA5B,QAAA,EAEhB,GAAG,CAACe,OAAO,GAAG,GAAG,EAAEc,OAAO,CAAC,CAAC,CAAC;IAAG;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEX,CAAC;EAED,oBACEjD,OAAA,CAACF,KAAK;IAAC2C,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,MAAM,EAAE,mBAAmB;MAAE+B,YAAY,EAAE;IAAE,CAAE;IAAA9B,QAAA,gBAChE5C,OAAA,CAACJ,UAAU;MAACiD,OAAO,EAAC,WAAW;MAACJ,EAAE,EAAE;QAAE+B,UAAU,EAAE,GAAG;QAAEG,EAAE,EAAE,CAAC;QAAEvD,KAAK,EAAE;MAAU,CAAE;MAAAwB,QAAA,EAAC;IAElF;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbjD,OAAA,CAACH,IAAI;MAAC+E,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAjC,QAAA,gBAEzB5C,OAAA,CAACH,IAAI;QAACiF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApC,QAAA,eACvB5C,OAAA,CAACL,GAAG;UAAC8C,EAAE,EAAE;YACPE,MAAM,EAAE,mBAAmB;YAC3B+B,YAAY,EAAE,CAAC;YACfO,QAAQ,EAAE,QAAQ;YAClBC,MAAM,EAAE;UACV,CAAE;UAAAtC,QAAA,gBACA5C,OAAA,CAACL,GAAG;YAAC8C,EAAE,EAAE;cACP0C,OAAO,EAAE,SAAS;cAClBzC,CAAC,EAAE,GAAG;cACN0C,YAAY,EAAE;YAChB,CAAE;YAAAxC,QAAA,eACA5C,OAAA,CAACJ,UAAU;cAACiD,OAAO,EAAC,WAAW;cAACJ,EAAE,EAAE;gBAAE+B,UAAU,EAAE,GAAG;gBAAEpD,KAAK,EAAE;cAAU,CAAE;cAAAwB,QAAA,EAAC;YAE3E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNjD,OAAA,CAACL,GAAG;YAAC8C,EAAE,EAAE;cAAEC,CAAC,EAAE,CAAC;cAAEwC,MAAM,EAAE,mBAAmB;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAA1C,QAAA,eACpF5C,OAAA,CAACR,mBAAmB;cAAC+F,KAAK,EAAC,MAAM;cAACL,MAAM,EAAE,GAAI;cAAAtC,QAAA,eAC5C5C,OAAA,CAAClB,QAAQ;gBAAA8D,QAAA,gBACP5C,OAAA,CAACjB,GAAG;kBACF+B,IAAI,EAAEE,YAAa;kBACnBsC,EAAE,EAAC,KAAK;kBACRC,EAAE,EAAC,KAAK;kBACRE,WAAW,EAAE,EAAG;kBAChBC,WAAW,EAAE,EAAG;kBAChB8B,YAAY,EAAE,CAAE;kBAChBC,OAAO,EAAC,OAAO;kBACfC,MAAM,EAAC,MAAM;kBAAA9C,QAAA,EAEZ5B,YAAY,CAACiB,GAAG,CAAC,CAACiB,KAAK,EAAEC,KAAK,kBAC7BnD,OAAA,CAAChB,IAAI;oBAAuBoF,IAAI,EAAElB,KAAK,CAAC9B;kBAAM,GAAnC,QAAQ+B,KAAK,EAAE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAsB,CACjD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNjD,OAAA,CAACV,OAAO;kBACNqG,OAAO,EAAEA,CAAC;oBAAEtD,MAAM;oBAAEC;kBAAQ,CAAC,KAAK;oBAChC,IAAID,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAACE,MAAM,EAAE;sBACvC,oBACExC,OAAA,CAACL,GAAG;wBAAC8C,EAAE,EAAE;0BACP0C,OAAO,EAAE,OAAO;0BAChBzC,CAAC,EAAE,CAAC;0BACJC,MAAM,EAAE,mBAAmB;0BAC3B+B,YAAY,EAAE,CAAC;0BACfH,QAAQ,EAAE;wBACZ,CAAE;wBAAA3B,QAAA,eACA5C,OAAA,CAACJ,UAAU;0BAACiD,OAAO,EAAC,SAAS;0BAACJ,EAAE,EAAE;4BAAE+B,UAAU,EAAE;0BAAI,CAAE;0BAAA5B,QAAA,GACnDN,OAAO,CAAC,CAAC,CAAC,CAACrB,IAAI,EAAC,IAAE,EAACqB,OAAO,CAAC,CAAC,CAAC,CAACpB,KAAK,EAAC,GACvC;wBAAA;0BAAA4B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAEV;oBACA,OAAO,IAAI;kBACb;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGNnC,IAAI,CAAC8E,iBAAiB,iBACrB5F,OAAA,CAACH,IAAI;QAACiF,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAnC,QAAA,eAChB5C,OAAA,CAACL,GAAG;UAAC8C,EAAE,EAAE;YACPE,MAAM,EAAE,mBAAmB;YAC3B+B,YAAY,EAAE,CAAC;YACfO,QAAQ,EAAE;UACZ,CAAE;UAAArC,QAAA,gBACA5C,OAAA,CAACL,GAAG;YAAC8C,EAAE,EAAE;cACP0C,OAAO,EAAE,SAAS;cAClBzC,CAAC,EAAE,GAAG;cACN0C,YAAY,EAAE;YAChB,CAAE;YAAAxC,QAAA,eACA5C,OAAA,CAACJ,UAAU;cAACiD,OAAO,EAAC,WAAW;cAACJ,EAAE,EAAE;gBAAE+B,UAAU,EAAE,GAAG;gBAAEpD,KAAK,EAAE;cAAU,CAAE;cAAAwB,QAAA,EAAC;YAE3E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNjD,OAAA,CAACL,GAAG;YAAC8C,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,eAChB5C,OAAA,CAACH,IAAI;cAAC+E,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAjC,QAAA,gBACzB5C,OAAA,CAACH,IAAI;gBAACiF,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAApC,QAAA,eACvB5C,OAAA,CAACL,GAAG;kBAAC8C,EAAE,EAAE;oBAAEoD,SAAS,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,gBAC/B5C,OAAA,CAACJ,UAAU;oBAACiD,OAAO,EAAC,IAAI;oBAACJ,EAAE,EAAE;sBAAE+B,UAAU,EAAE,GAAG;sBAAEpD,KAAK,EAAEjB,MAAM,CAACC;oBAAQ,CAAE;oBAAAwC,QAAA,GACrE9B,IAAI,CAAC8E,iBAAiB,EAAC,GAC1B;kBAAA;oBAAA9C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbjD,OAAA,CAACJ,UAAU;oBAACiD,OAAO,EAAC,SAAS;oBAACJ,EAAE,EAAE;sBAAErB,KAAK,EAAE;oBAAO,CAAE;oBAAAwB,QAAA,EAAC;kBAErD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,EACNnC,IAAI,CAACgF,cAAc,iBAClB9F,OAAA,CAAAE,SAAA;gBAAA0C,QAAA,gBACE5C,OAAA,CAACH,IAAI;kBAACiF,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,CAAE;kBAAApC,QAAA,eACvB5C,OAAA,CAACL,GAAG;oBAAC8C,EAAE,EAAE;sBAAEoD,SAAS,EAAE;oBAAS,CAAE;oBAAAjD,QAAA,gBAC/B5C,OAAA,CAACJ,UAAU;sBAACiD,OAAO,EAAC,IAAI;sBAACJ,EAAE,EAAE;wBAAE+B,UAAU,EAAE,GAAG;wBAAEpD,KAAK,EAAEjB,MAAM,CAACI;sBAAQ,CAAE;sBAAAqC,QAAA,EACrE9B,IAAI,CAACgF;oBAAc;sBAAAhD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACbjD,OAAA,CAACJ,UAAU;sBAACiD,OAAO,EAAC,SAAS;sBAACJ,EAAE,EAAE;wBAAErB,KAAK,EAAE;sBAAO,CAAE;sBAAAwB,QAAA,EAAC;oBAErD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACPjD,OAAA,CAACH,IAAI;kBAACiF,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,CAAE;kBAAApC,QAAA,eACvB5C,OAAA,CAACL,GAAG;oBAAC8C,EAAE,EAAE;sBAAEoD,SAAS,EAAE;oBAAS,CAAE;oBAAAjD,QAAA,gBAC/B5C,OAAA,CAACJ,UAAU;sBAACiD,OAAO,EAAC,OAAO;sBAACJ,EAAE,EAAE;wBAAE+B,UAAU,EAAE,GAAG;wBAAEpD,KAAK,EAAEjB,MAAM,CAACK;sBAAK,CAAE;sBAAAoC,QAAA,EACrE9B,IAAI,CAACiF;oBAAkB;sBAAAjD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC,eACbjD,OAAA,CAACJ,UAAU;sBAACiD,OAAO,EAAC,SAAS;sBAACJ,EAAE,EAAE;wBAAErB,KAAK,EAAE;sBAAO,CAAE;sBAAAwB,QAAA,EAAC;oBAErD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA,eACP,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEZ,CAAC;AAAC+C,EAAA,GA5NInF,aAAa;AA8NnB,eAAeA,aAAa;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}