{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\AggiungiCavoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, FormHelperText, Alert, CircularProgress, Typography, Paper } from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport config from '../../config';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AggiungiCavoForm = ({\n  cantiereId,\n  onSuccess,\n  onError,\n  isDialog = false\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [loadingRevisione, setLoadingRevisione] = useState(true);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    revisione_ufficiale: '',\n    sistema: '',\n    utility: '',\n    colore_cavo: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    sh: '',\n    ubicazione_partenza: '',\n    utenza_partenza: '',\n    descrizione_utenza_partenza: '',\n    ubicazione_arrivo: '',\n    utenza_arrivo: '',\n    descrizione_utenza_arrivo: '',\n    metri_teorici: '',\n    metratura_reale: '0',\n    responsabile_posa: '',\n    id_bobina: '',\n    stato_installazione: 'Da installare'\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Carica la revisione corrente all'avvio\n  useEffect(() => {\n    const loadRevisioneCorrente = async () => {\n      try {\n        setLoadingRevisione(true);\n        const revisione = await caviService.getRevisioneCorrente(cantiereId);\n        setFormData(prev => ({\n          ...prev,\n          revisione_ufficiale: revisione\n        }));\n      } catch (error) {\n        console.error('Errore nel caricamento della revisione corrente:', error);\n        onError('Errore nel caricamento della revisione corrente');\n      } finally {\n        setLoadingRevisione(false);\n      }\n    };\n    loadRevisioneCorrente();\n  }, [cantiereId, onError]);\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    const additionalParams = {};\n    if (name === 'metratura_reale') {\n      additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n    }\n    const result = validateField(name, value, additionalParams);\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: !result.valid ? result.message : null\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: result.warning ? result.message : null\n    }));\n  };\n\n  // Gestisce l'annullamento dell'operazione\n  const handleCancel = () => {\n    if (isDialog) {\n      // Se è in un dialog, non fare nulla (il dialog ha il suo pulsante di annullamento)\n      return;\n    }\n    // Reindirizza alla visualizzazione dei cavi\n    redirectToVisualizzaCavi(navigate);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      // Validazione completa dei dati del cavo\n      console.log('Dati del form prima della validazione:', formData);\n      const validation = validateCavoData(formData);\n      console.log('Risultato validazione:', validation);\n      if (!validation.isValid) {\n        console.error('Errori di validazione:', validation.errors);\n        setFormErrors(validation.errors);\n        setFormWarnings(validation.warnings);\n        setLoading(false);\n        onError('Ci sono errori nel form. Controlla i campi evidenziati in rosso.');\n        return;\n      }\n\n      // Usa i dati validati\n      const validatedData = validation.validatedData;\n\n      // Converti l'ID cavo in maiuscolo\n      validatedData.id_cavo = validatedData.id_cavo.toUpperCase();\n\n      // Assicurati che i campi obbligatori siano presenti (basati sulla definizione della tabella)\n      // Campi obbligatori: id_cavo, id_cantiere, utility, tipologia, n_conduttori, sezione, metri_teorici,\n      // ubicazione_partenza, ubicazione_arrivo, stato_installazione\n\n      // Verifica che i campi obbligatori siano presenti\n      const requiredFields = ['id_cavo', 'utility', 'tipologia', 'n_conduttori', 'sezione', 'metri_teorici', 'ubicazione_partenza', 'ubicazione_arrivo', 'stato_installazione'];\n      const missingFields = requiredFields.filter(field => !validatedData[field]);\n      if (missingFields.length > 0) {\n        throw new Error(`Campi obbligatori mancanti: ${missingFields.join(', ')}`);\n      }\n\n      // Prepara i dati da inviare\n      const dataToSend = {\n        ...validatedData,\n        // Assicurati che i campi obbligatori siano presenti\n        id_cavo: validatedData.id_cavo.toUpperCase(),\n        utility: validatedData.utility,\n        tipologia: validatedData.tipologia,\n        n_conduttori: validatedData.n_conduttori ? validatedData.n_conduttori.toString() : \"0\",\n        // Invia come stringa\n        sezione: validatedData.sezione ? validatedData.sezione.toString() : \"0\",\n        // Invia come stringa\n        metri_teorici: parseFloat(validatedData.metri_teorici) || 0,\n        ubicazione_partenza: validatedData.ubicazione_partenza,\n        ubicazione_arrivo: validatedData.ubicazione_arrivo,\n        stato_installazione: validatedData.stato_installazione || 'Da installare',\n        // Campi opzionali\n        metratura_reale: validatedData.metratura_reale ? parseFloat(validatedData.metratura_reale) : null,\n        id_bobina: validatedData.id_bobina || null,\n        // Altri campi che potrebbero essere utili\n        sistema: validatedData.sistema || null,\n        colore_cavo: validatedData.colore_cavo || null,\n        utenza_partenza: validatedData.utenza_partenza || null,\n        utenza_arrivo: validatedData.utenza_arrivo || null,\n        descrizione_utenza_partenza: validatedData.descrizione_utenza_partenza || null,\n        descrizione_utenza_arrivo: validatedData.descrizione_utenza_arrivo || null,\n        sh: validatedData.sh || 'N',\n        responsabile_posa: validatedData.responsabile_posa || null,\n        note: validatedData.note || null\n      };\n      console.log('Dati da inviare al server dopo la validazione:', dataToSend);\n      try {\n        // Invia i dati al server\n        console.log('Tentativo di invio dati al server...');\n\n        // Verifica che cantiereId sia valido\n        if (!cantiereId) {\n          throw new Error('ID cantiere non valido o mancante');\n        }\n\n        // Usa axios direttamente per avere più controllo\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('Token di autenticazione mancante. Effettua nuovamente il login.');\n        }\n        console.log(`Invio richiesta POST a ${config.API_URL}/cavi/${cantiereId}`);\n        console.log('Dati inviati:', JSON.stringify(dataToSend, null, 2));\n        try {\n          // Tenta di inviare la richiesta\n          const response = await axios.post(`${config.API_URL}/cavi/${cantiereId}`, dataToSend, {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${token}`\n            },\n            timeout: 10000 // 10 secondi\n          });\n          console.log('Risposta dal server:', response.data);\n          onSuccess('Cavo aggiunto con successo');\n\n          // Reindirizza alla visualizzazione dei cavi solo se non è in un dialog\n          if (!isDialog) {\n            console.log('Reindirizzamento a visualizza cavi...');\n            // Usa setTimeout per dare tempo al browser di mostrare il messaggio di successo\n            setTimeout(() => {\n              try {\n                window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/visualizza`;\n              } catch (navError) {\n                console.error('Errore durante il reindirizzamento:', navError);\n                // Fallback: ricarica la pagina\n                window.location.reload();\n              }\n            }, 1000);\n          }\n        } catch (error) {\n          console.error('Errore durante l\\'invio dei dati al server:', error);\n\n          // Se è un errore di rete, verifica se il cavo è stato inserito\n          if (!error.response || error.code === 'ECONNABORTED' || error.message.includes('Network Error')) {\n            // Mostra un messaggio di attesa\n            onError('Problema di connessione. Verifica in corso...');\n\n            // Attendi un secondo e poi verifica se il cavo è stato inserito\n            setTimeout(async () => {\n              try {\n                // Verifica se il cavo esiste\n                const checkResponse = await axios.get(`${config.API_URL}/cavi/${cantiereId}/check/${dataToSend.id_cavo}`, {\n                  headers: {\n                    'Authorization': `Bearer ${token}`\n                  },\n                  timeout: 5000\n                });\n                if (checkResponse.data && checkResponse.data.exists) {\n                  // Il cavo è stato inserito con successo\n                  onSuccess('Cavo aggiunto con successo');\n\n                  // Reindirizza alla visualizzazione dei cavi solo se non è in un dialog\n                  if (!isDialog) {\n                    console.log('Reindirizzamento a visualizza cavi...');\n                    setTimeout(() => {\n                      try {\n                        window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/visualizza`;\n                      } catch (navError) {\n                        console.error('Errore durante il reindirizzamento:', navError);\n                        window.location.reload();\n                      }\n                    }, 1000);\n                  }\n                } else {\n                  // Il cavo non è stato inserito\n                  onError('Il cavo non è stato inserito a causa di un problema di connessione. Riprova.');\n                  setLoading(false);\n                }\n              } catch (checkError) {\n                console.error('Errore durante la verifica:', checkError);\n                onError('Impossibile verificare se il cavo è stato inserito. Riprova.');\n                setLoading(false);\n              }\n            }, 1000);\n          } else {\n            // Per altri tipi di errori, mostra il messaggio di errore\n            let errorMessage = 'Errore durante l\\'aggiunta del cavo';\n            if (error.response && error.response.data) {\n              const responseData = error.response.data;\n              if (responseData.detail) {\n                errorMessage = responseData.detail;\n              } else if (typeof responseData === 'string') {\n                errorMessage = responseData;\n              } else {\n                errorMessage = JSON.stringify(responseData);\n              }\n            } else if (error.message) {\n              errorMessage = error.message;\n            }\n            onError(errorMessage);\n            setLoading(false);\n          }\n        }\n      } catch (error) {\n        console.error('Errore durante la preparazione della richiesta:', error);\n        let errorMessage = error.message || 'Errore durante l\\'aggiunta del cavo';\n        onError(errorMessage);\n        setLoading(false);\n      }\n    } catch (error) {\n      console.error('Errore durante l\\'aggiunta del cavo:', error);\n      onError(error.detail || 'Errore durante l\\'aggiunta del cavo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mostra un avviso se ci sono warning\n  const hasWarnings = Object.keys(formWarnings).length > 0;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"form\",\n    onSubmit: handleSubmit,\n    noValidate: true,\n    children: loadingRevisione ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [hasWarnings && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 23\n          }, this),\n          sx: {\n            mb: isDialog ? 1 : 2,\n            py: isDialog ? 0.5 : 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontSize: isDialog ? '0.8rem' : '0.875rem'\n            },\n            children: \"Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 15\n        }, this), formWarnings.network_error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: isDialog ? 2 : 3,\n            py: isDialog ? 0.5 : 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: formWarnings.network_error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 2 : 3,\n          mb: isDialog ? 2 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '1.1rem' : '1.25rem'\n          },\n          children: \"Informazioni Generali\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"id_cavo\",\n              label: \"ID Cavo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.id_cavo,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.id_cavo,\n              helperText: formErrors.id_cavo,\n              inputProps: {\n                style: {\n                  textTransform: 'uppercase'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"revisione_ufficiale\",\n              label: \"Revisione Ufficiale\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.revisione_ufficiale,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.revisione_ufficiale,\n              helperText: formErrors.revisione_ufficiale\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"sistema\",\n              label: \"Sistema\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.sistema,\n              onChange: handleFormChange,\n              error: !!formErrors.sistema,\n              helperText: formErrors.sistema\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utility\",\n              label: \"Utility\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utility,\n              onChange: handleFormChange,\n              error: !!formErrors.utility,\n              helperText: formErrors.utility\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Caratteristiche Tecniche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"colore_cavo\",\n              label: \"Colore Cavo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.colore_cavo,\n              onChange: handleFormChange,\n              error: !!formErrors.colore_cavo,\n              helperText: formErrors.colore_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"tipologia\",\n              label: \"Tipologia\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.tipologia,\n              onChange: handleFormChange,\n              error: !!formErrors.tipologia,\n              helperText: formErrors.tipologia\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"n_conduttori\",\n              label: \"Numero Conduttori\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.n_conduttori,\n              onChange: handleFormChange,\n              error: !!formErrors.n_conduttori,\n              helperText: formErrors.n_conduttori || formWarnings.n_conduttori,\n              FormHelperTextProps: {\n                style: {\n                  color: formWarnings.n_conduttori ? 'orange' : undefined\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"sezione\",\n              label: \"Sezione\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.sezione,\n              onChange: handleFormChange,\n              error: !!formErrors.sezione,\n              helperText: formErrors.sezione || formWarnings.sezione,\n              FormHelperTextProps: {\n                style: {\n                  color: formWarnings.sezione ? 'orange' : undefined\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              error: !!formErrors.sh,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"sh-label\",\n                children: \"Schermato (S/N)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"sh-label\",\n                name: \"sh\",\n                value: formData.sh,\n                label: \"Schermato (S/N)\",\n                onChange: handleFormChange,\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"S\",\n                  children: \"S\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"N\",\n                  children: \"N\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 19\n              }, this), formErrors.sh && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                children: formErrors.sh\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 2 : 3,\n          mb: isDialog ? 2 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '1.1rem' : '1.25rem'\n          },\n          children: \"Ubicazione Partenza\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"ubicazione_partenza\",\n              label: \"Ubicazione Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.ubicazione_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.ubicazione_partenza,\n              helperText: formErrors.ubicazione_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utenza_partenza\",\n              label: \"Utenza Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utenza_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.utenza_partenza,\n              helperText: formErrors.utenza_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"descrizione_utenza_partenza\",\n              label: \"Descrizione Utenza Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.descrizione_utenza_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.descrizione_utenza_partenza,\n              helperText: formErrors.descrizione_utenza_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 2 : 3,\n          mb: isDialog ? 2 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '1.1rem' : '1.25rem'\n          },\n          children: \"Ubicazione Arrivo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"ubicazione_arrivo\",\n              label: \"Ubicazione Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.ubicazione_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.ubicazione_arrivo,\n              helperText: formErrors.ubicazione_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utenza_arrivo\",\n              label: \"Utenza Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utenza_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.utenza_arrivo,\n              helperText: formErrors.utenza_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"descrizione_utenza_arrivo\",\n              label: \"Descrizione Utenza Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.descrizione_utenza_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.descrizione_utenza_arrivo,\n              helperText: formErrors.descrizione_utenza_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 2 : 3,\n          mb: isDialog ? 2 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '1.1rem' : '1.25rem'\n          },\n          children: \"Metratura\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"metri_teorici\",\n              label: \"Metri Teorici\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.metri_teorici,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.metri_teorici,\n              helperText: formErrors.metri_teorici\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 576,\n        columnNumber: 11\n      }, this), !isDialog && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          size: \"large\",\n          onClick: handleCancel,\n          disabled: loading,\n          sx: {\n            minWidth: 150\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          color: \"primary\",\n          size: \"large\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 28\n          }, this),\n          disabled: loading,\n          sx: {\n            minWidth: 150\n          },\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 28\n          }, this) : 'Salva Cavo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 599,\n        columnNumber: 13\n      }, this), isDialog && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          justifyContent: 'flex-end',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          color: \"primary\",\n          size: \"medium\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 28\n          }, this),\n          disabled: loading,\n          sx: {\n            minWidth: 120\n          },\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 28\n          }, this) : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 318,\n    columnNumber: 5\n  }, this);\n};\n_s(AggiungiCavoForm, \"Muxm1Wb8jDN/87yWsjtE8U4F0S8=\", false, function () {\n  return [useNavigate];\n});\n_c = AggiungiCavoForm;\nexport default AggiungiCavoForm;\nvar _c;\n$RefreshReg$(_c, \"AggiungiCavoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "FormHelperText", "<PERSON><PERSON>", "CircularProgress", "Typography", "Paper", "Save", "SaveIcon", "Warning", "WarningIcon", "useNavigate", "axios", "config", "caviService", "validateCavoData", "validateField", "isEmpty", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AggiungiCavoForm", "cantiereId", "onSuccess", "onError", "isDialog", "_s", "navigate", "loading", "setLoading", "loadingRevisione", "setLoadingRevisione", "formData", "setFormData", "id_cavo", "revisione_ufficiale", "sistema", "utility", "colore_cavo", "tipologia", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "metri_te<PERSON>ci", "metratura_reale", "responsabile_posa", "id_bobina", "stato_installazione", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "loadRevisioneCorrente", "revisione", "getRevisioneCorrente", "prev", "error", "console", "handleFormChange", "e", "name", "value", "target", "additionalParams", "metriTeorici", "parseFloat", "result", "valid", "message", "warning", "handleCancel", "handleSubmit", "preventDefault", "log", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "validatedData", "toUpperCase", "requiredFields", "missingFields", "filter", "field", "length", "Error", "join", "dataToSend", "toString", "note", "token", "localStorage", "getItem", "API_URL", "JSON", "stringify", "response", "post", "headers", "timeout", "data", "setTimeout", "window", "location", "href", "navError", "reload", "code", "includes", "checkResponse", "get", "exists", "checkError", "errorMessage", "responseData", "detail", "hasWarnings", "Object", "keys", "component", "onSubmit", "noValidate", "children", "sx", "display", "justifyContent", "my", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "icon", "mb", "py", "variant", "fontSize", "network_error", "fontWeight", "p", "boxShadow", "gutterBottom", "container", "spacing", "item", "xs", "sm", "label", "fullWidth", "onChange", "required", "helperText", "inputProps", "style", "textTransform", "FormHelperTextProps", "color", "undefined", "id", "labelId", "mt", "gap", "size", "onClick", "disabled", "min<PERSON><PERSON><PERSON>", "type", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/AggiungiCavoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  FormHelperText,\n  Alert,\n  CircularProgress,\n  Typography,\n  Paper\n} from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport config from '../../config';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\nconst AggiungiCavoForm = ({ cantiereId, onSuccess, onError, isDialog = false }) => {\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [loadingRevisione, setLoadingRevisione] = useState(true);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    revisione_ufficiale: '',\n    sistema: '',\n    utility: '',\n    colore_cavo: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    sh: '',\n    ubicazione_partenza: '',\n    utenza_partenza: '',\n    descrizione_utenza_partenza: '',\n    ubicazione_arrivo: '',\n    utenza_arrivo: '',\n    descrizione_utenza_arrivo: '',\n    metri_teorici: '',\n    metratura_reale: '0',\n    responsabile_posa: '',\n    id_bobina: '',\n    stato_installazione: 'Da installare'\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Carica la revisione corrente all'avvio\n  useEffect(() => {\n    const loadRevisioneCorrente = async () => {\n      try {\n        setLoadingRevisione(true);\n        const revisione = await caviService.getRevisioneCorrente(cantiereId);\n        setFormData(prev => ({ ...prev, revisione_ufficiale: revisione }));\n      } catch (error) {\n        console.error('Errore nel caricamento della revisione corrente:', error);\n        onError('Errore nel caricamento della revisione corrente');\n      } finally {\n        setLoadingRevisione(false);\n      }\n    };\n\n    loadRevisioneCorrente();\n  }, [cantiereId, onError]);\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    const additionalParams = {};\n    if (name === 'metratura_reale') {\n      additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n    }\n\n    const result = validateField(name, value, additionalParams);\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: !result.valid ? result.message : null\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: result.warning ? result.message : null\n    }));\n  };\n\n  // Gestisce l'annullamento dell'operazione\n  const handleCancel = () => {\n    if (isDialog) {\n      // Se è in un dialog, non fare nulla (il dialog ha il suo pulsante di annullamento)\n      return;\n    }\n    // Reindirizza alla visualizzazione dei cavi\n    redirectToVisualizzaCavi(navigate);\n  };\n\n\n\n  // Gestisce l'invio del form\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      // Validazione completa dei dati del cavo\n      console.log('Dati del form prima della validazione:', formData);\n      const validation = validateCavoData(formData);\n      console.log('Risultato validazione:', validation);\n\n      if (!validation.isValid) {\n        console.error('Errori di validazione:', validation.errors);\n        setFormErrors(validation.errors);\n        setFormWarnings(validation.warnings);\n        setLoading(false);\n        onError('Ci sono errori nel form. Controlla i campi evidenziati in rosso.');\n        return;\n      }\n\n      // Usa i dati validati\n      const validatedData = validation.validatedData;\n\n      // Converti l'ID cavo in maiuscolo\n      validatedData.id_cavo = validatedData.id_cavo.toUpperCase();\n\n      // Assicurati che i campi obbligatori siano presenti (basati sulla definizione della tabella)\n      // Campi obbligatori: id_cavo, id_cantiere, utility, tipologia, n_conduttori, sezione, metri_teorici,\n      // ubicazione_partenza, ubicazione_arrivo, stato_installazione\n\n      // Verifica che i campi obbligatori siano presenti\n      const requiredFields = [\n        'id_cavo', 'utility', 'tipologia', 'n_conduttori', 'sezione', 'metri_teorici',\n        'ubicazione_partenza', 'ubicazione_arrivo', 'stato_installazione'\n      ];\n\n      const missingFields = requiredFields.filter(field => !validatedData[field]);\n      if (missingFields.length > 0) {\n        throw new Error(`Campi obbligatori mancanti: ${missingFields.join(', ')}`);\n      }\n\n      // Prepara i dati da inviare\n      const dataToSend = {\n        ...validatedData,\n        // Assicurati che i campi obbligatori siano presenti\n        id_cavo: validatedData.id_cavo.toUpperCase(),\n        utility: validatedData.utility,\n        tipologia: validatedData.tipologia,\n        n_conduttori: validatedData.n_conduttori ? validatedData.n_conduttori.toString() : \"0\", // Invia come stringa\n        sezione: validatedData.sezione ? validatedData.sezione.toString() : \"0\", // Invia come stringa\n        metri_teorici: parseFloat(validatedData.metri_teorici) || 0,\n        ubicazione_partenza: validatedData.ubicazione_partenza,\n        ubicazione_arrivo: validatedData.ubicazione_arrivo,\n        stato_installazione: validatedData.stato_installazione || 'Da installare',\n\n        // Campi opzionali\n        metratura_reale: validatedData.metratura_reale ? parseFloat(validatedData.metratura_reale) : null,\n        id_bobina: validatedData.id_bobina || null,\n\n        // Altri campi che potrebbero essere utili\n        sistema: validatedData.sistema || null,\n        colore_cavo: validatedData.colore_cavo || null,\n        utenza_partenza: validatedData.utenza_partenza || null,\n        utenza_arrivo: validatedData.utenza_arrivo || null,\n        descrizione_utenza_partenza: validatedData.descrizione_utenza_partenza || null,\n        descrizione_utenza_arrivo: validatedData.descrizione_utenza_arrivo || null,\n        sh: validatedData.sh || 'N',\n        responsabile_posa: validatedData.responsabile_posa || null,\n        note: validatedData.note || null\n      };\n\n      console.log('Dati da inviare al server dopo la validazione:', dataToSend);\n\n      try {\n        // Invia i dati al server\n        console.log('Tentativo di invio dati al server...');\n\n        // Verifica che cantiereId sia valido\n        if (!cantiereId) {\n          throw new Error('ID cantiere non valido o mancante');\n        }\n\n        // Usa axios direttamente per avere più controllo\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('Token di autenticazione mancante. Effettua nuovamente il login.');\n        }\n\n        console.log(`Invio richiesta POST a ${config.API_URL}/cavi/${cantiereId}`);\n        console.log('Dati inviati:', JSON.stringify(dataToSend, null, 2));\n\n        try {\n          // Tenta di inviare la richiesta\n          const response = await axios.post(`${config.API_URL}/cavi/${cantiereId}`, dataToSend, {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${token}`\n            },\n            timeout: 10000 // 10 secondi\n          });\n\n          console.log('Risposta dal server:', response.data);\n          onSuccess('Cavo aggiunto con successo');\n\n          // Reindirizza alla visualizzazione dei cavi solo se non è in un dialog\n          if (!isDialog) {\n            console.log('Reindirizzamento a visualizza cavi...');\n            // Usa setTimeout per dare tempo al browser di mostrare il messaggio di successo\n            setTimeout(() => {\n              try {\n                window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/visualizza`;\n              } catch (navError) {\n                console.error('Errore durante il reindirizzamento:', navError);\n                // Fallback: ricarica la pagina\n                window.location.reload();\n              }\n            }, 1000);\n          }\n        } catch (error) {\n          console.error('Errore durante l\\'invio dei dati al server:', error);\n\n          // Se è un errore di rete, verifica se il cavo è stato inserito\n          if (!error.response || error.code === 'ECONNABORTED' || error.message.includes('Network Error')) {\n            // Mostra un messaggio di attesa\n            onError('Problema di connessione. Verifica in corso...');\n\n            // Attendi un secondo e poi verifica se il cavo è stato inserito\n            setTimeout(async () => {\n              try {\n                // Verifica se il cavo esiste\n                const checkResponse = await axios.get(`${config.API_URL}/cavi/${cantiereId}/check/${dataToSend.id_cavo}`, {\n                  headers: {\n                    'Authorization': `Bearer ${token}`\n                  },\n                  timeout: 5000\n                });\n\n                if (checkResponse.data && checkResponse.data.exists) {\n                  // Il cavo è stato inserito con successo\n                  onSuccess('Cavo aggiunto con successo');\n\n                  // Reindirizza alla visualizzazione dei cavi solo se non è in un dialog\n                  if (!isDialog) {\n                    console.log('Reindirizzamento a visualizza cavi...');\n                    setTimeout(() => {\n                      try {\n                        window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/visualizza`;\n                      } catch (navError) {\n                        console.error('Errore durante il reindirizzamento:', navError);\n                        window.location.reload();\n                      }\n                    }, 1000);\n                  }\n                } else {\n                  // Il cavo non è stato inserito\n                  onError('Il cavo non è stato inserito a causa di un problema di connessione. Riprova.');\n                  setLoading(false);\n                }\n              } catch (checkError) {\n                console.error('Errore durante la verifica:', checkError);\n                onError('Impossibile verificare se il cavo è stato inserito. Riprova.');\n                setLoading(false);\n              }\n            }, 1000);\n          } else {\n            // Per altri tipi di errori, mostra il messaggio di errore\n            let errorMessage = 'Errore durante l\\'aggiunta del cavo';\n\n            if (error.response && error.response.data) {\n              const responseData = error.response.data;\n              if (responseData.detail) {\n                errorMessage = responseData.detail;\n              } else if (typeof responseData === 'string') {\n                errorMessage = responseData;\n              } else {\n                errorMessage = JSON.stringify(responseData);\n              }\n            } else if (error.message) {\n              errorMessage = error.message;\n            }\n\n            onError(errorMessage);\n            setLoading(false);\n          }\n        }\n      } catch (error) {\n        console.error('Errore durante la preparazione della richiesta:', error);\n        let errorMessage = error.message || 'Errore durante l\\'aggiunta del cavo';\n        onError(errorMessage);\n        setLoading(false);\n      }\n    } catch (error) {\n      console.error('Errore durante l\\'aggiunta del cavo:', error);\n      onError(error.detail || 'Errore durante l\\'aggiunta del cavo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mostra un avviso se ci sono warning\n  const hasWarnings = Object.keys(formWarnings).length > 0;\n\n  return (\n    <Box component=\"form\" onSubmit={handleSubmit} noValidate>\n      {loadingRevisione ? (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      ) : (\n        <>\n          {hasWarnings && (\n            <>\n              <Alert\n                severity=\"warning\"\n                icon={<WarningIcon />}\n                sx={{ mb: isDialog ? 1 : 2, py: isDialog ? 0.5 : 1 }}\n              >\n                <Typography variant=\"subtitle2\" sx={{ fontSize: isDialog ? '0.8rem' : '0.875rem' }}>\n                  Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\n                </Typography>\n              </Alert>\n\n              {/* Mostra avvisi specifici */}\n              {formWarnings.network_error && (\n                <Alert\n                  severity=\"error\"\n                  sx={{ mb: isDialog ? 2 : 3, py: isDialog ? 0.5 : 1 }}\n                >\n                  <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold' }}>\n                    {formWarnings.network_error}\n                  </Typography>\n                </Alert>\n              )}\n            </>\n          )}\n\n          <Paper sx={{ p: isDialog ? 2 : 3, mb: isDialog ? 2 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '1.1rem' : '1.25rem' }}>\n              Informazioni Generali\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_cavo\"\n                  label=\"ID Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_cavo}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.id_cavo}\n                  helperText={formErrors.id_cavo}\n                  inputProps={{ style: { textTransform: 'uppercase' } }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"revisione_ufficiale\"\n                  label=\"Revisione Ufficiale\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.revisione_ufficiale}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.revisione_ufficiale}\n                  helperText={formErrors.revisione_ufficiale}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sistema\"\n                  label=\"Sistema\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sistema}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sistema}\n                  helperText={formErrors.sistema}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utility}\n                  helperText={formErrors.utility}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: 3, mb: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Caratteristiche Tecniche\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"colore_cavo\"\n                  label=\"Colore Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.colore_cavo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.colore_cavo}\n                  helperText={formErrors.colore_cavo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                  error={!!formErrors.tipologia}\n                  helperText={formErrors.tipologia}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"Numero Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                  error={!!formErrors.n_conduttori}\n                  helperText={formErrors.n_conduttori || formWarnings.n_conduttori}\n                  FormHelperTextProps={{\n                    style: { color: formWarnings.n_conduttori ? 'orange' : undefined }\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sezione}\n                  helperText={formErrors.sezione || formWarnings.sezione}\n                  FormHelperTextProps={{\n                    style: { color: formWarnings.sezione ? 'orange' : undefined }\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <FormControl fullWidth error={!!formErrors.sh}>\n                  <InputLabel id=\"sh-label\">Schermato (S/N)</InputLabel>\n                  <Select\n                    labelId=\"sh-label\"\n                    name=\"sh\"\n                    value={formData.sh}\n                    label=\"Schermato (S/N)\"\n                    onChange={handleFormChange}\n                  >\n                    <MenuItem value=\"S\">S</MenuItem>\n                    <MenuItem value=\"N\">N</MenuItem>\n                  </Select>\n                  {formErrors.sh && <FormHelperText>{formErrors.sh}</FormHelperText>}\n                </FormControl>\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 2 : 3, mb: isDialog ? 2 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '1.1rem' : '1.25rem' }}>\n              Ubicazione Partenza\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"ubicazione_partenza\"\n                  label=\"Ubicazione Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_partenza}\n                  helperText={formErrors.ubicazione_partenza}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"utenza_partenza\"\n                  label=\"Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utenza_partenza}\n                  helperText={formErrors.utenza_partenza}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"descrizione_utenza_partenza\"\n                  label=\"Descrizione Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.descrizione_utenza_partenza}\n                  helperText={formErrors.descrizione_utenza_partenza}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 2 : 3, mb: isDialog ? 2 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '1.1rem' : '1.25rem' }}>\n              Ubicazione Arrivo\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"ubicazione_arrivo\"\n                  label=\"Ubicazione Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_arrivo}\n                  helperText={formErrors.ubicazione_arrivo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"utenza_arrivo\"\n                  label=\"Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utenza_arrivo}\n                  helperText={formErrors.utenza_arrivo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"descrizione_utenza_arrivo\"\n                  label=\"Descrizione Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.descrizione_utenza_arrivo}\n                  helperText={formErrors.descrizione_utenza_arrivo}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 2 : 3, mb: isDialog ? 2 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '1.1rem' : '1.25rem' }}>\n              Metratura\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_teorici\"\n                  label=\"Metri Teorici\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_teorici}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_teorici}\n                  helperText={formErrors.metri_teorici}\n                />\n              </Grid>\n\n            </Grid>\n          </Paper>\n\n          {!isDialog && (\n            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center', gap: 2 }}>\n              <Button\n                variant=\"outlined\"\n                color=\"secondary\"\n                size=\"large\"\n                onClick={handleCancel}\n                disabled={loading}\n                sx={{ minWidth: 150 }}\n              >\n                Annulla\n              </Button>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                color=\"primary\"\n                size=\"large\"\n                startIcon={<SaveIcon />}\n                disabled={loading}\n                sx={{ minWidth: 150 }}\n              >\n                {loading ? <CircularProgress size={24} /> : 'Salva Cavo'}\n              </Button>\n            </Box>\n          )}\n          {isDialog && (\n            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                color=\"primary\"\n                size=\"medium\"\n                startIcon={<SaveIcon />}\n                disabled={loading}\n                sx={{ minWidth: 120 }}\n              >\n                {loading ? <CircularProgress size={20} /> : 'Salva'}\n              </Button>\n            </Box>\n          )}\n        </>\n      )}\n    </Box>\n  );\n};\n\nexport default AggiungiCavoForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SAASC,IAAI,IAAIC,QAAQ,EAAEC,OAAO,IAAIC,WAAW,QAAQ,qBAAqB;AAC9E,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,QAAQ,6BAA6B;AACtF,SAASC,wBAAwB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvE,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC,OAAO;EAAEC,QAAQ,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACjF,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC;IACvC4C,OAAO,EAAE,EAAE;IACXC,mBAAmB,EAAE,EAAE;IACvBC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,EAAE,EAAE,EAAE;IACNC,mBAAmB,EAAE,EAAE;IACvBC,eAAe,EAAE,EAAE;IACnBC,2BAA2B,EAAE,EAAE;IAC/BC,iBAAiB,EAAE,EAAE;IACrBC,aAAa,EAAE,EAAE;IACjBC,yBAAyB,EAAE,EAAE;IAC7BC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,GAAG;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,EAAE;IACbC,mBAAmB,EAAE;EACvB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMmE,qBAAqB,GAAG,MAAAA,CAAA,KAAY;MACxC,IAAI;QACF3B,mBAAmB,CAAC,IAAI,CAAC;QACzB,MAAM4B,SAAS,GAAG,MAAM/C,WAAW,CAACgD,oBAAoB,CAACtC,UAAU,CAAC;QACpEW,WAAW,CAAC4B,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE1B,mBAAmB,EAAEwB;QAAU,CAAC,CAAC,CAAC;MACpE,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;QACxEtC,OAAO,CAAC,iDAAiD,CAAC;MAC5D,CAAC,SAAS;QACRO,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC;IAED2B,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,CAACpC,UAAU,EAAEE,OAAO,CAAC,CAAC;;EAEzB;EACA,MAAMwC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACAnC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACkC,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,MAAME,gBAAgB,GAAG,CAAC,CAAC;IAC3B,IAAIH,IAAI,KAAK,iBAAiB,EAAE;MAC9BG,gBAAgB,CAACC,YAAY,GAAGC,UAAU,CAACvC,QAAQ,CAACiB,aAAa,IAAI,CAAC,CAAC;IACzE;IAEA,MAAMuB,MAAM,GAAG1D,aAAa,CAACoD,IAAI,EAAEC,KAAK,EAAEE,gBAAgB,CAAC;;IAE3D;IACAd,aAAa,CAACM,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACK,IAAI,GAAG,CAACM,MAAM,CAACC,KAAK,GAAGD,MAAM,CAACE,OAAO,GAAG;IAC3C,CAAC,CAAC,CAAC;;IAEH;IACAjB,eAAe,CAACI,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACK,IAAI,GAAGM,MAAM,CAACG,OAAO,GAAGH,MAAM,CAACE,OAAO,GAAG;IAC5C,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAInD,QAAQ,EAAE;MACZ;MACA;IACF;IACA;IACAT,wBAAwB,CAACW,QAAQ,CAAC;EACpC,CAAC;;EAID;EACA,MAAMkD,YAAY,GAAG,MAAOZ,CAAC,IAAK;IAChCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAClBjD,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACAkC,OAAO,CAACgB,GAAG,CAAC,wCAAwC,EAAE/C,QAAQ,CAAC;MAC/D,MAAMgD,UAAU,GAAGnE,gBAAgB,CAACmB,QAAQ,CAAC;MAC7C+B,OAAO,CAACgB,GAAG,CAAC,wBAAwB,EAAEC,UAAU,CAAC;MAEjD,IAAI,CAACA,UAAU,CAACC,OAAO,EAAE;QACvBlB,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEkB,UAAU,CAACE,MAAM,CAAC;QAC1D3B,aAAa,CAACyB,UAAU,CAACE,MAAM,CAAC;QAChCzB,eAAe,CAACuB,UAAU,CAACG,QAAQ,CAAC;QACpCtD,UAAU,CAAC,KAAK,CAAC;QACjBL,OAAO,CAAC,kEAAkE,CAAC;QAC3E;MACF;;MAEA;MACA,MAAM4D,aAAa,GAAGJ,UAAU,CAACI,aAAa;;MAE9C;MACAA,aAAa,CAAClD,OAAO,GAAGkD,aAAa,CAAClD,OAAO,CAACmD,WAAW,CAAC,CAAC;;MAE3D;MACA;MACA;;MAEA;MACA,MAAMC,cAAc,GAAG,CACrB,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,SAAS,EAAE,eAAe,EAC7E,qBAAqB,EAAE,mBAAmB,EAAE,qBAAqB,CAClE;MAED,MAAMC,aAAa,GAAGD,cAAc,CAACE,MAAM,CAACC,KAAK,IAAI,CAACL,aAAa,CAACK,KAAK,CAAC,CAAC;MAC3E,IAAIF,aAAa,CAACG,MAAM,GAAG,CAAC,EAAE;QAC5B,MAAM,IAAIC,KAAK,CAAC,+BAA+BJ,aAAa,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAC5E;;MAEA;MACA,MAAMC,UAAU,GAAG;QACjB,GAAGT,aAAa;QAChB;QACAlD,OAAO,EAAEkD,aAAa,CAAClD,OAAO,CAACmD,WAAW,CAAC,CAAC;QAC5ChD,OAAO,EAAE+C,aAAa,CAAC/C,OAAO;QAC9BE,SAAS,EAAE6C,aAAa,CAAC7C,SAAS;QAClCC,YAAY,EAAE4C,aAAa,CAAC5C,YAAY,GAAG4C,aAAa,CAAC5C,YAAY,CAACsD,QAAQ,CAAC,CAAC,GAAG,GAAG;QAAE;QACxFrD,OAAO,EAAE2C,aAAa,CAAC3C,OAAO,GAAG2C,aAAa,CAAC3C,OAAO,CAACqD,QAAQ,CAAC,CAAC,GAAG,GAAG;QAAE;QACzE7C,aAAa,EAAEsB,UAAU,CAACa,aAAa,CAACnC,aAAa,CAAC,IAAI,CAAC;QAC3DN,mBAAmB,EAAEyC,aAAa,CAACzC,mBAAmB;QACtDG,iBAAiB,EAAEsC,aAAa,CAACtC,iBAAiB;QAClDO,mBAAmB,EAAE+B,aAAa,CAAC/B,mBAAmB,IAAI,eAAe;QAEzE;QACAH,eAAe,EAAEkC,aAAa,CAAClC,eAAe,GAAGqB,UAAU,CAACa,aAAa,CAAClC,eAAe,CAAC,GAAG,IAAI;QACjGE,SAAS,EAAEgC,aAAa,CAAChC,SAAS,IAAI,IAAI;QAE1C;QACAhB,OAAO,EAAEgD,aAAa,CAAChD,OAAO,IAAI,IAAI;QACtCE,WAAW,EAAE8C,aAAa,CAAC9C,WAAW,IAAI,IAAI;QAC9CM,eAAe,EAAEwC,aAAa,CAACxC,eAAe,IAAI,IAAI;QACtDG,aAAa,EAAEqC,aAAa,CAACrC,aAAa,IAAI,IAAI;QAClDF,2BAA2B,EAAEuC,aAAa,CAACvC,2BAA2B,IAAI,IAAI;QAC9EG,yBAAyB,EAAEoC,aAAa,CAACpC,yBAAyB,IAAI,IAAI;QAC1EN,EAAE,EAAE0C,aAAa,CAAC1C,EAAE,IAAI,GAAG;QAC3BS,iBAAiB,EAAEiC,aAAa,CAACjC,iBAAiB,IAAI,IAAI;QAC1D4C,IAAI,EAAEX,aAAa,CAACW,IAAI,IAAI;MAC9B,CAAC;MAEDhC,OAAO,CAACgB,GAAG,CAAC,gDAAgD,EAAEc,UAAU,CAAC;MAEzE,IAAI;QACF;QACA9B,OAAO,CAACgB,GAAG,CAAC,sCAAsC,CAAC;;QAEnD;QACA,IAAI,CAACzD,UAAU,EAAE;UACf,MAAM,IAAIqE,KAAK,CAAC,mCAAmC,CAAC;QACtD;;QAEA;QACA,MAAMK,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAI,CAACF,KAAK,EAAE;UACV,MAAM,IAAIL,KAAK,CAAC,iEAAiE,CAAC;QACpF;QAEA5B,OAAO,CAACgB,GAAG,CAAC,0BAA0BpE,MAAM,CAACwF,OAAO,SAAS7E,UAAU,EAAE,CAAC;QAC1EyC,OAAO,CAACgB,GAAG,CAAC,eAAe,EAAEqB,IAAI,CAACC,SAAS,CAACR,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAEjE,IAAI;UACF;UACA,MAAMS,QAAQ,GAAG,MAAM5F,KAAK,CAAC6F,IAAI,CAAC,GAAG5F,MAAM,CAACwF,OAAO,SAAS7E,UAAU,EAAE,EAAEuE,UAAU,EAAE;YACpFW,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClC,eAAe,EAAE,UAAUR,KAAK;YAClC,CAAC;YACDS,OAAO,EAAE,KAAK,CAAC;UACjB,CAAC,CAAC;UAEF1C,OAAO,CAACgB,GAAG,CAAC,sBAAsB,EAAEuB,QAAQ,CAACI,IAAI,CAAC;UAClDnF,SAAS,CAAC,4BAA4B,CAAC;;UAEvC;UACA,IAAI,CAACE,QAAQ,EAAE;YACbsC,OAAO,CAACgB,GAAG,CAAC,uCAAuC,CAAC;YACpD;YACA4B,UAAU,CAAC,MAAM;cACf,IAAI;gBACFC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,uBAAuBxF,UAAU,kBAAkB;cAC5E,CAAC,CAAC,OAAOyF,QAAQ,EAAE;gBACjBhD,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEiD,QAAQ,CAAC;gBAC9D;gBACAH,MAAM,CAACC,QAAQ,CAACG,MAAM,CAAC,CAAC;cAC1B;YACF,CAAC,EAAE,IAAI,CAAC;UACV;QACF,CAAC,CAAC,OAAOlD,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;;UAEnE;UACA,IAAI,CAACA,KAAK,CAACwC,QAAQ,IAAIxC,KAAK,CAACmD,IAAI,KAAK,cAAc,IAAInD,KAAK,CAACY,OAAO,CAACwC,QAAQ,CAAC,eAAe,CAAC,EAAE;YAC/F;YACA1F,OAAO,CAAC,+CAA+C,CAAC;;YAExD;YACAmF,UAAU,CAAC,YAAY;cACrB,IAAI;gBACF;gBACA,MAAMQ,aAAa,GAAG,MAAMzG,KAAK,CAAC0G,GAAG,CAAC,GAAGzG,MAAM,CAACwF,OAAO,SAAS7E,UAAU,UAAUuE,UAAU,CAAC3D,OAAO,EAAE,EAAE;kBACxGsE,OAAO,EAAE;oBACP,eAAe,EAAE,UAAUR,KAAK;kBAClC,CAAC;kBACDS,OAAO,EAAE;gBACX,CAAC,CAAC;gBAEF,IAAIU,aAAa,CAACT,IAAI,IAAIS,aAAa,CAACT,IAAI,CAACW,MAAM,EAAE;kBACnD;kBACA9F,SAAS,CAAC,4BAA4B,CAAC;;kBAEvC;kBACA,IAAI,CAACE,QAAQ,EAAE;oBACbsC,OAAO,CAACgB,GAAG,CAAC,uCAAuC,CAAC;oBACpD4B,UAAU,CAAC,MAAM;sBACf,IAAI;wBACFC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,uBAAuBxF,UAAU,kBAAkB;sBAC5E,CAAC,CAAC,OAAOyF,QAAQ,EAAE;wBACjBhD,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEiD,QAAQ,CAAC;wBAC9DH,MAAM,CAACC,QAAQ,CAACG,MAAM,CAAC,CAAC;sBAC1B;oBACF,CAAC,EAAE,IAAI,CAAC;kBACV;gBACF,CAAC,MAAM;kBACL;kBACAxF,OAAO,CAAC,8EAA8E,CAAC;kBACvFK,UAAU,CAAC,KAAK,CAAC;gBACnB;cACF,CAAC,CAAC,OAAOyF,UAAU,EAAE;gBACnBvD,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEwD,UAAU,CAAC;gBACxD9F,OAAO,CAAC,8DAA8D,CAAC;gBACvEK,UAAU,CAAC,KAAK,CAAC;cACnB;YACF,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,MAAM;YACL;YACA,IAAI0F,YAAY,GAAG,qCAAqC;YAExD,IAAIzD,KAAK,CAACwC,QAAQ,IAAIxC,KAAK,CAACwC,QAAQ,CAACI,IAAI,EAAE;cACzC,MAAMc,YAAY,GAAG1D,KAAK,CAACwC,QAAQ,CAACI,IAAI;cACxC,IAAIc,YAAY,CAACC,MAAM,EAAE;gBACvBF,YAAY,GAAGC,YAAY,CAACC,MAAM;cACpC,CAAC,MAAM,IAAI,OAAOD,YAAY,KAAK,QAAQ,EAAE;gBAC3CD,YAAY,GAAGC,YAAY;cAC7B,CAAC,MAAM;gBACLD,YAAY,GAAGnB,IAAI,CAACC,SAAS,CAACmB,YAAY,CAAC;cAC7C;YACF,CAAC,MAAM,IAAI1D,KAAK,CAACY,OAAO,EAAE;cACxB6C,YAAY,GAAGzD,KAAK,CAACY,OAAO;YAC9B;YAEAlD,OAAO,CAAC+F,YAAY,CAAC;YACrB1F,UAAU,CAAC,KAAK,CAAC;UACnB;QACF;MACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;QACvE,IAAIyD,YAAY,GAAGzD,KAAK,CAACY,OAAO,IAAI,qCAAqC;QACzElD,OAAO,CAAC+F,YAAY,CAAC;QACrB1F,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DtC,OAAO,CAACsC,KAAK,CAAC2D,MAAM,IAAI,qCAAqC,CAAC;IAChE,CAAC,SAAS;MACR5F,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6F,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACpE,YAAY,CAAC,CAACkC,MAAM,GAAG,CAAC;EAExD,oBACExE,OAAA,CAAC1B,GAAG;IAACqI,SAAS,EAAC,MAAM;IAACC,QAAQ,EAAEjD,YAAa;IAACkD,UAAU;IAAAC,QAAA,EACrDlG,gBAAgB,gBACfZ,OAAA,CAAC1B,GAAG;MAACyI,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAC5D9G,OAAA,CAAChB,gBAAgB;QAAAmI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,gBAENtH,OAAA,CAAAE,SAAA;MAAA4G,QAAA,GACGN,WAAW,iBACVxG,OAAA,CAAAE,SAAA;QAAA4G,QAAA,gBACE9G,OAAA,CAACjB,KAAK;UACJwI,QAAQ,EAAC,SAAS;UAClBC,IAAI,eAAExH,OAAA,CAACV,WAAW;YAAA6H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBP,EAAE,EAAE;YAAEU,EAAE,EAAElH,QAAQ,GAAG,CAAC,GAAG,CAAC;YAAEmH,EAAE,EAAEnH,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAuG,QAAA,eAErD9G,OAAA,CAACf,UAAU;YAAC0I,OAAO,EAAC,WAAW;YAACZ,EAAE,EAAE;cAAEa,QAAQ,EAAErH,QAAQ,GAAG,QAAQ,GAAG;YAAW,CAAE;YAAAuG,QAAA,EAAC;UAEpF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAGPhF,YAAY,CAACuF,aAAa,iBACzB7H,OAAA,CAACjB,KAAK;UACJwI,QAAQ,EAAC,OAAO;UAChBR,EAAE,EAAE;YAAEU,EAAE,EAAElH,QAAQ,GAAG,CAAC,GAAG,CAAC;YAAEmH,EAAE,EAAEnH,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAuG,QAAA,eAErD9G,OAAA,CAACf,UAAU;YAAC0I,OAAO,EAAC,WAAW;YAACZ,EAAE,EAAE;cAAEe,UAAU,EAAE;YAAO,CAAE;YAAAhB,QAAA,EACxDxE,YAAY,CAACuF;UAAa;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR;MAAA,eACD,CACH,eAEDtH,OAAA,CAACd,KAAK;QAAC6H,EAAE,EAAE;UAAEgB,CAAC,EAAExH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEkH,EAAE,EAAElH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEyH,SAAS,EAAEzH,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAuG,QAAA,gBACpF9G,OAAA,CAACf,UAAU;UAAC0I,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAErH,QAAQ,GAAG,QAAQ,GAAG;UAAU,CAAE;UAAAuG,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtH,OAAA,CAACvB,IAAI;UAACyJ,SAAS;UAACC,OAAO,EAAE5H,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAuG,QAAA,gBACxC9G,OAAA,CAACvB,IAAI;YAAC2J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB9G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,SAAS;cACduF,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB1E,KAAK,EAAEnC,QAAQ,CAACE,OAAQ;cACxByH,QAAQ,EAAE3F,gBAAiB;cAC3B4F,QAAQ;cACR9F,KAAK,EAAE,CAAC,CAACR,UAAU,CAACpB,OAAQ;cAC5B2H,UAAU,EAAEvG,UAAU,CAACpB,OAAQ;cAC/B4H,UAAU,EAAE;gBAAEC,KAAK,EAAE;kBAAEC,aAAa,EAAE;gBAAY;cAAE;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPtH,OAAA,CAACvB,IAAI;YAAC2J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB9G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,qBAAqB;cAC1BuF,KAAK,EAAC,qBAAqB;cAC3BC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB1E,KAAK,EAAEnC,QAAQ,CAACG,mBAAoB;cACpCwH,QAAQ,EAAE3F,gBAAiB;cAC3B4F,QAAQ;cACR9F,KAAK,EAAE,CAAC,CAACR,UAAU,CAACnB,mBAAoB;cACxC0H,UAAU,EAAEvG,UAAU,CAACnB;YAAoB;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPtH,OAAA,CAACvB,IAAI;YAAC2J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB9G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,SAAS;cACduF,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB1E,KAAK,EAAEnC,QAAQ,CAACI,OAAQ;cACxBuH,QAAQ,EAAE3F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAAClB,OAAQ;cAC5ByH,UAAU,EAAEvG,UAAU,CAAClB;YAAQ;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPtH,OAAA,CAACvB,IAAI;YAAC2J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB9G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,SAAS;cACduF,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB1E,KAAK,EAAEnC,QAAQ,CAACK,OAAQ;cACxBsH,QAAQ,EAAE3F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACjB,OAAQ;cAC5BwH,UAAU,EAAEvG,UAAU,CAACjB;YAAQ;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERtH,OAAA,CAACd,KAAK;QAAC6H,EAAE,EAAE;UAAEgB,CAAC,EAAE,CAAC;UAAEN,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,gBACzB9G,OAAA,CAACf,UAAU;UAAC0I,OAAO,EAAC,IAAI;UAACM,YAAY;UAAAnB,QAAA,EAAC;QAEtC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtH,OAAA,CAACvB,IAAI;UAACyJ,SAAS;UAACC,OAAO,EAAE,CAAE;UAAArB,QAAA,gBACzB9G,OAAA,CAACvB,IAAI;YAAC2J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB9G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,aAAa;cAClBuF,KAAK,EAAC,aAAa;cACnBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB1E,KAAK,EAAEnC,QAAQ,CAACM,WAAY;cAC5BqH,QAAQ,EAAE3F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAAChB,WAAY;cAChCuH,UAAU,EAAEvG,UAAU,CAAChB;YAAY;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPtH,OAAA,CAACvB,IAAI;YAAC2J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB9G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,WAAW;cAChBuF,KAAK,EAAC,WAAW;cACjBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB1E,KAAK,EAAEnC,QAAQ,CAACO,SAAU;cAC1BoH,QAAQ,EAAE3F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACf,SAAU;cAC9BsH,UAAU,EAAEvG,UAAU,CAACf;YAAU;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPtH,OAAA,CAACvB,IAAI;YAAC2J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB9G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,cAAc;cACnBuF,KAAK,EAAC,mBAAmB;cACzBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB1E,KAAK,EAAEnC,QAAQ,CAACQ,YAAa;cAC7BmH,QAAQ,EAAE3F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACd,YAAa;cACjCqH,UAAU,EAAEvG,UAAU,CAACd,YAAY,IAAIgB,YAAY,CAAChB,YAAa;cACjEyH,mBAAmB,EAAE;gBACnBF,KAAK,EAAE;kBAAEG,KAAK,EAAE1G,YAAY,CAAChB,YAAY,GAAG,QAAQ,GAAG2H;gBAAU;cACnE;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPtH,OAAA,CAACvB,IAAI;YAAC2J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB9G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,SAAS;cACduF,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB1E,KAAK,EAAEnC,QAAQ,CAACS,OAAQ;cACxBkH,QAAQ,EAAE3F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACb,OAAQ;cAC5BoH,UAAU,EAAEvG,UAAU,CAACb,OAAO,IAAIe,YAAY,CAACf,OAAQ;cACvDwH,mBAAmB,EAAE;gBACnBF,KAAK,EAAE;kBAAEG,KAAK,EAAE1G,YAAY,CAACf,OAAO,GAAG,QAAQ,GAAG0H;gBAAU;cAC9D;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPtH,OAAA,CAACvB,IAAI;YAAC2J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB9G,OAAA,CAACtB,WAAW;cAAC8J,SAAS;cAAC5F,KAAK,EAAE,CAAC,CAACR,UAAU,CAACZ,EAAG;cAAAsF,QAAA,gBAC5C9G,OAAA,CAACrB,UAAU;gBAACuK,EAAE,EAAC,UAAU;gBAAApC,QAAA,EAAC;cAAe;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtDtH,OAAA,CAACpB,MAAM;gBACLuK,OAAO,EAAC,UAAU;gBAClBnG,IAAI,EAAC,IAAI;gBACTC,KAAK,EAAEnC,QAAQ,CAACU,EAAG;gBACnB+G,KAAK,EAAC,iBAAiB;gBACvBE,QAAQ,EAAE3F,gBAAiB;gBAAAgE,QAAA,gBAE3B9G,OAAA,CAACnB,QAAQ;kBAACoE,KAAK,EAAC,GAAG;kBAAA6D,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAChCtH,OAAA,CAACnB,QAAQ;kBAACoE,KAAK,EAAC,GAAG;kBAAA6D,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,EACRlF,UAAU,CAACZ,EAAE,iBAAIxB,OAAA,CAAClB,cAAc;gBAAAgI,QAAA,EAAE1E,UAAU,CAACZ;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERtH,OAAA,CAACd,KAAK;QAAC6H,EAAE,EAAE;UAAEgB,CAAC,EAAExH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEkH,EAAE,EAAElH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEyH,SAAS,EAAEzH,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAuG,QAAA,gBACpF9G,OAAA,CAACf,UAAU;UAAC0I,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAErH,QAAQ,GAAG,QAAQ,GAAG;UAAU,CAAE;UAAAuG,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtH,OAAA,CAACvB,IAAI;UAACyJ,SAAS;UAACC,OAAO,EAAE5H,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAuG,QAAA,gBACxC9G,OAAA,CAACvB,IAAI;YAAC2J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB9G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,qBAAqB;cAC1BuF,KAAK,EAAC,qBAAqB;cAC3BC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB1E,KAAK,EAAEnC,QAAQ,CAACW,mBAAoB;cACpCgH,QAAQ,EAAE3F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACX,mBAAoB;cACxCkH,UAAU,EAAEvG,UAAU,CAACX;YAAoB;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPtH,OAAA,CAACvB,IAAI;YAAC2J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB9G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,iBAAiB;cACtBuF,KAAK,EAAC,iBAAiB;cACvBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB1E,KAAK,EAAEnC,QAAQ,CAACY,eAAgB;cAChC+G,QAAQ,EAAE3F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACV,eAAgB;cACpCiH,UAAU,EAAEvG,UAAU,CAACV;YAAgB;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPtH,OAAA,CAACvB,IAAI;YAAC2J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB9G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,6BAA6B;cAClCuF,KAAK,EAAC,6BAA6B;cACnCC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB1E,KAAK,EAAEnC,QAAQ,CAACa,2BAA4B;cAC5C8G,QAAQ,EAAE3F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACT,2BAA4B;cAChDgH,UAAU,EAAEvG,UAAU,CAACT;YAA4B;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERtH,OAAA,CAACd,KAAK;QAAC6H,EAAE,EAAE;UAAEgB,CAAC,EAAExH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEkH,EAAE,EAAElH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEyH,SAAS,EAAEzH,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAuG,QAAA,gBACpF9G,OAAA,CAACf,UAAU;UAAC0I,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAErH,QAAQ,GAAG,QAAQ,GAAG;UAAU,CAAE;UAAAuG,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtH,OAAA,CAACvB,IAAI;UAACyJ,SAAS;UAACC,OAAO,EAAE5H,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAuG,QAAA,gBACxC9G,OAAA,CAACvB,IAAI;YAAC2J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB9G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,mBAAmB;cACxBuF,KAAK,EAAC,mBAAmB;cACzBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB1E,KAAK,EAAEnC,QAAQ,CAACc,iBAAkB;cAClC6G,QAAQ,EAAE3F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACR,iBAAkB;cACtC+G,UAAU,EAAEvG,UAAU,CAACR;YAAkB;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPtH,OAAA,CAACvB,IAAI;YAAC2J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB9G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,eAAe;cACpBuF,KAAK,EAAC,eAAe;cACrBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB1E,KAAK,EAAEnC,QAAQ,CAACe,aAAc;cAC9B4G,QAAQ,EAAE3F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACP,aAAc;cAClC8G,UAAU,EAAEvG,UAAU,CAACP;YAAc;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPtH,OAAA,CAACvB,IAAI;YAAC2J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB9G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,2BAA2B;cAChCuF,KAAK,EAAC,2BAA2B;cACjCC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB1E,KAAK,EAAEnC,QAAQ,CAACgB,yBAA0B;cAC1C2G,QAAQ,EAAE3F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACN,yBAA0B;cAC9C6G,UAAU,EAAEvG,UAAU,CAACN;YAA0B;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERtH,OAAA,CAACd,KAAK;QAAC6H,EAAE,EAAE;UAAEgB,CAAC,EAAExH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEkH,EAAE,EAAElH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEyH,SAAS,EAAEzH,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAuG,QAAA,gBACpF9G,OAAA,CAACf,UAAU;UAAC0I,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAErH,QAAQ,GAAG,QAAQ,GAAG;UAAU,CAAE;UAAAuG,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtH,OAAA,CAACvB,IAAI;UAACyJ,SAAS;UAACC,OAAO,EAAE5H,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAuG,QAAA,eACxC9G,OAAA,CAACvB,IAAI;YAAC2J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB9G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,eAAe;cACpBuF,KAAK,EAAC,eAAe;cACrBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB1E,KAAK,EAAEnC,QAAQ,CAACiB,aAAc;cAC9B0G,QAAQ,EAAE3F,gBAAiB;cAC3B4F,QAAQ;cACR9F,KAAK,EAAE,CAAC,CAACR,UAAU,CAACL,aAAc;cAClC4G,UAAU,EAAEvG,UAAU,CAACL;YAAc;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAEP,CAAC/G,QAAQ,iBACRP,OAAA,CAAC1B,GAAG;QAACyI,EAAE,EAAE;UAAEqC,EAAE,EAAE,CAAC;UAAEpC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEoC,GAAG,EAAE;QAAE,CAAE;QAAAvC,QAAA,gBACpE9G,OAAA,CAACxB,MAAM;UACLmJ,OAAO,EAAC,UAAU;UAClBqB,KAAK,EAAC,WAAW;UACjBM,IAAI,EAAC,OAAO;UACZC,OAAO,EAAE7F,YAAa;UACtB8F,QAAQ,EAAE9I,OAAQ;UAClBqG,EAAE,EAAE;YAAE0C,QAAQ,EAAE;UAAI,CAAE;UAAA3C,QAAA,EACvB;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtH,OAAA,CAACxB,MAAM;UACLkL,IAAI,EAAC,QAAQ;UACb/B,OAAO,EAAC,WAAW;UACnBqB,KAAK,EAAC,SAAS;UACfM,IAAI,EAAC,OAAO;UACZK,SAAS,eAAE3J,OAAA,CAACZ,QAAQ;YAAA+H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBkC,QAAQ,EAAE9I,OAAQ;UAClBqG,EAAE,EAAE;YAAE0C,QAAQ,EAAE;UAAI,CAAE;UAAA3C,QAAA,EAErBpG,OAAO,gBAAGV,OAAA,CAAChB,gBAAgB;YAACsK,IAAI,EAAE;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAY;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EACA/G,QAAQ,iBACPP,OAAA,CAAC1B,GAAG;QAACyI,EAAE,EAAE;UAAEqC,EAAE,EAAE,CAAC;UAAEpC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,UAAU;UAAEoC,GAAG,EAAE;QAAE,CAAE;QAAAvC,QAAA,eACtE9G,OAAA,CAACxB,MAAM;UACLkL,IAAI,EAAC,QAAQ;UACb/B,OAAO,EAAC,WAAW;UACnBqB,KAAK,EAAC,SAAS;UACfM,IAAI,EAAC,QAAQ;UACbK,SAAS,eAAE3J,OAAA,CAACZ,QAAQ;YAAA+H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBkC,QAAQ,EAAE9I,OAAQ;UAClBqG,EAAE,EAAE;YAAE0C,QAAQ,EAAE;UAAI,CAAE;UAAA3C,QAAA,EAErBpG,OAAO,gBAAGV,OAAA,CAAChB,gBAAgB;YAACsK,IAAI,EAAE;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA,eACD;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9G,EAAA,CAzmBIL,gBAAgB;EAAA,QACHZ,WAAW;AAAA;AAAAqK,EAAA,GADxBzJ,gBAAgB;AA2mBtB,eAAeA,gBAAgB;AAAC,IAAAyJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}