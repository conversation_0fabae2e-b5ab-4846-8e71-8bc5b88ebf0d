{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfQuarter} function options.\n */\n\n/**\n * @name endOfQuarter\n * @category Quarter Helpers\n * @summary Return the end of a year quarter for the given date.\n *\n * @description\n * Return the end of a year quarter for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a quarter\n *\n * @example\n * // The end of a quarter for 2 September 2014 11:55:00:\n * const result = endOfQuarter(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 23:59:59.999\n */\nexport function endOfQuarter(date, options) {\n  const _date = toDate(date, options?.in);\n  const currentMonth = _date.getMonth();\n  const month = currentMonth - currentMonth % 3 + 3;\n  _date.setMonth(month, 0);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfQuarter;", "map": {"version": 3, "names": ["toDate", "endOfQuarter", "date", "options", "_date", "in", "currentMonth", "getMonth", "month", "setMonth", "setHours"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/endOfQuarter.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfQuarter} function options.\n */\n\n/**\n * @name endOfQuarter\n * @category Quarter Helpers\n * @summary Return the end of a year quarter for the given date.\n *\n * @description\n * Return the end of a year quarter for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a quarter\n *\n * @example\n * // The end of a quarter for 2 September 2014 11:55:00:\n * const result = endOfQuarter(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 23:59:59.999\n */\nexport function endOfQuarter(date, options) {\n  const _date = toDate(date, options?.in);\n  const currentMonth = _date.getMonth();\n  const month = currentMonth - (currentMonth % 3) + 3;\n  _date.setMonth(month, 0);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfQuarter;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC1C,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvC,MAAMC,YAAY,GAAGF,KAAK,CAACG,QAAQ,CAAC,CAAC;EACrC,MAAMC,KAAK,GAAGF,YAAY,GAAIA,YAAY,GAAG,CAAE,GAAG,CAAC;EACnDF,KAAK,CAACK,QAAQ,CAACD,KAAK,EAAE,CAAC,CAAC;EACxBJ,KAAK,CAACM,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EAC/B,OAAON,KAAK;AACd;;AAEA;AACA,eAAeH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}