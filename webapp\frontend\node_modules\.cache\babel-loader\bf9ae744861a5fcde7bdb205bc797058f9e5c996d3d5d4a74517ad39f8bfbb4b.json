{"ast": null, "code": "import { isLeapYear } from \"./isLeapYear.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name getDaysInYear\n * @category Year Helpers\n * @summary Get the number of days in a year of the given date.\n *\n * @description\n * Get the number of days in a year of the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n *\n * @returns The number of days in a year\n *\n * @example\n * // How many days are in 2012?\n * const result = getDaysInYear(new Date(2012, 0, 1))\n * //=> 366\n */\nexport function getDaysInYear(date) {\n  const _date = toDate(date);\n  if (String(new Date(_date)) === \"Invalid Date\") {\n    return NaN;\n  }\n  return isLeapYear(_date) ? 366 : 365;\n}\n\n// Fallback for modularized imports:\nexport default getDaysInYear;", "map": {"version": 3, "names": ["isLeapYear", "toDate", "getDaysInYear", "date", "_date", "String", "Date", "NaN"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/getDaysInYear.mjs"], "sourcesContent": ["import { isLeapYear } from \"./isLeapYear.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name getDaysInYear\n * @category Year Helpers\n * @summary Get the number of days in a year of the given date.\n *\n * @description\n * Get the number of days in a year of the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n *\n * @returns The number of days in a year\n *\n * @example\n * // How many days are in 2012?\n * const result = getDaysInYear(new Date(2012, 0, 1))\n * //=> 366\n */\nexport function getDaysInYear(date) {\n  const _date = toDate(date);\n\n  if (String(new Date(_date)) === \"Invalid Date\") {\n    return NaN;\n  }\n\n  return isLeapYear(_date) ? 366 : 365;\n}\n\n// Fallback for modularized imports:\nexport default getDaysInYear;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,MAAMC,KAAK,GAAGH,MAAM,CAACE,IAAI,CAAC;EAE1B,IAAIE,MAAM,CAAC,IAAIC,IAAI,CAACF,KAAK,CAAC,CAAC,KAAK,cAAc,EAAE;IAC9C,OAAOG,GAAG;EACZ;EAEA,OAAOP,UAAU,CAACI,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG;AACtC;;AAEA;AACA,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}