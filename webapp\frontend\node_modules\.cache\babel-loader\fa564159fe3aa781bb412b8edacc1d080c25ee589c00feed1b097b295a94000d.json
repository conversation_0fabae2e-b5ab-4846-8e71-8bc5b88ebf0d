{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\ModificaBobinaForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Paper, Typography, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, Divider, Alert, CircularProgress, Dialog, DialogTitle, DialogContent, DialogActions, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Radio, RadioGroup, FormControlLabel, IconButton, InputAdornment, List, ListItem, ListItemText, ListItemButton, ListItemSecondaryAction, Chip } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, Cancel as CancelIcon, Warning as WarningIcon, Info as InfoIcon, AddCircleOutline as AddCircleOutlineIcon } from '@mui/icons-material';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoDetailsView from './CavoDetailsView';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Componente per la modifica della bobina di un cavo già posato\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ModificaBobinaForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [selectedOption, setSelectedOption] = useState('');\n  const [selectedBobinaId, setSelectedBobinaId] = useState('');\n  const [bobinaSearchText, setBobinaSearchText] = useState('');\n  const [bobinaNumericInput, setBobinaNumericInput] = useState('');\n\n  // Stati per gestire il dialog di incompatibilità\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReelData, setIncompatibleReelData] = useState({\n    cavo: null,\n    bobina: null\n  });\n\n  // Stati per i dialoghi\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');\n  const [confirmDialogAction, setConfirmDialogAction] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica i cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica le bobine quando viene selezionato un cavo\n  useEffect(() => {\n    if (selectedCavo) {\n      loadBobine();\n    }\n  }, [selectedCavo]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica solo i cavi installati (con metratura_reale > 0)\n      const caviData = await caviService.getCavi(cantiereId, null, 'Installato');\n      console.log(`Caricati ${caviData.length} cavi installati`);\n\n      // Filtra i cavi che hanno metratura_reale > 0\n      const caviInstallati = caviData.filter(cavo => parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato');\n      setCavi(caviInstallati);\n      console.log(`Filtrati ${caviInstallati.length} cavi effettivamente installati`);\n    } catch (error) {\n      console.error('Errore durante il caricamento dei cavi:', error);\n      onError('Errore durante il caricamento dei cavi: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    if (!selectedCavo) return;\n    try {\n      setBobineLoading(true);\n      console.log(`Caricamento bobine per il cantiere ${cantiereId}...`);\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Caricati ${bobineData.length} bobine`);\n\n      // Filtra le bobine compatibili\n      const compatibleBobineData = bobineData.filter(bobina => bobina.tipologia === selectedCavo.tipologia && bobina.sezione === selectedCavo.sezione && (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso'));\n      setBobine(bobineData);\n      setCompatibleBobine(compatibleBobineData);\n      console.log(`Filtrate ${compatibleBobineData.length} bobine compatibili`);\n    } catch (error) {\n      console.error('Errore durante il caricamento delle bobine:', error);\n      onError('Errore durante il caricamento delle bobine: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setLoading(true);\n      const cavo = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica che il cavo sia installato\n      if (parseFloat(cavo.metratura_reale) <= 0 && cavo.stato_installazione !== 'Installato') {\n        onError('Il cavo selezionato non risulta installato');\n        setLoading(false);\n        return;\n      }\n      setSelectedCavo(cavo);\n      setSelectedOption(''); // Reset dell'opzione selezionata\n      setSelectedBobinaId(''); // Reset della bobina selezionata\n      setShowSearchResults(false);\n    } catch (error) {\n      console.error('Errore durante la ricerca del cavo:', error);\n      onError('Errore durante la ricerca del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo dalla lista\n  const handleSelectCavo = cavo => {\n    setSelectedCavo(cavo);\n    setSelectedOption(''); // Reset dell'opzione selezionata\n    setSelectedBobinaId(''); // Reset della bobina selezionata\n    setShowSearchResults(false);\n  };\n\n  // Gestisce il cambio dell'opzione selezionata\n  const handleOptionChange = event => {\n    setSelectedOption(event.target.value);\n    setSelectedBobinaId(''); // Reset della bobina selezionata quando cambia l'opzione\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = bobinaId => {\n    console.log('Bobina selezionata:', bobinaId);\n\n    // Trova la bobina selezionata\n    const bobina = bobine.find(b => b.id_bobina === bobinaId);\n    if (bobina && selectedCavo) {\n      // Verifica compatibilità\n      const isCompatible = String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim();\n      if (!isCompatible) {\n        console.log('Bobina incompatibile selezionata:', bobina);\n        console.log('Cavo corrente:', selectedCavo);\n\n        // Mostra il dialogo di incompatibilità\n        setIncompatibleReelData({\n          cavo: selectedCavo,\n          bobina: bobina\n        });\n        setShowIncompatibleReelDialog(true);\n\n        // Imposta comunque la bobina selezionata per mostrare i dettagli\n        setSelectedBobinaId(bobinaId);\n        setBobinaNumericInput(getBobinaNumber(bobinaId));\n        return;\n      }\n    }\n\n    // Se compatibile o nessun cavo selezionato, procedi normalmente\n    setSelectedBobinaId(bobinaId);\n    setBobinaNumericInput(getBobinaNumber(bobinaId));\n  };\n\n  // Gestisce la selezione di una bobina tramite input numerico\n  const handleSelectBobinaByNumber = () => {\n    if (!bobinaNumericInput.trim()) {\n      onError('Inserisci un numero di bobina valido');\n      return;\n    }\n\n    // Costruisci l'ID completo della bobina\n    const fullBobinaId = `C${cantiereId}_B${bobinaNumericInput.trim()}`;\n\n    // Verifica che la bobina esista\n    const bobina = bobine.find(b => b.id_bobina === fullBobinaId);\n    if (!bobina) {\n      onError(`Bobina ${bobinaNumericInput} non trovata nel cantiere`);\n      return;\n    }\n\n    // Usa la stessa logica di handleSelectBobina\n    handleSelectBobina(fullBobinaId);\n  };\n\n  // Gestisce il salvataggio delle modifiche\n  const handleSave = () => {\n    if (!selectedCavo) {\n      onError('Seleziona un cavo prima di procedere');\n      return;\n    }\n    if (!selectedOption) {\n      onError('Seleziona un\\'opzione prima di procedere');\n      return;\n    }\n\n    // Verifica che sia stata selezionata una bobina se l'opzione è \"assegnaNuova\"\n    if (selectedOption === 'assegnaNuova' && !selectedBobinaId) {\n      onError('Seleziona una bobina prima di procedere');\n      return;\n    }\n\n    // Prepara il messaggio di conferma in base all'opzione selezionata\n    let message = '';\n    let action = null;\n    if (selectedOption === 'assegnaNuova') {\n      const bobina = bobine.find(b => b.id_bobina === selectedBobinaId);\n      message = `Sei sicuro di voler assegnare la bobina ${getBobinaNumber(selectedBobinaId)} al cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina(selectedBobinaId);\n    } else if (selectedOption === 'rimuoviBobina') {\n      message = `Sei sicuro di voler rimuovere la bobina attuale dal cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina('BOBINA_VUOTA');\n    } else if (selectedOption === 'annullaInstallazione') {\n      message = `ATTENZIONE: Questa operazione annullerà l'installazione del cavo ${selectedCavo.id_cavo}. Tutti i metri posati saranno restituiti alla bobina originale. Sei sicuro di voler procedere?`;\n      action = () => annullaInstallazione();\n    }\n    setConfirmDialogMessage(message);\n    setConfirmDialogAction(() => action);\n    setShowConfirmDialog(true);\n  };\n\n  // Funzione per aggiornare la bobina di un cavo\n  const updateBobina = async bobinaId => {\n    try {\n      setLoading(true);\n      await caviService.updateBobina(cantiereId, selectedCavo.id_cavo, bobinaId);\n      onSuccess(`Bobina ${bobinaId === 'BOBINA_VUOTA' ? 'vuota assegnata' : 'assegnata'} con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento della bobina:', error);\n      onError('Errore durante l\\'aggiornamento della bobina: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per annullare l'installazione di un cavo\n  const annullaInstallazione = async () => {\n    try {\n      setLoading(true);\n\n      // Chiamata all'API per annullare l'installazione\n      await caviService.cancelInstallation(cantiereId, selectedCavo.id_cavo);\n      onSuccess(`Installazione del cavo ${selectedCavo.id_cavo} annullata con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'annullamento dell\\'installazione:', error);\n      onError('Errore durante l\\'annullamento dell\\'installazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per chiudere il form e resettare tutto\n  const handleCloseForm = () => {\n    // Reset di tutti gli stati\n    setSelectedCavo(null);\n    setSelectedOption('');\n    setSelectedBobinaId('');\n    setCavoIdInput('');\n    setShowSearchResults(false);\n\n    // Messaggio di conferma\n    onSuccess('Operazione annullata');\n  };\n\n  // Gestisce la chiusura del dialog di incompatibilità\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReelData({\n      cavo: null,\n      bobina: null\n    });\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per compatibilità\n  const handleUpdateCavoForCompatibility = async () => {\n    const {\n      cavo,\n      bobina\n    } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per l\\'aggiornamento del cavo:', {\n        cavo,\n        bobina\n      });\n      onError('Dati mancanti per l\\'aggiornamento del cavo');\n      return;\n    }\n    try {\n      setLoading(true);\n      console.log(`Aggiornamento caratteristiche del cavo ${cavo.id_cavo} per compatibilità con bobina ${bobina.id_bobina}`);\n\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Mantieni la bobina selezionata\n      setSelectedBobinaId(bobina.id_bobina);\n      setBobinaNumericInput(getBobinaNumber(bobina.id_bobina));\n      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate per compatibilità con bobina ${getBobinaNumber(bobina.id_bobina)}`);\n      handleCloseIncompatibleReelDialog();\n\n      // Ricarica le bobine per aggiornare la compatibilità\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento del cavo:', error);\n      onError('Errore durante l\\'aggiornamento del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'utilizzo di una bobina incompatibile senza aggiornare le caratteristiche del cavo\n  const handleContinueWithIncompatible = async () => {\n    const {\n      cavo,\n      bobina\n    } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per utilizzare la bobina incompatibile:', {\n        cavo,\n        bobina\n      });\n      onError('Dati mancanti per utilizzare la bobina incompatibile');\n      return;\n    }\n    try {\n      setLoading(true);\n      console.log(`Utilizzo bobina incompatibile ${bobina.id_bobina} con cavo ${cavo.id_cavo} senza aggiornare le caratteristiche`);\n\n      // Mantieni la bobina selezionata\n      setSelectedBobinaId(bobina.id_bobina);\n      setBobinaNumericInput(getBobinaNumber(bobina.id_bobina));\n      onSuccess(`Bobina incompatibile ${getBobinaNumber(bobina.id_bobina)} selezionata per il cavo ${cavo.id_cavo}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante la selezione della bobina incompatibile:', error);\n      onError('Errore durante la selezione della bobina incompatibile: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = idBobina => {\n    if (idBobina === 'BOBINA_VUOTA') return 'BOBINA VUOTA';\n\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Renderizza il form per la selezione del cavo\n  const renderCavoSelectionForm = () => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"subtitle2\",\n      sx: {\n        mb: 1,\n        fontWeight: 'bold'\n      },\n      children: \"Seleziona un cavo posato\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 1.5,\n        mb: 2,\n        width: '100%'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          sx: {\n            mr: 1,\n            minWidth: '80px'\n          },\n          children: \"Cerca cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          size: \"small\",\n          label: \"ID Cavo\",\n          variant: \"outlined\",\n          value: cavoIdInput,\n          onChange: e => setCavoIdInput(e.target.value),\n          placeholder: \"Inserisci l'ID del cavo\",\n          sx: {\n            flexGrow: 0,\n            width: '200px',\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleSearchCavoById,\n          disabled: caviLoading || !cavoIdInput.trim(),\n          startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 38\n          }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 71\n          }, this),\n          size: \"small\",\n          sx: {\n            minWidth: '80px',\n            height: '36px',\n            mr: 2\n          },\n          children: \"CERCA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            flexGrow: 1,\n            flexWrap: 'nowrap',\n            overflow: 'hidden',\n            ml: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mr: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 'bold',\n                whiteSpace: 'nowrap',\n                mr: 1,\n                fontSize: '0.95rem'\n              },\n              children: [\"Cavo: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#1976d2'\n                },\n                children: selectedCavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              orientation: \"vertical\",\n              flexItem: true,\n              sx: {\n                mx: 1.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 3,\n                flexWrap: 'nowrap',\n                overflow: 'hidden'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.9rem',\n                    mr: 0.5\n                  },\n                  children: \"Tipo:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.9rem'\n                  },\n                  children: selectedCavo.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.9rem',\n                    mr: 0.5\n                  },\n                  children: \"Form:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.9rem'\n                  },\n                  children: selectedCavo.sezione || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.9rem',\n                    mr: 0.5\n                  },\n                  children: \"Metri:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.9rem'\n                  },\n                  children: [selectedCavo.metratura_reale || 'N/A', \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.9rem',\n                    mr: 0.5\n                  },\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: selectedCavo.stato_installazione || 'N/D',\n                  color: \"success\",\n                  sx: {\n                    height: '22px',\n                    '& .MuiChip-label': {\n                      px: 1,\n                      py: 0,\n                      fontSize: '0.85rem'\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 15\n          }, this), selectedCavo.id_bobina && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              orientation: \"vertical\",\n              flexItem: true,\n              sx: {\n                mx: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                sx: {\n                  fontWeight: 'bold',\n                  whiteSpace: 'nowrap',\n                  mr: 1,\n                  fontSize: '0.95rem',\n                  color: '#2e7d32'\n                },\n                children: [\"Bobina attuale: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: selectedCavo.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(selectedCavo.id_bobina)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 39\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 21\n              }, this), (() => {\n                if (selectedCavo.id_bobina === 'BOBINA_VUOTA') {\n                  return /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontSize: '0.9rem',\n                        color: 'text.secondary',\n                        fontStyle: 'italic'\n                      },\n                      children: \"(Cavo posato senza bobina specifica)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 532,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 531,\n                    columnNumber: 27\n                  }, this);\n                }\n                const bobina = bobine.find(b => b.id_bobina === selectedCavo.id_bobina);\n                return bobina ? /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 3,\n                    flexWrap: 'nowrap',\n                    overflow: 'hidden'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium',\n                        fontSize: '0.9rem',\n                        mr: 0.5\n                      },\n                      children: \"Residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 543,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontSize: '0.9rem',\n                        color: 'success.main',\n                        fontWeight: 'bold'\n                      },\n                      children: [bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 544,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 542,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium',\n                        fontSize: '0.9rem',\n                        mr: 0.5\n                      },\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 549,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: bobina.stato_bobina || 'N/D',\n                      color: bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning',\n                      sx: {\n                        height: '22px',\n                        '& .MuiChip-label': {\n                          px: 1,\n                          py: 0,\n                          fontSize: '0.85rem'\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 550,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 548,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 25\n                }, this) : null;\n              })()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true), selectedBobinaId && selectedBobinaId !== (selectedCavo === null || selectedCavo === void 0 ? void 0 : selectedCavo.id_bobina) && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              orientation: \"vertical\",\n              flexItem: true,\n              sx: {\n                mx: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                sx: {\n                  fontWeight: 'bold',\n                  whiteSpace: 'nowrap',\n                  mr: 1,\n                  fontSize: '0.95rem',\n                  color: '#1976d2'\n                },\n                children: [\"Bobina selezionata: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: selectedBobinaId === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(selectedBobinaId)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 43\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 21\n              }, this), (() => {\n                if (selectedBobinaId === 'BOBINA_VUOTA') {\n                  return /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontSize: '0.9rem',\n                        color: 'text.secondary',\n                        fontStyle: 'italic'\n                      },\n                      children: \"(Rimozione bobina attuale)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 576,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 27\n                  }, this);\n                }\n                const bobina = bobine.find(b => b.id_bobina === selectedBobinaId);\n                if (!bobina) return null;\n\n                // Verifica compatibilità\n                const isCompatible = selectedCavo && String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim();\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 3,\n                    flexWrap: 'nowrap',\n                    overflow: 'hidden'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium',\n                        fontSize: '0.9rem',\n                        mr: 0.5\n                      },\n                      children: \"Tipo:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 594,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontSize: '0.9rem',\n                        color: isCompatible ? 'text.primary' : 'error.main'\n                      },\n                      children: bobina.tipologia || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 595,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium',\n                        fontSize: '0.9rem',\n                        mr: 0.5\n                      },\n                      children: \"Form:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 600,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontSize: '0.9rem',\n                        color: isCompatible ? 'text.primary' : 'error.main'\n                      },\n                      children: bobina.sezione || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 601,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium',\n                        fontSize: '0.9rem',\n                        mr: 0.5\n                      },\n                      children: \"Residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 606,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontSize: '0.9rem',\n                        color: 'primary.main',\n                        fontWeight: 'bold'\n                      },\n                      children: [bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 607,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium',\n                        fontSize: '0.9rem',\n                        mr: 0.5\n                      },\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 612,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: bobina.stato_bobina || 'N/D',\n                      color: bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning',\n                      sx: {\n                        height: '22px',\n                        '& .MuiChip-label': {\n                          px: 1,\n                          py: 0,\n                          fontSize: '0.85rem'\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 613,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 611,\n                    columnNumber: 27\n                  }, this), !isCompatible && /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: \"Non compatibile\",\n                      color: \"error\",\n                      variant: \"outlined\",\n                      sx: {\n                        height: '22px',\n                        '& .MuiChip-label': {\n                          px: 1,\n                          py: 0,\n                          fontSize: '0.85rem'\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 622,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 25\n                }, this);\n              })()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 1.5,\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        sx: {\n          mb: 1\n        },\n        children: \"Seleziona dalla lista\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 644,\n        columnNumber: 9\n      }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 649,\n        columnNumber: 11\n      }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          py: 0.5\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          children: \"Non ci sono cavi posati disponibili.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 654,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 653,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        variant: \"outlined\",\n        sx: {\n          maxHeight: '300px',\n          overflow: 'auto',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          stickyHeader: true,\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                '& th': {\n                  fontWeight: 'bold',\n                  py: 1,\n                  bgcolor: '#f5f5f5'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 662,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Formazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 665,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                sx: {\n                  width: '40px'\n                },\n                children: \"Info\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n              hover: true,\n              onClick: () => handleSelectCavo(cavo),\n              sx: {\n                cursor: 'pointer',\n                '&:hover': {\n                  bgcolor: '#f1f8e9'\n                },\n                '& td': {\n                  py: 0.5\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: 'medium'\n                },\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.sezione || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [cavo.metratura_reale || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.id_bobina ? cavo.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(cavo.id_bobina) : 'VUOTA'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: \"Installato\",\n                  color: \"success\",\n                  sx: {\n                    height: '20px',\n                    '& .MuiChip-label': {\n                      px: 1,\n                      py: 0,\n                      fontSize: '0.7rem'\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: e => {\n                    e.stopPropagation();\n                    setSelectedCavo(cavo);\n                    setShowCavoDetailsDialog(true);\n                  },\n                  children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 706,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 21\n              }, this)]\n            }, cavo.id_cavo, true, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 658,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 657,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 643,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 455,\n    columnNumber: 5\n  }, this);\n\n  // Renderizza le opzioni di modifica\n  const renderModificaOptions = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Opzioni di modifica\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 727,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n        value: selectedOption,\n        onChange: handleOptionChange,\n        children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"assegnaNuova\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 737,\n            columnNumber: 22\n          }, this),\n          label: \"Assegna nuova bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 735,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"rimuoviBobina\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 22\n          }, this),\n          label: \"Rimuovi bobina attuale\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"annullaInstallazione\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 747,\n            columnNumber: 22\n          }, this),\n          label: \"Annulla installazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 731,\n        columnNumber: 9\n      }, this), selectedOption === 'assegnaNuova' && renderBobineSelection(), selectedOption === 'rimuoviBobina' && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mt: 2\n        },\n        children: \"Questa operazione rimuover\\xE0 l'associazione con la bobina attuale, assegnando una \\\"BOBINA_VUOTA\\\" al cavo. Il cavo rimarr\\xE0 nello stato posato e i metri posati rimarranno invariati. La bobina attuale (se presente) riavr\\xE0 i suoi metri restituiti.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 755,\n        columnNumber: 11\n      }, this), selectedOption === 'annullaInstallazione' && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        sx: {\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          fontWeight: \"bold\",\n          children: \"ATTENZIONE: Questa operazione annuller\\xE0 completamente l'installazione del cavo.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 764,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [\"- Il cavo torner\\xE0 allo stato \\\"Da installare\\\"\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 768,\n            columnNumber: 59\n          }, this), \"- La metratura reale sar\\xE0 azzerata\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 769,\n            columnNumber: 49\n          }, this), \"- L'associazione con la bobina sar\\xE0 rimossa\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 770,\n            columnNumber: 58\n          }, this), \"- I metri posati saranno restituiti alla bobina originale (se presente)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 767,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 763,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'flex-end',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 24\n          }, this),\n          onClick: handleCloseForm,\n          disabled: loading,\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 777,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 24\n          }, this),\n          onClick: handleSave,\n          disabled: loading || !selectedOption || selectedOption === 'assegnaNuova' && !selectedBobinaId,\n          children: \"Salva modifiche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 786,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 776,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 726,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza la selezione delle bobine\n  const renderBobineSelection = () => {\n    if (bobineLoading) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 805,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 804,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Filtra le bobine in base al testo di ricerca\n    const bobineFiltrate = bobine.filter(bobina => {\n      const searchLower = bobinaSearchText.toLowerCase();\n      return !bobinaSearchText || getBobinaNumber(bobina.id_bobina).toLowerCase().includes(searchLower) || String(bobina.tipologia || '').toLowerCase().includes(searchLower) || String(bobina.sezione || '').toLowerCase().includes(searchLower);\n    });\n\n    // Separa le bobine compatibili e non compatibili\n    const bobineCompatibili = selectedCavo ? bobineFiltrate.filter(bobina => String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim() && (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso')) : [];\n    const bobineNonCompatibili = selectedCavo ? bobineFiltrate.filter(bobina => !(String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim()) && (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso')) : [];\n\n    // Ordina per metri residui (decrescente)\n    bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n    bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Ricerca bobina (ID, tipologia, formazione)\",\n          value: bobinaSearchText,\n          onChange: e => setBobinaSearchText(e.target.value),\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 850,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 849,\n              columnNumber: 17\n            }, this)\n          },\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 842,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 841,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2,\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Inserisci numero bobina (es. 3)\",\n          value: bobinaNumericInput,\n          onChange: e => setBobinaNumericInput(e.target.value),\n          type: \"number\",\n          sx: {\n            width: '250px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 860,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          onClick: handleSelectBobinaByNumber,\n          disabled: !bobinaNumericInput.trim(),\n          children: \"Seleziona\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 867,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 859,\n        columnNumber: 9\n      }, this), selectedBobinaId && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"success\",\n        sx: {\n          mb: 2\n        },\n        children: [\"Bobina selezionata: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: getBobinaNumber(selectedBobinaId)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 879,\n          columnNumber: 33\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 878,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            variant: \"outlined\",\n            sx: {\n              p: 2,\n              height: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 'bold',\n                mb: 1\n              },\n              children: \"ELENCO BOBINE COMPATIBILI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 15\n            }, this), bobineCompatibili.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  width: '100%',\n                  py: 0.8,\n                  px: 1.8,\n                  bgcolor: '#f5f5f5',\n                  borderRadius: 1,\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '60px',\n                    mr: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 896,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 895,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '120px',\n                    mr: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"Tipo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 899,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 898,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '100px',\n                    mr: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"Form.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 902,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 901,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '100px',\n                    mr: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"Residui\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 905,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 904,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flexGrow: 0\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"Stato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 908,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 907,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 894,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(List, {\n                sx: {\n                  maxHeight: '300px',\n                  overflow: 'auto',\n                  bgcolor: 'background.paper'\n                },\n                children: bobineCompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n                  disablePadding: true,\n                  secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                    edge: \"end\",\n                    size: \"small\",\n                    onClick: () => handleSelectBobina(bobina.id_bobina),\n                    children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                      color: \"primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 922,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 917,\n                    columnNumber: 27\n                  }, this),\n                  sx: {\n                    bgcolor: selectedBobinaId === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                    borderRadius: '4px',\n                    mb: 0.5,\n                    border: selectedBobinaId === bobina.id_bobina ? '1px solid #4caf50' : 'none'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                    dense: true,\n                    onClick: () => handleSelectBobina(bobina.id_bobina),\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        width: '100%',\n                        py: 0.8\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '60px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.9rem'\n                          },\n                          children: getBobinaNumber(bobina.id_bobina)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 938,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 937,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '120px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontSize: '0.85rem'\n                          },\n                          children: bobina.tipologia || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 943,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 942,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '100px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontSize: '0.85rem'\n                          },\n                          children: bobina.sezione || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 948,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 947,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '100px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.85rem',\n                            color: 'success.main'\n                          },\n                          children: [bobina.metri_residui || 0, \" m\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 953,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 952,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          flexGrow: 0\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Chip, {\n                          size: \"small\",\n                          label: bobina.stato_bobina || 'N/D',\n                          color: getReelStateColor(bobina.stato_bobina),\n                          variant: \"outlined\",\n                          sx: {\n                            height: 22,\n                            fontSize: '0.8rem',\n                            '& .MuiChip-label': {\n                              px: 1,\n                              py: 0\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 958,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 957,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 936,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 932,\n                    columnNumber: 25\n                  }, this)\n                }, bobina.id_bobina, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 913,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 911,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              sx: {\n                mt: 1\n              },\n              children: \"Nessuna bobina compatibile disponibile. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 973,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 887,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 886,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            variant: \"outlined\",\n            sx: {\n              p: 2,\n              height: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 'bold',\n                mb: 1\n              },\n              children: \"ELENCO BOBINE NON COMPATIBILI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 983,\n              columnNumber: 15\n            }, this), bobineNonCompatibili.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  width: '100%',\n                  py: 0.8,\n                  px: 1.8,\n                  bgcolor: '#f5f5f5',\n                  borderRadius: 1,\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '60px',\n                    mr: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 991,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 990,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '120px',\n                    mr: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"Tipo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 994,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 993,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '100px',\n                    mr: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"Form.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 997,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 996,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '100px',\n                    mr: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"Residui\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1000,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 999,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flexGrow: 0\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"Stato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1003,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1002,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 989,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(List, {\n                sx: {\n                  maxHeight: '300px',\n                  overflow: 'auto',\n                  bgcolor: 'background.paper'\n                },\n                children: bobineNonCompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n                  disablePadding: true,\n                  secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                    edge: \"end\",\n                    size: \"small\",\n                    onClick: () => handleSelectBobina(bobina.id_bobina),\n                    children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                      color: \"primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1017,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1012,\n                    columnNumber: 27\n                  }, this),\n                  sx: {\n                    bgcolor: selectedBobinaId === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                    borderRadius: '4px',\n                    mb: 0.5,\n                    border: selectedBobinaId === bobina.id_bobina ? '1px solid #4caf50' : 'none'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                    dense: true,\n                    onClick: () => handleSelectBobina(bobina.id_bobina),\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        width: '100%',\n                        py: 0.8\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '60px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.9rem'\n                          },\n                          children: getBobinaNumber(bobina.id_bobina)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1033,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1032,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '120px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontSize: '0.85rem'\n                          },\n                          children: bobina.tipologia || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1038,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1037,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '100px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontSize: '0.85rem'\n                          },\n                          children: bobina.sezione || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1043,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1042,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '100px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.85rem',\n                            color: 'success.main'\n                          },\n                          children: [bobina.metri_residui || 0, \" m\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1048,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1047,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          gap: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Chip, {\n                          size: \"small\",\n                          label: bobina.stato_bobina || 'N/D',\n                          color: getReelStateColor(bobina.stato_bobina),\n                          variant: \"outlined\",\n                          sx: {\n                            height: 22,\n                            fontSize: '0.8rem',\n                            '& .MuiChip-label': {\n                              px: 1,\n                              py: 0\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1053,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                          size: \"small\",\n                          label: \"Non comp.\",\n                          color: \"warning\",\n                          variant: \"outlined\",\n                          sx: {\n                            height: 22,\n                            fontSize: '0.8rem',\n                            '& .MuiChip-label': {\n                              px: 1,\n                              py: 0\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1060,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1052,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1031,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1027,\n                    columnNumber: 25\n                  }, this)\n                }, bobina.id_bobina, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1008,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1006,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              sx: {\n                mt: 1\n              },\n              children: \"Nessuna bobina non compatibile disponibile con i filtri attuali.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1075,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 982,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 981,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 884,\n        columnNumber: 9\n      }, this), bobineCompatibili.length === 0 && bobineNonCompatibili.length === 0 && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mt: 2\n        },\n        children: bobinaSearchText ? 'Nessuna bobina trovata con i criteri di ricerca specificati.' : 'Non ci sono bobine disponibili.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1085,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 839,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [renderCavoSelectionForm(), renderModificaOptions(), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showCavoDetailsDialog,\n      onClose: () => setShowCavoDetailsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Dettagli completi del cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: selectedCavo && /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1110,\n          columnNumber: 28\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowCavoDetailsDialog(false),\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1113,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1112,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1102,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showConfirmDialog,\n      onClose: () => setShowConfirmDialog(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Conferma operazione\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: confirmDialogMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1124,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowConfirmDialog(false),\n          color: \"primary\",\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setShowConfirmDialog(false);\n            if (confirmDialogAction) confirmDialogAction();\n          },\n          color: \"primary\",\n          variant: \"contained\",\n          autoFocus: true,\n          children: \"Conferma\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1126,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: handleCloseIncompatibleReelDialog,\n      cavo: incompatibleReelData.cavo,\n      bobina: incompatibleReelData.bobina,\n      onUpdateCavo: handleUpdateCavoForCompatibility,\n      onSelectAnotherReel: handleCloseIncompatibleReelDialog,\n      onContinueWithIncompatible: handleContinueWithIncompatible\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1148,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1094,\n    columnNumber: 5\n  }, this);\n};\n_s(ModificaBobinaForm, \"86u2LdFc2mrYTgVLDgQv/odoD98=\", false, function () {\n  return [useNavigate];\n});\n_c = ModificaBobinaForm;\nexport default ModificaBobinaForm;\nvar _c;\n$RefreshReg$(_c, \"ModificaBobinaForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "<PERSON><PERSON>", "CircularProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Radio", "RadioGroup", "FormControlLabel", "IconButton", "InputAdornment", "List", "ListItem", "ListItemText", "ListItemButton", "ListItemSecondaryAction", "Chip", "Search", "SearchIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "AddCircleOutline", "AddCircleOutlineIcon", "IncompatibleReelDialog", "useNavigate", "caviService", "parcoCaviService", "CavoDetailsView", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ModificaBobinaForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "searchResults", "setSearchResults", "showSearchResults", "setShowSearchResults", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "compatibleBobine", "setCompatibleBobine", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "selectedOption", "setSelectedOption", "selectedBobinaId", "setSelectedBobinaId", "bobinaSearchText", "setBobinaSearchText", "bobinaNumericInput", "setBobinaNumericInput", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReelData", "setIncompatibleReelData", "cavo", "bobina", "showConfirmDialog", "setShowConfirmDialog", "confirmDialogMessage", "setConfirmDialogMessage", "confirmDialogAction", "setConfirmDialogAction", "showCavoDetailsDialog", "setShowCavoDetailsDialog", "loadCavi", "loadBobine", "console", "log", "caviData", "get<PERSON><PERSON>", "length", "caviInstallati", "filter", "parseFloat", "metratura_reale", "stato_installazione", "error", "detail", "message", "bobine<PERSON><PERSON>", "getBobine", "compatibleBobineData", "tipologia", "sezione", "stato_bobina", "handleSearchCavoById", "trim", "getCavoById", "handleSelectCavo", "handleOptionChange", "event", "target", "value", "handleSelectBobina", "bobina<PERSON>d", "find", "b", "id_bobina", "isCompatible", "String", "getBobinaNumber", "handleSelectBobinaByNumber", "fullBobinaId", "handleSave", "action", "id_cavo", "updateBobina", "annullaInstallazione", "cancelInstallation", "handleCloseForm", "handleCloseIncompatibleReelDialog", "handleUpdateCavoForCompatibility", "updateCavoForCompatibility", "updatedCavo", "handleContinueWithIncompatible", "idBobina", "includes", "split", "renderCavoSelectionForm", "children", "variant", "sx", "mb", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "width", "display", "alignItems", "mr", "min<PERSON><PERSON><PERSON>", "size", "label", "onChange", "e", "placeholder", "flexGrow", "color", "onClick", "disabled", "startIcon", "fontSize", "height", "flexWrap", "overflow", "ml", "whiteSpace", "style", "orientation", "flexItem", "mx", "gap", "px", "py", "fontStyle", "metri_residui", "justifyContent", "my", "severity", "component", "maxHeight", "<PERSON><PERSON><PERSON><PERSON>", "bgcolor", "align", "map", "hover", "cursor", "stopPropagation", "renderModificaOptions", "gutterBottom", "control", "renderBobineSelection", "mt", "bobineFiltrate", "searchLower", "toLowerCase", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "bobineNonCompatibili", "sort", "a", "fullWidth", "InputProps", "startAdornment", "position", "type", "container", "spacing", "item", "xs", "md", "borderRadius", "disablePadding", "secondaryAction", "edge", "border", "dense", "open", "onClose", "max<PERSON><PERSON><PERSON>", "autoFocus", "onUpdateCavo", "onSelectAnotherReel", "onContinueWithIncompatible", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/ModificaBobinaForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Alert,\n  CircularProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Radio,\n  RadioGroup,\n  FormControlLabel,\n  IconButton,\n  InputAdornment,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemButton,\n  ListItemSecondaryAction,\n  Chip\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon,\n  AddCircleOutline as AddCircleOutlineIcon\n} from '@mui/icons-material';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoDetailsView from './CavoDetailsView';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\n\n/**\n * Componente per la modifica della bobina di un cavo già posato\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst ModificaBobinaForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [selectedOption, setSelectedOption] = useState('');\n  const [selectedBobinaId, setSelectedBobinaId] = useState('');\n  const [bobinaSearchText, setBobinaSearchText] = useState('');\n  const [bobinaNumericInput, setBobinaNumericInput] = useState('');\n\n  // Stati per gestire il dialog di incompatibilità\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReelData, setIncompatibleReelData] = useState({ cavo: null, bobina: null });\n\n  // Stati per i dialoghi\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');\n  const [confirmDialogAction, setConfirmDialogAction] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica i cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica le bobine quando viene selezionato un cavo\n  useEffect(() => {\n    if (selectedCavo) {\n      loadBobine();\n    }\n  }, [selectedCavo]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica solo i cavi installati (con metratura_reale > 0)\n      const caviData = await caviService.getCavi(cantiereId, null, 'Installato');\n      console.log(`Caricati ${caviData.length} cavi installati`);\n\n      // Filtra i cavi che hanno metratura_reale > 0\n      const caviInstallati = caviData.filter(cavo =>\n        parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato'\n      );\n\n      setCavi(caviInstallati);\n      console.log(`Filtrati ${caviInstallati.length} cavi effettivamente installati`);\n    } catch (error) {\n      console.error('Errore durante il caricamento dei cavi:', error);\n      onError('Errore durante il caricamento dei cavi: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    if (!selectedCavo) return;\n\n    try {\n      setBobineLoading(true);\n      console.log(`Caricamento bobine per il cantiere ${cantiereId}...`);\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Caricati ${bobineData.length} bobine`);\n\n      // Filtra le bobine compatibili\n      const compatibleBobineData = bobineData.filter(bobina =>\n        bobina.tipologia === selectedCavo.tipologia &&\n        bobina.sezione === selectedCavo.sezione &&\n        (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso')\n      );\n\n      setBobine(bobineData);\n      setCompatibleBobine(compatibleBobineData);\n      console.log(`Filtrate ${compatibleBobineData.length} bobine compatibili`);\n    } catch (error) {\n      console.error('Errore durante il caricamento delle bobine:', error);\n      onError('Errore durante il caricamento delle bobine: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      const cavo = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica che il cavo sia installato\n      if (parseFloat(cavo.metratura_reale) <= 0 && cavo.stato_installazione !== 'Installato') {\n        onError('Il cavo selezionato non risulta installato');\n        setLoading(false);\n        return;\n      }\n\n      setSelectedCavo(cavo);\n      setSelectedOption(''); // Reset dell'opzione selezionata\n      setSelectedBobinaId(''); // Reset della bobina selezionata\n      setShowSearchResults(false);\n    } catch (error) {\n      console.error('Errore durante la ricerca del cavo:', error);\n      onError('Errore durante la ricerca del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo dalla lista\n  const handleSelectCavo = (cavo) => {\n    setSelectedCavo(cavo);\n    setSelectedOption(''); // Reset dell'opzione selezionata\n    setSelectedBobinaId(''); // Reset della bobina selezionata\n    setShowSearchResults(false);\n  };\n\n  // Gestisce il cambio dell'opzione selezionata\n  const handleOptionChange = (event) => {\n    setSelectedOption(event.target.value);\n    setSelectedBobinaId(''); // Reset della bobina selezionata quando cambia l'opzione\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = (bobinaId) => {\n    console.log('Bobina selezionata:', bobinaId);\n\n    // Trova la bobina selezionata\n    const bobina = bobine.find(b => b.id_bobina === bobinaId);\n\n    if (bobina && selectedCavo) {\n      // Verifica compatibilità\n      const isCompatible =\n        String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n        String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim();\n\n      if (!isCompatible) {\n        console.log('Bobina incompatibile selezionata:', bobina);\n        console.log('Cavo corrente:', selectedCavo);\n\n        // Mostra il dialogo di incompatibilità\n        setIncompatibleReelData({\n          cavo: selectedCavo,\n          bobina: bobina\n        });\n        setShowIncompatibleReelDialog(true);\n\n        // Imposta comunque la bobina selezionata per mostrare i dettagli\n        setSelectedBobinaId(bobinaId);\n        setBobinaNumericInput(getBobinaNumber(bobinaId));\n        return;\n      }\n    }\n\n    // Se compatibile o nessun cavo selezionato, procedi normalmente\n    setSelectedBobinaId(bobinaId);\n    setBobinaNumericInput(getBobinaNumber(bobinaId));\n  };\n\n  // Gestisce la selezione di una bobina tramite input numerico\n  const handleSelectBobinaByNumber = () => {\n    if (!bobinaNumericInput.trim()) {\n      onError('Inserisci un numero di bobina valido');\n      return;\n    }\n\n    // Costruisci l'ID completo della bobina\n    const fullBobinaId = `C${cantiereId}_B${bobinaNumericInput.trim()}`;\n\n    // Verifica che la bobina esista\n    const bobina = bobine.find(b => b.id_bobina === fullBobinaId);\n    if (!bobina) {\n      onError(`Bobina ${bobinaNumericInput} non trovata nel cantiere`);\n      return;\n    }\n\n    // Usa la stessa logica di handleSelectBobina\n    handleSelectBobina(fullBobinaId);\n  };\n\n  // Gestisce il salvataggio delle modifiche\n  const handleSave = () => {\n    if (!selectedCavo) {\n      onError('Seleziona un cavo prima di procedere');\n      return;\n    }\n\n    if (!selectedOption) {\n      onError('Seleziona un\\'opzione prima di procedere');\n      return;\n    }\n\n    // Verifica che sia stata selezionata una bobina se l'opzione è \"assegnaNuova\"\n    if (selectedOption === 'assegnaNuova' && !selectedBobinaId) {\n      onError('Seleziona una bobina prima di procedere');\n      return;\n    }\n\n    // Prepara il messaggio di conferma in base all'opzione selezionata\n    let message = '';\n    let action = null;\n\n    if (selectedOption === 'assegnaNuova') {\n      const bobina = bobine.find(b => b.id_bobina === selectedBobinaId);\n      message = `Sei sicuro di voler assegnare la bobina ${getBobinaNumber(selectedBobinaId)} al cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina(selectedBobinaId);\n    } else if (selectedOption === 'rimuoviBobina') {\n      message = `Sei sicuro di voler rimuovere la bobina attuale dal cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina('BOBINA_VUOTA');\n    } else if (selectedOption === 'annullaInstallazione') {\n      message = `ATTENZIONE: Questa operazione annullerà l'installazione del cavo ${selectedCavo.id_cavo}. Tutti i metri posati saranno restituiti alla bobina originale. Sei sicuro di voler procedere?`;\n      action = () => annullaInstallazione();\n    }\n\n    setConfirmDialogMessage(message);\n    setConfirmDialogAction(() => action);\n    setShowConfirmDialog(true);\n  };\n\n  // Funzione per aggiornare la bobina di un cavo\n  const updateBobina = async (bobinaId) => {\n    try {\n      setLoading(true);\n      await caviService.updateBobina(cantiereId, selectedCavo.id_cavo, bobinaId);\n\n      onSuccess(`Bobina ${bobinaId === 'BOBINA_VUOTA' ? 'vuota assegnata' : 'assegnata'} con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento della bobina:', error);\n      onError('Errore durante l\\'aggiornamento della bobina: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per annullare l'installazione di un cavo\n  const annullaInstallazione = async () => {\n    try {\n      setLoading(true);\n\n      // Chiamata all'API per annullare l'installazione\n      await caviService.cancelInstallation(cantiereId, selectedCavo.id_cavo);\n\n      onSuccess(`Installazione del cavo ${selectedCavo.id_cavo} annullata con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'annullamento dell\\'installazione:', error);\n      onError('Errore durante l\\'annullamento dell\\'installazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per chiudere il form e resettare tutto\n  const handleCloseForm = () => {\n    // Reset di tutti gli stati\n    setSelectedCavo(null);\n    setSelectedOption('');\n    setSelectedBobinaId('');\n    setCavoIdInput('');\n    setShowSearchResults(false);\n\n    // Messaggio di conferma\n    onSuccess('Operazione annullata');\n  };\n\n  // Gestisce la chiusura del dialog di incompatibilità\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReelData({ cavo: null, bobina: null });\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per compatibilità\n  const handleUpdateCavoForCompatibility = async () => {\n    const { cavo, bobina } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per l\\'aggiornamento del cavo:', { cavo, bobina });\n      onError('Dati mancanti per l\\'aggiornamento del cavo');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      console.log(`Aggiornamento caratteristiche del cavo ${cavo.id_cavo} per compatibilità con bobina ${bobina.id_bobina}`);\n\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Mantieni la bobina selezionata\n      setSelectedBobinaId(bobina.id_bobina);\n      setBobinaNumericInput(getBobinaNumber(bobina.id_bobina));\n\n      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate per compatibilità con bobina ${getBobinaNumber(bobina.id_bobina)}`);\n      handleCloseIncompatibleReelDialog();\n\n      // Ricarica le bobine per aggiornare la compatibilità\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento del cavo:', error);\n      onError('Errore durante l\\'aggiornamento del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'utilizzo di una bobina incompatibile senza aggiornare le caratteristiche del cavo\n  const handleContinueWithIncompatible = async () => {\n    const { cavo, bobina } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per utilizzare la bobina incompatibile:', { cavo, bobina });\n      onError('Dati mancanti per utilizzare la bobina incompatibile');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      console.log(`Utilizzo bobina incompatibile ${bobina.id_bobina} con cavo ${cavo.id_cavo} senza aggiornare le caratteristiche`);\n\n      // Mantieni la bobina selezionata\n      setSelectedBobinaId(bobina.id_bobina);\n      setBobinaNumericInput(getBobinaNumber(bobina.id_bobina));\n\n      onSuccess(`Bobina incompatibile ${getBobinaNumber(bobina.id_bobina)} selezionata per il cavo ${cavo.id_cavo}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante la selezione della bobina incompatibile:', error);\n      onError('Errore durante la selezione della bobina incompatibile: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina) => {\n    if (idBobina === 'BOBINA_VUOTA') return 'BOBINA VUOTA';\n\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Renderizza il form per la selezione del cavo\n  const renderCavoSelectionForm = () => (\n    <Box>\n      <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 'bold' }}>\n        Seleziona un cavo posato\n      </Typography>\n\n      {/* Ricerca per ID - Versione compatta con dettagli cavo selezionato */}\n      <Paper sx={{ p: 1.5, mb: 2, width: '100%' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\n          <Typography variant=\"subtitle2\" sx={{ mr: 1, minWidth: '80px' }}>\n            Cerca cavo\n          </Typography>\n          <TextField\n            size=\"small\"\n            label=\"ID Cavo\"\n            variant=\"outlined\"\n            value={cavoIdInput}\n            onChange={(e) => setCavoIdInput(e.target.value)}\n            placeholder=\"Inserisci l'ID del cavo\"\n            sx={{ flexGrow: 0, width: '200px', mr: 1 }}\n          />\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            onClick={handleSearchCavoById}\n            disabled={caviLoading || !cavoIdInput.trim()}\n            startIcon={caviLoading ? <CircularProgress size={16} /> : <SearchIcon fontSize=\"small\" />}\n            size=\"small\"\n            sx={{ minWidth: '80px', height: '36px', mr: 2 }}\n          >\n            CERCA\n          </Button>\n\n          {/* Dettagli cavo selezionato in riga singola */}\n          {selectedCavo && (\n            <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, flexWrap: 'nowrap', overflow: 'hidden', ml: 4 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>\n                <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.95rem' }}>\n                  Cavo: <span style={{ color: '#1976d2' }}>{selectedCavo.id_cavo}</span>\n                </Typography>\n                <Divider orientation=\"vertical\" flexItem sx={{ mx: 1.5 }} />\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Tipo:</Typography>\n                    <Typography variant=\"body2\" sx={{ fontSize: '0.9rem' }}>{selectedCavo.tipologia || 'N/A'}</Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Form:</Typography>\n                    <Typography variant=\"body2\" sx={{ fontSize: '0.9rem' }}>{selectedCavo.sezione || 'N/A'}</Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Metri:</Typography>\n                    <Typography variant=\"body2\" sx={{ fontSize: '0.9rem' }}>{selectedCavo.metratura_reale || 'N/A'} m</Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>\n                    <Chip\n                      size=\"small\"\n                      label={selectedCavo.stato_installazione || 'N/D'}\n                      color=\"success\"\n                      sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}\n                    />\n                  </Box>\n                </Box>\n              </Box>\n\n              {/* Dettagli bobina attuale */}\n              {selectedCavo.id_bobina && (\n                <>\n                  <Divider orientation=\"vertical\" flexItem sx={{ mx: 2 }} />\n                  <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                    <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.95rem', color: '#2e7d32' }}>\n                      Bobina attuale: <span>{selectedCavo.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(selectedCavo.id_bobina)}</span>\n                    </Typography>\n                    {(() => {\n                      if (selectedCavo.id_bobina === 'BOBINA_VUOTA') {\n                        return (\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: 'text.secondary', fontStyle: 'italic' }}>\n                              (Cavo posato senza bobina specifica)\n                            </Typography>\n                          </Box>\n                        );\n                      }\n\n                      const bobina = bobine.find(b => b.id_bobina === selectedCavo.id_bobina);\n                      return bobina ? (\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Residui:</Typography>\n                            <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: 'success.main', fontWeight: 'bold' }}>\n                              {bobina.metri_residui || 0} m\n                            </Typography>\n                          </Box>\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>\n                            <Chip\n                              size=\"small\"\n                              label={bobina.stato_bobina || 'N/D'}\n                              color={bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning'}\n                              sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}\n                            />\n                          </Box>\n                        </Box>\n                      ) : null;\n                    })()}\n                  </Box>\n                </>\n              )}\n\n              {/* Dettagli bobina selezionata (se diversa da quella attuale) */}\n              {selectedBobinaId && selectedBobinaId !== selectedCavo?.id_bobina && (\n                <>\n                  <Divider orientation=\"vertical\" flexItem sx={{ mx: 2 }} />\n                  <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                    <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.95rem', color: '#1976d2' }}>\n                      Bobina selezionata: <span>{selectedBobinaId === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(selectedBobinaId)}</span>\n                    </Typography>\n                    {(() => {\n                      if (selectedBobinaId === 'BOBINA_VUOTA') {\n                        return (\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: 'text.secondary', fontStyle: 'italic' }}>\n                              (Rimozione bobina attuale)\n                            </Typography>\n                          </Box>\n                        );\n                      }\n\n                      const bobina = bobine.find(b => b.id_bobina === selectedBobinaId);\n                      if (!bobina) return null;\n\n                      // Verifica compatibilità\n                      const isCompatible = selectedCavo &&\n                        String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n                        String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim();\n\n                      return (\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Tipo:</Typography>\n                            <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: isCompatible ? 'text.primary' : 'error.main' }}>\n                              {bobina.tipologia || 'N/A'}\n                            </Typography>\n                          </Box>\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Form:</Typography>\n                            <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: isCompatible ? 'text.primary' : 'error.main' }}>\n                              {bobina.sezione || 'N/A'}\n                            </Typography>\n                          </Box>\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Residui:</Typography>\n                            <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: 'primary.main', fontWeight: 'bold' }}>\n                              {bobina.metri_residui || 0} m\n                            </Typography>\n                          </Box>\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>\n                            <Chip\n                              size=\"small\"\n                              label={bobina.stato_bobina || 'N/D'}\n                              color={bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning'}\n                              sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}\n                            />\n                          </Box>\n                          {!isCompatible && (\n                            <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                              <Chip\n                                size=\"small\"\n                                label=\"Non compatibile\"\n                                color=\"error\"\n                                variant=\"outlined\"\n                                sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}\n                              />\n                            </Box>\n                          )}\n                        </Box>\n                      );\n                    })()}\n                  </Box>\n                </>\n              )}\n            </Box>\n          )}\n        </Box>\n      </Paper>\n\n      {/* Lista cavi - versione compatta */}\n      <Paper sx={{ p: 1.5, width: '100%' }}>\n        <Typography variant=\"subtitle2\" sx={{ mb: 1 }}>\n          Seleziona dalla lista\n        </Typography>\n\n        {caviLoading ? (\n          <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n            <CircularProgress size={24} />\n          </Box>\n        ) : cavi.length === 0 ? (\n          <Alert severity=\"info\" sx={{ py: 0.5 }}>\n            <Typography variant=\"caption\">Non ci sono cavi posati disponibili.</Typography>\n          </Alert>\n        ) : (\n          <TableContainer component={Paper} variant=\"outlined\" sx={{ maxHeight: '300px', overflow: 'auto', width: '100%' }}>\n            <Table size=\"small\" stickyHeader>\n              <TableHead>\n                <TableRow sx={{ '& th': { fontWeight: 'bold', py: 1, bgcolor: '#f5f5f5' } }}>\n                  <TableCell>ID Cavo</TableCell>\n                  <TableCell>Tipologia</TableCell>\n                  <TableCell>Formazione</TableCell>\n                  <TableCell>Metri</TableCell>\n                  <TableCell>Bobina</TableCell>\n                  <TableCell>Stato</TableCell>\n                  <TableCell align=\"center\" sx={{ width: '40px' }}>Info</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {cavi.map((cavo) => (\n                  <TableRow\n                    key={cavo.id_cavo}\n                    hover\n                    onClick={() => handleSelectCavo(cavo)}\n                    sx={{\n                      cursor: 'pointer',\n                      '&:hover': { bgcolor: '#f1f8e9' },\n                      '& td': { py: 0.5 }\n                    }}\n                  >\n                    <TableCell sx={{ fontWeight: 'medium' }}>{cavo.id_cavo}</TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>{cavo.sezione || 'N/A'}</TableCell>\n                    <TableCell>{cavo.metratura_reale || 'N/A'} m</TableCell>\n                    <TableCell>\n                      {cavo.id_bobina ? (cavo.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(cavo.id_bobina)) : 'VUOTA'}\n                    </TableCell>\n                    <TableCell>\n                      <Chip\n                        size=\"small\"\n                        label=\"Installato\"\n                        color=\"success\"\n                        sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.7rem' } }}\n                      />\n                    </TableCell>\n                    <TableCell align=\"center\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          setSelectedCavo(cavo);\n                          setShowCavoDetailsDialog(true);\n                        }}\n                      >\n                        <InfoIcon fontSize=\"small\" />\n                      </IconButton>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        )}\n      </Paper>\n    </Box>\n  );\n\n\n\n  // Renderizza le opzioni di modifica\n  const renderModificaOptions = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Opzioni di modifica\n        </Typography>\n\n        <RadioGroup\n          value={selectedOption}\n          onChange={handleOptionChange}\n        >\n          <FormControlLabel\n            value=\"assegnaNuova\"\n            control={<Radio />}\n            label=\"Assegna nuova bobina\"\n          />\n          <FormControlLabel\n            value=\"rimuoviBobina\"\n            control={<Radio />}\n            label=\"Rimuovi bobina attuale\"\n          />\n          <FormControlLabel\n            value=\"annullaInstallazione\"\n            control={<Radio />}\n            label=\"Annulla installazione\"\n          />\n        </RadioGroup>\n\n        {selectedOption === 'assegnaNuova' && renderBobineSelection()}\n\n        {selectedOption === 'rimuoviBobina' && (\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            Questa operazione rimuoverà l'associazione con la bobina attuale, assegnando una \"BOBINA_VUOTA\" al cavo.\n            Il cavo rimarrà nello stato posato e i metri posati rimarranno invariati.\n            La bobina attuale (se presente) riavrà i suoi metri restituiti.\n          </Alert>\n        )}\n\n        {selectedOption === 'annullaInstallazione' && (\n          <Alert severity=\"warning\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\" fontWeight=\"bold\">\n              ATTENZIONE: Questa operazione annullerà completamente l'installazione del cavo.\n            </Typography>\n            <Typography variant=\"body2\">\n              - Il cavo tornerà allo stato \"Da installare\"<br />\n              - La metratura reale sarà azzerata<br />\n              - L'associazione con la bobina sarà rimossa<br />\n              - I metri posati saranno restituiti alla bobina originale (se presente)\n            </Typography>\n          </Alert>\n        )}\n\n        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>\n          <Button\n            variant=\"outlined\"\n            color=\"secondary\"\n            startIcon={<CancelIcon />}\n            onClick={handleCloseForm}\n            disabled={loading}\n          >\n            Annulla operazione\n          </Button>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<SaveIcon />}\n            onClick={handleSave}\n            disabled={loading || !selectedOption || (selectedOption === 'assegnaNuova' && !selectedBobinaId)}\n          >\n            Salva modifiche\n          </Button>\n        </Box>\n      </Paper>\n    );\n  };\n\n  // Renderizza la selezione delle bobine\n  const renderBobineSelection = () => {\n    if (bobineLoading) {\n      return (\n        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n          <CircularProgress />\n        </Box>\n      );\n    }\n\n    // Filtra le bobine in base al testo di ricerca\n    const bobineFiltrate = bobine.filter(bobina => {\n      const searchLower = bobinaSearchText.toLowerCase();\n      return !bobinaSearchText ||\n        getBobinaNumber(bobina.id_bobina).toLowerCase().includes(searchLower) ||\n        String(bobina.tipologia || '').toLowerCase().includes(searchLower) ||\n        String(bobina.sezione || '').toLowerCase().includes(searchLower);\n    });\n\n    // Separa le bobine compatibili e non compatibili\n    const bobineCompatibili = selectedCavo\n      ? bobineFiltrate.filter(bobina =>\n          String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n          String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim() &&\n          (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso'))\n      : [];\n\n    const bobineNonCompatibili = selectedCavo\n      ? bobineFiltrate.filter(bobina =>\n          !(String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n            String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim()) &&\n          (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso'))\n      : [];\n\n    // Ordina per metri residui (decrescente)\n    bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n    bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n    return (\n      <Box sx={{ mt: 2 }}>\n        {/* Campo di ricerca rapida */}\n        <Box sx={{ mb: 2 }}>\n          <TextField\n            fullWidth\n            label=\"Ricerca bobina (ID, tipologia, formazione)\"\n            value={bobinaSearchText}\n            onChange={(e) => setBobinaSearchText(e.target.value)}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon />\n                </InputAdornment>\n              )\n            }}\n            sx={{ mb: 2 }}\n          />\n        </Box>\n\n        {/* Input numerico per selezione diretta */}\n        <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>\n          <TextField\n            label=\"Inserisci numero bobina (es. 3)\"\n            value={bobinaNumericInput}\n            onChange={(e) => setBobinaNumericInput(e.target.value)}\n            type=\"number\"\n            sx={{ width: '250px' }}\n          />\n          <Button\n            variant=\"outlined\"\n            onClick={handleSelectBobinaByNumber}\n            disabled={!bobinaNumericInput.trim()}\n          >\n            Seleziona\n          </Button>\n        </Box>\n\n        {/* Bobina selezionata */}\n        {selectedBobinaId && (\n          <Alert severity=\"success\" sx={{ mb: 2 }}>\n            Bobina selezionata: <strong>{getBobinaNumber(selectedBobinaId)}</strong>\n          </Alert>\n        )}\n\n        {/* Griglia per le due liste di bobine */}\n        <Grid container spacing={2}>\n          {/* Colonna sinistra: Bobine compatibili */}\n          <Grid item xs={12} md={6}>\n            <Paper variant=\"outlined\" sx={{ p: 2, height: '100%' }}>\n              <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                ELENCO BOBINE COMPATIBILI\n              </Typography>\n\n              {bobineCompatibili.length > 0 ? (\n                <>\n                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8, px: 1.8, bgcolor: '#f5f5f5', borderRadius: 1, mb: 1 }}>\n                    <Box sx={{ width: '60px', mr: 2 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>\n                    </Box>\n                    <Box sx={{ width: '120px', mr: 2 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipo</Typography>\n                    </Box>\n                    <Box sx={{ width: '100px', mr: 2 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Form.</Typography>\n                    </Box>\n                    <Box sx={{ width: '100px', mr: 2 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Residui</Typography>\n                    </Box>\n                    <Box sx={{ flexGrow: 0 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>\n                    </Box>\n                  </Box>\n                  <List sx={{ maxHeight: '300px', overflow: 'auto', bgcolor: 'background.paper' }}>\n                    {bobineCompatibili.map((bobina) => (\n                      <ListItem\n                        key={bobina.id_bobina}\n                        disablePadding\n                        secondaryAction={\n                          <IconButton\n                            edge=\"end\"\n                            size=\"small\"\n                            onClick={() => handleSelectBobina(bobina.id_bobina)}\n                          >\n                            <AddCircleOutlineIcon color=\"primary\" />\n                          </IconButton>\n                        }\n                        sx={{\n                          bgcolor: selectedBobinaId === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                          borderRadius: '4px',\n                          mb: 0.5,\n                          border: selectedBobinaId === bobina.id_bobina ? '1px solid #4caf50' : 'none',\n                        }}\n                      >\n                        <ListItemButton\n                          dense\n                          onClick={() => handleSelectBobina(bobina.id_bobina)}\n                        >\n                          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8 }}>\n                            <Box sx={{ width: '60px', mr: 2 }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>\n                                {getBobinaNumber(bobina.id_bobina)}\n                              </Typography>\n                            </Box>\n                            <Box sx={{ width: '120px', mr: 2 }}>\n                              <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                {bobina.tipologia || 'N/A'}\n                              </Typography>\n                            </Box>\n                            <Box sx={{ width: '100px', mr: 2 }}>\n                              <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                {bobina.sezione || 'N/A'}\n                              </Typography>\n                            </Box>\n                            <Box sx={{ width: '100px', mr: 2 }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.85rem', color: 'success.main' }}>\n                                {bobina.metri_residui || 0} m\n                              </Typography>\n                            </Box>\n                            <Box sx={{ flexGrow: 0 }}>\n                              <Chip\n                                size=\"small\"\n                                label={bobina.stato_bobina || 'N/D'}\n                                color={getReelStateColor(bobina.stato_bobina)}\n                                variant=\"outlined\"\n                                sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}\n                              />\n                            </Box>\n                          </Box>\n                        </ListItemButton>\n                      </ListItem>\n                    ))}\n                  </List>\n                </>\n              ) : (\n                <Alert severity=\"info\" sx={{ mt: 1 }}>\n                  Nessuna bobina compatibile disponibile. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\n                </Alert>\n              )}\n            </Paper>\n          </Grid>\n\n          {/* Colonna destra: Bobine non compatibili */}\n          <Grid item xs={12} md={6}>\n            <Paper variant=\"outlined\" sx={{ p: 2, height: '100%' }}>\n              <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                ELENCO BOBINE NON COMPATIBILI\n              </Typography>\n\n              {bobineNonCompatibili.length > 0 ? (\n                <>\n                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8, px: 1.8, bgcolor: '#f5f5f5', borderRadius: 1, mb: 1 }}>\n                    <Box sx={{ width: '60px', mr: 2 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>\n                    </Box>\n                    <Box sx={{ width: '120px', mr: 2 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipo</Typography>\n                    </Box>\n                    <Box sx={{ width: '100px', mr: 2 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Form.</Typography>\n                    </Box>\n                    <Box sx={{ width: '100px', mr: 2 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Residui</Typography>\n                    </Box>\n                    <Box sx={{ flexGrow: 0 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>\n                    </Box>\n                  </Box>\n                  <List sx={{ maxHeight: '300px', overflow: 'auto', bgcolor: 'background.paper' }}>\n                    {bobineNonCompatibili.map((bobina) => (\n                      <ListItem\n                        key={bobina.id_bobina}\n                        disablePadding\n                        secondaryAction={\n                          <IconButton\n                            edge=\"end\"\n                            size=\"small\"\n                            onClick={() => handleSelectBobina(bobina.id_bobina)}\n                          >\n                            <AddCircleOutlineIcon color=\"primary\" />\n                          </IconButton>\n                        }\n                        sx={{\n                          bgcolor: selectedBobinaId === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                          borderRadius: '4px',\n                          mb: 0.5,\n                          border: selectedBobinaId === bobina.id_bobina ? '1px solid #4caf50' : 'none',\n                        }}\n                      >\n                        <ListItemButton\n                          dense\n                          onClick={() => handleSelectBobina(bobina.id_bobina)}\n                        >\n                          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8 }}>\n                            <Box sx={{ width: '60px', mr: 2 }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>\n                                {getBobinaNumber(bobina.id_bobina)}\n                              </Typography>\n                            </Box>\n                            <Box sx={{ width: '120px', mr: 2 }}>\n                              <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                {bobina.tipologia || 'N/A'}\n                              </Typography>\n                            </Box>\n                            <Box sx={{ width: '100px', mr: 2 }}>\n                              <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                {bobina.sezione || 'N/A'}\n                              </Typography>\n                            </Box>\n                            <Box sx={{ width: '100px', mr: 2 }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.85rem', color: 'success.main' }}>\n                                {bobina.metri_residui || 0} m\n                              </Typography>\n                            </Box>\n                            <Box sx={{ display: 'flex', gap: 1 }}>\n                              <Chip\n                                size=\"small\"\n                                label={bobina.stato_bobina || 'N/D'}\n                                color={getReelStateColor(bobina.stato_bobina)}\n                                variant=\"outlined\"\n                                sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}\n                              />\n                              <Chip\n                                size=\"small\"\n                                label=\"Non comp.\"\n                                color=\"warning\"\n                                variant=\"outlined\"\n                                sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}\n                              />\n                            </Box>\n                          </Box>\n                        </ListItemButton>\n                      </ListItem>\n                    ))}\n                  </List>\n                </>\n              ) : (\n                <Alert severity=\"info\" sx={{ mt: 1 }}>\n                  Nessuna bobina non compatibile disponibile con i filtri attuali.\n                </Alert>\n              )}\n            </Paper>\n          </Grid>\n        </Grid>\n\n        {/* Messaggio se non ci sono bobine */}\n        {bobineCompatibili.length === 0 && bobineNonCompatibili.length === 0 && (\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            {bobinaSearchText ? 'Nessuna bobina trovata con i criteri di ricerca specificati.' : 'Non ci sono bobine disponibili.'}\n          </Alert>\n        )}\n      </Box>\n    );\n  };\n\n  return (\n    <Box>\n      {/* Form per la selezione del cavo */}\n      {renderCavoSelectionForm()}\n\n      {/* Opzioni di modifica */}\n      {renderModificaOptions()}\n\n      {/* Dialog per la visualizzazione dei dettagli completi del cavo */}\n      <Dialog\n        open={showCavoDetailsDialog}\n        onClose={() => setShowCavoDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>Dettagli completi del cavo</DialogTitle>\n        <DialogContent>\n          {selectedCavo && <CavoDetailsView cavo={selectedCavo} />}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowCavoDetailsDialog(false)}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog di conferma */}\n      <Dialog\n        open={showConfirmDialog}\n        onClose={() => setShowConfirmDialog(false)}\n      >\n        <DialogTitle>Conferma operazione</DialogTitle>\n        <DialogContent>\n          <Typography>{confirmDialogMessage}</Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button\n            onClick={() => setShowConfirmDialog(false)}\n            color=\"primary\"\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={() => {\n              setShowConfirmDialog(false);\n              if (confirmDialogAction) confirmDialogAction();\n            }}\n            color=\"primary\"\n            variant=\"contained\"\n            autoFocus\n          >\n            Conferma\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={handleCloseIncompatibleReelDialog}\n        cavo={incompatibleReelData.cavo}\n        bobina={incompatibleReelData.bobina}\n        onUpdateCavo={handleUpdateCavoForCompatibility}\n        onSelectAnotherReel={handleCloseIncompatibleReelDialog}\n        onContinueWithIncompatible={handleContinueWithIncompatible}\n      />\n    </Box>\n  );\n};\n\nexport default ModificaBobinaForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,UAAU,EACVC,cAAc,EACdC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,cAAc,EACdC,uBAAuB,EACvBC,IAAI,QACC,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,gBAAgB,IAAIC,oBAAoB,QACnC,qBAAqB;AAC5B,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0E,WAAW,EAAEC,cAAc,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC4E,aAAa,EAAEC,gBAAgB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8E,aAAa,EAAEC,gBAAgB,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACgF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACkF,IAAI,EAAEC,OAAO,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACoF,MAAM,EAAEC,SAAS,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACsF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACwF,YAAY,EAAEC,eAAe,CAAC,GAAGzF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0F,WAAW,EAAEC,cAAc,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4F,cAAc,EAAEC,iBAAiB,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACgG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACkG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnG,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAM,CAACoG,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACsG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvG,QAAQ,CAAC;IAAEwG,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK,CAAC,CAAC;;EAE9F;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC4G,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7G,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC8G,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/G,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACgH,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjH,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACdiH,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC/C,UAAU,CAAC,CAAC;;EAEhB;EACAlE,SAAS,CAAC,MAAM;IACd,IAAIuF,YAAY,EAAE;MAChB2B,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAAC3B,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM0B,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFvC,cAAc,CAAC,IAAI,CAAC;MACpByC,OAAO,CAACC,GAAG,CAAC,oCAAoClD,UAAU,KAAK,CAAC;;MAEhE;MACA,MAAMmD,QAAQ,GAAG,MAAMpE,WAAW,CAACqE,OAAO,CAACpD,UAAU,EAAE,IAAI,EAAE,YAAY,CAAC;MAC1EiD,OAAO,CAACC,GAAG,CAAC,YAAYC,QAAQ,CAACE,MAAM,kBAAkB,CAAC;;MAE1D;MACA,MAAMC,cAAc,GAAGH,QAAQ,CAACI,MAAM,CAAClB,IAAI,IACzCmB,UAAU,CAACnB,IAAI,CAACoB,eAAe,CAAC,GAAG,CAAC,IAAIpB,IAAI,CAACqB,mBAAmB,KAAK,YACvE,CAAC;MAED1C,OAAO,CAACsC,cAAc,CAAC;MACvBL,OAAO,CAACC,GAAG,CAAC,YAAYI,cAAc,CAACD,MAAM,iCAAiC,CAAC;IACjF,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/DzD,OAAO,CAAC,0CAA0C,IAAIyD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC/G,CAAC,SAAS;MACRrD,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMwC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAAC3B,YAAY,EAAE;IAEnB,IAAI;MACFX,gBAAgB,CAAC,IAAI,CAAC;MACtBuC,OAAO,CAACC,GAAG,CAAC,sCAAsClD,UAAU,KAAK,CAAC;;MAElE;MACA,MAAM8D,UAAU,GAAG,MAAM9E,gBAAgB,CAAC+E,SAAS,CAAC/D,UAAU,CAAC;MAC/DiD,OAAO,CAACC,GAAG,CAAC,YAAYY,UAAU,CAACT,MAAM,SAAS,CAAC;;MAEnD;MACA,MAAMW,oBAAoB,GAAGF,UAAU,CAACP,MAAM,CAACjB,MAAM,IACnDA,MAAM,CAAC2B,SAAS,KAAK5C,YAAY,CAAC4C,SAAS,IAC3C3B,MAAM,CAAC4B,OAAO,KAAK7C,YAAY,CAAC6C,OAAO,KACtC5B,MAAM,CAAC6B,YAAY,KAAK,aAAa,IAAI7B,MAAM,CAAC6B,YAAY,KAAK,QAAQ,CAC5E,CAAC;MAEDjD,SAAS,CAAC4C,UAAU,CAAC;MACrB1C,mBAAmB,CAAC4C,oBAAoB,CAAC;MACzCf,OAAO,CAACC,GAAG,CAAC,YAAYc,oBAAoB,CAACX,MAAM,qBAAqB,CAAC;IAC3E,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnEzD,OAAO,CAAC,8CAA8C,IAAIyD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACnH,CAAC,SAAS;MACRnD,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM0D,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAAC7C,WAAW,CAAC8C,IAAI,CAAC,CAAC,EAAE;MACvBnE,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFI,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM+B,IAAI,GAAG,MAAMtD,WAAW,CAACuF,WAAW,CAACtE,UAAU,EAAEuB,WAAW,CAAC8C,IAAI,CAAC,CAAC,CAAC;;MAE1E;MACA,IAAIb,UAAU,CAACnB,IAAI,CAACoB,eAAe,CAAC,IAAI,CAAC,IAAIpB,IAAI,CAACqB,mBAAmB,KAAK,YAAY,EAAE;QACtFxD,OAAO,CAAC,4CAA4C,CAAC;QACrDI,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEAgB,eAAe,CAACe,IAAI,CAAC;MACrBX,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;MACvBE,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;MACzBd,oBAAoB,CAAC,KAAK,CAAC;IAC7B,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3DzD,OAAO,CAAC,sCAAsC,IAAIyD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC3G,CAAC,SAAS;MACRvD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiE,gBAAgB,GAAIlC,IAAI,IAAK;IACjCf,eAAe,CAACe,IAAI,CAAC;IACrBX,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;IACvBE,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;IACzBd,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAM0D,kBAAkB,GAAIC,KAAK,IAAK;IACpC/C,iBAAiB,CAAC+C,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;IACrC/C,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMgD,kBAAkB,GAAIC,QAAQ,IAAK;IACvC5B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE2B,QAAQ,CAAC;;IAE5C;IACA,MAAMvC,MAAM,GAAGrB,MAAM,CAAC6D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKH,QAAQ,CAAC;IAEzD,IAAIvC,MAAM,IAAIjB,YAAY,EAAE;MAC1B;MACA,MAAM4D,YAAY,GAChBC,MAAM,CAAC5C,MAAM,CAAC2B,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKa,MAAM,CAAC7D,YAAY,CAAC4C,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFa,MAAM,CAAC5C,MAAM,CAAC4B,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKa,MAAM,CAAC7D,YAAY,CAAC6C,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC;MAEnF,IAAI,CAACY,YAAY,EAAE;QACjBhC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEZ,MAAM,CAAC;QACxDW,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE7B,YAAY,CAAC;;QAE3C;QACAe,uBAAuB,CAAC;UACtBC,IAAI,EAAEhB,YAAY;UAClBiB,MAAM,EAAEA;QACV,CAAC,CAAC;QACFJ,6BAA6B,CAAC,IAAI,CAAC;;QAEnC;QACAN,mBAAmB,CAACiD,QAAQ,CAAC;QAC7B7C,qBAAqB,CAACmD,eAAe,CAACN,QAAQ,CAAC,CAAC;QAChD;MACF;IACF;;IAEA;IACAjD,mBAAmB,CAACiD,QAAQ,CAAC;IAC7B7C,qBAAqB,CAACmD,eAAe,CAACN,QAAQ,CAAC,CAAC;EAClD,CAAC;;EAED;EACA,MAAMO,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAI,CAACrD,kBAAkB,CAACsC,IAAI,CAAC,CAAC,EAAE;MAC9BnE,OAAO,CAAC,sCAAsC,CAAC;MAC/C;IACF;;IAEA;IACA,MAAMmF,YAAY,GAAG,IAAIrF,UAAU,KAAK+B,kBAAkB,CAACsC,IAAI,CAAC,CAAC,EAAE;;IAEnE;IACA,MAAM/B,MAAM,GAAGrB,MAAM,CAAC6D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKK,YAAY,CAAC;IAC7D,IAAI,CAAC/C,MAAM,EAAE;MACXpC,OAAO,CAAC,UAAU6B,kBAAkB,2BAA2B,CAAC;MAChE;IACF;;IAEA;IACA6C,kBAAkB,CAACS,YAAY,CAAC;EAClC,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACjE,YAAY,EAAE;MACjBnB,OAAO,CAAC,sCAAsC,CAAC;MAC/C;IACF;IAEA,IAAI,CAACuB,cAAc,EAAE;MACnBvB,OAAO,CAAC,0CAA0C,CAAC;MACnD;IACF;;IAEA;IACA,IAAIuB,cAAc,KAAK,cAAc,IAAI,CAACE,gBAAgB,EAAE;MAC1DzB,OAAO,CAAC,yCAAyC,CAAC;MAClD;IACF;;IAEA;IACA,IAAI2D,OAAO,GAAG,EAAE;IAChB,IAAI0B,MAAM,GAAG,IAAI;IAEjB,IAAI9D,cAAc,KAAK,cAAc,EAAE;MACrC,MAAMa,MAAM,GAAGrB,MAAM,CAAC6D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKrD,gBAAgB,CAAC;MACjEkC,OAAO,GAAG,2CAA2CsB,eAAe,CAACxD,gBAAgB,CAAC,YAAYN,YAAY,CAACmE,OAAO,GAAG;MACzHD,MAAM,GAAGA,CAAA,KAAME,YAAY,CAAC9D,gBAAgB,CAAC;IAC/C,CAAC,MAAM,IAAIF,cAAc,KAAK,eAAe,EAAE;MAC7CoC,OAAO,GAAG,4DAA4DxC,YAAY,CAACmE,OAAO,GAAG;MAC7FD,MAAM,GAAGA,CAAA,KAAME,YAAY,CAAC,cAAc,CAAC;IAC7C,CAAC,MAAM,IAAIhE,cAAc,KAAK,sBAAsB,EAAE;MACpDoC,OAAO,GAAG,oEAAoExC,YAAY,CAACmE,OAAO,iGAAiG;MACnMD,MAAM,GAAGA,CAAA,KAAMG,oBAAoB,CAAC,CAAC;IACvC;IAEAhD,uBAAuB,CAACmB,OAAO,CAAC;IAChCjB,sBAAsB,CAAC,MAAM2C,MAAM,CAAC;IACpC/C,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMiD,YAAY,GAAG,MAAOZ,QAAQ,IAAK;IACvC,IAAI;MACFvE,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMvB,WAAW,CAAC0G,YAAY,CAACzF,UAAU,EAAEqB,YAAY,CAACmE,OAAO,EAAEX,QAAQ,CAAC;MAE1E5E,SAAS,CAAC,UAAU4E,QAAQ,KAAK,cAAc,GAAG,iBAAiB,GAAG,WAAW,eAAe,CAAC;;MAEjG;MACAvD,eAAe,CAAC,IAAI,CAAC;MACrBI,iBAAiB,CAAC,EAAE,CAAC;MACrBE,mBAAmB,CAAC,EAAE,CAAC;MACvBJ,cAAc,CAAC,EAAE,CAAC;;MAElB;MACAuB,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrEzD,OAAO,CAAC,gDAAgD,IAAIyD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACrH,CAAC,SAAS;MACRvD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoF,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFpF,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMvB,WAAW,CAAC4G,kBAAkB,CAAC3F,UAAU,EAAEqB,YAAY,CAACmE,OAAO,CAAC;MAEtEvF,SAAS,CAAC,0BAA0BoB,YAAY,CAACmE,OAAO,yBAAyB,CAAC;;MAElF;MACAlE,eAAe,CAAC,IAAI,CAAC;MACrBI,iBAAiB,CAAC,EAAE,CAAC;MACrBE,mBAAmB,CAAC,EAAE,CAAC;MACvBJ,cAAc,CAAC,EAAE,CAAC;;MAElB;MACAuB,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;MAC3EzD,OAAO,CAAC,sDAAsD,IAAIyD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC3H,CAAC,SAAS;MACRvD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsF,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACAtE,eAAe,CAAC,IAAI,CAAC;IACrBI,iBAAiB,CAAC,EAAE,CAAC;IACrBE,mBAAmB,CAAC,EAAE,CAAC;IACvBJ,cAAc,CAAC,EAAE,CAAC;IAClBV,oBAAoB,CAAC,KAAK,CAAC;;IAE3B;IACAb,SAAS,CAAC,sBAAsB,CAAC;EACnC,CAAC;;EAED;EACA,MAAM4F,iCAAiC,GAAGA,CAAA,KAAM;IAC9C3D,6BAA6B,CAAC,KAAK,CAAC;IACpCE,uBAAuB,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAK,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMwD,gCAAgC,GAAG,MAAAA,CAAA,KAAY;IACnD,MAAM;MAAEzD,IAAI;MAAEC;IAAO,CAAC,GAAGH,oBAAoB;IAC7C,IAAI,CAACE,IAAI,IAAI,CAACC,MAAM,EAAE;MACpBW,OAAO,CAACU,KAAK,CAAC,8CAA8C,EAAE;QAAEtB,IAAI;QAAEC;MAAO,CAAC,CAAC;MAC/EpC,OAAO,CAAC,6CAA6C,CAAC;MACtD;IACF;IAEA,IAAI;MACFI,UAAU,CAAC,IAAI,CAAC;MAChB2C,OAAO,CAACC,GAAG,CAAC,0CAA0Cb,IAAI,CAACmD,OAAO,iCAAiClD,MAAM,CAAC0C,SAAS,EAAE,CAAC;;MAEtH;MACA,MAAMjG,WAAW,CAACgH,0BAA0B,CAAC/F,UAAU,EAAEqC,IAAI,CAACmD,OAAO,EAAElD,MAAM,CAAC0C,SAAS,CAAC;;MAExF;MACA,MAAMgB,WAAW,GAAG,MAAMjH,WAAW,CAACuF,WAAW,CAACtE,UAAU,EAAEqC,IAAI,CAACmD,OAAO,CAAC;MAC3ElE,eAAe,CAAC0E,WAAW,CAAC;;MAE5B;MACApE,mBAAmB,CAACU,MAAM,CAAC0C,SAAS,CAAC;MACrChD,qBAAqB,CAACmD,eAAe,CAAC7C,MAAM,CAAC0C,SAAS,CAAC,CAAC;MAExD/E,SAAS,CAAC,4BAA4BoC,IAAI,CAACmD,OAAO,4CAA4CL,eAAe,CAAC7C,MAAM,CAAC0C,SAAS,CAAC,EAAE,CAAC;MAClIa,iCAAiC,CAAC,CAAC;;MAEnC;MACA7C,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjEzD,OAAO,CAAC,4CAA4C,IAAIyD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACjH,CAAC,SAAS;MACRvD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2F,8BAA8B,GAAG,MAAAA,CAAA,KAAY;IACjD,MAAM;MAAE5D,IAAI;MAAEC;IAAO,CAAC,GAAGH,oBAAoB;IAC7C,IAAI,CAACE,IAAI,IAAI,CAACC,MAAM,EAAE;MACpBW,OAAO,CAACU,KAAK,CAAC,uDAAuD,EAAE;QAAEtB,IAAI;QAAEC;MAAO,CAAC,CAAC;MACxFpC,OAAO,CAAC,sDAAsD,CAAC;MAC/D;IACF;IAEA,IAAI;MACFI,UAAU,CAAC,IAAI,CAAC;MAChB2C,OAAO,CAACC,GAAG,CAAC,iCAAiCZ,MAAM,CAAC0C,SAAS,aAAa3C,IAAI,CAACmD,OAAO,sCAAsC,CAAC;;MAE7H;MACA5D,mBAAmB,CAACU,MAAM,CAAC0C,SAAS,CAAC;MACrChD,qBAAqB,CAACmD,eAAe,CAAC7C,MAAM,CAAC0C,SAAS,CAAC,CAAC;MAExD/E,SAAS,CAAC,wBAAwBkF,eAAe,CAAC7C,MAAM,CAAC0C,SAAS,CAAC,4BAA4B3C,IAAI,CAACmD,OAAO,EAAE,CAAC;MAC9GK,iCAAiC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;MAC/EzD,OAAO,CAAC,0DAA0D,IAAIyD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC/H,CAAC,SAAS;MACRvD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6E,eAAe,GAAIe,QAAQ,IAAK;IACpC,IAAIA,QAAQ,KAAK,cAAc,EAAE,OAAO,cAAc;;IAEtD;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvC,OAAOD,QAAQ,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAOF,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMG,uBAAuB,GAAGA,CAAA,kBAC9BzG,OAAA,CAAC7D,GAAG;IAAAuK,QAAA,gBACF1G,OAAA,CAAC3D,UAAU;MAACsK,OAAO,EAAC,WAAW;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAAJ,QAAA,EAAC;IAEnE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGblH,OAAA,CAAC5D,KAAK;MAACwK,EAAE,EAAE;QAAEO,CAAC,EAAE,GAAG;QAAEN,EAAE,EAAE,CAAC;QAAEO,KAAK,EAAE;MAAO,CAAE;MAAAV,QAAA,eAC1C1G,OAAA,CAAC7D,GAAG;QAACyK,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEF,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,gBAChE1G,OAAA,CAAC3D,UAAU;UAACsK,OAAO,EAAC,WAAW;UAACC,EAAE,EAAE;YAAEW,EAAE,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAd,QAAA,EAAC;QAEjE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblH,OAAA,CAAC1D,SAAS;UACRmL,IAAI,EAAC,OAAO;UACZC,KAAK,EAAC,SAAS;UACff,OAAO,EAAC,UAAU;UAClB5B,KAAK,EAAEpD,WAAY;UACnBgG,QAAQ,EAAGC,CAAC,IAAKhG,cAAc,CAACgG,CAAC,CAAC9C,MAAM,CAACC,KAAK,CAAE;UAChD8C,WAAW,EAAC,yBAAyB;UACrCjB,EAAE,EAAE;YAAEkB,QAAQ,EAAE,CAAC;YAAEV,KAAK,EAAE,OAAO;YAAEG,EAAE,EAAE;UAAE;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACFlH,OAAA,CAACzD,MAAM;UACLoK,OAAO,EAAC,WAAW;UACnBoB,KAAK,EAAC,SAAS;UACfC,OAAO,EAAExD,oBAAqB;UAC9ByD,QAAQ,EAAEtH,WAAW,IAAI,CAACgB,WAAW,CAAC8C,IAAI,CAAC,CAAE;UAC7CyD,SAAS,EAAEvH,WAAW,gBAAGX,OAAA,CAACjD,gBAAgB;YAAC0K,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGlH,OAAA,CAAC1B,UAAU;YAAC6J,QAAQ,EAAC;UAAO;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1FO,IAAI,EAAC,OAAO;UACZb,EAAE,EAAE;YAAEY,QAAQ,EAAE,MAAM;YAAEY,MAAM,EAAE,MAAM;YAAEb,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,EACjD;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAGRzF,YAAY,iBACXzB,OAAA,CAAC7D,GAAG;UAACyK,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEQ,QAAQ,EAAE,CAAC;YAAEO,QAAQ,EAAE,QAAQ;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA7B,QAAA,gBAC7G1G,OAAA,CAAC7D,GAAG;YAACyK,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAb,QAAA,gBACxD1G,OAAA,CAAC3D,UAAU;cAACsK,OAAO,EAAC,WAAW;cAACC,EAAE,EAAE;gBAAEE,UAAU,EAAE,MAAM;gBAAE0B,UAAU,EAAE,QAAQ;gBAAEjB,EAAE,EAAE,CAAC;gBAAEY,QAAQ,EAAE;cAAU,CAAE;cAAAzB,QAAA,GAAC,QACtG,eAAA1G,OAAA;gBAAMyI,KAAK,EAAE;kBAAEV,KAAK,EAAE;gBAAU,CAAE;gBAAArB,QAAA,EAAEjF,YAAY,CAACmE;cAAO;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACblH,OAAA,CAACnD,OAAO;cAAC6L,WAAW,EAAC,UAAU;cAACC,QAAQ;cAAC/B,EAAE,EAAE;gBAAEgC,EAAE,EAAE;cAAI;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DlH,OAAA,CAAC7D,GAAG;cAACyK,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEuB,GAAG,EAAE,CAAC;gBAAER,QAAQ,EAAE,QAAQ;gBAAEC,QAAQ,EAAE;cAAS,CAAE;cAAA5B,QAAA,gBACjG1G,OAAA,CAAC7D,GAAG;gBAACyK,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEkB,UAAU,EAAE;gBAAS,CAAE;gBAAA9B,QAAA,gBACvE1G,OAAA,CAAC3D,UAAU;kBAACsK,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEqB,QAAQ,EAAE,QAAQ;oBAAEZ,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzGlH,OAAA,CAAC3D,UAAU;kBAACsK,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEuB,QAAQ,EAAE;kBAAS,CAAE;kBAAAzB,QAAA,EAAEjF,YAAY,CAAC4C,SAAS,IAAI;gBAAK;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnG,CAAC,eACNlH,OAAA,CAAC7D,GAAG;gBAACyK,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEkB,UAAU,EAAE;gBAAS,CAAE;gBAAA9B,QAAA,gBACvE1G,OAAA,CAAC3D,UAAU;kBAACsK,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEqB,QAAQ,EAAE,QAAQ;oBAAEZ,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzGlH,OAAA,CAAC3D,UAAU;kBAACsK,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEuB,QAAQ,EAAE;kBAAS,CAAE;kBAAAzB,QAAA,EAAEjF,YAAY,CAAC6C,OAAO,IAAI;gBAAK;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjG,CAAC,eACNlH,OAAA,CAAC7D,GAAG;gBAACyK,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEkB,UAAU,EAAE;gBAAS,CAAE;gBAAA9B,QAAA,gBACvE1G,OAAA,CAAC3D,UAAU;kBAACsK,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEqB,QAAQ,EAAE,QAAQ;oBAAEZ,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1GlH,OAAA,CAAC3D,UAAU;kBAACsK,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEuB,QAAQ,EAAE;kBAAS,CAAE;kBAAAzB,QAAA,GAAEjF,YAAY,CAACoC,eAAe,IAAI,KAAK,EAAC,IAAE;gBAAA;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3G,CAAC,eACNlH,OAAA,CAAC7D,GAAG;gBAACyK,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEkB,UAAU,EAAE;gBAAS,CAAE;gBAAA9B,QAAA,gBACvE1G,OAAA,CAAC3D,UAAU;kBAACsK,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEqB,QAAQ,EAAE,QAAQ;oBAAEZ,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1GlH,OAAA,CAAC5B,IAAI;kBACHqJ,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAEjG,YAAY,CAACqC,mBAAmB,IAAI,KAAM;kBACjDiE,KAAK,EAAC,SAAS;kBACfnB,EAAE,EAAE;oBAAEwB,MAAM,EAAE,MAAM;oBAAE,kBAAkB,EAAE;sBAAEU,EAAE,EAAE,CAAC;sBAAEC,EAAE,EAAE,CAAC;sBAAEZ,QAAQ,EAAE;oBAAU;kBAAE;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLzF,YAAY,CAAC2D,SAAS,iBACrBpF,OAAA,CAAAE,SAAA;YAAAwG,QAAA,gBACE1G,OAAA,CAACnD,OAAO;cAAC6L,WAAW,EAAC,UAAU;cAACC,QAAQ;cAAC/B,EAAE,EAAE;gBAAEgC,EAAE,EAAE;cAAE;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DlH,OAAA,CAAC7D,GAAG;cAACyK,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAZ,QAAA,gBACjD1G,OAAA,CAAC3D,UAAU;gBAACsK,OAAO,EAAC,WAAW;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAE0B,UAAU,EAAE,QAAQ;kBAAEjB,EAAE,EAAE,CAAC;kBAAEY,QAAQ,EAAE,SAAS;kBAAEJ,KAAK,EAAE;gBAAU,CAAE;gBAAArB,QAAA,GAAC,kBAC9G,eAAA1G,OAAA;kBAAA0G,QAAA,EAAOjF,YAAY,CAAC2D,SAAS,KAAK,cAAc,GAAG,OAAO,GAAGG,eAAe,CAAC9D,YAAY,CAAC2D,SAAS;gBAAC;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC,EACZ,CAAC,MAAM;gBACN,IAAIzF,YAAY,CAAC2D,SAAS,KAAK,cAAc,EAAE;kBAC7C,oBACEpF,OAAA,CAAC7D,GAAG;oBAACyK,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,eACvE1G,OAAA,CAAC3D,UAAU;sBAACsK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEuB,QAAQ,EAAE,QAAQ;wBAAEJ,KAAK,EAAE,gBAAgB;wBAAEiB,SAAS,EAAE;sBAAS,CAAE;sBAAAtC,QAAA,EAAC;oBAEtG;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAEV;gBAEA,MAAMxE,MAAM,GAAGrB,MAAM,CAAC6D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAK3D,YAAY,CAAC2D,SAAS,CAAC;gBACvE,OAAO1C,MAAM,gBACX1C,OAAA,CAAC7D,GAAG;kBAACyK,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEuB,GAAG,EAAE,CAAC;oBAAER,QAAQ,EAAE,QAAQ;oBAAEC,QAAQ,EAAE;kBAAS,CAAE;kBAAA5B,QAAA,gBACjG1G,OAAA,CAAC7D,GAAG;oBAACyK,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,gBACvE1G,OAAA,CAAC3D,UAAU;sBAACsK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEE,UAAU,EAAE,QAAQ;wBAAEqB,QAAQ,EAAE,QAAQ;wBAAEZ,EAAE,EAAE;sBAAI,CAAE;sBAAAb,QAAA,EAAC;oBAAQ;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC5GlH,OAAA,CAAC3D,UAAU;sBAACsK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEuB,QAAQ,EAAE,QAAQ;wBAAEJ,KAAK,EAAE,cAAc;wBAAEjB,UAAU,EAAE;sBAAO,CAAE;sBAAAJ,QAAA,GAC/FhE,MAAM,CAACuG,aAAa,IAAI,CAAC,EAAC,IAC7B;oBAAA;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNlH,OAAA,CAAC7D,GAAG;oBAACyK,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,gBACvE1G,OAAA,CAAC3D,UAAU;sBAACsK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEE,UAAU,EAAE,QAAQ;wBAAEqB,QAAQ,EAAE,QAAQ;wBAAEZ,EAAE,EAAE;sBAAI,CAAE;sBAAAb,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC1GlH,OAAA,CAAC5B,IAAI;sBACHqJ,IAAI,EAAC,OAAO;sBACZC,KAAK,EAAEhF,MAAM,CAAC6B,YAAY,IAAI,KAAM;sBACpCwD,KAAK,EAAErF,MAAM,CAAC6B,YAAY,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;sBACrEqC,EAAE,EAAE;wBAAEwB,MAAM,EAAE,MAAM;wBAAE,kBAAkB,EAAE;0BAAEU,EAAE,EAAE,CAAC;0BAAEC,EAAE,EAAE,CAAC;0BAAEZ,QAAQ,EAAE;wBAAU;sBAAE;oBAAE;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACJ,IAAI;cACV,CAAC,EAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,eACN,CACH,EAGAnF,gBAAgB,IAAIA,gBAAgB,MAAKN,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE2D,SAAS,kBAC/DpF,OAAA,CAAAE,SAAA;YAAAwG,QAAA,gBACE1G,OAAA,CAACnD,OAAO;cAAC6L,WAAW,EAAC,UAAU;cAACC,QAAQ;cAAC/B,EAAE,EAAE;gBAAEgC,EAAE,EAAE;cAAE;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DlH,OAAA,CAAC7D,GAAG;cAACyK,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAZ,QAAA,gBACjD1G,OAAA,CAAC3D,UAAU;gBAACsK,OAAO,EAAC,WAAW;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAE0B,UAAU,EAAE,QAAQ;kBAAEjB,EAAE,EAAE,CAAC;kBAAEY,QAAQ,EAAE,SAAS;kBAAEJ,KAAK,EAAE;gBAAU,CAAE;gBAAArB,QAAA,GAAC,sBAC1G,eAAA1G,OAAA;kBAAA0G,QAAA,EAAO3E,gBAAgB,KAAK,cAAc,GAAG,OAAO,GAAGwD,eAAe,CAACxD,gBAAgB;gBAAC;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC,EACZ,CAAC,MAAM;gBACN,IAAInF,gBAAgB,KAAK,cAAc,EAAE;kBACvC,oBACE/B,OAAA,CAAC7D,GAAG;oBAACyK,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,eACvE1G,OAAA,CAAC3D,UAAU;sBAACsK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEuB,QAAQ,EAAE,QAAQ;wBAAEJ,KAAK,EAAE,gBAAgB;wBAAEiB,SAAS,EAAE;sBAAS,CAAE;sBAAAtC,QAAA,EAAC;oBAEtG;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAEV;gBAEA,MAAMxE,MAAM,GAAGrB,MAAM,CAAC6D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKrD,gBAAgB,CAAC;gBACjE,IAAI,CAACW,MAAM,EAAE,OAAO,IAAI;;gBAExB;gBACA,MAAM2C,YAAY,GAAG5D,YAAY,IAC/B6D,MAAM,CAAC5C,MAAM,CAAC2B,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKa,MAAM,CAAC7D,YAAY,CAAC4C,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFa,MAAM,CAAC5C,MAAM,CAAC4B,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKa,MAAM,CAAC7D,YAAY,CAAC6C,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC;gBAEnF,oBACEzE,OAAA,CAAC7D,GAAG;kBAACyK,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEuB,GAAG,EAAE,CAAC;oBAAER,QAAQ,EAAE,QAAQ;oBAAEC,QAAQ,EAAE;kBAAS,CAAE;kBAAA5B,QAAA,gBACjG1G,OAAA,CAAC7D,GAAG;oBAACyK,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,gBACvE1G,OAAA,CAAC3D,UAAU;sBAACsK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEE,UAAU,EAAE,QAAQ;wBAAEqB,QAAQ,EAAE,QAAQ;wBAAEZ,EAAE,EAAE;sBAAI,CAAE;sBAAAb,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACzGlH,OAAA,CAAC3D,UAAU;sBAACsK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEuB,QAAQ,EAAE,QAAQ;wBAAEJ,KAAK,EAAE1C,YAAY,GAAG,cAAc,GAAG;sBAAa,CAAE;sBAAAqB,QAAA,EACzGhE,MAAM,CAAC2B,SAAS,IAAI;oBAAK;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNlH,OAAA,CAAC7D,GAAG;oBAACyK,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,gBACvE1G,OAAA,CAAC3D,UAAU;sBAACsK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEE,UAAU,EAAE,QAAQ;wBAAEqB,QAAQ,EAAE,QAAQ;wBAAEZ,EAAE,EAAE;sBAAI,CAAE;sBAAAb,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACzGlH,OAAA,CAAC3D,UAAU;sBAACsK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEuB,QAAQ,EAAE,QAAQ;wBAAEJ,KAAK,EAAE1C,YAAY,GAAG,cAAc,GAAG;sBAAa,CAAE;sBAAAqB,QAAA,EACzGhE,MAAM,CAAC4B,OAAO,IAAI;oBAAK;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNlH,OAAA,CAAC7D,GAAG;oBAACyK,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,gBACvE1G,OAAA,CAAC3D,UAAU;sBAACsK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEE,UAAU,EAAE,QAAQ;wBAAEqB,QAAQ,EAAE,QAAQ;wBAAEZ,EAAE,EAAE;sBAAI,CAAE;sBAAAb,QAAA,EAAC;oBAAQ;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC5GlH,OAAA,CAAC3D,UAAU;sBAACsK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEuB,QAAQ,EAAE,QAAQ;wBAAEJ,KAAK,EAAE,cAAc;wBAAEjB,UAAU,EAAE;sBAAO,CAAE;sBAAAJ,QAAA,GAC/FhE,MAAM,CAACuG,aAAa,IAAI,CAAC,EAAC,IAC7B;oBAAA;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNlH,OAAA,CAAC7D,GAAG;oBAACyK,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,gBACvE1G,OAAA,CAAC3D,UAAU;sBAACsK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEE,UAAU,EAAE,QAAQ;wBAAEqB,QAAQ,EAAE,QAAQ;wBAAEZ,EAAE,EAAE;sBAAI,CAAE;sBAAAb,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC1GlH,OAAA,CAAC5B,IAAI;sBACHqJ,IAAI,EAAC,OAAO;sBACZC,KAAK,EAAEhF,MAAM,CAAC6B,YAAY,IAAI,KAAM;sBACpCwD,KAAK,EAAErF,MAAM,CAAC6B,YAAY,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;sBACrEqC,EAAE,EAAE;wBAAEwB,MAAM,EAAE,MAAM;wBAAE,kBAAkB,EAAE;0BAAEU,EAAE,EAAE,CAAC;0BAAEC,EAAE,EAAE,CAAC;0BAAEZ,QAAQ,EAAE;wBAAU;sBAAE;oBAAE;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,EACL,CAAC7B,YAAY,iBACZrF,OAAA,CAAC7D,GAAG;oBAACyK,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,eACvE1G,OAAA,CAAC5B,IAAI;sBACHqJ,IAAI,EAAC,OAAO;sBACZC,KAAK,EAAC,iBAAiB;sBACvBK,KAAK,EAAC,OAAO;sBACbpB,OAAO,EAAC,UAAU;sBAClBC,EAAE,EAAE;wBAAEwB,MAAM,EAAE,MAAM;wBAAE,kBAAkB,EAAE;0BAAEU,EAAE,EAAE,CAAC;0BAAEC,EAAE,EAAE,CAAC;0BAAEZ,QAAQ,EAAE;wBAAU;sBAAE;oBAAE;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAEV,CAAC,EAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,eACN,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRlH,OAAA,CAAC5D,KAAK;MAACwK,EAAE,EAAE;QAAEO,CAAC,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAO,CAAE;MAAAV,QAAA,gBACnC1G,OAAA,CAAC3D,UAAU;QAACsK,OAAO,EAAC,WAAW;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAE/C;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZvG,WAAW,gBACVX,OAAA,CAAC7D,GAAG;QAACyK,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAE6B,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAzC,QAAA,eAC5D1G,OAAA,CAACjD,gBAAgB;UAAC0K,IAAI,EAAE;QAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,GACJ/F,IAAI,CAACsC,MAAM,KAAK,CAAC,gBACnBzD,OAAA,CAAClD,KAAK;QAACsM,QAAQ,EAAC,MAAM;QAACxC,EAAE,EAAE;UAAEmC,EAAE,EAAE;QAAI,CAAE;QAAArC,QAAA,eACrC1G,OAAA,CAAC3D,UAAU;UAACsK,OAAO,EAAC,SAAS;UAAAD,QAAA,EAAC;QAAoC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC,gBAERlH,OAAA,CAACzC,cAAc;QAAC8L,SAAS,EAAEjN,KAAM;QAACuK,OAAO,EAAC,UAAU;QAACC,EAAE,EAAE;UAAE0C,SAAS,EAAE,OAAO;UAAEhB,QAAQ,EAAE,MAAM;UAAElB,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,eAC/G1G,OAAA,CAAC5C,KAAK;UAACqK,IAAI,EAAC,OAAO;UAAC8B,YAAY;UAAA7C,QAAA,gBAC9B1G,OAAA,CAACxC,SAAS;YAAAkJ,QAAA,eACR1G,OAAA,CAACvC,QAAQ;cAACmJ,EAAE,EAAE;gBAAE,MAAM,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAEiC,EAAE,EAAE,CAAC;kBAAES,OAAO,EAAE;gBAAU;cAAE,CAAE;cAAA9C,QAAA,gBAC1E1G,OAAA,CAAC1C,SAAS;gBAAAoJ,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BlH,OAAA,CAAC1C,SAAS;gBAAAoJ,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChClH,OAAA,CAAC1C,SAAS;gBAAAoJ,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjClH,OAAA,CAAC1C,SAAS;gBAAAoJ,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BlH,OAAA,CAAC1C,SAAS;gBAAAoJ,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7BlH,OAAA,CAAC1C,SAAS;gBAAAoJ,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BlH,OAAA,CAAC1C,SAAS;gBAACmM,KAAK,EAAC,QAAQ;gBAAC7C,EAAE,EAAE;kBAAEQ,KAAK,EAAE;gBAAO,CAAE;gBAAAV,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZlH,OAAA,CAAC3C,SAAS;YAAAqJ,QAAA,EACPvF,IAAI,CAACuI,GAAG,CAAEjH,IAAI,iBACbzC,OAAA,CAACvC,QAAQ;cAEPkM,KAAK;cACL3B,OAAO,EAAEA,CAAA,KAAMrD,gBAAgB,CAAClC,IAAI,CAAE;cACtCmE,EAAE,EAAE;gBACFgD,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE;kBAAEJ,OAAO,EAAE;gBAAU,CAAC;gBACjC,MAAM,EAAE;kBAAET,EAAE,EAAE;gBAAI;cACpB,CAAE;cAAArC,QAAA,gBAEF1G,OAAA,CAAC1C,SAAS;gBAACsJ,EAAE,EAAE;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,EAAEjE,IAAI,CAACmD;cAAO;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnElH,OAAA,CAAC1C,SAAS;gBAAAoJ,QAAA,EAAEjE,IAAI,CAAC4B,SAAS,IAAI;cAAK;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChDlH,OAAA,CAAC1C,SAAS;gBAAAoJ,QAAA,EAAEjE,IAAI,CAAC6B,OAAO,IAAI;cAAK;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9ClH,OAAA,CAAC1C,SAAS;gBAAAoJ,QAAA,GAAEjE,IAAI,CAACoB,eAAe,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACxDlH,OAAA,CAAC1C,SAAS;gBAAAoJ,QAAA,EACPjE,IAAI,CAAC2C,SAAS,GAAI3C,IAAI,CAAC2C,SAAS,KAAK,cAAc,GAAG,OAAO,GAAGG,eAAe,CAAC9C,IAAI,CAAC2C,SAAS,CAAC,GAAI;cAAO;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClG,CAAC,eACZlH,OAAA,CAAC1C,SAAS;gBAAAoJ,QAAA,eACR1G,OAAA,CAAC5B,IAAI;kBACHqJ,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAC,YAAY;kBAClBK,KAAK,EAAC,SAAS;kBACfnB,EAAE,EAAE;oBAAEwB,MAAM,EAAE,MAAM;oBAAE,kBAAkB,EAAE;sBAAEU,EAAE,EAAE,CAAC;sBAAEC,EAAE,EAAE,CAAC;sBAAEZ,QAAQ,EAAE;oBAAS;kBAAE;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZlH,OAAA,CAAC1C,SAAS;gBAACmM,KAAK,EAAC,QAAQ;gBAAA/C,QAAA,eACvB1G,OAAA,CAACnC,UAAU;kBACT4J,IAAI,EAAC,OAAO;kBACZO,OAAO,EAAGJ,CAAC,IAAK;oBACdA,CAAC,CAACiC,eAAe,CAAC,CAAC;oBACnBnI,eAAe,CAACe,IAAI,CAAC;oBACrBS,wBAAwB,CAAC,IAAI,CAAC;kBAChC,CAAE;kBAAAwD,QAAA,eAEF1G,OAAA,CAAClB,QAAQ;oBAACqJ,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAnCPzE,IAAI,CAACmD,OAAO;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoCT,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CACjB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;;EAID;EACA,MAAM4C,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACrI,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEzB,OAAA,CAAC5D,KAAK;MAACwK,EAAE,EAAE;QAAEO,CAAC,EAAE,CAAC;QAAEN,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACzB1G,OAAA,CAAC3D,UAAU;QAACsK,OAAO,EAAC,IAAI;QAACoD,YAAY;QAAArD,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEblH,OAAA,CAACrC,UAAU;QACToH,KAAK,EAAElD,cAAe;QACtB8F,QAAQ,EAAE/C,kBAAmB;QAAA8B,QAAA,gBAE7B1G,OAAA,CAACpC,gBAAgB;UACfmH,KAAK,EAAC,cAAc;UACpBiF,OAAO,eAAEhK,OAAA,CAACtC,KAAK;YAAAqJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBQ,KAAK,EAAC;QAAsB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACFlH,OAAA,CAACpC,gBAAgB;UACfmH,KAAK,EAAC,eAAe;UACrBiF,OAAO,eAAEhK,OAAA,CAACtC,KAAK;YAAAqJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBQ,KAAK,EAAC;QAAwB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACFlH,OAAA,CAACpC,gBAAgB;UACfmH,KAAK,EAAC,sBAAsB;UAC5BiF,OAAO,eAAEhK,OAAA,CAACtC,KAAK;YAAAqJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBQ,KAAK,EAAC;QAAuB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,EAEZrF,cAAc,KAAK,cAAc,IAAIoI,qBAAqB,CAAC,CAAC,EAE5DpI,cAAc,KAAK,eAAe,iBACjC7B,OAAA,CAAClD,KAAK;QAACsM,QAAQ,EAAC,MAAM;QAACxC,EAAE,EAAE;UAAEsD,EAAE,EAAE;QAAE,CAAE;QAAAxD,QAAA,EAAC;MAItC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,EAEArF,cAAc,KAAK,sBAAsB,iBACxC7B,OAAA,CAAClD,KAAK;QAACsM,QAAQ,EAAC,SAAS;QAACxC,EAAE,EAAE;UAAEsD,EAAE,EAAE;QAAE,CAAE;QAAAxD,QAAA,gBACtC1G,OAAA,CAAC3D,UAAU;UAACsK,OAAO,EAAC,OAAO;UAACG,UAAU,EAAC,MAAM;UAAAJ,QAAA,EAAC;QAE9C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblH,OAAA,CAAC3D,UAAU;UAACsK,OAAO,EAAC,OAAO;UAAAD,QAAA,GAAC,mDACkB,eAAA1G,OAAA;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,yCAChB,eAAAlH,OAAA;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,kDACG,eAAAlH,OAAA;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,2EAEnD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACR,eAEDlH,OAAA,CAAC7D,GAAG;QAACyK,EAAE,EAAE;UAAEsD,EAAE,EAAE,CAAC;UAAE7C,OAAO,EAAE,MAAM;UAAE6B,cAAc,EAAE,UAAU;UAAEL,GAAG,EAAE;QAAE,CAAE;QAAAnC,QAAA,gBACtE1G,OAAA,CAACzD,MAAM;UACLoK,OAAO,EAAC,UAAU;UAClBoB,KAAK,EAAC,WAAW;UACjBG,SAAS,eAAElI,OAAA,CAACtB,UAAU;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Bc,OAAO,EAAEhC,eAAgB;UACzBiC,QAAQ,EAAExH,OAAQ;UAAAiG,QAAA,EACnB;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlH,OAAA,CAACzD,MAAM;UACLoK,OAAO,EAAC,WAAW;UACnBoB,KAAK,EAAC,SAAS;UACfG,SAAS,eAAElI,OAAA,CAACxB,QAAQ;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBc,OAAO,EAAEtC,UAAW;UACpBuC,QAAQ,EAAExH,OAAO,IAAI,CAACoB,cAAc,IAAKA,cAAc,KAAK,cAAc,IAAI,CAACE,gBAAkB;UAAA2E,QAAA,EAClG;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEZ,CAAC;;EAED;EACA,MAAM+C,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAIpJ,aAAa,EAAE;MACjB,oBACEb,OAAA,CAAC7D,GAAG;QAACyK,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAE6B,cAAc,EAAE,QAAQ;UAAEgB,EAAE,EAAE;QAAE,CAAE;QAAAxD,QAAA,eAC5D1G,OAAA,CAACjD,gBAAgB;UAAAgK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAEV;;IAEA;IACA,MAAMiD,cAAc,GAAG9I,MAAM,CAACsC,MAAM,CAACjB,MAAM,IAAI;MAC7C,MAAM0H,WAAW,GAAGnI,gBAAgB,CAACoI,WAAW,CAAC,CAAC;MAClD,OAAO,CAACpI,gBAAgB,IACtBsD,eAAe,CAAC7C,MAAM,CAAC0C,SAAS,CAAC,CAACiF,WAAW,CAAC,CAAC,CAAC9D,QAAQ,CAAC6D,WAAW,CAAC,IACrE9E,MAAM,CAAC5C,MAAM,CAAC2B,SAAS,IAAI,EAAE,CAAC,CAACgG,WAAW,CAAC,CAAC,CAAC9D,QAAQ,CAAC6D,WAAW,CAAC,IAClE9E,MAAM,CAAC5C,MAAM,CAAC4B,OAAO,IAAI,EAAE,CAAC,CAAC+F,WAAW,CAAC,CAAC,CAAC9D,QAAQ,CAAC6D,WAAW,CAAC;IACpE,CAAC,CAAC;;IAEF;IACA,MAAME,iBAAiB,GAAG7I,YAAY,GAClC0I,cAAc,CAACxG,MAAM,CAACjB,MAAM,IAC1B4C,MAAM,CAAC5C,MAAM,CAAC2B,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKa,MAAM,CAAC7D,YAAY,CAAC4C,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFa,MAAM,CAAC5C,MAAM,CAAC4B,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKa,MAAM,CAAC7D,YAAY,CAAC6C,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAChF/B,MAAM,CAAC6B,YAAY,KAAK,aAAa,IAAI7B,MAAM,CAAC6B,YAAY,KAAK,QAAQ,CAAC,CAAC,GAC9E,EAAE;IAEN,MAAMgG,oBAAoB,GAAG9I,YAAY,GACrC0I,cAAc,CAACxG,MAAM,CAACjB,MAAM,IAC1B,EAAE4C,MAAM,CAAC5C,MAAM,CAAC2B,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKa,MAAM,CAAC7D,YAAY,CAAC4C,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFa,MAAM,CAAC5C,MAAM,CAAC4B,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKa,MAAM,CAAC7D,YAAY,CAAC6C,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC,KACnF/B,MAAM,CAAC6B,YAAY,KAAK,aAAa,IAAI7B,MAAM,CAAC6B,YAAY,KAAK,QAAQ,CAAC,CAAC,GAC9E,EAAE;;IAEN;IACA+F,iBAAiB,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEtF,CAAC,KAAKA,CAAC,CAAC8D,aAAa,GAAGwB,CAAC,CAACxB,aAAa,CAAC;IACnEsB,oBAAoB,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEtF,CAAC,KAAKA,CAAC,CAAC8D,aAAa,GAAGwB,CAAC,CAACxB,aAAa,CAAC;IAEtE,oBACEjJ,OAAA,CAAC7D,GAAG;MAACyK,EAAE,EAAE;QAAEsD,EAAE,EAAE;MAAE,CAAE;MAAAxD,QAAA,gBAEjB1G,OAAA,CAAC7D,GAAG;QAACyK,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,eACjB1G,OAAA,CAAC1D,SAAS;UACRoO,SAAS;UACThD,KAAK,EAAC,4CAA4C;UAClD3C,KAAK,EAAE9C,gBAAiB;UACxB0F,QAAQ,EAAGC,CAAC,IAAK1F,mBAAmB,CAAC0F,CAAC,CAAC9C,MAAM,CAACC,KAAK,CAAE;UACrD4F,UAAU,EAAE;YACVC,cAAc,eACZ5K,OAAA,CAAClC,cAAc;cAAC+M,QAAQ,EAAC,OAAO;cAAAnE,QAAA,eAC9B1G,OAAA,CAAC1B,UAAU;gBAAAyI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAEpB,CAAE;UACFN,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNlH,OAAA,CAAC7D,GAAG;QAACyK,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEuB,GAAG,EAAE;QAAE,CAAE;QAAAnC,QAAA,gBAChE1G,OAAA,CAAC1D,SAAS;UACRoL,KAAK,EAAC,iCAAiC;UACvC3C,KAAK,EAAE5C,kBAAmB;UAC1BwF,QAAQ,EAAGC,CAAC,IAAKxF,qBAAqB,CAACwF,CAAC,CAAC9C,MAAM,CAACC,KAAK,CAAE;UACvD+F,IAAI,EAAC,QAAQ;UACblE,EAAE,EAAE;YAAEQ,KAAK,EAAE;UAAQ;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACFlH,OAAA,CAACzD,MAAM;UACLoK,OAAO,EAAC,UAAU;UAClBqB,OAAO,EAAExC,0BAA2B;UACpCyC,QAAQ,EAAE,CAAC9F,kBAAkB,CAACsC,IAAI,CAAC,CAAE;UAAAiC,QAAA,EACtC;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLnF,gBAAgB,iBACf/B,OAAA,CAAClD,KAAK;QAACsM,QAAQ,EAAC,SAAS;QAACxC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,GAAC,sBACnB,eAAA1G,OAAA;UAAA0G,QAAA,EAASnB,eAAe,CAACxD,gBAAgB;QAAC;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CACR,eAGDlH,OAAA,CAACxD,IAAI;QAACuO,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAtE,QAAA,gBAEzB1G,OAAA,CAACxD,IAAI;UAACyO,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAzE,QAAA,eACvB1G,OAAA,CAAC5D,KAAK;YAACuK,OAAO,EAAC,UAAU;YAACC,EAAE,EAAE;cAAEO,CAAC,EAAE,CAAC;cAAEiB,MAAM,EAAE;YAAO,CAAE;YAAA1B,QAAA,gBACrD1G,OAAA,CAAC3D,UAAU;cAACsK,OAAO,EAAC,WAAW;cAACC,EAAE,EAAE;gBAAEE,UAAU,EAAE,MAAM;gBAAED,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,EAAC;YAEnE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAEZoD,iBAAiB,CAAC7G,MAAM,GAAG,CAAC,gBAC3BzD,OAAA,CAAAE,SAAA;cAAAwG,QAAA,gBACE1G,OAAA,CAAC7D,GAAG;gBAACyK,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEF,KAAK,EAAE,MAAM;kBAAE2B,EAAE,EAAE,GAAG;kBAAED,EAAE,EAAE,GAAG;kBAAEU,OAAO,EAAE,SAAS;kBAAE4B,YAAY,EAAE,CAAC;kBAAEvE,EAAE,EAAE;gBAAE,CAAE;gBAAAH,QAAA,gBAC9H1G,OAAA,CAAC7D,GAAG;kBAACyK,EAAE,EAAE;oBAAEQ,KAAK,EAAE,MAAM;oBAAEG,EAAE,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eAChC1G,OAAA,CAAC3D,UAAU;oBAACsK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3F,CAAC,eACNlH,OAAA,CAAC7D,GAAG;kBAACyK,EAAE,EAAE;oBAAEQ,KAAK,EAAE,OAAO;oBAAEG,EAAE,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eACjC1G,OAAA,CAAC3D,UAAU;oBAACsK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAI;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7F,CAAC,eACNlH,OAAA,CAAC7D,GAAG;kBAACyK,EAAE,EAAE;oBAAEQ,KAAK,EAAE,OAAO;oBAAEG,EAAE,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eACjC1G,OAAA,CAAC3D,UAAU;oBAACsK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9F,CAAC,eACNlH,OAAA,CAAC7D,GAAG;kBAACyK,EAAE,EAAE;oBAAEQ,KAAK,EAAE,OAAO;oBAAEG,EAAE,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eACjC1G,OAAA,CAAC3D,UAAU;oBAACsK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAO;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG,CAAC,eACNlH,OAAA,CAAC7D,GAAG;kBAACyK,EAAE,EAAE;oBAAEkB,QAAQ,EAAE;kBAAE,CAAE;kBAAApB,QAAA,eACvB1G,OAAA,CAAC3D,UAAU;oBAACsK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlH,OAAA,CAACjC,IAAI;gBAAC6I,EAAE,EAAE;kBAAE0C,SAAS,EAAE,OAAO;kBAAEhB,QAAQ,EAAE,MAAM;kBAAEkB,OAAO,EAAE;gBAAmB,CAAE;gBAAA9C,QAAA,EAC7E4D,iBAAiB,CAACZ,GAAG,CAAEhH,MAAM,iBAC5B1C,OAAA,CAAChC,QAAQ;kBAEPqN,cAAc;kBACdC,eAAe,eACbtL,OAAA,CAACnC,UAAU;oBACT0N,IAAI,EAAC,KAAK;oBACV9D,IAAI,EAAC,OAAO;oBACZO,OAAO,EAAEA,CAAA,KAAMhD,kBAAkB,CAACtC,MAAM,CAAC0C,SAAS,CAAE;oBAAAsB,QAAA,eAEpD1G,OAAA,CAAChB,oBAAoB;sBAAC+I,KAAK,EAAC;oBAAS;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CACb;kBACDN,EAAE,EAAE;oBACF4C,OAAO,EAAEzH,gBAAgB,KAAKW,MAAM,CAAC0C,SAAS,GAAG,yBAAyB,GAAG,SAAS;oBACtFgG,YAAY,EAAE,KAAK;oBACnBvE,EAAE,EAAE,GAAG;oBACP2E,MAAM,EAAEzJ,gBAAgB,KAAKW,MAAM,CAAC0C,SAAS,GAAG,mBAAmB,GAAG;kBACxE,CAAE;kBAAAsB,QAAA,eAEF1G,OAAA,CAAC9B,cAAc;oBACbuN,KAAK;oBACLzD,OAAO,EAAEA,CAAA,KAAMhD,kBAAkB,CAACtC,MAAM,CAAC0C,SAAS,CAAE;oBAAAsB,QAAA,eAEpD1G,OAAA,CAAC7D,GAAG;sBAACyK,EAAE,EAAE;wBAAES,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEF,KAAK,EAAE,MAAM;wBAAE2B,EAAE,EAAE;sBAAI,CAAE;sBAAArC,QAAA,gBACzE1G,OAAA,CAAC7D,GAAG;wBAACyK,EAAE,EAAE;0BAAEQ,KAAK,EAAE,MAAM;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAb,QAAA,eAChC1G,OAAA,CAAC3D,UAAU;0BAACsK,OAAO,EAAC,OAAO;0BAACC,EAAE,EAAE;4BAAEE,UAAU,EAAE,MAAM;4BAAEqB,QAAQ,EAAE;0BAAS,CAAE;0BAAAzB,QAAA,EACxEnB,eAAe,CAAC7C,MAAM,CAAC0C,SAAS;wBAAC;0BAAA2B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNlH,OAAA,CAAC7D,GAAG;wBAACyK,EAAE,EAAE;0BAAEQ,KAAK,EAAE,OAAO;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAb,QAAA,eACjC1G,OAAA,CAAC3D,UAAU;0BAACsK,OAAO,EAAC,OAAO;0BAACC,EAAE,EAAE;4BAAEuB,QAAQ,EAAE;0BAAU,CAAE;0BAAAzB,QAAA,EACrDhE,MAAM,CAAC2B,SAAS,IAAI;wBAAK;0BAAA0C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNlH,OAAA,CAAC7D,GAAG;wBAACyK,EAAE,EAAE;0BAAEQ,KAAK,EAAE,OAAO;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAb,QAAA,eACjC1G,OAAA,CAAC3D,UAAU;0BAACsK,OAAO,EAAC,OAAO;0BAACC,EAAE,EAAE;4BAAEuB,QAAQ,EAAE;0BAAU,CAAE;0BAAAzB,QAAA,EACrDhE,MAAM,CAAC4B,OAAO,IAAI;wBAAK;0BAAAyC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACd;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNlH,OAAA,CAAC7D,GAAG;wBAACyK,EAAE,EAAE;0BAAEQ,KAAK,EAAE,OAAO;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAb,QAAA,eACjC1G,OAAA,CAAC3D,UAAU;0BAACsK,OAAO,EAAC,OAAO;0BAACC,EAAE,EAAE;4BAAEE,UAAU,EAAE,MAAM;4BAAEqB,QAAQ,EAAE,SAAS;4BAAEJ,KAAK,EAAE;0BAAe,CAAE;0BAAArB,QAAA,GAChGhE,MAAM,CAACuG,aAAa,IAAI,CAAC,EAAC,IAC7B;wBAAA;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNlH,OAAA,CAAC7D,GAAG;wBAACyK,EAAE,EAAE;0BAAEkB,QAAQ,EAAE;wBAAE,CAAE;wBAAApB,QAAA,eACvB1G,OAAA,CAAC5B,IAAI;0BACHqJ,IAAI,EAAC,OAAO;0BACZC,KAAK,EAAEhF,MAAM,CAAC6B,YAAY,IAAI,KAAM;0BACpCwD,KAAK,EAAEjI,iBAAiB,CAAC4C,MAAM,CAAC6B,YAAY,CAAE;0BAC9CoC,OAAO,EAAC,UAAU;0BAClBC,EAAE,EAAE;4BAAEwB,MAAM,EAAE,EAAE;4BAAED,QAAQ,EAAE,QAAQ;4BAAE,kBAAkB,EAAE;8BAAEW,EAAE,EAAE,CAAC;8BAAEC,EAAE,EAAE;4BAAE;0BAAE;wBAAE;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9E;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC,GArDZxE,MAAM,CAAC0C,SAAS;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsDb,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,eACP,CAAC,gBAEHlH,OAAA,CAAClD,KAAK;cAACsM,QAAQ,EAAC,MAAM;cAACxC,EAAE,EAAE;gBAAEsD,EAAE,EAAE;cAAE,CAAE;cAAAxD,QAAA,EAAC;YAEtC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPlH,OAAA,CAACxD,IAAI;UAACyO,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAzE,QAAA,eACvB1G,OAAA,CAAC5D,KAAK;YAACuK,OAAO,EAAC,UAAU;YAACC,EAAE,EAAE;cAAEO,CAAC,EAAE,CAAC;cAAEiB,MAAM,EAAE;YAAO,CAAE;YAAA1B,QAAA,gBACrD1G,OAAA,CAAC3D,UAAU;cAACsK,OAAO,EAAC,WAAW;cAACC,EAAE,EAAE;gBAAEE,UAAU,EAAE,MAAM;gBAAED,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,EAAC;YAEnE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAEZqD,oBAAoB,CAAC9G,MAAM,GAAG,CAAC,gBAC9BzD,OAAA,CAAAE,SAAA;cAAAwG,QAAA,gBACE1G,OAAA,CAAC7D,GAAG;gBAACyK,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEF,KAAK,EAAE,MAAM;kBAAE2B,EAAE,EAAE,GAAG;kBAAED,EAAE,EAAE,GAAG;kBAAEU,OAAO,EAAE,SAAS;kBAAE4B,YAAY,EAAE,CAAC;kBAAEvE,EAAE,EAAE;gBAAE,CAAE;gBAAAH,QAAA,gBAC9H1G,OAAA,CAAC7D,GAAG;kBAACyK,EAAE,EAAE;oBAAEQ,KAAK,EAAE,MAAM;oBAAEG,EAAE,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eAChC1G,OAAA,CAAC3D,UAAU;oBAACsK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3F,CAAC,eACNlH,OAAA,CAAC7D,GAAG;kBAACyK,EAAE,EAAE;oBAAEQ,KAAK,EAAE,OAAO;oBAAEG,EAAE,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eACjC1G,OAAA,CAAC3D,UAAU;oBAACsK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAI;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7F,CAAC,eACNlH,OAAA,CAAC7D,GAAG;kBAACyK,EAAE,EAAE;oBAAEQ,KAAK,EAAE,OAAO;oBAAEG,EAAE,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eACjC1G,OAAA,CAAC3D,UAAU;oBAACsK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9F,CAAC,eACNlH,OAAA,CAAC7D,GAAG;kBAACyK,EAAE,EAAE;oBAAEQ,KAAK,EAAE,OAAO;oBAAEG,EAAE,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eACjC1G,OAAA,CAAC3D,UAAU;oBAACsK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAO;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG,CAAC,eACNlH,OAAA,CAAC7D,GAAG;kBAACyK,EAAE,EAAE;oBAAEkB,QAAQ,EAAE;kBAAE,CAAE;kBAAApB,QAAA,eACvB1G,OAAA,CAAC3D,UAAU;oBAACsK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlH,OAAA,CAACjC,IAAI;gBAAC6I,EAAE,EAAE;kBAAE0C,SAAS,EAAE,OAAO;kBAAEhB,QAAQ,EAAE,MAAM;kBAAEkB,OAAO,EAAE;gBAAmB,CAAE;gBAAA9C,QAAA,EAC7E6D,oBAAoB,CAACb,GAAG,CAAEhH,MAAM,iBAC/B1C,OAAA,CAAChC,QAAQ;kBAEPqN,cAAc;kBACdC,eAAe,eACbtL,OAAA,CAACnC,UAAU;oBACT0N,IAAI,EAAC,KAAK;oBACV9D,IAAI,EAAC,OAAO;oBACZO,OAAO,EAAEA,CAAA,KAAMhD,kBAAkB,CAACtC,MAAM,CAAC0C,SAAS,CAAE;oBAAAsB,QAAA,eAEpD1G,OAAA,CAAChB,oBAAoB;sBAAC+I,KAAK,EAAC;oBAAS;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CACb;kBACDN,EAAE,EAAE;oBACF4C,OAAO,EAAEzH,gBAAgB,KAAKW,MAAM,CAAC0C,SAAS,GAAG,yBAAyB,GAAG,SAAS;oBACtFgG,YAAY,EAAE,KAAK;oBACnBvE,EAAE,EAAE,GAAG;oBACP2E,MAAM,EAAEzJ,gBAAgB,KAAKW,MAAM,CAAC0C,SAAS,GAAG,mBAAmB,GAAG;kBACxE,CAAE;kBAAAsB,QAAA,eAEF1G,OAAA,CAAC9B,cAAc;oBACbuN,KAAK;oBACLzD,OAAO,EAAEA,CAAA,KAAMhD,kBAAkB,CAACtC,MAAM,CAAC0C,SAAS,CAAE;oBAAAsB,QAAA,eAEpD1G,OAAA,CAAC7D,GAAG;sBAACyK,EAAE,EAAE;wBAAES,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEF,KAAK,EAAE,MAAM;wBAAE2B,EAAE,EAAE;sBAAI,CAAE;sBAAArC,QAAA,gBACzE1G,OAAA,CAAC7D,GAAG;wBAACyK,EAAE,EAAE;0BAAEQ,KAAK,EAAE,MAAM;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAb,QAAA,eAChC1G,OAAA,CAAC3D,UAAU;0BAACsK,OAAO,EAAC,OAAO;0BAACC,EAAE,EAAE;4BAAEE,UAAU,EAAE,MAAM;4BAAEqB,QAAQ,EAAE;0BAAS,CAAE;0BAAAzB,QAAA,EACxEnB,eAAe,CAAC7C,MAAM,CAAC0C,SAAS;wBAAC;0BAAA2B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNlH,OAAA,CAAC7D,GAAG;wBAACyK,EAAE,EAAE;0BAAEQ,KAAK,EAAE,OAAO;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAb,QAAA,eACjC1G,OAAA,CAAC3D,UAAU;0BAACsK,OAAO,EAAC,OAAO;0BAACC,EAAE,EAAE;4BAAEuB,QAAQ,EAAE;0BAAU,CAAE;0BAAAzB,QAAA,EACrDhE,MAAM,CAAC2B,SAAS,IAAI;wBAAK;0BAAA0C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNlH,OAAA,CAAC7D,GAAG;wBAACyK,EAAE,EAAE;0BAAEQ,KAAK,EAAE,OAAO;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAb,QAAA,eACjC1G,OAAA,CAAC3D,UAAU;0BAACsK,OAAO,EAAC,OAAO;0BAACC,EAAE,EAAE;4BAAEuB,QAAQ,EAAE;0BAAU,CAAE;0BAAAzB,QAAA,EACrDhE,MAAM,CAAC4B,OAAO,IAAI;wBAAK;0BAAAyC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACd;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNlH,OAAA,CAAC7D,GAAG;wBAACyK,EAAE,EAAE;0BAAEQ,KAAK,EAAE,OAAO;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAb,QAAA,eACjC1G,OAAA,CAAC3D,UAAU;0BAACsK,OAAO,EAAC,OAAO;0BAACC,EAAE,EAAE;4BAAEE,UAAU,EAAE,MAAM;4BAAEqB,QAAQ,EAAE,SAAS;4BAAEJ,KAAK,EAAE;0BAAe,CAAE;0BAAArB,QAAA,GAChGhE,MAAM,CAACuG,aAAa,IAAI,CAAC,EAAC,IAC7B;wBAAA;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNlH,OAAA,CAAC7D,GAAG;wBAACyK,EAAE,EAAE;0BAAES,OAAO,EAAE,MAAM;0BAAEwB,GAAG,EAAE;wBAAE,CAAE;wBAAAnC,QAAA,gBACnC1G,OAAA,CAAC5B,IAAI;0BACHqJ,IAAI,EAAC,OAAO;0BACZC,KAAK,EAAEhF,MAAM,CAAC6B,YAAY,IAAI,KAAM;0BACpCwD,KAAK,EAAEjI,iBAAiB,CAAC4C,MAAM,CAAC6B,YAAY,CAAE;0BAC9CoC,OAAO,EAAC,UAAU;0BAClBC,EAAE,EAAE;4BAAEwB,MAAM,EAAE,EAAE;4BAAED,QAAQ,EAAE,QAAQ;4BAAE,kBAAkB,EAAE;8BAAEW,EAAE,EAAE,CAAC;8BAAEC,EAAE,EAAE;4BAAE;0BAAE;wBAAE;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9E,CAAC,eACFlH,OAAA,CAAC5B,IAAI;0BACHqJ,IAAI,EAAC,OAAO;0BACZC,KAAK,EAAC,WAAW;0BACjBK,KAAK,EAAC,SAAS;0BACfpB,OAAO,EAAC,UAAU;0BAClBC,EAAE,EAAE;4BAAEwB,MAAM,EAAE,EAAE;4BAAED,QAAQ,EAAE,QAAQ;4BAAE,kBAAkB,EAAE;8BAAEW,EAAE,EAAE,CAAC;8BAAEC,EAAE,EAAE;4BAAE;0BAAE;wBAAE;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9E,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC,GA5DZxE,MAAM,CAAC0C,SAAS;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA6Db,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,eACP,CAAC,gBAEHlH,OAAA,CAAClD,KAAK;cAACsM,QAAQ,EAAC,MAAM;cAACxC,EAAE,EAAE;gBAAEsD,EAAE,EAAE;cAAE,CAAE;cAAAxD,QAAA,EAAC;YAEtC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGNoD,iBAAiB,CAAC7G,MAAM,KAAK,CAAC,IAAI8G,oBAAoB,CAAC9G,MAAM,KAAK,CAAC,iBAClEzD,OAAA,CAAClD,KAAK;QAACsM,QAAQ,EAAC,MAAM;QAACxC,EAAE,EAAE;UAAEsD,EAAE,EAAE;QAAE,CAAE;QAAAxD,QAAA,EAClCzE,gBAAgB,GAAG,8DAA8D,GAAG;MAAiC;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjH,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,oBACElH,OAAA,CAAC7D,GAAG;IAAAuK,QAAA,GAEDD,uBAAuB,CAAC,CAAC,EAGzBqD,qBAAqB,CAAC,CAAC,eAGxB9J,OAAA,CAAChD,MAAM;MACL0O,IAAI,EAAEzI,qBAAsB;MAC5B0I,OAAO,EAAEA,CAAA,KAAMzI,wBAAwB,CAAC,KAAK,CAAE;MAC/C0I,QAAQ,EAAC,IAAI;MACblB,SAAS;MAAAhE,QAAA,gBAET1G,OAAA,CAAC/C,WAAW;QAAAyJ,QAAA,EAAC;MAA0B;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACrDlH,OAAA,CAAC9C,aAAa;QAAAwJ,QAAA,EACXjF,YAAY,iBAAIzB,OAAA,CAACX,eAAe;UAACoD,IAAI,EAAEhB;QAAa;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAChBlH,OAAA,CAAC7C,aAAa;QAAAuJ,QAAA,eACZ1G,OAAA,CAACzD,MAAM;UAACyL,OAAO,EAAEA,CAAA,KAAM9E,wBAAwB,CAAC,KAAK,CAAE;UAAAwD,QAAA,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTlH,OAAA,CAAChD,MAAM;MACL0O,IAAI,EAAE/I,iBAAkB;MACxBgJ,OAAO,EAAEA,CAAA,KAAM/I,oBAAoB,CAAC,KAAK,CAAE;MAAA8D,QAAA,gBAE3C1G,OAAA,CAAC/C,WAAW;QAAAyJ,QAAA,EAAC;MAAmB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC9ClH,OAAA,CAAC9C,aAAa;QAAAwJ,QAAA,eACZ1G,OAAA,CAAC3D,UAAU;UAAAqK,QAAA,EAAE7D;QAAoB;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAChBlH,OAAA,CAAC7C,aAAa;QAAAuJ,QAAA,gBACZ1G,OAAA,CAACzD,MAAM;UACLyL,OAAO,EAAEA,CAAA,KAAMpF,oBAAoB,CAAC,KAAK,CAAE;UAC3CmF,KAAK,EAAC,SAAS;UAAArB,QAAA,EAChB;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlH,OAAA,CAACzD,MAAM;UACLyL,OAAO,EAAEA,CAAA,KAAM;YACbpF,oBAAoB,CAAC,KAAK,CAAC;YAC3B,IAAIG,mBAAmB,EAAEA,mBAAmB,CAAC,CAAC;UAChD,CAAE;UACFgF,KAAK,EAAC,SAAS;UACfpB,OAAO,EAAC,WAAW;UACnBkF,SAAS;UAAAnF,QAAA,EACV;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTlH,OAAA,CAACf,sBAAsB;MACrByM,IAAI,EAAErJ,0BAA2B;MACjCsJ,OAAO,EAAE1F,iCAAkC;MAC3CxD,IAAI,EAAEF,oBAAoB,CAACE,IAAK;MAChCC,MAAM,EAAEH,oBAAoB,CAACG,MAAO;MACpCoJ,YAAY,EAAE5F,gCAAiC;MAC/C6F,mBAAmB,EAAE9F,iCAAkC;MACvD+F,0BAA0B,EAAE3F;IAA+B;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC3G,EAAA,CAhkCIJ,kBAAkB;EAAA,QACLjB,WAAW;AAAA;AAAA+M,EAAA,GADxB9L,kBAAkB;AAkkCxB,eAAeA,kBAAkB;AAAC,IAAA8L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}