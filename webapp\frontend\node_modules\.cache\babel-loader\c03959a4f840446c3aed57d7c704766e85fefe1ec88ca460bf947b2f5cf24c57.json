{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListSubheaderUtilityClass(slot) {\n  return generateUtilityClass('MuiListSubheader', slot);\n}\nconst listSubheaderClasses = generateUtilityClasses('MuiListSubheader', ['root', 'colorPrimary', 'colorInherit', 'gutters', 'inset', 'sticky']);\nexport default listSubheaderClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getListSubheaderUtilityClass", "slot", "listSubheaderClasses"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/material/ListSubheader/listSubheaderClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListSubheaderUtilityClass(slot) {\n  return generateUtilityClass('MuiListSubheader', slot);\n}\nconst listSubheaderClasses = generateUtilityClasses('MuiListSubheader', ['root', 'colorPrimary', 'colorInherit', 'gutters', 'inset', 'sticky']);\nexport default listSubheaderClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,4BAA4BA,CAACC,IAAI,EAAE;EACjD,OAAOF,oBAAoB,CAAC,kBAAkB,EAAEE,IAAI,CAAC;AACvD;AACA,MAAMC,oBAAoB,GAAGJ,sBAAsB,CAAC,kBAAkB,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;AAC/I,eAAeI,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}