{"ast": null, "code": "import requiredArgs from \"../requiredArgs/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nexport default function isSameUTCWeek(dirtyDateLeft, dirtyDateRight, options) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfWeek = startOfUTCWeek(dirtyDateLeft, options);\n  var dateRightStartOfWeek = startOfUTCWeek(dirtyDateRight, options);\n  return dateLeftStartOfWeek.getTime() === dateRightStartOfWeek.getTime();\n}", "map": {"version": 3, "names": ["requiredArgs", "startOfUTCWeek", "isSameUTCWeek", "dirtyDateLeft", "dirtyDateRight", "options", "arguments", "dateLeftStartOfWeek", "dateRightStartOfWeek", "getTime"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/_lib/isSameUTCWeek/index.js"], "sourcesContent": ["import requiredArgs from \"../requiredArgs/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nexport default function isSameUTCWeek(dirtyDateLeft, dirtyDateRight, options) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfWeek = startOfUTCWeek(dirtyDateLeft, options);\n  var dateRightStartOfWeek = startOfUTCWeek(dirtyDateRight, options);\n  return dateLeftStartOfWeek.getTime() === dateRightStartOfWeek.getTime();\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,0BAA0B;AACnD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,eAAe,SAASC,aAAaA,CAACC,aAAa,EAAEC,cAAc,EAAEC,OAAO,EAAE;EAC5EL,YAAY,CAAC,CAAC,EAAEM,SAAS,CAAC;EAC1B,IAAIC,mBAAmB,GAAGN,cAAc,CAACE,aAAa,EAAEE,OAAO,CAAC;EAChE,IAAIG,oBAAoB,GAAGP,cAAc,CAACG,cAAc,EAAEC,OAAO,CAAC;EAClE,OAAOE,mBAAmB,CAACE,OAAO,CAAC,CAAC,KAAKD,oBAAoB,CAACC,OAAO,CAAC,CAAC;AACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}