{"ast": null, "code": "import formatDistance from \"../en-US/_lib/formatDistance/index.js\";\nimport formatRelative from \"../en-US/_lib/formatRelative/index.js\";\nimport localize from \"../en-US/_lib/localize/index.js\";\nimport match from \"../en-US/_lib/match/index.js\";\nimport formatLong from \"./_lib/formatLong/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary English locale (South Africa).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@shaykav]{@link https://github.com/shaykav}\n */\nvar locale = {\n  code: 'en-ZA',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0,\n    // Sunday is the first day of the week.\n    firstWeekContainsDate: 1 // The week that contains Jan 1st is the first week of the year.\n  }\n};\nexport default locale;", "map": {"version": 3, "names": ["formatDistance", "formatRelative", "localize", "match", "formatLong", "locale", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/en-ZA/index.js"], "sourcesContent": ["import formatDistance from \"../en-US/_lib/formatDistance/index.js\";\nimport formatRelative from \"../en-US/_lib/formatRelative/index.js\";\nimport localize from \"../en-US/_lib/localize/index.js\";\nimport match from \"../en-US/_lib/match/index.js\";\nimport formatLong from \"./_lib/formatLong/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary English locale (South Africa).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@shaykav]{@link https://github.com/shaykav}\n */\nvar locale = {\n  code: 'en-ZA',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0,\n    // Sunday is the first day of the week.\n    firstWeekContainsDate: 1 // The week that contains Jan 1st is the first week of the year.\n  }\n};\n\nexport default locale;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,uCAAuC;AAClE,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,QAAQ,MAAM,iCAAiC;AACtD,OAAOC,KAAK,MAAM,8BAA8B;AAChD,OAAOC,UAAU,MAAM,4BAA4B;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAG;EACXC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BI,UAAU,EAAEA,UAAU;EACtBH,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZI,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC;IACf;IACAC,qBAAqB,EAAE,CAAC,CAAC;EAC3B;AACF,CAAC;AAED,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}