{"ast": null, "code": "import React from'react';import{<PERSON><PERSON><PERSON>,Line,XAxis,<PERSON><PERSON><PERSON><PERSON>,CartesianGrid,<PERSON>lt<PERSON>,Legend,ResponsiveContainer,BarChart,Bar,ComposedChart,Area,AreaChart}from'recharts';import{Box,Typography,Grid,Paper,Chip}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const COLORS={primary:'#1976d2',secondary:'#dc004e',success:'#2e7d32',warning:'#ed6c02',info:'#0288d1',error:'#d32f2f'};const TimelineChart=_ref=>{var _data$posa_giornalier,_stats$giorno_miglior;let{data}=_ref;if(!data)return null;// Prepara dati per il grafico temporale\nconst timelineData=((_data$posa_giornalier=data.posa_giornaliera)===null||_data$posa_giornalier===void 0?void 0:_data$posa_giornalier.map(posa=>({data:posa.data,metri:posa.metri,data_formatted:new Date(posa.data).toLocaleDateString('it-IT',{day:'2-digit',month:'2-digit'})})))||[];// Calcola media mobile (7 giorni)\nconst timelineWithMovingAverage=timelineData.map((item,index)=>{const start=Math.max(0,index-6);const end=index+1;const slice=timelineData.slice(start,end);const average=slice.reduce((sum,day)=>sum+day.metri,0)/slice.length;return{...item,media_mobile:average};});// Raggruppa per settimana\nconst weeklyData=[];let currentWeek=null;let weekMeters=0;let weekDays=0;timelineData.forEach((day,index)=>{const date=new Date(day.data);const weekStart=new Date(date);weekStart.setDate(date.getDate()-date.getDay()+1);// Lunedì\nconst weekKey=weekStart.toISOString().split('T')[0];if(currentWeek!==weekKey){if(currentWeek!==null){weeklyData.push({settimana:`${currentWeek.split('-')[2]}/${currentWeek.split('-')[1]}`,metri_totali:weekMeters,giorni_attivi:weekDays,media_giornaliera:weekDays>0?weekMeters/weekDays:0});}currentWeek=weekKey;weekMeters=day.metri;weekDays=1;}else{weekMeters+=day.metri;weekDays++;}});// Aggiungi l'ultima settimana\nif(currentWeek!==null){weeklyData.push({settimana:`${currentWeek.split('-')[2]}/${currentWeek.split('-')[1]}`,metri_totali:weekMeters,giorni_attivi:weekDays,media_giornaliera:weekDays>0?weekMeters/weekDays:0});}// Statistiche del periodo\nconst stats={totale_metri:data.totale_metri_periodo||0,giorni_attivi:data.giorni_attivi||0,media_giornaliera:data.media_giornaliera||0,giorno_migliore:timelineData.reduce((max,day)=>day.metri>max.metri?day:max,{metri:0}),giorno_peggiore:timelineData.reduce((min,day)=>day.metri<min.metri?day:min,{metri:Infinity})};const CustomTooltip=_ref2=>{let{active,payload,label}=_ref2;if(active&&payload&&payload.length){return/*#__PURE__*/_jsxs(Paper,{sx:{p:1,border:'1px solid #ccc'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:`Data: ${label}`}),payload.map((entry,index)=>{var _entry$value;return/*#__PURE__*/_jsx(Typography,{variant:\"body2\",style:{color:entry.color},children:`${entry.name}: ${(_entry$value=entry.value)===null||_entry$value===void 0?void 0:_entry$value.toFixed(2)}m`},index);})]});}return null;};return/*#__PURE__*/_jsxs(Box,{sx:{mt:3},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",gutterBottom:true,children:[\"Analisi Temporale Posa (\",data.data_inizio,\" - \",data.data_fine,\")\"]}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Statistiche del Periodo\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',p:1,border:'1px solid #e0e0e0',borderRadius:1},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",color:\"primary\",children:[stats.totale_metri.toFixed(0),\"m\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Metri Totali\"})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',p:1,border:'1px solid #e0e0e0',borderRadius:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"success.main\",children:stats.giorni_attivi}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Giorni Attivi\"})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',p:1,border:'1px solid #e0e0e0',borderRadius:1},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",color:\"info.main\",children:[stats.media_giornaliera.toFixed(1),\"m\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Media Giornaliera\"})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',p:1,border:'1px solid #e0e0e0',borderRadius:1},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",color:\"warning.main\",children:[(_stats$giorno_miglior=stats.giorno_migliore.metri)===null||_stats$giorno_miglior===void 0?void 0:_stats$giorno_miglior.toFixed(0),\"m\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Giorno Migliore\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",children:stats.giorno_migliore.data_formatted})]})})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,height:400},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,align:\"center\",children:\"Trend Posa Giornaliera con Media Mobile (7 giorni)\"}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:330,children:/*#__PURE__*/_jsxs(ComposedChart,{data:timelineWithMovingAverage,margin:{top:20,right:30,left:20,bottom:5},children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"data_formatted\",angle:-45,textAnchor:\"end\",height:80}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{content:/*#__PURE__*/_jsx(CustomTooltip,{})}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Bar,{dataKey:\"metri\",fill:COLORS.primary,name:\"Metri Giornalieri\",opacity:0.7}),/*#__PURE__*/_jsx(Line,{type:\"monotone\",dataKey:\"media_mobile\",stroke:COLORS.error,strokeWidth:3,name:\"Media Mobile (7gg)\",dot:false})]})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,height:350},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,align:\"center\",children:\"Progresso Cumulativo\"}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:280,children:/*#__PURE__*/_jsxs(AreaChart,{data:timelineData.map((item,index)=>({...item,cumulativo:timelineData.slice(0,index+1).reduce((sum,day)=>sum+day.metri,0)})),margin:{top:20,right:30,left:20,bottom:5},children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"data_formatted\",angle:-45,textAnchor:\"end\",height:80}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{content:/*#__PURE__*/_jsx(CustomTooltip,{})}),/*#__PURE__*/_jsx(Area,{type:\"monotone\",dataKey:\"cumulativo\",stroke:COLORS.success,fill:COLORS.success,fillOpacity:0.6,name:\"Metri Cumulativi\"})]})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,height:350},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,align:\"center\",children:\"Performance Settimanale\"}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:280,children:/*#__PURE__*/_jsxs(BarChart,{data:weeklyData,margin:{top:20,right:30,left:20,bottom:5},children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"settimana\"}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{content:/*#__PURE__*/_jsx(CustomTooltip,{})}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Bar,{dataKey:\"metri_totali\",fill:COLORS.info,name:\"Metri Settimanali\"}),/*#__PURE__*/_jsx(Bar,{dataKey:\"media_giornaliera\",fill:COLORS.warning,name:\"Media Giornaliera\"})]})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Analisi Performance Dettagliata\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",gutterBottom:true,children:\"Top 5 Giorni Migliori\"}),timelineData.sort((a,b)=>b.metri-a.metri).slice(0,5).map((day,index)=>/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',p:1,mb:1,border:'1px solid #e0e0e0',borderRadius:1,borderLeft:`4px solid ${COLORS.success}`},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:day.data}),/*#__PURE__*/_jsx(Chip,{label:`${day.metri.toFixed(0)}m`,color:\"success\",size:\"small\"})]},index))]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",gutterBottom:true,children:\"Top 3 Settimane Migliori\"}),weeklyData.sort((a,b)=>b.metri_totali-a.metri_totali).slice(0,3).map((week,index)=>/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',p:1,mb:1,border:'1px solid #e0e0e0',borderRadius:1,borderLeft:`4px solid ${COLORS.info}`},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Settimana \",week.settimana]}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",children:[week.giorni_attivi,\" giorni attivi\"]})]}),/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'right'},children:[/*#__PURE__*/_jsx(Chip,{label:`${week.metri_totali.toFixed(0)}m`,color:\"info\",size:\"small\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",display:\"block\",children:[\"Media: \",week.media_giornaliera.toFixed(1),\"m/g\"]})]})]},index))]})]})]})})]})]});};export default TimelineChart;", "map": {"version": 3, "names": ["React", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Bar", "ComposedChart", "Area", "AreaChart", "Box", "Typography", "Grid", "Paper", "Chip", "jsx", "_jsx", "jsxs", "_jsxs", "COLORS", "primary", "secondary", "success", "warning", "info", "error", "TimelineChart", "_ref", "_data$posa_giornalier", "_stats$giorno_miglior", "data", "timelineData", "posa_giornal<PERSON>", "map", "posa", "metri", "data_formatted", "Date", "toLocaleDateString", "day", "month", "timelineWithMovingAverage", "item", "index", "start", "Math", "max", "end", "slice", "average", "reduce", "sum", "length", "media_mobile", "weeklyData", "currentWeek", "weekMeters", "weekDays", "for<PERSON>ach", "date", "weekStart", "setDate", "getDate", "getDay", "<PERSON><PERSON><PERSON>", "toISOString", "split", "push", "<PERSON><PERSON><PERSON>", "metri_totali", "giorni_attivi", "media_giornaliera", "stats", "totale_metri", "totale_metri_periodo", "giorno_migliore", "giorno_peggiore", "min", "Infinity", "CustomTooltip", "_ref2", "active", "payload", "label", "sx", "p", "border", "children", "variant", "entry", "_entry$value", "style", "color", "name", "value", "toFixed", "mt", "gutterBottom", "data_inizio", "data_fine", "container", "spacing", "xs", "sm", "md", "textAlign", "borderRadius", "height", "align", "width", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "angle", "textAnchor", "content", "fill", "opacity", "type", "stroke", "strokeWidth", "dot", "cumulativo", "fillOpacity", "sort", "a", "b", "display", "justifyContent", "alignItems", "mb", "borderLeft", "size", "week"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/charts/TimelineChart.js"], "sourcesContent": ["import React from 'react';\nimport {\n  LineChart,\n  Line,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer,\n  BarChart,\n  Bar,\n  ComposedChart,\n  Area,\n  AreaChart\n} from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\n\nconst COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f'\n};\n\nconst TimelineChart = ({ data }) => {\n  if (!data) return null;\n\n  // Prepara dati per il grafico temporale\n  const timelineData = data.posa_giornaliera?.map(posa => ({\n    data: posa.data,\n    metri: posa.metri,\n    data_formatted: new Date(posa.data).toLocaleDateString('it-IT', { \n      day: '2-digit', \n      month: '2-digit' \n    })\n  })) || [];\n\n  // Calcola media mobile (7 giorni)\n  const timelineWithMovingAverage = timelineData.map((item, index) => {\n    const start = Math.max(0, index - 6);\n    const end = index + 1;\n    const slice = timelineData.slice(start, end);\n    const average = slice.reduce((sum, day) => sum + day.metri, 0) / slice.length;\n    \n    return {\n      ...item,\n      media_mobile: average\n    };\n  });\n\n  // Raggruppa per settimana\n  const weeklyData = [];\n  let currentWeek = null;\n  let weekMeters = 0;\n  let weekDays = 0;\n\n  timelineData.forEach((day, index) => {\n    const date = new Date(day.data);\n    const weekStart = new Date(date);\n    weekStart.setDate(date.getDate() - date.getDay() + 1); // Lunedì\n    const weekKey = weekStart.toISOString().split('T')[0];\n\n    if (currentWeek !== weekKey) {\n      if (currentWeek !== null) {\n        weeklyData.push({\n          settimana: `${currentWeek.split('-')[2]}/${currentWeek.split('-')[1]}`,\n          metri_totali: weekMeters,\n          giorni_attivi: weekDays,\n          media_giornaliera: weekDays > 0 ? weekMeters / weekDays : 0\n        });\n      }\n      currentWeek = weekKey;\n      weekMeters = day.metri;\n      weekDays = 1;\n    } else {\n      weekMeters += day.metri;\n      weekDays++;\n    }\n  });\n\n  // Aggiungi l'ultima settimana\n  if (currentWeek !== null) {\n    weeklyData.push({\n      settimana: `${currentWeek.split('-')[2]}/${currentWeek.split('-')[1]}`,\n      metri_totali: weekMeters,\n      giorni_attivi: weekDays,\n      media_giornaliera: weekDays > 0 ? weekMeters / weekDays : 0\n    });\n  }\n\n  // Statistiche del periodo\n  const stats = {\n    totale_metri: data.totale_metri_periodo || 0,\n    giorni_attivi: data.giorni_attivi || 0,\n    media_giornaliera: data.media_giornaliera || 0,\n    giorno_migliore: timelineData.reduce((max, day) => day.metri > max.metri ? day : max, { metri: 0 }),\n    giorno_peggiore: timelineData.reduce((min, day) => day.metri < min.metri ? day : min, { metri: Infinity })\n  };\n\n  const CustomTooltip = ({ active, payload, label }) => {\n    if (active && payload && payload.length) {\n      return (\n        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>\n          <Typography variant=\"body2\">{`Data: ${label}`}</Typography>\n          {payload.map((entry, index) => (\n            <Typography key={index} variant=\"body2\" style={{ color: entry.color }}>\n              {`${entry.name}: ${entry.value?.toFixed(2)}m`}\n            </Typography>\n          ))}\n        </Paper>\n      );\n    }\n    return null;\n  };\n\n  return (\n    <Box sx={{ mt: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        Analisi Temporale Posa ({data.data_inizio} - {data.data_fine})\n      </Typography>\n      \n      <Grid container spacing={3}>\n        {/* Statistiche Periodo */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"subtitle1\" gutterBottom>\n              Statistiche del Periodo\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} sm={6} md={3}>\n                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                  <Typography variant=\"h6\" color=\"primary\">\n                    {stats.totale_metri.toFixed(0)}m\n                  </Typography>\n                  <Typography variant=\"body2\">Metri Totali</Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={12} sm={6} md={3}>\n                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                  <Typography variant=\"h6\" color=\"success.main\">\n                    {stats.giorni_attivi}\n                  </Typography>\n                  <Typography variant=\"body2\">Giorni Attivi</Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={12} sm={6} md={3}>\n                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                  <Typography variant=\"h6\" color=\"info.main\">\n                    {stats.media_giornaliera.toFixed(1)}m\n                  </Typography>\n                  <Typography variant=\"body2\">Media Giornaliera</Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={12} sm={6} md={3}>\n                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                  <Typography variant=\"h6\" color=\"warning.main\">\n                    {stats.giorno_migliore.metri?.toFixed(0)}m\n                  </Typography>\n                  <Typography variant=\"body2\">Giorno Migliore</Typography>\n                  <Typography variant=\"caption\">\n                    {stats.giorno_migliore.data_formatted}\n                  </Typography>\n                </Box>\n              </Grid>\n            </Grid>\n          </Paper>\n        </Grid>\n\n        {/* Grafico Temporale Giornaliero */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2, height: 400 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Trend Posa Giornaliera con Media Mobile (7 giorni)\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={330}>\n              <ComposedChart data={timelineWithMovingAverage} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis \n                  dataKey=\"data_formatted\" \n                  angle={-45} \n                  textAnchor=\"end\" \n                  height={80}\n                />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"metri\" fill={COLORS.primary} name=\"Metri Giornalieri\" opacity={0.7} />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"media_mobile\" \n                  stroke={COLORS.error} \n                  strokeWidth={3}\n                  name=\"Media Mobile (7gg)\"\n                  dot={false}\n                />\n              </ComposedChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico Area Cumulativo */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Progresso Cumulativo\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <AreaChart data={timelineData.map((item, index) => ({\n                ...item,\n                cumulativo: timelineData.slice(0, index + 1).reduce((sum, day) => sum + day.metri, 0)\n              }))} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"data_formatted\" angle={-45} textAnchor=\"end\" height={80} />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Area \n                  type=\"monotone\" \n                  dataKey=\"cumulativo\" \n                  stroke={COLORS.success} \n                  fill={COLORS.success}\n                  fillOpacity={0.6}\n                  name=\"Metri Cumulativi\"\n                />\n              </AreaChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico Settimanale */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Performance Settimanale\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <BarChart data={weeklyData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"settimana\" />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"metri_totali\" fill={COLORS.info} name=\"Metri Settimanali\" />\n                <Bar dataKey=\"media_giornaliera\" fill={COLORS.warning} name=\"Media Giornaliera\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Analisi Performance */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"subtitle1\" gutterBottom>\n              Analisi Performance Dettagliata\n            </Typography>\n            <Grid container spacing={2}>\n              {/* Top 5 giorni migliori */}\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  Top 5 Giorni Migliori\n                </Typography>\n                {timelineData\n                  .sort((a, b) => b.metri - a.metri)\n                  .slice(0, 5)\n                  .map((day, index) => (\n                    <Box key={index} sx={{ \n                      display: 'flex', \n                      justifyContent: 'space-between', \n                      alignItems: 'center',\n                      p: 1, \n                      mb: 1,\n                      border: '1px solid #e0e0e0', \n                      borderRadius: 1,\n                      borderLeft: `4px solid ${COLORS.success}`\n                    }}>\n                      <Typography variant=\"body2\">{day.data}</Typography>\n                      <Chip \n                        label={`${day.metri.toFixed(0)}m`}\n                        color=\"success\"\n                        size=\"small\"\n                      />\n                    </Box>\n                  ))}\n              </Grid>\n\n              {/* Settimane migliori */}\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  Top 3 Settimane Migliori\n                </Typography>\n                {weeklyData\n                  .sort((a, b) => b.metri_totali - a.metri_totali)\n                  .slice(0, 3)\n                  .map((week, index) => (\n                    <Box key={index} sx={{ \n                      display: 'flex', \n                      justifyContent: 'space-between', \n                      alignItems: 'center',\n                      p: 1, \n                      mb: 1,\n                      border: '1px solid #e0e0e0', \n                      borderRadius: 1,\n                      borderLeft: `4px solid ${COLORS.info}`\n                    }}>\n                      <Box>\n                        <Typography variant=\"body2\">Settimana {week.settimana}</Typography>\n                        <Typography variant=\"caption\">\n                          {week.giorni_attivi} giorni attivi\n                        </Typography>\n                      </Box>\n                      <Box sx={{ textAlign: 'right' }}>\n                        <Chip \n                          label={`${week.metri_totali.toFixed(0)}m`}\n                          color=\"info\"\n                          size=\"small\"\n                        />\n                        <Typography variant=\"caption\" display=\"block\">\n                          Media: {week.media_giornaliera.toFixed(1)}m/g\n                        </Typography>\n                      </Box>\n                    </Box>\n                  ))}\n              </Grid>\n            </Grid>\n          </Paper>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default TimelineChart;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,SAAS,CACTC,IAAI,CACJC,KAAK,CACLC,KAAK,CACLC,aAAa,CACbC,OAAO,CACPC,MAAM,CACNC,mBAAmB,CACnBC,QAAQ,CACRC,GAAG,CACHC,aAAa,CACbC,IAAI,CACJC,SAAS,KACJ,UAAU,CACjB,OAASC,GAAG,CAAEC,UAAU,CAAEC,IAAI,CAAEC,KAAK,CAAEC,IAAI,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnE,KAAM,CAAAC,MAAM,CAAG,CACbC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,SAAS,CACpBC,OAAO,CAAE,SAAS,CAClBC,OAAO,CAAE,SAAS,CAClBC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SACT,CAAC,CAED,KAAM,CAAAC,aAAa,CAAGC,IAAA,EAAc,KAAAC,qBAAA,CAAAC,qBAAA,IAAb,CAAEC,IAAK,CAAC,CAAAH,IAAA,CAC7B,GAAI,CAACG,IAAI,CAAE,MAAO,KAAI,CAEtB;AACA,KAAM,CAAAC,YAAY,CAAG,EAAAH,qBAAA,CAAAE,IAAI,CAACE,gBAAgB,UAAAJ,qBAAA,iBAArBA,qBAAA,CAAuBK,GAAG,CAACC,IAAI,GAAK,CACvDJ,IAAI,CAAEI,IAAI,CAACJ,IAAI,CACfK,KAAK,CAAED,IAAI,CAACC,KAAK,CACjBC,cAAc,CAAE,GAAI,CAAAC,IAAI,CAACH,IAAI,CAACJ,IAAI,CAAC,CAACQ,kBAAkB,CAAC,OAAO,CAAE,CAC9DC,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,SACT,CAAC,CACH,CAAC,CAAC,CAAC,GAAI,EAAE,CAET;AACA,KAAM,CAAAC,yBAAyB,CAAGV,YAAY,CAACE,GAAG,CAAC,CAACS,IAAI,CAAEC,KAAK,GAAK,CAClE,KAAM,CAAAC,KAAK,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEH,KAAK,CAAG,CAAC,CAAC,CACpC,KAAM,CAAAI,GAAG,CAAGJ,KAAK,CAAG,CAAC,CACrB,KAAM,CAAAK,KAAK,CAAGjB,YAAY,CAACiB,KAAK,CAACJ,KAAK,CAAEG,GAAG,CAAC,CAC5C,KAAM,CAAAE,OAAO,CAAGD,KAAK,CAACE,MAAM,CAAC,CAACC,GAAG,CAAEZ,GAAG,GAAKY,GAAG,CAAGZ,GAAG,CAACJ,KAAK,CAAE,CAAC,CAAC,CAAGa,KAAK,CAACI,MAAM,CAE7E,MAAO,CACL,GAAGV,IAAI,CACPW,YAAY,CAAEJ,OAChB,CAAC,CACH,CAAC,CAAC,CAEF;AACA,KAAM,CAAAK,UAAU,CAAG,EAAE,CACrB,GAAI,CAAAC,WAAW,CAAG,IAAI,CACtB,GAAI,CAAAC,UAAU,CAAG,CAAC,CAClB,GAAI,CAAAC,QAAQ,CAAG,CAAC,CAEhB1B,YAAY,CAAC2B,OAAO,CAAC,CAACnB,GAAG,CAAEI,KAAK,GAAK,CACnC,KAAM,CAAAgB,IAAI,CAAG,GAAI,CAAAtB,IAAI,CAACE,GAAG,CAACT,IAAI,CAAC,CAC/B,KAAM,CAAA8B,SAAS,CAAG,GAAI,CAAAvB,IAAI,CAACsB,IAAI,CAAC,CAChCC,SAAS,CAACC,OAAO,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAGH,IAAI,CAACI,MAAM,CAAC,CAAC,CAAG,CAAC,CAAC,CAAE;AACvD,KAAM,CAAAC,OAAO,CAAGJ,SAAS,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAErD,GAAIX,WAAW,GAAKS,OAAO,CAAE,CAC3B,GAAIT,WAAW,GAAK,IAAI,CAAE,CACxBD,UAAU,CAACa,IAAI,CAAC,CACdC,SAAS,CAAE,GAAGb,WAAW,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIX,WAAW,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CACtEG,YAAY,CAAEb,UAAU,CACxBc,aAAa,CAAEb,QAAQ,CACvBc,iBAAiB,CAAEd,QAAQ,CAAG,CAAC,CAAGD,UAAU,CAAGC,QAAQ,CAAG,CAC5D,CAAC,CAAC,CACJ,CACAF,WAAW,CAAGS,OAAO,CACrBR,UAAU,CAAGjB,GAAG,CAACJ,KAAK,CACtBsB,QAAQ,CAAG,CAAC,CACd,CAAC,IAAM,CACLD,UAAU,EAAIjB,GAAG,CAACJ,KAAK,CACvBsB,QAAQ,EAAE,CACZ,CACF,CAAC,CAAC,CAEF;AACA,GAAIF,WAAW,GAAK,IAAI,CAAE,CACxBD,UAAU,CAACa,IAAI,CAAC,CACdC,SAAS,CAAE,GAAGb,WAAW,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIX,WAAW,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CACtEG,YAAY,CAAEb,UAAU,CACxBc,aAAa,CAAEb,QAAQ,CACvBc,iBAAiB,CAAEd,QAAQ,CAAG,CAAC,CAAGD,UAAU,CAAGC,QAAQ,CAAG,CAC5D,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAe,KAAK,CAAG,CACZC,YAAY,CAAE3C,IAAI,CAAC4C,oBAAoB,EAAI,CAAC,CAC5CJ,aAAa,CAAExC,IAAI,CAACwC,aAAa,EAAI,CAAC,CACtCC,iBAAiB,CAAEzC,IAAI,CAACyC,iBAAiB,EAAI,CAAC,CAC9CI,eAAe,CAAE5C,YAAY,CAACmB,MAAM,CAAC,CAACJ,GAAG,CAAEP,GAAG,GAAKA,GAAG,CAACJ,KAAK,CAAGW,GAAG,CAACX,KAAK,CAAGI,GAAG,CAAGO,GAAG,CAAE,CAAEX,KAAK,CAAE,CAAE,CAAC,CAAC,CACnGyC,eAAe,CAAE7C,YAAY,CAACmB,MAAM,CAAC,CAAC2B,GAAG,CAAEtC,GAAG,GAAKA,GAAG,CAACJ,KAAK,CAAG0C,GAAG,CAAC1C,KAAK,CAAGI,GAAG,CAAGsC,GAAG,CAAE,CAAE1C,KAAK,CAAE2C,QAAS,CAAC,CAC3G,CAAC,CAED,KAAM,CAAAC,aAAa,CAAGC,KAAA,EAAgC,IAA/B,CAAEC,MAAM,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAAH,KAAA,CAC/C,GAAIC,MAAM,EAAIC,OAAO,EAAIA,OAAO,CAAC9B,MAAM,CAAE,CACvC,mBACElC,KAAA,CAACL,KAAK,EAACuE,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEC,MAAM,CAAE,gBAAiB,CAAE,CAAAC,QAAA,eAC5CvE,IAAA,CAACL,UAAU,EAAC6E,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAE,SAASJ,KAAK,EAAE,CAAa,CAAC,CAC1DD,OAAO,CAACjD,GAAG,CAAC,CAACwD,KAAK,CAAE9C,KAAK,QAAA+C,YAAA,oBACxB1E,IAAA,CAACL,UAAU,EAAa6E,OAAO,CAAC,OAAO,CAACG,KAAK,CAAE,CAAEC,KAAK,CAAEH,KAAK,CAACG,KAAM,CAAE,CAAAL,QAAA,CACnE,GAAGE,KAAK,CAACI,IAAI,MAAAH,YAAA,CAAKD,KAAK,CAACK,KAAK,UAAAJ,YAAA,iBAAXA,YAAA,CAAaK,OAAO,CAAC,CAAC,CAAC,GAAG,EAD9BpD,KAEL,CAAC,EACd,CAAC,EACG,CAAC,CAEZ,CACA,MAAO,KAAI,CACb,CAAC,CAED,mBACEzB,KAAA,CAACR,GAAG,EAAC0E,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAT,QAAA,eACjBrE,KAAA,CAACP,UAAU,EAAC6E,OAAO,CAAC,IAAI,CAACS,YAAY,MAAAV,QAAA,EAAC,0BACZ,CAACzD,IAAI,CAACoE,WAAW,CAAC,KAAG,CAACpE,IAAI,CAACqE,SAAS,CAAC,GAC/D,EAAY,CAAC,cAEbjF,KAAA,CAACN,IAAI,EAACwF,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAd,QAAA,eAEzBvE,IAAA,CAACJ,IAAI,EAAC8B,IAAI,MAAC4D,EAAE,CAAE,EAAG,CAAAf,QAAA,cAChBrE,KAAA,CAACL,KAAK,EAACuE,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAE,QAAA,eAClBvE,IAAA,CAACL,UAAU,EAAC6E,OAAO,CAAC,WAAW,CAACS,YAAY,MAAAV,QAAA,CAAC,yBAE7C,CAAY,CAAC,cACbrE,KAAA,CAACN,IAAI,EAACwF,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAd,QAAA,eACzBvE,IAAA,CAACJ,IAAI,EAAC8B,IAAI,MAAC4D,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAjB,QAAA,cAC9BrE,KAAA,CAACR,GAAG,EAAC0E,EAAE,CAAE,CAAEqB,SAAS,CAAE,QAAQ,CAAEpB,CAAC,CAAE,CAAC,CAAEC,MAAM,CAAE,mBAAmB,CAAEoB,YAAY,CAAE,CAAE,CAAE,CAAAnB,QAAA,eACnFrE,KAAA,CAACP,UAAU,EAAC6E,OAAO,CAAC,IAAI,CAACI,KAAK,CAAC,SAAS,CAAAL,QAAA,EACrCf,KAAK,CAACC,YAAY,CAACsB,OAAO,CAAC,CAAC,CAAC,CAAC,GACjC,EAAY,CAAC,cACb/E,IAAA,CAACL,UAAU,EAAC6E,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAC,cAAY,CAAY,CAAC,EAClD,CAAC,CACF,CAAC,cACPvE,IAAA,CAACJ,IAAI,EAAC8B,IAAI,MAAC4D,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAjB,QAAA,cAC9BrE,KAAA,CAACR,GAAG,EAAC0E,EAAE,CAAE,CAAEqB,SAAS,CAAE,QAAQ,CAAEpB,CAAC,CAAE,CAAC,CAAEC,MAAM,CAAE,mBAAmB,CAAEoB,YAAY,CAAE,CAAE,CAAE,CAAAnB,QAAA,eACnFvE,IAAA,CAACL,UAAU,EAAC6E,OAAO,CAAC,IAAI,CAACI,KAAK,CAAC,cAAc,CAAAL,QAAA,CAC1Cf,KAAK,CAACF,aAAa,CACV,CAAC,cACbtD,IAAA,CAACL,UAAU,EAAC6E,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAC,eAAa,CAAY,CAAC,EACnD,CAAC,CACF,CAAC,cACPvE,IAAA,CAACJ,IAAI,EAAC8B,IAAI,MAAC4D,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAjB,QAAA,cAC9BrE,KAAA,CAACR,GAAG,EAAC0E,EAAE,CAAE,CAAEqB,SAAS,CAAE,QAAQ,CAAEpB,CAAC,CAAE,CAAC,CAAEC,MAAM,CAAE,mBAAmB,CAAEoB,YAAY,CAAE,CAAE,CAAE,CAAAnB,QAAA,eACnFrE,KAAA,CAACP,UAAU,EAAC6E,OAAO,CAAC,IAAI,CAACI,KAAK,CAAC,WAAW,CAAAL,QAAA,EACvCf,KAAK,CAACD,iBAAiB,CAACwB,OAAO,CAAC,CAAC,CAAC,CAAC,GACtC,EAAY,CAAC,cACb/E,IAAA,CAACL,UAAU,EAAC6E,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAC,mBAAiB,CAAY,CAAC,EACvD,CAAC,CACF,CAAC,cACPvE,IAAA,CAACJ,IAAI,EAAC8B,IAAI,MAAC4D,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAjB,QAAA,cAC9BrE,KAAA,CAACR,GAAG,EAAC0E,EAAE,CAAE,CAAEqB,SAAS,CAAE,QAAQ,CAAEpB,CAAC,CAAE,CAAC,CAAEC,MAAM,CAAE,mBAAmB,CAAEoB,YAAY,CAAE,CAAE,CAAE,CAAAnB,QAAA,eACnFrE,KAAA,CAACP,UAAU,EAAC6E,OAAO,CAAC,IAAI,CAACI,KAAK,CAAC,cAAc,CAAAL,QAAA,GAAA1D,qBAAA,CAC1C2C,KAAK,CAACG,eAAe,CAACxC,KAAK,UAAAN,qBAAA,iBAA3BA,qBAAA,CAA6BkE,OAAO,CAAC,CAAC,CAAC,CAAC,GAC3C,EAAY,CAAC,cACb/E,IAAA,CAACL,UAAU,EAAC6E,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAC,iBAAe,CAAY,CAAC,cACxDvE,IAAA,CAACL,UAAU,EAAC6E,OAAO,CAAC,SAAS,CAAAD,QAAA,CAC1Bf,KAAK,CAACG,eAAe,CAACvC,cAAc,CAC3B,CAAC,EACV,CAAC,CACF,CAAC,EACH,CAAC,EACF,CAAC,CACJ,CAAC,cAGPpB,IAAA,CAACJ,IAAI,EAAC8B,IAAI,MAAC4D,EAAE,CAAE,EAAG,CAAAf,QAAA,cAChBrE,KAAA,CAACL,KAAK,EAACuE,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEsB,MAAM,CAAE,GAAI,CAAE,CAAApB,QAAA,eAC/BvE,IAAA,CAACL,UAAU,EAAC6E,OAAO,CAAC,WAAW,CAACS,YAAY,MAACW,KAAK,CAAC,QAAQ,CAAArB,QAAA,CAAC,oDAE5D,CAAY,CAAC,cACbvE,IAAA,CAACZ,mBAAmB,EAACyG,KAAK,CAAC,MAAM,CAACF,MAAM,CAAE,GAAI,CAAApB,QAAA,cAC5CrE,KAAA,CAACX,aAAa,EAACuB,IAAI,CAAEW,yBAA0B,CAACqE,MAAM,CAAE,CAAEC,GAAG,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,MAAM,CAAE,CAAE,CAAE,CAAA3B,QAAA,eAClGvE,IAAA,CAACf,aAAa,EAACkH,eAAe,CAAC,KAAK,CAAE,CAAC,cACvCnG,IAAA,CAACjB,KAAK,EACJqH,OAAO,CAAC,gBAAgB,CACxBC,KAAK,CAAE,CAAC,EAAG,CACXC,UAAU,CAAC,KAAK,CAChBX,MAAM,CAAE,EAAG,CACZ,CAAC,cACF3F,IAAA,CAAChB,KAAK,GAAE,CAAC,cACTgB,IAAA,CAACd,OAAO,EAACqH,OAAO,cAAEvG,IAAA,CAAC+D,aAAa,GAAE,CAAE,CAAE,CAAC,cACvC/D,IAAA,CAACb,MAAM,GAAE,CAAC,cACVa,IAAA,CAACV,GAAG,EAAC8G,OAAO,CAAC,OAAO,CAACI,IAAI,CAAErG,MAAM,CAACC,OAAQ,CAACyE,IAAI,CAAC,mBAAmB,CAAC4B,OAAO,CAAE,GAAI,CAAE,CAAC,cACpFzG,IAAA,CAAClB,IAAI,EACH4H,IAAI,CAAC,UAAU,CACfN,OAAO,CAAC,cAAc,CACtBO,MAAM,CAAExG,MAAM,CAACM,KAAM,CACrBmG,WAAW,CAAE,CAAE,CACf/B,IAAI,CAAC,oBAAoB,CACzBgC,GAAG,CAAE,KAAM,CACZ,CAAC,EACW,CAAC,CACG,CAAC,EACjB,CAAC,CACJ,CAAC,cAGP7G,IAAA,CAACJ,IAAI,EAAC8B,IAAI,MAAC4D,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,CAAE,CAAAjB,QAAA,cACvBrE,KAAA,CAACL,KAAK,EAACuE,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEsB,MAAM,CAAE,GAAI,CAAE,CAAApB,QAAA,eAC/BvE,IAAA,CAACL,UAAU,EAAC6E,OAAO,CAAC,WAAW,CAACS,YAAY,MAACW,KAAK,CAAC,QAAQ,CAAArB,QAAA,CAAC,sBAE5D,CAAY,CAAC,cACbvE,IAAA,CAACZ,mBAAmB,EAACyG,KAAK,CAAC,MAAM,CAACF,MAAM,CAAE,GAAI,CAAApB,QAAA,cAC5CrE,KAAA,CAACT,SAAS,EAACqB,IAAI,CAAEC,YAAY,CAACE,GAAG,CAAC,CAACS,IAAI,CAAEC,KAAK,IAAM,CAClD,GAAGD,IAAI,CACPoF,UAAU,CAAE/F,YAAY,CAACiB,KAAK,CAAC,CAAC,CAAEL,KAAK,CAAG,CAAC,CAAC,CAACO,MAAM,CAAC,CAACC,GAAG,CAAEZ,GAAG,GAAKY,GAAG,CAAGZ,GAAG,CAACJ,KAAK,CAAE,CAAC,CACtF,CAAC,CAAC,CAAE,CAAC2E,MAAM,CAAE,CAAEC,GAAG,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,MAAM,CAAE,CAAE,CAAE,CAAA3B,QAAA,eACvDvE,IAAA,CAACf,aAAa,EAACkH,eAAe,CAAC,KAAK,CAAE,CAAC,cACvCnG,IAAA,CAACjB,KAAK,EAACqH,OAAO,CAAC,gBAAgB,CAACC,KAAK,CAAE,CAAC,EAAG,CAACC,UAAU,CAAC,KAAK,CAACX,MAAM,CAAE,EAAG,CAAE,CAAC,cAC3E3F,IAAA,CAAChB,KAAK,GAAE,CAAC,cACTgB,IAAA,CAACd,OAAO,EAACqH,OAAO,cAAEvG,IAAA,CAAC+D,aAAa,GAAE,CAAE,CAAE,CAAC,cACvC/D,IAAA,CAACR,IAAI,EACHkH,IAAI,CAAC,UAAU,CACfN,OAAO,CAAC,YAAY,CACpBO,MAAM,CAAExG,MAAM,CAACG,OAAQ,CACvBkG,IAAI,CAAErG,MAAM,CAACG,OAAQ,CACrByG,WAAW,CAAE,GAAI,CACjBlC,IAAI,CAAC,kBAAkB,CACxB,CAAC,EACO,CAAC,CACO,CAAC,EACjB,CAAC,CACJ,CAAC,cAGP7E,IAAA,CAACJ,IAAI,EAAC8B,IAAI,MAAC4D,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,CAAE,CAAAjB,QAAA,cACvBrE,KAAA,CAACL,KAAK,EAACuE,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEsB,MAAM,CAAE,GAAI,CAAE,CAAApB,QAAA,eAC/BvE,IAAA,CAACL,UAAU,EAAC6E,OAAO,CAAC,WAAW,CAACS,YAAY,MAACW,KAAK,CAAC,QAAQ,CAAArB,QAAA,CAAC,yBAE5D,CAAY,CAAC,cACbvE,IAAA,CAACZ,mBAAmB,EAACyG,KAAK,CAAC,MAAM,CAACF,MAAM,CAAE,GAAI,CAAApB,QAAA,cAC5CrE,KAAA,CAACb,QAAQ,EAACyB,IAAI,CAAEwB,UAAW,CAACwD,MAAM,CAAE,CAAEC,GAAG,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,MAAM,CAAE,CAAE,CAAE,CAAA3B,QAAA,eAC9EvE,IAAA,CAACf,aAAa,EAACkH,eAAe,CAAC,KAAK,CAAE,CAAC,cACvCnG,IAAA,CAACjB,KAAK,EAACqH,OAAO,CAAC,WAAW,CAAE,CAAC,cAC7BpG,IAAA,CAAChB,KAAK,GAAE,CAAC,cACTgB,IAAA,CAACd,OAAO,EAACqH,OAAO,cAAEvG,IAAA,CAAC+D,aAAa,GAAE,CAAE,CAAE,CAAC,cACvC/D,IAAA,CAACb,MAAM,GAAE,CAAC,cACVa,IAAA,CAACV,GAAG,EAAC8G,OAAO,CAAC,cAAc,CAACI,IAAI,CAAErG,MAAM,CAACK,IAAK,CAACqE,IAAI,CAAC,mBAAmB,CAAE,CAAC,cAC1E7E,IAAA,CAACV,GAAG,EAAC8G,OAAO,CAAC,mBAAmB,CAACI,IAAI,CAAErG,MAAM,CAACI,OAAQ,CAACsE,IAAI,CAAC,mBAAmB,CAAE,CAAC,EAC1E,CAAC,CACQ,CAAC,EACjB,CAAC,CACJ,CAAC,cAGP7E,IAAA,CAACJ,IAAI,EAAC8B,IAAI,MAAC4D,EAAE,CAAE,EAAG,CAAAf,QAAA,cAChBrE,KAAA,CAACL,KAAK,EAACuE,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAE,QAAA,eAClBvE,IAAA,CAACL,UAAU,EAAC6E,OAAO,CAAC,WAAW,CAACS,YAAY,MAAAV,QAAA,CAAC,iCAE7C,CAAY,CAAC,cACbrE,KAAA,CAACN,IAAI,EAACwF,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAd,QAAA,eAEzBrE,KAAA,CAACN,IAAI,EAAC8B,IAAI,MAAC4D,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,CAAE,CAAAjB,QAAA,eACvBvE,IAAA,CAACL,UAAU,EAAC6E,OAAO,CAAC,WAAW,CAACS,YAAY,MAAAV,QAAA,CAAC,uBAE7C,CAAY,CAAC,CACZxD,YAAY,CACViG,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAAC/F,KAAK,CAAG8F,CAAC,CAAC9F,KAAK,CAAC,CACjCa,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CACXf,GAAG,CAAC,CAACM,GAAG,CAAEI,KAAK,gBACdzB,KAAA,CAACR,GAAG,EAAa0E,EAAE,CAAE,CACnB+C,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBhD,CAAC,CAAE,CAAC,CACJiD,EAAE,CAAE,CAAC,CACLhD,MAAM,CAAE,mBAAmB,CAC3BoB,YAAY,CAAE,CAAC,CACf6B,UAAU,CAAE,aAAapH,MAAM,CAACG,OAAO,EACzC,CAAE,CAAAiE,QAAA,eACAvE,IAAA,CAACL,UAAU,EAAC6E,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAEhD,GAAG,CAACT,IAAI,CAAa,CAAC,cACnDd,IAAA,CAACF,IAAI,EACHqE,KAAK,CAAE,GAAG5C,GAAG,CAACJ,KAAK,CAAC4D,OAAO,CAAC,CAAC,CAAC,GAAI,CAClCH,KAAK,CAAC,SAAS,CACf4C,IAAI,CAAC,OAAO,CACb,CAAC,GAfM7F,KAgBL,CACN,CAAC,EACA,CAAC,cAGPzB,KAAA,CAACN,IAAI,EAAC8B,IAAI,MAAC4D,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,CAAE,CAAAjB,QAAA,eACvBvE,IAAA,CAACL,UAAU,EAAC6E,OAAO,CAAC,WAAW,CAACS,YAAY,MAAAV,QAAA,CAAC,0BAE7C,CAAY,CAAC,CACZjC,UAAU,CACR0E,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAAC7D,YAAY,CAAG4D,CAAC,CAAC5D,YAAY,CAAC,CAC/CrB,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CACXf,GAAG,CAAC,CAACwG,IAAI,CAAE9F,KAAK,gBACfzB,KAAA,CAACR,GAAG,EAAa0E,EAAE,CAAE,CACnB+C,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBhD,CAAC,CAAE,CAAC,CACJiD,EAAE,CAAE,CAAC,CACLhD,MAAM,CAAE,mBAAmB,CAC3BoB,YAAY,CAAE,CAAC,CACf6B,UAAU,CAAE,aAAapH,MAAM,CAACK,IAAI,EACtC,CAAE,CAAA+D,QAAA,eACArE,KAAA,CAACR,GAAG,EAAA6E,QAAA,eACFrE,KAAA,CAACP,UAAU,EAAC6E,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,YAAU,CAACkD,IAAI,CAACrE,SAAS,EAAa,CAAC,cACnElD,KAAA,CAACP,UAAU,EAAC6E,OAAO,CAAC,SAAS,CAAAD,QAAA,EAC1BkD,IAAI,CAACnE,aAAa,CAAC,gBACtB,EAAY,CAAC,EACV,CAAC,cACNpD,KAAA,CAACR,GAAG,EAAC0E,EAAE,CAAE,CAAEqB,SAAS,CAAE,OAAQ,CAAE,CAAAlB,QAAA,eAC9BvE,IAAA,CAACF,IAAI,EACHqE,KAAK,CAAE,GAAGsD,IAAI,CAACpE,YAAY,CAAC0B,OAAO,CAAC,CAAC,CAAC,GAAI,CAC1CH,KAAK,CAAC,MAAM,CACZ4C,IAAI,CAAC,OAAO,CACb,CAAC,cACFtH,KAAA,CAACP,UAAU,EAAC6E,OAAO,CAAC,SAAS,CAAC2C,OAAO,CAAC,OAAO,CAAA5C,QAAA,EAAC,SACrC,CAACkD,IAAI,CAAClE,iBAAiB,CAACwB,OAAO,CAAC,CAAC,CAAC,CAAC,KAC5C,EAAY,CAAC,EACV,CAAC,GAzBEpD,KA0BL,CACN,CAAC,EACA,CAAC,EACH,CAAC,EACF,CAAC,CACJ,CAAC,EACH,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAjB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}