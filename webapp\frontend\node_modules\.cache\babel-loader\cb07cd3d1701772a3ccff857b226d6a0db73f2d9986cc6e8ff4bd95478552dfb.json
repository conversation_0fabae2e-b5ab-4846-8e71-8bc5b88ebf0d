{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"onClick\", \"onKeyDown\", \"onFocus\", \"onBlur\", \"onMouseUp\", \"onPaste\", \"error\", \"clearable\", \"onClear\", \"disabled\"];\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { useTheme } from '@mui/material/styles';\nimport { useValidation } from '../useValidation';\nimport { useUtils } from '../useUtils';\nimport { adjustSectionValue, isAndroid, cleanString, getSectionOrder } from './useField.utils';\nimport { useFieldState } from './useFieldState';\nimport { useFieldCharacterEditing } from './useFieldCharacterEditing';\nimport { getActiveElement } from '../../utils/utils';\nexport const useField = params => {\n  const utils = useUtils();\n  const {\n    state,\n    selectedSectionIndexes,\n    setSelectedSections,\n    clearValue,\n    clearActiveSection,\n    updateSectionValue,\n    updateValueFromValueStr,\n    setTempAndroidValueStr,\n    sectionsValueBoundaries,\n    placeholder,\n    timezone\n  } = useFieldState(params);\n  const {\n      inputRef: inputRefProp,\n      internalProps,\n      internalProps: {\n        readOnly = false,\n        unstableFieldRef,\n        minutesStep\n      },\n      forwardedProps: {\n        onClick,\n        onKeyDown,\n        onFocus,\n        onBlur,\n        onMouseUp,\n        onPaste,\n        error,\n        clearable,\n        onClear,\n        disabled\n      },\n      fieldValueManager,\n      valueManager,\n      validator\n    } = params,\n    otherForwardedProps = _objectWithoutPropertiesLoose(params.forwardedProps, _excluded);\n  const {\n    applyCharacterEditing,\n    resetCharacterQuery\n  } = useFieldCharacterEditing({\n    sections: state.sections,\n    updateSectionValue,\n    sectionsValueBoundaries,\n    setTempAndroidValueStr,\n    timezone\n  });\n  const inputRef = React.useRef(null);\n  const handleRef = useForkRef(inputRefProp, inputRef);\n  const focusTimeoutRef = React.useRef(undefined);\n  const theme = useTheme();\n  const isRTL = theme.direction === 'rtl';\n  const sectionOrder = React.useMemo(() => getSectionOrder(state.sections, isRTL), [state.sections, isRTL]);\n  const syncSelectionFromDOM = () => {\n    var _selectionStart;\n    if (readOnly) {\n      setSelectedSections(null);\n      return;\n    }\n    const browserStartIndex = (_selectionStart = inputRef.current.selectionStart) != null ? _selectionStart : 0;\n    let nextSectionIndex;\n    if (browserStartIndex <= state.sections[0].startInInput) {\n      // Special case if browser index is in invisible characters at the beginning\n      nextSectionIndex = 1;\n    } else if (browserStartIndex >= state.sections[state.sections.length - 1].endInInput) {\n      // If the click is after the last character of the input, then we want to select the 1st section.\n      nextSectionIndex = 1;\n    } else {\n      nextSectionIndex = state.sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n    }\n    const sectionIndex = nextSectionIndex === -1 ? state.sections.length - 1 : nextSectionIndex - 1;\n    setSelectedSections(sectionIndex);\n  };\n  const handleInputClick = useEventCallback((event, ...args) => {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call of `handleInputClick` is actually intended, or a side effect.\n    if (event.isDefaultPrevented()) {\n      return;\n    }\n    onClick == null || onClick(event, ...args);\n    syncSelectionFromDOM();\n  });\n  const handleInputMouseUp = useEventCallback(event => {\n    onMouseUp == null || onMouseUp(event);\n\n    // Without this, the browser will remove the selected when clicking inside an already-selected section.\n    event.preventDefault();\n  });\n  const handleInputFocus = useEventCallback((...args) => {\n    onFocus == null || onFocus(...args);\n    // The ref is guaranteed to be resolved at this point.\n    const input = inputRef.current;\n    window.clearTimeout(focusTimeoutRef.current);\n    focusTimeoutRef.current = setTimeout(() => {\n      // The ref changed, the component got remounted, the focus event is no longer relevant.\n      if (!input || input !== inputRef.current) {\n        return;\n      }\n      if (selectedSectionIndexes != null || readOnly) {\n        return;\n      }\n      if (\n      // avoid selecting all sections when focusing empty field without value\n      input.value.length && Number(input.selectionEnd) - Number(input.selectionStart) === input.value.length) {\n        setSelectedSections('all');\n      } else {\n        syncSelectionFromDOM();\n      }\n    });\n  });\n  const handleInputBlur = useEventCallback((...args) => {\n    onBlur == null || onBlur(...args);\n    setSelectedSections(null);\n  });\n  const handleInputPaste = useEventCallback(event => {\n    onPaste == null || onPaste(event);\n    if (readOnly) {\n      event.preventDefault();\n      return;\n    }\n    const pastedValue = event.clipboardData.getData('text');\n    if (selectedSectionIndexes && selectedSectionIndexes.startIndex === selectedSectionIndexes.endIndex) {\n      const activeSection = state.sections[selectedSectionIndexes.startIndex];\n      const lettersOnly = /^[a-zA-Z]+$/.test(pastedValue);\n      const digitsOnly = /^[0-9]+$/.test(pastedValue);\n      const digitsAndLetterOnly = /^(([a-zA-Z]+)|)([0-9]+)(([a-zA-Z]+)|)$/.test(pastedValue);\n      const isValidPastedValue = activeSection.contentType === 'letter' && lettersOnly || activeSection.contentType === 'digit' && digitsOnly || activeSection.contentType === 'digit-with-letter' && digitsAndLetterOnly;\n      if (isValidPastedValue) {\n        resetCharacterQuery();\n        updateSectionValue({\n          activeSection,\n          newSectionValue: pastedValue,\n          shouldGoToNextSection: true\n        });\n        // prevent default to avoid the input change handler being called\n        event.preventDefault();\n        return;\n      }\n      if (lettersOnly || digitsOnly) {\n        // The pasted value correspond to a single section but not the expected type\n        // skip the modification\n        event.preventDefault();\n        return;\n      }\n    }\n    event.preventDefault();\n    resetCharacterQuery();\n    updateValueFromValueStr(pastedValue);\n  });\n  const handleInputChange = useEventCallback(event => {\n    if (readOnly) {\n      return;\n    }\n    const targetValue = event.target.value;\n    if (targetValue === '') {\n      resetCharacterQuery();\n      clearValue();\n      return;\n    }\n    const eventData = event.nativeEvent.data;\n    // Calling `.fill(04/11/2022)` in playwright will trigger a change event with the requested content to insert in `event.nativeEvent.data`\n    // usual changes have only the currently typed character in the `event.nativeEvent.data`\n    const shouldUseEventData = eventData && eventData.length > 1;\n    const valueStr = shouldUseEventData ? eventData : targetValue;\n    const cleanValueStr = cleanString(valueStr);\n\n    // If no section is selected or eventData should be used, we just try to parse the new value\n    // This line is mostly triggered by imperative code / application tests.\n    if (selectedSectionIndexes == null || shouldUseEventData) {\n      updateValueFromValueStr(shouldUseEventData ? eventData : cleanValueStr);\n      return;\n    }\n    let keyPressed;\n    if (selectedSectionIndexes.startIndex === 0 && selectedSectionIndexes.endIndex === state.sections.length - 1 && cleanValueStr.length === 1) {\n      keyPressed = cleanValueStr;\n    } else {\n      const prevValueStr = cleanString(fieldValueManager.getValueStrFromSections(state.sections, isRTL));\n      let startOfDiffIndex = -1;\n      let endOfDiffIndex = -1;\n      for (let i = 0; i < prevValueStr.length; i += 1) {\n        if (startOfDiffIndex === -1 && prevValueStr[i] !== cleanValueStr[i]) {\n          startOfDiffIndex = i;\n        }\n        if (endOfDiffIndex === -1 && prevValueStr[prevValueStr.length - i - 1] !== cleanValueStr[cleanValueStr.length - i - 1]) {\n          endOfDiffIndex = i;\n        }\n      }\n      const activeSection = state.sections[selectedSectionIndexes.startIndex];\n      const hasDiffOutsideOfActiveSection = startOfDiffIndex < activeSection.start || prevValueStr.length - endOfDiffIndex - 1 > activeSection.end;\n      if (hasDiffOutsideOfActiveSection) {\n        // TODO: Support if the new date is valid\n        return;\n      }\n\n      // The active section being selected, the browser has replaced its value with the key pressed by the user.\n      const activeSectionEndRelativeToNewValue = cleanValueStr.length - prevValueStr.length + activeSection.end - cleanString(activeSection.endSeparator || '').length;\n      keyPressed = cleanValueStr.slice(activeSection.start + cleanString(activeSection.startSeparator || '').length, activeSectionEndRelativeToNewValue);\n    }\n    if (keyPressed.length === 0) {\n      if (isAndroid()) {\n        setTempAndroidValueStr(valueStr);\n      } else {\n        resetCharacterQuery();\n        clearActiveSection();\n      }\n      return;\n    }\n    applyCharacterEditing({\n      keyPressed,\n      sectionIndex: selectedSectionIndexes.startIndex\n    });\n  });\n  const handleInputKeyDown = useEventCallback(event => {\n    onKeyDown == null || onKeyDown(event);\n\n    // eslint-disable-next-line default-case\n    switch (true) {\n      // Select all\n      case event.key === 'a' && (event.ctrlKey || event.metaKey):\n        {\n          // prevent default to make sure that the next line \"select all\" while updating\n          // the internal state at the same time.\n          event.preventDefault();\n          setSelectedSections('all');\n          break;\n        }\n\n      // Move selection to next section\n      case event.key === 'ArrowRight':\n        {\n          event.preventDefault();\n          if (selectedSectionIndexes == null) {\n            setSelectedSections(sectionOrder.startIndex);\n          } else if (selectedSectionIndexes.startIndex !== selectedSectionIndexes.endIndex) {\n            setSelectedSections(selectedSectionIndexes.endIndex);\n          } else {\n            const nextSectionIndex = sectionOrder.neighbors[selectedSectionIndexes.startIndex].rightIndex;\n            if (nextSectionIndex !== null) {\n              setSelectedSections(nextSectionIndex);\n            }\n          }\n          break;\n        }\n\n      // Move selection to previous section\n      case event.key === 'ArrowLeft':\n        {\n          event.preventDefault();\n          if (selectedSectionIndexes == null) {\n            setSelectedSections(sectionOrder.endIndex);\n          } else if (selectedSectionIndexes.startIndex !== selectedSectionIndexes.endIndex) {\n            setSelectedSections(selectedSectionIndexes.startIndex);\n          } else {\n            const nextSectionIndex = sectionOrder.neighbors[selectedSectionIndexes.startIndex].leftIndex;\n            if (nextSectionIndex !== null) {\n              setSelectedSections(nextSectionIndex);\n            }\n          }\n          break;\n        }\n\n      // Reset the value of the selected section\n      case event.key === 'Delete':\n        {\n          event.preventDefault();\n          if (readOnly) {\n            break;\n          }\n          if (selectedSectionIndexes == null || selectedSectionIndexes.startIndex === 0 && selectedSectionIndexes.endIndex === state.sections.length - 1) {\n            clearValue();\n          } else {\n            clearActiveSection();\n          }\n          resetCharacterQuery();\n          break;\n        }\n\n      // Increment / decrement the selected section value\n      case ['ArrowUp', 'ArrowDown', 'Home', 'End', 'PageUp', 'PageDown'].includes(event.key):\n        {\n          event.preventDefault();\n          if (readOnly || selectedSectionIndexes == null) {\n            break;\n          }\n          const activeSection = state.sections[selectedSectionIndexes.startIndex];\n          const activeDateManager = fieldValueManager.getActiveDateManager(utils, state, activeSection);\n          const newSectionValue = adjustSectionValue(utils, timezone, activeSection, event.key, sectionsValueBoundaries, activeDateManager.date, {\n            minutesStep\n          });\n          updateSectionValue({\n            activeSection,\n            newSectionValue,\n            shouldGoToNextSection: false\n          });\n          break;\n        }\n    }\n  });\n  useEnhancedEffect(() => {\n    if (!inputRef.current) {\n      return;\n    }\n    if (selectedSectionIndexes == null) {\n      if (inputRef.current.scrollLeft) {\n        // Ensure that input content is not marked as selected.\n        // setting selection range to 0 causes issues in Safari.\n        // https://bugs.webkit.org/show_bug.cgi?id=224425\n        inputRef.current.scrollLeft = 0;\n      }\n      return;\n    }\n    const firstSelectedSection = state.sections[selectedSectionIndexes.startIndex];\n    const lastSelectedSection = state.sections[selectedSectionIndexes.endIndex];\n    let selectionStart = firstSelectedSection.startInInput;\n    let selectionEnd = lastSelectedSection.endInInput;\n    if (selectedSectionIndexes.shouldSelectBoundarySelectors) {\n      selectionStart -= firstSelectedSection.startSeparator.length;\n      selectionEnd += lastSelectedSection.endSeparator.length;\n    }\n    if (selectionStart !== inputRef.current.selectionStart || selectionEnd !== inputRef.current.selectionEnd) {\n      // Fix scroll jumping on iOS browser: https://github.com/mui/mui-x/issues/8321\n      const currentScrollTop = inputRef.current.scrollTop;\n      // On multi input range pickers we want to update selection range only for the active input\n      // This helps to avoid the focus jumping on Safari https://github.com/mui/mui-x/issues/9003\n      // because WebKit implements the `setSelectionRange` based on the spec: https://bugs.webkit.org/show_bug.cgi?id=224425\n      if (inputRef.current === getActiveElement(document)) {\n        inputRef.current.setSelectionRange(selectionStart, selectionEnd);\n      }\n      // Even reading this variable seems to do the trick, but also setting it just to make use of it\n      inputRef.current.scrollTop = currentScrollTop;\n    }\n  });\n  const validationError = useValidation(_extends({}, internalProps, {\n    value: state.value,\n    timezone\n  }), validator, valueManager.isSameError, valueManager.defaultErrorState);\n  const inputError = React.useMemo(() => {\n    // only override when `error` is undefined.\n    // in case of multi input fields, the `error` value is provided externally and will always be defined.\n    if (error !== undefined) {\n      return error;\n    }\n    return valueManager.hasError(validationError);\n  }, [valueManager, validationError, error]);\n  React.useEffect(() => {\n    if (!inputError && !selectedSectionIndexes) {\n      resetCharacterQuery();\n    }\n  }, [state.referenceValue, selectedSectionIndexes, inputError]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  React.useEffect(() => {\n    // Select the right section when focused on mount (`autoFocus = true` on the input)\n    if (inputRef.current && inputRef.current === document.activeElement) {\n      setSelectedSections('all');\n    }\n    return () => window.clearTimeout(focusTimeoutRef.current);\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // If `state.tempValueStrAndroid` is still defined when running `useEffect`,\n  // Then `onChange` has only been called once, which means the user pressed `Backspace` to reset the section.\n  // This causes a small flickering on Android,\n  // But we can't use `useEnhancedEffect` which is always called before the second `onChange` call and then would cause false positives.\n  React.useEffect(() => {\n    if (state.tempValueStrAndroid != null && selectedSectionIndexes != null) {\n      resetCharacterQuery();\n      clearActiveSection();\n    }\n  }, [state.tempValueStrAndroid]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const valueStr = React.useMemo(() => {\n    var _state$tempValueStrAn;\n    return (_state$tempValueStrAn = state.tempValueStrAndroid) != null ? _state$tempValueStrAn : fieldValueManager.getValueStrFromSections(state.sections, isRTL);\n  }, [state.sections, fieldValueManager, state.tempValueStrAndroid, isRTL]);\n  const inputMode = React.useMemo(() => {\n    if (selectedSectionIndexes == null) {\n      return 'text';\n    }\n    if (state.sections[selectedSectionIndexes.startIndex].contentType === 'letter') {\n      return 'text';\n    }\n    return 'numeric';\n  }, [selectedSectionIndexes, state.sections]);\n  const inputHasFocus = inputRef.current && inputRef.current === getActiveElement(document);\n  const areAllSectionsEmpty = valueManager.areValuesEqual(utils, state.value, valueManager.emptyValue);\n  const shouldShowPlaceholder = !inputHasFocus && areAllSectionsEmpty;\n  React.useImperativeHandle(unstableFieldRef, () => ({\n    getSections: () => state.sections,\n    getActiveSectionIndex: () => {\n      var _selectionStart2, _selectionEnd, _inputRef$current;\n      const browserStartIndex = (_selectionStart2 = inputRef.current.selectionStart) != null ? _selectionStart2 : 0;\n      const browserEndIndex = (_selectionEnd = inputRef.current.selectionEnd) != null ? _selectionEnd : 0;\n      const isInputReadOnly = !!((_inputRef$current = inputRef.current) != null && _inputRef$current.readOnly);\n      if (browserStartIndex === 0 && browserEndIndex === 0 || isInputReadOnly) {\n        return null;\n      }\n      const nextSectionIndex = browserStartIndex <= state.sections[0].startInInput ? 1 // Special case if browser index is in invisible characters at the beginning.\n      : state.sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n      return nextSectionIndex === -1 ? state.sections.length - 1 : nextSectionIndex - 1;\n    },\n    setSelectedSections: activeSectionIndex => setSelectedSections(activeSectionIndex)\n  }));\n  const handleClearValue = useEventCallback((event, ...args) => {\n    var _inputRef$current2;\n    event.preventDefault();\n    onClear == null || onClear(event, ...args);\n    clearValue();\n    inputRef == null || (_inputRef$current2 = inputRef.current) == null || _inputRef$current2.focus();\n    setSelectedSections(0);\n  });\n  return _extends({\n    placeholder,\n    autoComplete: 'off',\n    disabled: Boolean(disabled)\n  }, otherForwardedProps, {\n    value: shouldShowPlaceholder ? '' : valueStr,\n    inputMode,\n    readOnly,\n    onClick: handleInputClick,\n    onFocus: handleInputFocus,\n    onBlur: handleInputBlur,\n    onPaste: handleInputPaste,\n    onChange: handleInputChange,\n    onKeyDown: handleInputKeyDown,\n    onMouseUp: handleInputMouseUp,\n    onClear: handleClearValue,\n    error: inputError,\n    ref: handleRef,\n    clearable: Boolean(clearable && !areAllSectionsEmpty && !readOnly && !disabled)\n  });\n};", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "useEnhancedEffect", "useEventCallback", "useForkRef", "useTheme", "useValidation", "useUtils", "adjustSectionValue", "isAndroid", "cleanString", "getSectionOrder", "useFieldState", "useFieldCharacterEditing", "getActiveElement", "useField", "params", "utils", "state", "selectedSectionIndexes", "setSelectedSections", "clearValue", "clearActiveSection", "updateSectionValue", "updateValueFromValueStr", "setTempAndroidValueStr", "sectionsValueBoundaries", "placeholder", "timezone", "inputRef", "inputRefProp", "internalProps", "readOnly", "unstableFieldRef", "minutesStep", "forwardedProps", "onClick", "onKeyDown", "onFocus", "onBlur", "onMouseUp", "onPaste", "error", "clearable", "onClear", "disabled", "field<PERSON><PERSON>ueManager", "valueManager", "validator", "otherForwardedProps", "applyCharacterEditing", "resetCharacterQuery", "sections", "useRef", "handleRef", "focusTimeoutRef", "undefined", "theme", "isRTL", "direction", "sectionOrder", "useMemo", "syncSelectionFromDOM", "_selectionStart", "browserStartIndex", "current", "selectionStart", "nextSectionIndex", "startInInput", "length", "endInInput", "findIndex", "section", "startSeparator", "sectionIndex", "handleInputClick", "event", "args", "isDefaultPrevented", "handleInputMouseUp", "preventDefault", "handleInputFocus", "input", "window", "clearTimeout", "setTimeout", "value", "Number", "selectionEnd", "handleInputBlur", "handleInputPaste", "pastedValue", "clipboardData", "getData", "startIndex", "endIndex", "activeSection", "lettersOnly", "test", "digitsOnly", "digitsAndLetterOnly", "isValidPastedValue", "contentType", "newSectionValue", "shouldGoToNextSection", "handleInputChange", "targetValue", "target", "eventData", "nativeEvent", "data", "shouldUseEventData", "valueStr", "cleanValueStr", "keyPressed", "prevValueStr", "getValueStrFromSections", "startOfDiffIndex", "endOfDiffIndex", "i", "hasDiffOutsideOfActiveSection", "start", "end", "activeSectionEndRelativeToNewValue", "endSeparator", "slice", "handleInputKeyDown", "key", "ctrl<PERSON>ey", "metaKey", "neighbors", "rightIndex", "leftIndex", "includes", "activeDateManager", "getActiveDateManager", "date", "scrollLeft", "firstSelectedSection", "lastSelectedSection", "shouldSelectBoundarySelectors", "currentScrollTop", "scrollTop", "document", "setSelectionRange", "validationError", "isSameError", "defaultErrorState", "inputError", "<PERSON><PERSON><PERSON><PERSON>", "useEffect", "referenceValue", "activeElement", "tempValueStrAndroid", "_state$tempValueStrAn", "inputMode", "inputHasFocus", "areAllSectionsEmpty", "areValuesEqual", "emptyValue", "shouldShowPlaceholder", "useImperativeHandle", "getSections", "getActiveSectionIndex", "_selectionStart2", "_selectionEnd", "_inputRef$current", "browserEndIndex", "isInputReadOnly", "activeSectionIndex", "handleClearValue", "_inputRef$current2", "focus", "autoComplete", "Boolean", "onChange", "ref"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useField/useField.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"onClick\", \"onKeyDown\", \"onFocus\", \"onBlur\", \"onMouseUp\", \"onPaste\", \"error\", \"clearable\", \"onClear\", \"disabled\"];\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { useTheme } from '@mui/material/styles';\nimport { useValidation } from '../useValidation';\nimport { useUtils } from '../useUtils';\nimport { adjustSectionValue, isAndroid, cleanString, getSectionOrder } from './useField.utils';\nimport { useFieldState } from './useFieldState';\nimport { useFieldCharacterEditing } from './useFieldCharacterEditing';\nimport { getActiveElement } from '../../utils/utils';\nexport const useField = params => {\n  const utils = useUtils();\n  const {\n    state,\n    selectedSectionIndexes,\n    setSelectedSections,\n    clearValue,\n    clearActiveSection,\n    updateSectionValue,\n    updateValueFromValueStr,\n    setTempAndroidValueStr,\n    sectionsValueBoundaries,\n    placeholder,\n    timezone\n  } = useFieldState(params);\n  const {\n      inputRef: inputRefProp,\n      internalProps,\n      internalProps: {\n        readOnly = false,\n        unstableFieldRef,\n        minutesStep\n      },\n      forwardedProps: {\n        onClick,\n        onKeyDown,\n        onFocus,\n        onBlur,\n        onMouseUp,\n        onPaste,\n        error,\n        clearable,\n        onClear,\n        disabled\n      },\n      fieldValueManager,\n      valueManager,\n      validator\n    } = params,\n    otherForwardedProps = _objectWithoutPropertiesLoose(params.forwardedProps, _excluded);\n  const {\n    applyCharacterEditing,\n    resetCharacterQuery\n  } = useFieldCharacterEditing({\n    sections: state.sections,\n    updateSectionValue,\n    sectionsValueBoundaries,\n    setTempAndroidValueStr,\n    timezone\n  });\n  const inputRef = React.useRef(null);\n  const handleRef = useForkRef(inputRefProp, inputRef);\n  const focusTimeoutRef = React.useRef(undefined);\n  const theme = useTheme();\n  const isRTL = theme.direction === 'rtl';\n  const sectionOrder = React.useMemo(() => getSectionOrder(state.sections, isRTL), [state.sections, isRTL]);\n  const syncSelectionFromDOM = () => {\n    var _selectionStart;\n    if (readOnly) {\n      setSelectedSections(null);\n      return;\n    }\n    const browserStartIndex = (_selectionStart = inputRef.current.selectionStart) != null ? _selectionStart : 0;\n    let nextSectionIndex;\n    if (browserStartIndex <= state.sections[0].startInInput) {\n      // Special case if browser index is in invisible characters at the beginning\n      nextSectionIndex = 1;\n    } else if (browserStartIndex >= state.sections[state.sections.length - 1].endInInput) {\n      // If the click is after the last character of the input, then we want to select the 1st section.\n      nextSectionIndex = 1;\n    } else {\n      nextSectionIndex = state.sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n    }\n    const sectionIndex = nextSectionIndex === -1 ? state.sections.length - 1 : nextSectionIndex - 1;\n    setSelectedSections(sectionIndex);\n  };\n  const handleInputClick = useEventCallback((event, ...args) => {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call of `handleInputClick` is actually intended, or a side effect.\n    if (event.isDefaultPrevented()) {\n      return;\n    }\n    onClick == null || onClick(event, ...args);\n    syncSelectionFromDOM();\n  });\n  const handleInputMouseUp = useEventCallback(event => {\n    onMouseUp == null || onMouseUp(event);\n\n    // Without this, the browser will remove the selected when clicking inside an already-selected section.\n    event.preventDefault();\n  });\n  const handleInputFocus = useEventCallback((...args) => {\n    onFocus == null || onFocus(...args);\n    // The ref is guaranteed to be resolved at this point.\n    const input = inputRef.current;\n    window.clearTimeout(focusTimeoutRef.current);\n    focusTimeoutRef.current = setTimeout(() => {\n      // The ref changed, the component got remounted, the focus event is no longer relevant.\n      if (!input || input !== inputRef.current) {\n        return;\n      }\n      if (selectedSectionIndexes != null || readOnly) {\n        return;\n      }\n      if (\n      // avoid selecting all sections when focusing empty field without value\n      input.value.length && Number(input.selectionEnd) - Number(input.selectionStart) === input.value.length) {\n        setSelectedSections('all');\n      } else {\n        syncSelectionFromDOM();\n      }\n    });\n  });\n  const handleInputBlur = useEventCallback((...args) => {\n    onBlur == null || onBlur(...args);\n    setSelectedSections(null);\n  });\n  const handleInputPaste = useEventCallback(event => {\n    onPaste == null || onPaste(event);\n    if (readOnly) {\n      event.preventDefault();\n      return;\n    }\n    const pastedValue = event.clipboardData.getData('text');\n    if (selectedSectionIndexes && selectedSectionIndexes.startIndex === selectedSectionIndexes.endIndex) {\n      const activeSection = state.sections[selectedSectionIndexes.startIndex];\n      const lettersOnly = /^[a-zA-Z]+$/.test(pastedValue);\n      const digitsOnly = /^[0-9]+$/.test(pastedValue);\n      const digitsAndLetterOnly = /^(([a-zA-Z]+)|)([0-9]+)(([a-zA-Z]+)|)$/.test(pastedValue);\n      const isValidPastedValue = activeSection.contentType === 'letter' && lettersOnly || activeSection.contentType === 'digit' && digitsOnly || activeSection.contentType === 'digit-with-letter' && digitsAndLetterOnly;\n      if (isValidPastedValue) {\n        resetCharacterQuery();\n        updateSectionValue({\n          activeSection,\n          newSectionValue: pastedValue,\n          shouldGoToNextSection: true\n        });\n        // prevent default to avoid the input change handler being called\n        event.preventDefault();\n        return;\n      }\n      if (lettersOnly || digitsOnly) {\n        // The pasted value correspond to a single section but not the expected type\n        // skip the modification\n        event.preventDefault();\n        return;\n      }\n    }\n    event.preventDefault();\n    resetCharacterQuery();\n    updateValueFromValueStr(pastedValue);\n  });\n  const handleInputChange = useEventCallback(event => {\n    if (readOnly) {\n      return;\n    }\n    const targetValue = event.target.value;\n    if (targetValue === '') {\n      resetCharacterQuery();\n      clearValue();\n      return;\n    }\n    const eventData = event.nativeEvent.data;\n    // Calling `.fill(04/11/2022)` in playwright will trigger a change event with the requested content to insert in `event.nativeEvent.data`\n    // usual changes have only the currently typed character in the `event.nativeEvent.data`\n    const shouldUseEventData = eventData && eventData.length > 1;\n    const valueStr = shouldUseEventData ? eventData : targetValue;\n    const cleanValueStr = cleanString(valueStr);\n\n    // If no section is selected or eventData should be used, we just try to parse the new value\n    // This line is mostly triggered by imperative code / application tests.\n    if (selectedSectionIndexes == null || shouldUseEventData) {\n      updateValueFromValueStr(shouldUseEventData ? eventData : cleanValueStr);\n      return;\n    }\n    let keyPressed;\n    if (selectedSectionIndexes.startIndex === 0 && selectedSectionIndexes.endIndex === state.sections.length - 1 && cleanValueStr.length === 1) {\n      keyPressed = cleanValueStr;\n    } else {\n      const prevValueStr = cleanString(fieldValueManager.getValueStrFromSections(state.sections, isRTL));\n      let startOfDiffIndex = -1;\n      let endOfDiffIndex = -1;\n      for (let i = 0; i < prevValueStr.length; i += 1) {\n        if (startOfDiffIndex === -1 && prevValueStr[i] !== cleanValueStr[i]) {\n          startOfDiffIndex = i;\n        }\n        if (endOfDiffIndex === -1 && prevValueStr[prevValueStr.length - i - 1] !== cleanValueStr[cleanValueStr.length - i - 1]) {\n          endOfDiffIndex = i;\n        }\n      }\n      const activeSection = state.sections[selectedSectionIndexes.startIndex];\n      const hasDiffOutsideOfActiveSection = startOfDiffIndex < activeSection.start || prevValueStr.length - endOfDiffIndex - 1 > activeSection.end;\n      if (hasDiffOutsideOfActiveSection) {\n        // TODO: Support if the new date is valid\n        return;\n      }\n\n      // The active section being selected, the browser has replaced its value with the key pressed by the user.\n      const activeSectionEndRelativeToNewValue = cleanValueStr.length - prevValueStr.length + activeSection.end - cleanString(activeSection.endSeparator || '').length;\n      keyPressed = cleanValueStr.slice(activeSection.start + cleanString(activeSection.startSeparator || '').length, activeSectionEndRelativeToNewValue);\n    }\n    if (keyPressed.length === 0) {\n      if (isAndroid()) {\n        setTempAndroidValueStr(valueStr);\n      } else {\n        resetCharacterQuery();\n        clearActiveSection();\n      }\n      return;\n    }\n    applyCharacterEditing({\n      keyPressed,\n      sectionIndex: selectedSectionIndexes.startIndex\n    });\n  });\n  const handleInputKeyDown = useEventCallback(event => {\n    onKeyDown == null || onKeyDown(event);\n\n    // eslint-disable-next-line default-case\n    switch (true) {\n      // Select all\n      case event.key === 'a' && (event.ctrlKey || event.metaKey):\n        {\n          // prevent default to make sure that the next line \"select all\" while updating\n          // the internal state at the same time.\n          event.preventDefault();\n          setSelectedSections('all');\n          break;\n        }\n\n      // Move selection to next section\n      case event.key === 'ArrowRight':\n        {\n          event.preventDefault();\n          if (selectedSectionIndexes == null) {\n            setSelectedSections(sectionOrder.startIndex);\n          } else if (selectedSectionIndexes.startIndex !== selectedSectionIndexes.endIndex) {\n            setSelectedSections(selectedSectionIndexes.endIndex);\n          } else {\n            const nextSectionIndex = sectionOrder.neighbors[selectedSectionIndexes.startIndex].rightIndex;\n            if (nextSectionIndex !== null) {\n              setSelectedSections(nextSectionIndex);\n            }\n          }\n          break;\n        }\n\n      // Move selection to previous section\n      case event.key === 'ArrowLeft':\n        {\n          event.preventDefault();\n          if (selectedSectionIndexes == null) {\n            setSelectedSections(sectionOrder.endIndex);\n          } else if (selectedSectionIndexes.startIndex !== selectedSectionIndexes.endIndex) {\n            setSelectedSections(selectedSectionIndexes.startIndex);\n          } else {\n            const nextSectionIndex = sectionOrder.neighbors[selectedSectionIndexes.startIndex].leftIndex;\n            if (nextSectionIndex !== null) {\n              setSelectedSections(nextSectionIndex);\n            }\n          }\n          break;\n        }\n\n      // Reset the value of the selected section\n      case event.key === 'Delete':\n        {\n          event.preventDefault();\n          if (readOnly) {\n            break;\n          }\n          if (selectedSectionIndexes == null || selectedSectionIndexes.startIndex === 0 && selectedSectionIndexes.endIndex === state.sections.length - 1) {\n            clearValue();\n          } else {\n            clearActiveSection();\n          }\n          resetCharacterQuery();\n          break;\n        }\n\n      // Increment / decrement the selected section value\n      case ['ArrowUp', 'ArrowDown', 'Home', 'End', 'PageUp', 'PageDown'].includes(event.key):\n        {\n          event.preventDefault();\n          if (readOnly || selectedSectionIndexes == null) {\n            break;\n          }\n          const activeSection = state.sections[selectedSectionIndexes.startIndex];\n          const activeDateManager = fieldValueManager.getActiveDateManager(utils, state, activeSection);\n          const newSectionValue = adjustSectionValue(utils, timezone, activeSection, event.key, sectionsValueBoundaries, activeDateManager.date, {\n            minutesStep\n          });\n          updateSectionValue({\n            activeSection,\n            newSectionValue,\n            shouldGoToNextSection: false\n          });\n          break;\n        }\n    }\n  });\n  useEnhancedEffect(() => {\n    if (!inputRef.current) {\n      return;\n    }\n    if (selectedSectionIndexes == null) {\n      if (inputRef.current.scrollLeft) {\n        // Ensure that input content is not marked as selected.\n        // setting selection range to 0 causes issues in Safari.\n        // https://bugs.webkit.org/show_bug.cgi?id=224425\n        inputRef.current.scrollLeft = 0;\n      }\n      return;\n    }\n    const firstSelectedSection = state.sections[selectedSectionIndexes.startIndex];\n    const lastSelectedSection = state.sections[selectedSectionIndexes.endIndex];\n    let selectionStart = firstSelectedSection.startInInput;\n    let selectionEnd = lastSelectedSection.endInInput;\n    if (selectedSectionIndexes.shouldSelectBoundarySelectors) {\n      selectionStart -= firstSelectedSection.startSeparator.length;\n      selectionEnd += lastSelectedSection.endSeparator.length;\n    }\n    if (selectionStart !== inputRef.current.selectionStart || selectionEnd !== inputRef.current.selectionEnd) {\n      // Fix scroll jumping on iOS browser: https://github.com/mui/mui-x/issues/8321\n      const currentScrollTop = inputRef.current.scrollTop;\n      // On multi input range pickers we want to update selection range only for the active input\n      // This helps to avoid the focus jumping on Safari https://github.com/mui/mui-x/issues/9003\n      // because WebKit implements the `setSelectionRange` based on the spec: https://bugs.webkit.org/show_bug.cgi?id=224425\n      if (inputRef.current === getActiveElement(document)) {\n        inputRef.current.setSelectionRange(selectionStart, selectionEnd);\n      }\n      // Even reading this variable seems to do the trick, but also setting it just to make use of it\n      inputRef.current.scrollTop = currentScrollTop;\n    }\n  });\n  const validationError = useValidation(_extends({}, internalProps, {\n    value: state.value,\n    timezone\n  }), validator, valueManager.isSameError, valueManager.defaultErrorState);\n  const inputError = React.useMemo(() => {\n    // only override when `error` is undefined.\n    // in case of multi input fields, the `error` value is provided externally and will always be defined.\n    if (error !== undefined) {\n      return error;\n    }\n    return valueManager.hasError(validationError);\n  }, [valueManager, validationError, error]);\n  React.useEffect(() => {\n    if (!inputError && !selectedSectionIndexes) {\n      resetCharacterQuery();\n    }\n  }, [state.referenceValue, selectedSectionIndexes, inputError]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  React.useEffect(() => {\n    // Select the right section when focused on mount (`autoFocus = true` on the input)\n    if (inputRef.current && inputRef.current === document.activeElement) {\n      setSelectedSections('all');\n    }\n    return () => window.clearTimeout(focusTimeoutRef.current);\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // If `state.tempValueStrAndroid` is still defined when running `useEffect`,\n  // Then `onChange` has only been called once, which means the user pressed `Backspace` to reset the section.\n  // This causes a small flickering on Android,\n  // But we can't use `useEnhancedEffect` which is always called before the second `onChange` call and then would cause false positives.\n  React.useEffect(() => {\n    if (state.tempValueStrAndroid != null && selectedSectionIndexes != null) {\n      resetCharacterQuery();\n      clearActiveSection();\n    }\n  }, [state.tempValueStrAndroid]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const valueStr = React.useMemo(() => {\n    var _state$tempValueStrAn;\n    return (_state$tempValueStrAn = state.tempValueStrAndroid) != null ? _state$tempValueStrAn : fieldValueManager.getValueStrFromSections(state.sections, isRTL);\n  }, [state.sections, fieldValueManager, state.tempValueStrAndroid, isRTL]);\n  const inputMode = React.useMemo(() => {\n    if (selectedSectionIndexes == null) {\n      return 'text';\n    }\n    if (state.sections[selectedSectionIndexes.startIndex].contentType === 'letter') {\n      return 'text';\n    }\n    return 'numeric';\n  }, [selectedSectionIndexes, state.sections]);\n  const inputHasFocus = inputRef.current && inputRef.current === getActiveElement(document);\n  const areAllSectionsEmpty = valueManager.areValuesEqual(utils, state.value, valueManager.emptyValue);\n  const shouldShowPlaceholder = !inputHasFocus && areAllSectionsEmpty;\n  React.useImperativeHandle(unstableFieldRef, () => ({\n    getSections: () => state.sections,\n    getActiveSectionIndex: () => {\n      var _selectionStart2, _selectionEnd, _inputRef$current;\n      const browserStartIndex = (_selectionStart2 = inputRef.current.selectionStart) != null ? _selectionStart2 : 0;\n      const browserEndIndex = (_selectionEnd = inputRef.current.selectionEnd) != null ? _selectionEnd : 0;\n      const isInputReadOnly = !!((_inputRef$current = inputRef.current) != null && _inputRef$current.readOnly);\n      if (browserStartIndex === 0 && browserEndIndex === 0 || isInputReadOnly) {\n        return null;\n      }\n      const nextSectionIndex = browserStartIndex <= state.sections[0].startInInput ? 1 // Special case if browser index is in invisible characters at the beginning.\n      : state.sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n      return nextSectionIndex === -1 ? state.sections.length - 1 : nextSectionIndex - 1;\n    },\n    setSelectedSections: activeSectionIndex => setSelectedSections(activeSectionIndex)\n  }));\n  const handleClearValue = useEventCallback((event, ...args) => {\n    var _inputRef$current2;\n    event.preventDefault();\n    onClear == null || onClear(event, ...args);\n    clearValue();\n    inputRef == null || (_inputRef$current2 = inputRef.current) == null || _inputRef$current2.focus();\n    setSelectedSections(0);\n  });\n  return _extends({\n    placeholder,\n    autoComplete: 'off',\n    disabled: Boolean(disabled)\n  }, otherForwardedProps, {\n    value: shouldShowPlaceholder ? '' : valueStr,\n    inputMode,\n    readOnly,\n    onClick: handleInputClick,\n    onFocus: handleInputFocus,\n    onBlur: handleInputBlur,\n    onPaste: handleInputPaste,\n    onChange: handleInputChange,\n    onKeyDown: handleInputKeyDown,\n    onMouseUp: handleInputMouseUp,\n    onClear: handleClearValue,\n    error: inputError,\n    ref: handleRef,\n    clearable: Boolean(clearable && !areAllSectionsEmpty && !readOnly && !disabled)\n  });\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,CAAC;AACpI,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,kBAAkB,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAC9F,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,wBAAwB,QAAQ,4BAA4B;AACrE,SAASC,gBAAgB,QAAQ,mBAAmB;AACpD,OAAO,MAAMC,QAAQ,GAAGC,MAAM,IAAI;EAChC,MAAMC,KAAK,GAAGV,QAAQ,CAAC,CAAC;EACxB,MAAM;IACJW,KAAK;IACLC,sBAAsB;IACtBC,mBAAmB;IACnBC,UAAU;IACVC,kBAAkB;IAClBC,kBAAkB;IAClBC,uBAAuB;IACvBC,sBAAsB;IACtBC,uBAAuB;IACvBC,WAAW;IACXC;EACF,CAAC,GAAGhB,aAAa,CAACI,MAAM,CAAC;EACzB,MAAM;MACFa,QAAQ,EAAEC,YAAY;MACtBC,aAAa;MACbA,aAAa,EAAE;QACbC,QAAQ,GAAG,KAAK;QAChBC,gBAAgB;QAChBC;MACF,CAAC;MACDC,cAAc,EAAE;QACdC,OAAO;QACPC,SAAS;QACTC,OAAO;QACPC,MAAM;QACNC,SAAS;QACTC,OAAO;QACPC,KAAK;QACLC,SAAS;QACTC,OAAO;QACPC;MACF,CAAC;MACDC,iBAAiB;MACjBC,YAAY;MACZC;IACF,CAAC,GAAGhC,MAAM;IACViC,mBAAmB,GAAGlD,6BAA6B,CAACiB,MAAM,CAACmB,cAAc,EAAEnC,SAAS,CAAC;EACvF,MAAM;IACJkD,qBAAqB;IACrBC;EACF,CAAC,GAAGtC,wBAAwB,CAAC;IAC3BuC,QAAQ,EAAElC,KAAK,CAACkC,QAAQ;IACxB7B,kBAAkB;IAClBG,uBAAuB;IACvBD,sBAAsB;IACtBG;EACF,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAG5B,KAAK,CAACoD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMC,SAAS,GAAGlD,UAAU,CAAC0B,YAAY,EAAED,QAAQ,CAAC;EACpD,MAAM0B,eAAe,GAAGtD,KAAK,CAACoD,MAAM,CAACG,SAAS,CAAC;EAC/C,MAAMC,KAAK,GAAGpD,QAAQ,CAAC,CAAC;EACxB,MAAMqD,KAAK,GAAGD,KAAK,CAACE,SAAS,KAAK,KAAK;EACvC,MAAMC,YAAY,GAAG3D,KAAK,CAAC4D,OAAO,CAAC,MAAMlD,eAAe,CAACO,KAAK,CAACkC,QAAQ,EAAEM,KAAK,CAAC,EAAE,CAACxC,KAAK,CAACkC,QAAQ,EAAEM,KAAK,CAAC,CAAC;EACzG,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIC,eAAe;IACnB,IAAI/B,QAAQ,EAAE;MACZZ,mBAAmB,CAAC,IAAI,CAAC;MACzB;IACF;IACA,MAAM4C,iBAAiB,GAAG,CAACD,eAAe,GAAGlC,QAAQ,CAACoC,OAAO,CAACC,cAAc,KAAK,IAAI,GAAGH,eAAe,GAAG,CAAC;IAC3G,IAAII,gBAAgB;IACpB,IAAIH,iBAAiB,IAAI9C,KAAK,CAACkC,QAAQ,CAAC,CAAC,CAAC,CAACgB,YAAY,EAAE;MACvD;MACAD,gBAAgB,GAAG,CAAC;IACtB,CAAC,MAAM,IAAIH,iBAAiB,IAAI9C,KAAK,CAACkC,QAAQ,CAAClC,KAAK,CAACkC,QAAQ,CAACiB,MAAM,GAAG,CAAC,CAAC,CAACC,UAAU,EAAE;MACpF;MACAH,gBAAgB,GAAG,CAAC;IACtB,CAAC,MAAM;MACLA,gBAAgB,GAAGjD,KAAK,CAACkC,QAAQ,CAACmB,SAAS,CAACC,OAAO,IAAIA,OAAO,CAACJ,YAAY,GAAGI,OAAO,CAACC,cAAc,CAACJ,MAAM,GAAGL,iBAAiB,CAAC;IAClI;IACA,MAAMU,YAAY,GAAGP,gBAAgB,KAAK,CAAC,CAAC,GAAGjD,KAAK,CAACkC,QAAQ,CAACiB,MAAM,GAAG,CAAC,GAAGF,gBAAgB,GAAG,CAAC;IAC/F/C,mBAAmB,CAACsD,YAAY,CAAC;EACnC,CAAC;EACD,MAAMC,gBAAgB,GAAGxE,gBAAgB,CAAC,CAACyE,KAAK,EAAE,GAAGC,IAAI,KAAK;IAC5D;IACA;IACA,IAAID,KAAK,CAACE,kBAAkB,CAAC,CAAC,EAAE;MAC9B;IACF;IACA1C,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACwC,KAAK,EAAE,GAAGC,IAAI,CAAC;IAC1Cf,oBAAoB,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,MAAMiB,kBAAkB,GAAG5E,gBAAgB,CAACyE,KAAK,IAAI;IACnDpC,SAAS,IAAI,IAAI,IAAIA,SAAS,CAACoC,KAAK,CAAC;;IAErC;IACAA,KAAK,CAACI,cAAc,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAG9E,gBAAgB,CAAC,CAAC,GAAG0E,IAAI,KAAK;IACrDvC,OAAO,IAAI,IAAI,IAAIA,OAAO,CAAC,GAAGuC,IAAI,CAAC;IACnC;IACA,MAAMK,KAAK,GAAGrD,QAAQ,CAACoC,OAAO;IAC9BkB,MAAM,CAACC,YAAY,CAAC7B,eAAe,CAACU,OAAO,CAAC;IAC5CV,eAAe,CAACU,OAAO,GAAGoB,UAAU,CAAC,MAAM;MACzC;MACA,IAAI,CAACH,KAAK,IAAIA,KAAK,KAAKrD,QAAQ,CAACoC,OAAO,EAAE;QACxC;MACF;MACA,IAAI9C,sBAAsB,IAAI,IAAI,IAAIa,QAAQ,EAAE;QAC9C;MACF;MACA;MACA;MACAkD,KAAK,CAACI,KAAK,CAACjB,MAAM,IAAIkB,MAAM,CAACL,KAAK,CAACM,YAAY,CAAC,GAAGD,MAAM,CAACL,KAAK,CAAChB,cAAc,CAAC,KAAKgB,KAAK,CAACI,KAAK,CAACjB,MAAM,EAAE;QACtGjD,mBAAmB,CAAC,KAAK,CAAC;MAC5B,CAAC,MAAM;QACL0C,oBAAoB,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAM2B,eAAe,GAAGtF,gBAAgB,CAAC,CAAC,GAAG0E,IAAI,KAAK;IACpDtC,MAAM,IAAI,IAAI,IAAIA,MAAM,CAAC,GAAGsC,IAAI,CAAC;IACjCzD,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC,CAAC;EACF,MAAMsE,gBAAgB,GAAGvF,gBAAgB,CAACyE,KAAK,IAAI;IACjDnC,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACmC,KAAK,CAAC;IACjC,IAAI5C,QAAQ,EAAE;MACZ4C,KAAK,CAACI,cAAc,CAAC,CAAC;MACtB;IACF;IACA,MAAMW,WAAW,GAAGf,KAAK,CAACgB,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;IACvD,IAAI1E,sBAAsB,IAAIA,sBAAsB,CAAC2E,UAAU,KAAK3E,sBAAsB,CAAC4E,QAAQ,EAAE;MACnG,MAAMC,aAAa,GAAG9E,KAAK,CAACkC,QAAQ,CAACjC,sBAAsB,CAAC2E,UAAU,CAAC;MACvE,MAAMG,WAAW,GAAG,aAAa,CAACC,IAAI,CAACP,WAAW,CAAC;MACnD,MAAMQ,UAAU,GAAG,UAAU,CAACD,IAAI,CAACP,WAAW,CAAC;MAC/C,MAAMS,mBAAmB,GAAG,wCAAwC,CAACF,IAAI,CAACP,WAAW,CAAC;MACtF,MAAMU,kBAAkB,GAAGL,aAAa,CAACM,WAAW,KAAK,QAAQ,IAAIL,WAAW,IAAID,aAAa,CAACM,WAAW,KAAK,OAAO,IAAIH,UAAU,IAAIH,aAAa,CAACM,WAAW,KAAK,mBAAmB,IAAIF,mBAAmB;MACnN,IAAIC,kBAAkB,EAAE;QACtBlD,mBAAmB,CAAC,CAAC;QACrB5B,kBAAkB,CAAC;UACjByE,aAAa;UACbO,eAAe,EAAEZ,WAAW;UAC5Ba,qBAAqB,EAAE;QACzB,CAAC,CAAC;QACF;QACA5B,KAAK,CAACI,cAAc,CAAC,CAAC;QACtB;MACF;MACA,IAAIiB,WAAW,IAAIE,UAAU,EAAE;QAC7B;QACA;QACAvB,KAAK,CAACI,cAAc,CAAC,CAAC;QACtB;MACF;IACF;IACAJ,KAAK,CAACI,cAAc,CAAC,CAAC;IACtB7B,mBAAmB,CAAC,CAAC;IACrB3B,uBAAuB,CAACmE,WAAW,CAAC;EACtC,CAAC,CAAC;EACF,MAAMc,iBAAiB,GAAGtG,gBAAgB,CAACyE,KAAK,IAAI;IAClD,IAAI5C,QAAQ,EAAE;MACZ;IACF;IACA,MAAM0E,WAAW,GAAG9B,KAAK,CAAC+B,MAAM,CAACrB,KAAK;IACtC,IAAIoB,WAAW,KAAK,EAAE,EAAE;MACtBvD,mBAAmB,CAAC,CAAC;MACrB9B,UAAU,CAAC,CAAC;MACZ;IACF;IACA,MAAMuF,SAAS,GAAGhC,KAAK,CAACiC,WAAW,CAACC,IAAI;IACxC;IACA;IACA,MAAMC,kBAAkB,GAAGH,SAAS,IAAIA,SAAS,CAACvC,MAAM,GAAG,CAAC;IAC5D,MAAM2C,QAAQ,GAAGD,kBAAkB,GAAGH,SAAS,GAAGF,WAAW;IAC7D,MAAMO,aAAa,GAAGvG,WAAW,CAACsG,QAAQ,CAAC;;IAE3C;IACA;IACA,IAAI7F,sBAAsB,IAAI,IAAI,IAAI4F,kBAAkB,EAAE;MACxDvF,uBAAuB,CAACuF,kBAAkB,GAAGH,SAAS,GAAGK,aAAa,CAAC;MACvE;IACF;IACA,IAAIC,UAAU;IACd,IAAI/F,sBAAsB,CAAC2E,UAAU,KAAK,CAAC,IAAI3E,sBAAsB,CAAC4E,QAAQ,KAAK7E,KAAK,CAACkC,QAAQ,CAACiB,MAAM,GAAG,CAAC,IAAI4C,aAAa,CAAC5C,MAAM,KAAK,CAAC,EAAE;MAC1I6C,UAAU,GAAGD,aAAa;IAC5B,CAAC,MAAM;MACL,MAAME,YAAY,GAAGzG,WAAW,CAACoC,iBAAiB,CAACsE,uBAAuB,CAAClG,KAAK,CAACkC,QAAQ,EAAEM,KAAK,CAAC,CAAC;MAClG,IAAI2D,gBAAgB,GAAG,CAAC,CAAC;MACzB,IAAIC,cAAc,GAAG,CAAC,CAAC;MACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,CAAC9C,MAAM,EAAEkD,CAAC,IAAI,CAAC,EAAE;QAC/C,IAAIF,gBAAgB,KAAK,CAAC,CAAC,IAAIF,YAAY,CAACI,CAAC,CAAC,KAAKN,aAAa,CAACM,CAAC,CAAC,EAAE;UACnEF,gBAAgB,GAAGE,CAAC;QACtB;QACA,IAAID,cAAc,KAAK,CAAC,CAAC,IAAIH,YAAY,CAACA,YAAY,CAAC9C,MAAM,GAAGkD,CAAC,GAAG,CAAC,CAAC,KAAKN,aAAa,CAACA,aAAa,CAAC5C,MAAM,GAAGkD,CAAC,GAAG,CAAC,CAAC,EAAE;UACtHD,cAAc,GAAGC,CAAC;QACpB;MACF;MACA,MAAMvB,aAAa,GAAG9E,KAAK,CAACkC,QAAQ,CAACjC,sBAAsB,CAAC2E,UAAU,CAAC;MACvE,MAAM0B,6BAA6B,GAAGH,gBAAgB,GAAGrB,aAAa,CAACyB,KAAK,IAAIN,YAAY,CAAC9C,MAAM,GAAGiD,cAAc,GAAG,CAAC,GAAGtB,aAAa,CAAC0B,GAAG;MAC5I,IAAIF,6BAA6B,EAAE;QACjC;QACA;MACF;;MAEA;MACA,MAAMG,kCAAkC,GAAGV,aAAa,CAAC5C,MAAM,GAAG8C,YAAY,CAAC9C,MAAM,GAAG2B,aAAa,CAAC0B,GAAG,GAAGhH,WAAW,CAACsF,aAAa,CAAC4B,YAAY,IAAI,EAAE,CAAC,CAACvD,MAAM;MAChK6C,UAAU,GAAGD,aAAa,CAACY,KAAK,CAAC7B,aAAa,CAACyB,KAAK,GAAG/G,WAAW,CAACsF,aAAa,CAACvB,cAAc,IAAI,EAAE,CAAC,CAACJ,MAAM,EAAEsD,kCAAkC,CAAC;IACpJ;IACA,IAAIT,UAAU,CAAC7C,MAAM,KAAK,CAAC,EAAE;MAC3B,IAAI5D,SAAS,CAAC,CAAC,EAAE;QACfgB,sBAAsB,CAACuF,QAAQ,CAAC;MAClC,CAAC,MAAM;QACL7D,mBAAmB,CAAC,CAAC;QACrB7B,kBAAkB,CAAC,CAAC;MACtB;MACA;IACF;IACA4B,qBAAqB,CAAC;MACpBgE,UAAU;MACVxC,YAAY,EAAEvD,sBAAsB,CAAC2E;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMgC,kBAAkB,GAAG3H,gBAAgB,CAACyE,KAAK,IAAI;IACnDvC,SAAS,IAAI,IAAI,IAAIA,SAAS,CAACuC,KAAK,CAAC;;IAErC;IACA,QAAQ,IAAI;MACV;MACA,KAAKA,KAAK,CAACmD,GAAG,KAAK,GAAG,KAAKnD,KAAK,CAACoD,OAAO,IAAIpD,KAAK,CAACqD,OAAO,CAAC;QACxD;UACE;UACA;UACArD,KAAK,CAACI,cAAc,CAAC,CAAC;UACtB5D,mBAAmB,CAAC,KAAK,CAAC;UAC1B;QACF;;MAEF;MACA,KAAKwD,KAAK,CAACmD,GAAG,KAAK,YAAY;QAC7B;UACEnD,KAAK,CAACI,cAAc,CAAC,CAAC;UACtB,IAAI7D,sBAAsB,IAAI,IAAI,EAAE;YAClCC,mBAAmB,CAACwC,YAAY,CAACkC,UAAU,CAAC;UAC9C,CAAC,MAAM,IAAI3E,sBAAsB,CAAC2E,UAAU,KAAK3E,sBAAsB,CAAC4E,QAAQ,EAAE;YAChF3E,mBAAmB,CAACD,sBAAsB,CAAC4E,QAAQ,CAAC;UACtD,CAAC,MAAM;YACL,MAAM5B,gBAAgB,GAAGP,YAAY,CAACsE,SAAS,CAAC/G,sBAAsB,CAAC2E,UAAU,CAAC,CAACqC,UAAU;YAC7F,IAAIhE,gBAAgB,KAAK,IAAI,EAAE;cAC7B/C,mBAAmB,CAAC+C,gBAAgB,CAAC;YACvC;UACF;UACA;QACF;;MAEF;MACA,KAAKS,KAAK,CAACmD,GAAG,KAAK,WAAW;QAC5B;UACEnD,KAAK,CAACI,cAAc,CAAC,CAAC;UACtB,IAAI7D,sBAAsB,IAAI,IAAI,EAAE;YAClCC,mBAAmB,CAACwC,YAAY,CAACmC,QAAQ,CAAC;UAC5C,CAAC,MAAM,IAAI5E,sBAAsB,CAAC2E,UAAU,KAAK3E,sBAAsB,CAAC4E,QAAQ,EAAE;YAChF3E,mBAAmB,CAACD,sBAAsB,CAAC2E,UAAU,CAAC;UACxD,CAAC,MAAM;YACL,MAAM3B,gBAAgB,GAAGP,YAAY,CAACsE,SAAS,CAAC/G,sBAAsB,CAAC2E,UAAU,CAAC,CAACsC,SAAS;YAC5F,IAAIjE,gBAAgB,KAAK,IAAI,EAAE;cAC7B/C,mBAAmB,CAAC+C,gBAAgB,CAAC;YACvC;UACF;UACA;QACF;;MAEF;MACA,KAAKS,KAAK,CAACmD,GAAG,KAAK,QAAQ;QACzB;UACEnD,KAAK,CAACI,cAAc,CAAC,CAAC;UACtB,IAAIhD,QAAQ,EAAE;YACZ;UACF;UACA,IAAIb,sBAAsB,IAAI,IAAI,IAAIA,sBAAsB,CAAC2E,UAAU,KAAK,CAAC,IAAI3E,sBAAsB,CAAC4E,QAAQ,KAAK7E,KAAK,CAACkC,QAAQ,CAACiB,MAAM,GAAG,CAAC,EAAE;YAC9IhD,UAAU,CAAC,CAAC;UACd,CAAC,MAAM;YACLC,kBAAkB,CAAC,CAAC;UACtB;UACA6B,mBAAmB,CAAC,CAAC;UACrB;QACF;;MAEF;MACA,KAAK,CAAC,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,CAAC,CAACkF,QAAQ,CAACzD,KAAK,CAACmD,GAAG,CAAC;QACpF;UACEnD,KAAK,CAACI,cAAc,CAAC,CAAC;UACtB,IAAIhD,QAAQ,IAAIb,sBAAsB,IAAI,IAAI,EAAE;YAC9C;UACF;UACA,MAAM6E,aAAa,GAAG9E,KAAK,CAACkC,QAAQ,CAACjC,sBAAsB,CAAC2E,UAAU,CAAC;UACvE,MAAMwC,iBAAiB,GAAGxF,iBAAiB,CAACyF,oBAAoB,CAACtH,KAAK,EAAEC,KAAK,EAAE8E,aAAa,CAAC;UAC7F,MAAMO,eAAe,GAAG/F,kBAAkB,CAACS,KAAK,EAAEW,QAAQ,EAAEoE,aAAa,EAAEpB,KAAK,CAACmD,GAAG,EAAErG,uBAAuB,EAAE4G,iBAAiB,CAACE,IAAI,EAAE;YACrItG;UACF,CAAC,CAAC;UACFX,kBAAkB,CAAC;YACjByE,aAAa;YACbO,eAAe;YACfC,qBAAqB,EAAE;UACzB,CAAC,CAAC;UACF;QACF;IACJ;EACF,CAAC,CAAC;EACFtG,iBAAiB,CAAC,MAAM;IACtB,IAAI,CAAC2B,QAAQ,CAACoC,OAAO,EAAE;MACrB;IACF;IACA,IAAI9C,sBAAsB,IAAI,IAAI,EAAE;MAClC,IAAIU,QAAQ,CAACoC,OAAO,CAACwE,UAAU,EAAE;QAC/B;QACA;QACA;QACA5G,QAAQ,CAACoC,OAAO,CAACwE,UAAU,GAAG,CAAC;MACjC;MACA;IACF;IACA,MAAMC,oBAAoB,GAAGxH,KAAK,CAACkC,QAAQ,CAACjC,sBAAsB,CAAC2E,UAAU,CAAC;IAC9E,MAAM6C,mBAAmB,GAAGzH,KAAK,CAACkC,QAAQ,CAACjC,sBAAsB,CAAC4E,QAAQ,CAAC;IAC3E,IAAI7B,cAAc,GAAGwE,oBAAoB,CAACtE,YAAY;IACtD,IAAIoB,YAAY,GAAGmD,mBAAmB,CAACrE,UAAU;IACjD,IAAInD,sBAAsB,CAACyH,6BAA6B,EAAE;MACxD1E,cAAc,IAAIwE,oBAAoB,CAACjE,cAAc,CAACJ,MAAM;MAC5DmB,YAAY,IAAImD,mBAAmB,CAACf,YAAY,CAACvD,MAAM;IACzD;IACA,IAAIH,cAAc,KAAKrC,QAAQ,CAACoC,OAAO,CAACC,cAAc,IAAIsB,YAAY,KAAK3D,QAAQ,CAACoC,OAAO,CAACuB,YAAY,EAAE;MACxG;MACA,MAAMqD,gBAAgB,GAAGhH,QAAQ,CAACoC,OAAO,CAAC6E,SAAS;MACnD;MACA;MACA;MACA,IAAIjH,QAAQ,CAACoC,OAAO,KAAKnD,gBAAgB,CAACiI,QAAQ,CAAC,EAAE;QACnDlH,QAAQ,CAACoC,OAAO,CAAC+E,iBAAiB,CAAC9E,cAAc,EAAEsB,YAAY,CAAC;MAClE;MACA;MACA3D,QAAQ,CAACoC,OAAO,CAAC6E,SAAS,GAAGD,gBAAgB;IAC/C;EACF,CAAC,CAAC;EACF,MAAMI,eAAe,GAAG3I,aAAa,CAACR,QAAQ,CAAC,CAAC,CAAC,EAAEiC,aAAa,EAAE;IAChEuD,KAAK,EAAEpE,KAAK,CAACoE,KAAK;IAClB1D;EACF,CAAC,CAAC,EAAEoB,SAAS,EAAED,YAAY,CAACmG,WAAW,EAAEnG,YAAY,CAACoG,iBAAiB,CAAC;EACxE,MAAMC,UAAU,GAAGnJ,KAAK,CAAC4D,OAAO,CAAC,MAAM;IACrC;IACA;IACA,IAAInB,KAAK,KAAKc,SAAS,EAAE;MACvB,OAAOd,KAAK;IACd;IACA,OAAOK,YAAY,CAACsG,QAAQ,CAACJ,eAAe,CAAC;EAC/C,CAAC,EAAE,CAAClG,YAAY,EAAEkG,eAAe,EAAEvG,KAAK,CAAC,CAAC;EAC1CzC,KAAK,CAACqJ,SAAS,CAAC,MAAM;IACpB,IAAI,CAACF,UAAU,IAAI,CAACjI,sBAAsB,EAAE;MAC1CgC,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACjC,KAAK,CAACqI,cAAc,EAAEpI,sBAAsB,EAAEiI,UAAU,CAAC,CAAC,CAAC,CAAC;;EAEhEnJ,KAAK,CAACqJ,SAAS,CAAC,MAAM;IACpB;IACA,IAAIzH,QAAQ,CAACoC,OAAO,IAAIpC,QAAQ,CAACoC,OAAO,KAAK8E,QAAQ,CAACS,aAAa,EAAE;MACnEpI,mBAAmB,CAAC,KAAK,CAAC;IAC5B;IACA,OAAO,MAAM+D,MAAM,CAACC,YAAY,CAAC7B,eAAe,CAACU,OAAO,CAAC;EAC3D,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA;EACA;EACA;EACAhE,KAAK,CAACqJ,SAAS,CAAC,MAAM;IACpB,IAAIpI,KAAK,CAACuI,mBAAmB,IAAI,IAAI,IAAItI,sBAAsB,IAAI,IAAI,EAAE;MACvEgC,mBAAmB,CAAC,CAAC;MACrB7B,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACJ,KAAK,CAACuI,mBAAmB,CAAC,CAAC,CAAC,CAAC;;EAEjC,MAAMzC,QAAQ,GAAG/G,KAAK,CAAC4D,OAAO,CAAC,MAAM;IACnC,IAAI6F,qBAAqB;IACzB,OAAO,CAACA,qBAAqB,GAAGxI,KAAK,CAACuI,mBAAmB,KAAK,IAAI,GAAGC,qBAAqB,GAAG5G,iBAAiB,CAACsE,uBAAuB,CAAClG,KAAK,CAACkC,QAAQ,EAAEM,KAAK,CAAC;EAC/J,CAAC,EAAE,CAACxC,KAAK,CAACkC,QAAQ,EAAEN,iBAAiB,EAAE5B,KAAK,CAACuI,mBAAmB,EAAE/F,KAAK,CAAC,CAAC;EACzE,MAAMiG,SAAS,GAAG1J,KAAK,CAAC4D,OAAO,CAAC,MAAM;IACpC,IAAI1C,sBAAsB,IAAI,IAAI,EAAE;MAClC,OAAO,MAAM;IACf;IACA,IAAID,KAAK,CAACkC,QAAQ,CAACjC,sBAAsB,CAAC2E,UAAU,CAAC,CAACQ,WAAW,KAAK,QAAQ,EAAE;MAC9E,OAAO,MAAM;IACf;IACA,OAAO,SAAS;EAClB,CAAC,EAAE,CAACnF,sBAAsB,EAAED,KAAK,CAACkC,QAAQ,CAAC,CAAC;EAC5C,MAAMwG,aAAa,GAAG/H,QAAQ,CAACoC,OAAO,IAAIpC,QAAQ,CAACoC,OAAO,KAAKnD,gBAAgB,CAACiI,QAAQ,CAAC;EACzF,MAAMc,mBAAmB,GAAG9G,YAAY,CAAC+G,cAAc,CAAC7I,KAAK,EAAEC,KAAK,CAACoE,KAAK,EAAEvC,YAAY,CAACgH,UAAU,CAAC;EACpG,MAAMC,qBAAqB,GAAG,CAACJ,aAAa,IAAIC,mBAAmB;EACnE5J,KAAK,CAACgK,mBAAmB,CAAChI,gBAAgB,EAAE,OAAO;IACjDiI,WAAW,EAAEA,CAAA,KAAMhJ,KAAK,CAACkC,QAAQ;IACjC+G,qBAAqB,EAAEA,CAAA,KAAM;MAC3B,IAAIC,gBAAgB,EAAEC,aAAa,EAAEC,iBAAiB;MACtD,MAAMtG,iBAAiB,GAAG,CAACoG,gBAAgB,GAAGvI,QAAQ,CAACoC,OAAO,CAACC,cAAc,KAAK,IAAI,GAAGkG,gBAAgB,GAAG,CAAC;MAC7G,MAAMG,eAAe,GAAG,CAACF,aAAa,GAAGxI,QAAQ,CAACoC,OAAO,CAACuB,YAAY,KAAK,IAAI,GAAG6E,aAAa,GAAG,CAAC;MACnG,MAAMG,eAAe,GAAG,CAAC,EAAE,CAACF,iBAAiB,GAAGzI,QAAQ,CAACoC,OAAO,KAAK,IAAI,IAAIqG,iBAAiB,CAACtI,QAAQ,CAAC;MACxG,IAAIgC,iBAAiB,KAAK,CAAC,IAAIuG,eAAe,KAAK,CAAC,IAAIC,eAAe,EAAE;QACvE,OAAO,IAAI;MACb;MACA,MAAMrG,gBAAgB,GAAGH,iBAAiB,IAAI9C,KAAK,CAACkC,QAAQ,CAAC,CAAC,CAAC,CAACgB,YAAY,GAAG,CAAC,CAAC;MAAA,EAC/ElD,KAAK,CAACkC,QAAQ,CAACmB,SAAS,CAACC,OAAO,IAAIA,OAAO,CAACJ,YAAY,GAAGI,OAAO,CAACC,cAAc,CAACJ,MAAM,GAAGL,iBAAiB,CAAC;MAC/G,OAAOG,gBAAgB,KAAK,CAAC,CAAC,GAAGjD,KAAK,CAACkC,QAAQ,CAACiB,MAAM,GAAG,CAAC,GAAGF,gBAAgB,GAAG,CAAC;IACnF,CAAC;IACD/C,mBAAmB,EAAEqJ,kBAAkB,IAAIrJ,mBAAmB,CAACqJ,kBAAkB;EACnF,CAAC,CAAC,CAAC;EACH,MAAMC,gBAAgB,GAAGvK,gBAAgB,CAAC,CAACyE,KAAK,EAAE,GAAGC,IAAI,KAAK;IAC5D,IAAI8F,kBAAkB;IACtB/F,KAAK,CAACI,cAAc,CAAC,CAAC;IACtBpC,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACgC,KAAK,EAAE,GAAGC,IAAI,CAAC;IAC1CxD,UAAU,CAAC,CAAC;IACZQ,QAAQ,IAAI,IAAI,IAAI,CAAC8I,kBAAkB,GAAG9I,QAAQ,CAACoC,OAAO,KAAK,IAAI,IAAI0G,kBAAkB,CAACC,KAAK,CAAC,CAAC;IACjGxJ,mBAAmB,CAAC,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,OAAOtB,QAAQ,CAAC;IACd6B,WAAW;IACXkJ,YAAY,EAAE,KAAK;IACnBhI,QAAQ,EAAEiI,OAAO,CAACjI,QAAQ;EAC5B,CAAC,EAAEI,mBAAmB,EAAE;IACtBqC,KAAK,EAAE0E,qBAAqB,GAAG,EAAE,GAAGhD,QAAQ;IAC5C2C,SAAS;IACT3H,QAAQ;IACRI,OAAO,EAAEuC,gBAAgB;IACzBrC,OAAO,EAAE2C,gBAAgB;IACzB1C,MAAM,EAAEkD,eAAe;IACvBhD,OAAO,EAAEiD,gBAAgB;IACzBqF,QAAQ,EAAEtE,iBAAiB;IAC3BpE,SAAS,EAAEyF,kBAAkB;IAC7BtF,SAAS,EAAEuC,kBAAkB;IAC7BnC,OAAO,EAAE8H,gBAAgB;IACzBhI,KAAK,EAAE0G,UAAU;IACjB4B,GAAG,EAAE1H,SAAS;IACdX,SAAS,EAAEmI,OAAO,CAACnI,SAAS,IAAI,CAACkH,mBAAmB,IAAI,CAAC7H,QAAQ,IAAI,CAACa,QAAQ;EAChF,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}