{"ast": null, "code": "import { previousDay } from \"./previousDay.mjs\";\n\n/**\n * @name previousSaturday\n * @category Weekday Helpers\n * @summary When is the previous Saturday?\n *\n * @description\n * When is the previous Saturday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to start counting from\n *\n * @returns The previous Saturday\n *\n * @example\n * // When is the previous Saturday before Jun, 20, 2021?\n * const result = previousSaturday(new Date(2021, 5, 20))\n * //=> Sat June 19 2021 00:00:00\n */\nexport function previousSaturday(date) {\n  return previousDay(date, 6);\n}\n\n// Fallback for modularized imports:\nexport default previousSaturday;", "map": {"version": 3, "names": ["previousDay", "previousSaturday", "date"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/previousSaturday.mjs"], "sourcesContent": ["import { previousDay } from \"./previousDay.mjs\";\n\n/**\n * @name previousSaturday\n * @category Weekday Helpers\n * @summary When is the previous Saturday?\n *\n * @description\n * When is the previous Saturday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to start counting from\n *\n * @returns The previous Saturday\n *\n * @example\n * // When is the previous Saturday before Jun, 20, 2021?\n * const result = previousSaturday(new Date(2021, 5, 20))\n * //=> Sat June 19 2021 00:00:00\n */\nexport function previousSaturday(date) {\n  return previousDay(date, 6);\n}\n\n// Fallback for modularized imports:\nexport default previousSaturday;\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,mBAAmB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EACrC,OAAOF,WAAW,CAACE,IAAI,EAAE,CAAC,CAAC;AAC7B;;AAEA;AACA,eAAeD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}