{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'menos de um segundo',\n    other: 'menos de {{count}} segundos'\n  },\n  xSeconds: {\n    one: '1 segundo',\n    other: '{{count}} segundos'\n  },\n  halfAMinute: 'meio minuto',\n  lessThanXMinutes: {\n    one: 'menos de um minuto',\n    other: 'menos de {{count}} minutos'\n  },\n  xMinutes: {\n    one: '1 minuto',\n    other: '{{count}} minutos'\n  },\n  aboutXHours: {\n    one: 'cerca de 1 hora',\n    other: 'cerca de {{count}} horas'\n  },\n  xHours: {\n    one: '1 hora',\n    other: '{{count}} horas'\n  },\n  xDays: {\n    one: '1 dia',\n    other: '{{count}} dias'\n  },\n  aboutXWeeks: {\n    one: 'cerca de 1 semana',\n    other: 'cerca de {{count}} semanas'\n  },\n  xWeeks: {\n    one: '1 semana',\n    other: '{{count}} semanas'\n  },\n  aboutXMonths: {\n    one: 'cerca de 1 mês',\n    other: 'cerca de {{count}} meses'\n  },\n  xMonths: {\n    one: '1 mês',\n    other: '{{count}} meses'\n  },\n  aboutXYears: {\n    one: 'cerca de 1 ano',\n    other: 'cerca de {{count}} anos'\n  },\n  xYears: {\n    one: '1 ano',\n    other: '{{count}} anos'\n  },\n  overXYears: {\n    one: 'mais de 1 ano',\n    other: 'mais de {{count}} anos'\n  },\n  almostXYears: {\n    one: 'quase 1 ano',\n    other: 'quase {{count}} anos'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'em ' + result;\n    } else {\n      return 'há ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/pt-BR/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'menos de um segundo',\n    other: 'menos de {{count}} segundos'\n  },\n  xSeconds: {\n    one: '1 segundo',\n    other: '{{count}} segundos'\n  },\n  halfAMinute: 'meio minuto',\n  lessThanXMinutes: {\n    one: 'menos de um minuto',\n    other: 'menos de {{count}} minutos'\n  },\n  xMinutes: {\n    one: '1 minuto',\n    other: '{{count}} minutos'\n  },\n  aboutXHours: {\n    one: 'cerca de 1 hora',\n    other: 'cerca de {{count}} horas'\n  },\n  xHours: {\n    one: '1 hora',\n    other: '{{count}} horas'\n  },\n  xDays: {\n    one: '1 dia',\n    other: '{{count}} dias'\n  },\n  aboutXWeeks: {\n    one: 'cerca de 1 semana',\n    other: 'cerca de {{count}} semanas'\n  },\n  xWeeks: {\n    one: '1 semana',\n    other: '{{count}} semanas'\n  },\n  aboutXMonths: {\n    one: 'cerca de 1 mês',\n    other: 'cerca de {{count}} meses'\n  },\n  xMonths: {\n    one: '1 mês',\n    other: '{{count}} meses'\n  },\n  aboutXYears: {\n    one: 'cerca de 1 ano',\n    other: 'cerca de {{count}} anos'\n  },\n  xYears: {\n    one: '1 ano',\n    other: '{{count}} anos'\n  },\n  overXYears: {\n    one: 'mais de 1 ano',\n    other: 'mais de {{count}} anos'\n  },\n  almostXYears: {\n    one: 'quase 1 ano',\n    other: 'quase {{count}} anos'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'em ' + result;\n    } else {\n      return 'há ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,aAAa;EAC1BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAO,KAAK,GAAGA,MAAM;IACvB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}