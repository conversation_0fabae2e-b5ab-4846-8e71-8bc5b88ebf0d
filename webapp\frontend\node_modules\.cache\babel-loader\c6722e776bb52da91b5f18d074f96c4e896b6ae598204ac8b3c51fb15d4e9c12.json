{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nimport axiosInstance from './axiosConfig';\nconst API_URL = config.API_URL;\nconst userService = {\n  // Ottiene la lista di tutti gli utenti\n  getUsers: async () => {\n    try {\n      const response = await axiosInstance.get('/users');\n      return response.data;\n    } catch (error) {\n      console.error('Get users error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea un nuovo utente\n  createUser: async userData => {\n    try {\n      const response = await axiosInstance.post('/users', userData);\n      return response.data;\n    } catch (error) {\n      console.error('Create user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna un utente esistente\n  updateUser: async (userId, userData) => {\n    try {\n      const response = await axiosInstance.put(`/users/${userId}`, userData);\n      return response.data;\n    } catch (error) {\n      console.error('Update user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina un utente\n  deleteUser: async userId => {\n    try {\n      const response = await axiosInstance.delete(`/users/${userId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Abilita/disabilita un utente\n  toggleUserStatus: async userId => {\n    try {\n      const response = await axiosInstance.get(`/users/toggle/${userId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Toggle user status error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene una visualizzazione raw del database\n  getDbRaw: async () => {\n    try {\n      console.log('Chiamata API a /users/db-raw');\n      const response = await axiosInstance.get('/users/db-raw');\n      console.log('Risposta API ricevuta:', response);\n      return response.data;\n    } catch (error) {\n      console.error('Get DB raw error:', error);\n      console.error('Dettagli errore:', error.response ? error.response.data : 'Nessun dettaglio disponibile');\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Verifica e disabilita gli utenti scaduti\n  checkExpiredUsers: async () => {\n    try {\n      const response = await axiosInstance.post('/users/check-expired');\n      return response.data;\n    } catch (error) {\n      console.error('Check expired users error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default userService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "userService", "getUsers", "response", "get", "data", "error", "console", "createUser", "userData", "post", "updateUser", "userId", "put", "deleteUser", "delete", "toggleUserStatus", "getDbRaw", "log", "checkExpiredUsers"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/userService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\nimport axiosInstance from './axiosConfig';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\nconst userService = {\r\n  // Ottiene la lista di tutti gli utenti\r\n  getUsers: async () => {\r\n    try {\r\n      const response = await axiosInstance.get('/users');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get users error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea un nuovo utente\r\n  createUser: async (userData) => {\r\n    try {\r\n      const response = await axiosInstance.post('/users', userData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Create user error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Aggiorna un utente esistente\r\n  updateUser: async (userId, userData) => {\r\n    try {\r\n      const response = await axiosInstance.put(`/users/${userId}`, userData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Update user error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Elimina un utente\r\n  deleteUser: async (userId) => {\r\n    try {\r\n      const response = await axiosInstance.delete(`/users/${userId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Delete user error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Abilita/disabilita un utente\r\n  toggleUserStatus: async (userId) => {\r\n    try {\r\n      const response = await axiosInstance.get(`/users/toggle/${userId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Toggle user status error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene una visualizzazione raw del database\r\n  getDbRaw: async () => {\r\n    try {\r\n      console.log('Chiamata API a /users/db-raw');\r\n      const response = await axiosInstance.get('/users/db-raw');\r\n      console.log('Risposta API ricevuta:', response);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get DB raw error:', error);\r\n      console.error('Dettagli errore:', error.response ? error.response.data : 'Nessun dettaglio disponibile');\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Verifica e disabilita gli utenti scaduti\r\n  checkExpiredUsers: async () => {\r\n    try {\r\n      const response = await axiosInstance.post('/users/check-expired');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Check expired users error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default userService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,aAAa,MAAM,eAAe;AAEzC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO;AAE9B,MAAMC,WAAW,GAAG;EAClB;EACAC,QAAQ,EAAE,MAAAA,CAAA,KAAY;IACpB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACK,GAAG,CAAC,QAAQ,CAAC;MAClD,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAE,UAAU,EAAE,MAAOC,QAAQ,IAAK;IAC9B,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMJ,aAAa,CAACW,IAAI,CAAC,QAAQ,EAAED,QAAQ,CAAC;MAC7D,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAK,UAAU,EAAE,MAAAA,CAAOC,MAAM,EAAEH,QAAQ,KAAK;IACtC,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMJ,aAAa,CAACc,GAAG,CAAC,UAAUD,MAAM,EAAE,EAAEH,QAAQ,CAAC;MACtE,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAQ,UAAU,EAAE,MAAOF,MAAM,IAAK;IAC5B,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMJ,aAAa,CAACgB,MAAM,CAAC,UAAUH,MAAM,EAAE,CAAC;MAC/D,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAU,gBAAgB,EAAE,MAAOJ,MAAM,IAAK;IAClC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMJ,aAAa,CAACK,GAAG,CAAC,iBAAiBQ,MAAM,EAAE,CAAC;MACnE,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAW,QAAQ,EAAE,MAAAA,CAAA,KAAY;IACpB,IAAI;MACFV,OAAO,CAACW,GAAG,CAAC,8BAA8B,CAAC;MAC3C,MAAMf,QAAQ,GAAG,MAAMJ,aAAa,CAACK,GAAG,CAAC,eAAe,CAAC;MACzDG,OAAO,CAACW,GAAG,CAAC,wBAAwB,EAAEf,QAAQ,CAAC;MAC/C,OAAOA,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAG,8BAA8B,CAAC;MACxG,MAAMC,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAa,iBAAiB,EAAE,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMJ,aAAa,CAACW,IAAI,CAAC,sBAAsB,CAAC;MACjE,OAAOP,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}