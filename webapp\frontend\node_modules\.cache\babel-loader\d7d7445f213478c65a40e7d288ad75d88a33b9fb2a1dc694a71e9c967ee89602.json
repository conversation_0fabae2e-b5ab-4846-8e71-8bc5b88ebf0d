{"ast": null, "code": "import { unstable_generateUtilityClass as generateUtilityClass, unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nexport function getTimeClockUtilityClass(slot) {\n  return generateUtilityClass('MuiTimeClock', slot);\n}\nexport const timeClockClasses = generateUtilityClasses('MuiTimeClock', ['root', 'arrowSwitcher']);", "map": {"version": 3, "names": ["unstable_generateUtilityClass", "generateUtilityClass", "unstable_generateUtilityClasses", "generateUtilityClasses", "getTimeClockUtilityClass", "slot", "timeClockClasses"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/TimeClock/timeClockClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClass as generateUtilityClass, unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nexport function getTimeClockUtilityClass(slot) {\n  return generateUtilityClass('MuiTimeClock', slot);\n}\nexport const timeClockClasses = generateUtilityClasses('MuiTimeClock', ['root', 'arrowSwitcher']);"], "mappings": "AAAA,SAASA,6BAA6B,IAAIC,oBAAoB,EAAEC,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AAC7I,OAAO,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EAC7C,OAAOJ,oBAAoB,CAAC,cAAc,EAAEI,IAAI,CAAC;AACnD;AACA,OAAO,MAAMC,gBAAgB,GAAGH,sBAAsB,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}