{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CaviFilterableTable.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, Checkbox, Button } from '@mui/material';\nimport { CheckBox as CheckBoxIcon, Clear as ClearIcon, Straighten as RulerIcon, Settings as SettingsIcon, PlayArrow as StartIcon } from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\nimport SmartCaviFilter from './SmartCaviFilter';\nimport ContextMenu from '../common/ContextMenu';\nimport useContextMenu from '../../hooks/useContextMenu';\nimport { formatDate } from '../../utils/dateUtils';\n\n/**\n * Componente per visualizzare la lista dei cavi con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.cavi - Lista dei cavi da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {string} props.revisioneCorrente - Revisione corrente da mostrare nelle statistiche\n * @param {boolean} props.selectionEnabled - Abilita la selezione dei cavi\n * @param {Array} props.selectedCavi - Array degli ID dei cavi selezionati\n * @param {Function} props.onSelectionChange - Funzione chiamata quando cambia la selezione\n * @param {Array} props.contextMenuItems - Array di elementi per il menu contestuale\n * @param {Function} props.onContextMenuAction - Funzione chiamata quando si clicca su un elemento del menu contestuale\n * @param {Function} props.onStatusAction - Funzione chiamata quando si clicca sul pulsante stato\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CaviFilterableTable = ({\n  cavi = [],\n  loading = false,\n  onFilteredDataChange = null,\n  revisioneCorrente = null,\n  selectionEnabled = false,\n  selectedCavi = [],\n  onSelectionChange = null,\n  contextMenuItems = [],\n  onContextMenuAction = null,\n  onStatusAction = null\n}) => {\n  _s();\n  const [filteredCavi, setFilteredCavi] = useState(cavi);\n  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi);\n\n  // Hook per il menu contestuale\n  const {\n    contextMenu,\n    handleContextMenu,\n    closeContextMenu\n  } = useContextMenu();\n\n  // Aggiorna i dati filtrati quando cambiano i cavi\n  useEffect(() => {\n    setFilteredCavi(cavi);\n    setSmartFilteredCavi(cavi);\n  }, [cavi]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = data => {\n    setFilteredCavi(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Gestisce il cambio dei dati dal filtro intelligente\n  const handleSmartFilterChange = data => {\n    console.log('CaviFilterableTable - Smart filter change:', {\n      originalCount: cavi.length,\n      filteredCount: data.length,\n      filteredIds: data.map(c => c.id_cavo)\n    });\n    setSmartFilteredCavi(data);\n    // Il filtro intelligente ha la priorità sui filtri Excel-like\n    setFilteredCavi(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Gestisce la selezione di un singolo cavo\n  const handleCavoToggle = cavoId => {\n    if (!selectionEnabled || !onSelectionChange) return;\n    const isSelected = selectedCavi.includes(cavoId);\n    let newSelection;\n    if (isSelected) {\n      // Rimuovi dalla selezione\n      newSelection = selectedCavi.filter(id => id !== cavoId);\n      console.log(`Cavo ${cavoId} deselezionato`);\n    } else {\n      // Aggiungi alla selezione\n      newSelection = [...selectedCavi, cavoId];\n      console.log(`Cavo ${cavoId} selezionato`);\n    }\n    onSelectionChange(newSelection);\n\n    // Feedback visivo rapido (opzionale - può essere rimosso se troppo invasivo)\n    // Potresti aggiungere qui un piccolo toast o animazione\n  };\n\n  // Seleziona tutti i cavi visibili (filtrati)\n  const handleSelectAll = () => {\n    if (!selectionEnabled || !onSelectionChange) return;\n    const visibleCaviIds = filteredCavi.map(cavo => cavo.id_cavo);\n    const allSelected = visibleCaviIds.every(id => selectedCavi.includes(id));\n    if (allSelected) {\n      // Deseleziona tutti i cavi visibili\n      const newSelection = selectedCavi.filter(id => !visibleCaviIds.includes(id));\n      onSelectionChange(newSelection);\n    } else {\n      // Seleziona tutti i cavi visibili\n      const newSelection = [...new Set([...selectedCavi, ...visibleCaviIds])];\n      onSelectionChange(newSelection);\n    }\n  };\n\n  // Deseleziona tutti i cavi\n  const handleClearSelection = () => {\n    if (!selectionEnabled || !onSelectionChange) return;\n    onSelectionChange([]);\n  };\n\n  // Definizione delle colonne\n  const columns = [\n  // Colonna di selezione (solo se abilitata)\n  ...(selectionEnabled ? [{\n    field: 'selection',\n    headerName: '',\n    disableFilter: true,\n    disableSort: true,\n    width: 50,\n    align: 'center',\n    headerStyle: {\n      width: '50px',\n      padding: '4px'\n    },\n    cellStyle: {\n      width: '50px',\n      padding: '4px',\n      textAlign: 'center'\n    },\n    renderHeader: () => {\n      const visibleCaviIds = filteredCavi.map(cavo => cavo.id_cavo);\n      const allSelected = visibleCaviIds.length > 0 && visibleCaviIds.every(id => selectedCavi.includes(id));\n      const someSelected = visibleCaviIds.some(id => selectedCavi.includes(id));\n      return /*#__PURE__*/_jsxDEV(Checkbox, {\n        checked: allSelected,\n        indeterminate: someSelected && !allSelected,\n        onChange: handleSelectAll,\n        size: \"small\",\n        title: allSelected ? \"Deseleziona tutti\" : \"Seleziona tutti\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this);\n    },\n    renderCell: row => /*#__PURE__*/_jsxDEV(Checkbox, {\n      checked: selectedCavi.includes(row.id_cavo),\n      onChange: () => handleCavoToggle(row.id_cavo),\n      size: \"small\",\n      onClick: e => e.stopPropagation()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 9\n    }, this)\n  }] : []), {\n    field: 'id_cavo',\n    headerName: 'ID Cavo',\n    dataType: 'text',\n    headerStyle: {\n      fontWeight: 'bold'\n    }\n  },\n  // Colonna Revisione rimossa e spostata nella tabella delle statistiche\n  {\n    field: 'sistema',\n    headerName: 'Sistema',\n    dataType: 'text'\n  }, {\n    field: 'utility',\n    headerName: 'Utility',\n    dataType: 'text'\n  }, {\n    field: 'tipologia',\n    headerName: 'Tipologia',\n    dataType: 'text'\n  },\n  // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n  {\n    field: 'sezione',\n    headerName: 'Formazione',\n    dataType: 'text',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    }\n  }, {\n    field: 'metri_teorici',\n    headerName: 'Metri Teorici',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    },\n    renderCell: row => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'\n  }, {\n    field: 'metratura_reale',\n    headerName: 'Metri Reali',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    },\n    renderCell: row => row.metratura_reale ? row.metratura_reale.toFixed(1) : '0'\n  }, {\n    field: 'stato_installazione',\n    headerName: 'Stato',\n    dataType: 'text',\n    renderCell: row => {\n      var _row$stato_installazi;\n      // Determina colore, icona e azione in base allo stato\n      let color = 'default';\n      let icon = null;\n      let actionLabel = '';\n      let actionType = '';\n\n      // Normalizza lo stato per gestire diverse varianti\n      const statoNormalizzato = (_row$stato_installazi = row.stato_installazione) === null || _row$stato_installazi === void 0 ? void 0 : _row$stato_installazi.toUpperCase();\n      if (statoNormalizzato === 'INSTALLATO' || row.stato_installazione === 'Installato') {\n        color = 'success';\n        icon = /*#__PURE__*/_jsxDEV(SettingsIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 18\n        }, this);\n        actionLabel = 'Modifica Bobina';\n        actionType = 'modify_reel';\n      } else if (statoNormalizzato === 'IN_CORSO' || row.stato_installazione === 'In corso') {\n        color = 'warning';\n        icon = /*#__PURE__*/_jsxDEV(RulerIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 18\n        }, this);\n        actionLabel = 'Inserisci Metri Posati';\n        actionType = 'insert_meters';\n      } else if (statoNormalizzato === 'DA_INSTALLARE' || row.stato_installazione === 'Da installare' || !row.stato_installazione) {\n        color = 'error';\n        icon = /*#__PURE__*/_jsxDEV(StartIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 18\n        }, this);\n        actionLabel = 'Inserisci Metri Posati';\n        actionType = 'insert_meters';\n      }\n\n      // Debug: verifica se onStatusAction è definito e se c'è un'azione per questo stato\n      console.log('🔍 Pulsante stato per cavo:', row.id_cavo, {\n        stato: row.stato_installazione,\n        actionType,\n        actionLabel,\n        willBeClickable: !!(onStatusAction && actionType && actionLabel)\n      });\n\n      // Determina se il pulsante deve essere cliccabile\n      const isClickable = onStatusAction && actionType && actionLabel;\n      return /*#__PURE__*/_jsxDEV(Chip, {\n        label: row.stato_installazione || 'N/D',\n        size: \"small\",\n        color: color,\n        variant: \"outlined\",\n        icon: icon,\n        onClick: isClickable ? e => {\n          e.stopPropagation();\n          console.log('🔥 CLICK su pulsante stato!', {\n            cavoId: row.id_cavo,\n            stato: row.stato_installazione,\n            actionType,\n            actionLabel\n          });\n          onStatusAction(row, actionType, actionLabel);\n        } : undefined,\n        sx: {\n          cursor: isClickable ? 'pointer' : 'default',\n          transition: 'all 0.2s ease',\n          '&:hover': isClickable ? {\n            transform: 'scale(1.05)',\n            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n            backgroundColor: `${color}.light`\n          } : {},\n          // Aggiungi un bordo più marcato per i pulsanti cliccabili\n          border: isClickable ? '2px solid currentColor' : '1px solid currentColor'\n        },\n        title: isClickable ? actionLabel : 'Nessuna azione disponibile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    field: 'id_bobina',\n    headerName: 'Bobina',\n    dataType: 'text',\n    renderCell: row => {\n      // Gestione differenziata per null e BOBINA_VUOTA\n      if (row.id_bobina === null) {\n        // Per cavi non posati (id_bobina è null)\n        return '-';\n      } else if (row.id_bobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        return 'BOBINA VUOTA';\n      } else if (!row.id_bobina) {\n        // Per altri casi in cui id_bobina è falsy (undefined, stringa vuota)\n        return '-';\n      }\n\n      // Estrai solo il numero della bobina (parte dopo '_B')\n      const match = row.id_bobina.match(/_B(.+)$/);\n      return match ? match[1] : row.id_bobina;\n    }\n  }, {\n    field: 'timestamp',\n    headerName: 'Data Modifica',\n    dataType: 'date',\n    renderCell: row => formatDate(row.timestamp)\n  }, {\n    field: 'collegamenti',\n    headerName: 'Collegamenti',\n    dataType: 'number',\n    align: 'center',\n    cellStyle: {\n      textAlign: 'center'\n    },\n    renderCell: row => {\n      let color = 'default';\n      if (row.collegamenti === 2) color = 'success';else if (row.collegamenti === 1) color = 'warning';else color = 'error';\n      return /*#__PURE__*/_jsxDEV(Chip, {\n        label: row.collegamenti,\n        size: \"small\",\n        color: color,\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 11\n      }, this);\n    }\n  }];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    var _row$stato_installazi2;\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    const statoNormalizzato = (_row$stato_installazi2 = row.stato_installazione) === null || _row$stato_installazi2 === void 0 ? void 0 : _row$stato_installazi2.toUpperCase();\n    if (statoNormalizzato === 'INSTALLATO' || row.stato_installazione === 'Installato') {\n      bgColor = 'rgba(76, 175, 80, 0.1)';\n    } else if (statoNormalizzato === 'IN_CORSO' || row.stato_installazione === 'In corso') {\n      bgColor = 'rgba(255, 152, 0, 0.1)';\n    }\n\n    // Se la selezione è abilitata, evidenzia le righe selezionate\n    const isSelected = selectionEnabled && selectedCavi.includes(row.id_cavo);\n    if (isSelected) {\n      bgColor = 'rgba(25, 118, 210, 0.12)'; // Blu delicato per le righe selezionate\n    }\n    return /*#__PURE__*/_jsxDEV(TableRow, {\n      selected: isSelected,\n      hover: true,\n      onClick: selectionEnabled ? () => handleCavoToggle(row.id_cavo) : undefined,\n      onContextMenu: e => contextMenuItems.length > 0 ? handleContextMenu(e, row) : undefined,\n      sx: {\n        backgroundColor: bgColor,\n        cursor: selectionEnabled ? 'pointer' : 'default',\n        transition: 'all 0.2s ease',\n        border: isSelected ? '2px solid #1976d2' : 'none',\n        '&:hover': {\n          backgroundColor: selectionEnabled ? isSelected ? 'rgba(25, 118, 210, 0.18)' : 'rgba(25, 118, 210, 0.08)' : 'rgba(0, 0, 0, 0.04)',\n          transform: selectionEnabled ? 'scale(1.005)' : 'none',\n          boxShadow: selectionEnabled ? '0 1px 4px rgba(0,0,0,0.08)' : 'none'\n        }\n      },\n      children: columns.map(column => /*#__PURE__*/_jsxDEV(TableCell, {\n        align: column.align || 'left',\n        sx: column.cellStyle,\n        children: column.renderCell ? column.renderCell(row) : row[column.field]\n      }, column.field, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 11\n      }, this))\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(SmartCaviFilter, {\n      cavi: cavi,\n      onFilteredDataChange: handleSmartFilterChange,\n      loading: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 7\n    }, this), selectionEnabled && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2,\n        p: 2,\n        backgroundColor: selectedCavi.length > 0 ? 'rgba(25, 118, 210, 0.15)' : 'rgba(25, 118, 210, 0.08)',\n        borderRadius: 2,\n        border: selectedCavi.length > 0 ? '2px solid rgba(25, 118, 210, 0.5)' : '2px solid rgba(25, 118, 210, 0.25)',\n        transition: 'all 0.3s ease',\n        boxShadow: selectedCavi.length > 0 ? '0 4px 12px rgba(25, 118, 210, 0.25)' : '0 2px 6px rgba(25, 118, 210, 0.1)'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [selectedCavi.length > 0 && /*#__PURE__*/_jsxDEV(CheckBoxIcon, {\n            color: \"primary\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            sx: {\n              fontWeight: 600,\n              color: selectedCavi.length > 0 ? 'primary.main' : 'text.primary'\n            },\n            children: selectedCavi.length > 0 ? `${selectedCavi.length} cavi selezionati` : 'Modalità selezione attiva - Click sui cavi per selezionarli'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"small\",\n            onClick: handleSelectAll,\n            disabled: filteredCavi.length === 0,\n            children: filteredCavi.length > 0 && filteredCavi.every(cavo => selectedCavi.includes(cavo.id_cavo)) ? 'Deseleziona tutti' : 'Seleziona tutti'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this), selectedCavi.length > 0 && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"small\",\n            startIcon: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 30\n            }, this),\n            onClick: handleClearSelection,\n            color: \"error\",\n            children: \"Cancella selezione\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n      data: smartFilteredCavi,\n      columns: columns,\n      onFilteredDataChange: handleFilteredDataChange,\n      loading: loading,\n      emptyMessage: \"Nessun cavo disponibile\",\n      renderRow: renderRow\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ContextMenu, {\n      open: contextMenu.open,\n      anchorPosition: contextMenu.anchorPosition,\n      onClose: closeContextMenu,\n      menuItems: typeof contextMenuItems === 'function' ? contextMenuItems(contextMenu.contextData) : contextMenuItems,\n      contextData: contextMenu.contextData\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 390,\n    columnNumber: 5\n  }, this);\n};\n_s(CaviFilterableTable, \"8HPaO1mqoR5Gc08zbVuoa6vh04g=\", false, function () {\n  return [useContextMenu];\n});\n_c = CaviFilterableTable;\nexport default CaviFilterableTable;\nvar _c;\n$RefreshReg$(_c, \"CaviFilterableTable\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Chip", "TableRow", "TableCell", "Checkbox", "<PERSON><PERSON>", "CheckBox", "CheckBoxIcon", "Clear", "ClearIcon", "<PERSON>en", "RulerIcon", "Settings", "SettingsIcon", "PlayArrow", "StartIcon", "FilterableTable", "SmartCaviFilter", "ContextMenu", "useContextMenu", "formatDate", "jsxDEV", "_jsxDEV", "CaviFilterableTable", "cavi", "loading", "onFilteredDataChange", "revisioneCorrente", "selectionEnabled", "<PERSON><PERSON><PERSON>", "onSelectionChange", "contextMenuItems", "onContextMenuAction", "onStatusAction", "_s", "filteredCavi", "setFilteredCavi", "smartFilteredCavi", "setSmartFilteredCavi", "contextMenu", "handleContextMenu", "closeContextMenu", "handleFilteredDataChange", "data", "handleSmartFilterChange", "console", "log", "originalCount", "length", "filteredCount", "filteredIds", "map", "c", "id_cavo", "handleCavoToggle", "cavoId", "isSelected", "includes", "newSelection", "filter", "id", "handleSelectAll", "visibleCaviIds", "cavo", "allSelected", "every", "Set", "handleClearSelection", "columns", "field", "headerName", "disableFilter", "disableSort", "width", "align", "headerStyle", "padding", "cellStyle", "textAlign", "renderHeader", "someSelected", "some", "checked", "indeterminate", "onChange", "size", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderCell", "row", "onClick", "e", "stopPropagation", "dataType", "fontWeight", "metri_te<PERSON>ci", "toFixed", "metratura_reale", "_row$stato_installazi", "color", "icon", "actionLabel", "actionType", "statoNormalizzato", "stato_installazione", "toUpperCase", "fontSize", "stato", "willBeClickable", "isClickable", "label", "variant", "undefined", "sx", "cursor", "transition", "transform", "boxShadow", "backgroundColor", "border", "id_bobina", "match", "timestamp", "colle<PERSON>nti", "renderRow", "index", "_row$stato_installazi2", "bgColor", "selected", "hover", "onContextMenu", "children", "column", "mb", "p", "borderRadius", "display", "justifyContent", "alignItems", "gap", "disabled", "startIcon", "emptyMessage", "open", "anchorPosition", "onClose", "menuItems", "contextData", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/CaviFilterableTable.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, Checkbox, Button } from '@mui/material';\nimport {\n  CheckBox as CheckBoxIcon,\n  Clear as ClearIcon,\n  Straighten as RulerIcon,\n  Settings as SettingsIcon,\n  PlayArrow as StartIcon\n} from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\nimport SmartCaviFilter from './SmartCaviFilter';\nimport ContextMenu from '../common/ContextMenu';\nimport useContextMenu from '../../hooks/useContextMenu';\nimport { formatDate } from '../../utils/dateUtils';\n\n/**\n * Componente per visualizzare la lista dei cavi con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.cavi - Lista dei cavi da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {string} props.revisioneCorrente - Revisione corrente da mostrare nelle statistiche\n * @param {boolean} props.selectionEnabled - Abilita la selezione dei cavi\n * @param {Array} props.selectedCavi - Array degli ID dei cavi selezionati\n * @param {Function} props.onSelectionChange - Funzione chiamata quando cambia la selezione\n * @param {Array} props.contextMenuItems - Array di elementi per il menu contestuale\n * @param {Function} props.onContextMenuAction - Funzione chiamata quando si clicca su un elemento del menu contestuale\n * @param {Function} props.onStatusAction - Funzione chiamata quando si clicca sul pulsante stato\n */\nconst CaviFilterableTable = ({\n  cavi = [],\n  loading = false,\n  onFilteredDataChange = null,\n  revisioneCorrente = null,\n  selectionEnabled = false,\n  selectedCavi = [],\n  onSelectionChange = null,\n  contextMenuItems = [],\n  onContextMenuAction = null,\n  onStatusAction = null\n}) => {\n  const [filteredCavi, setFilteredCavi] = useState(cavi);\n  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi);\n\n  // Hook per il menu contestuale\n  const { contextMenu, handleContextMenu, closeContextMenu } = useContextMenu();\n\n  // Aggiorna i dati filtrati quando cambiano i cavi\n  useEffect(() => {\n    setFilteredCavi(cavi);\n    setSmartFilteredCavi(cavi);\n  }, [cavi]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = (data) => {\n    setFilteredCavi(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Gestisce il cambio dei dati dal filtro intelligente\n  const handleSmartFilterChange = (data) => {\n    console.log('CaviFilterableTable - Smart filter change:', {\n      originalCount: cavi.length,\n      filteredCount: data.length,\n      filteredIds: data.map(c => c.id_cavo)\n    });\n    setSmartFilteredCavi(data);\n    // Il filtro intelligente ha la priorità sui filtri Excel-like\n    setFilteredCavi(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Gestisce la selezione di un singolo cavo\n  const handleCavoToggle = (cavoId) => {\n    if (!selectionEnabled || !onSelectionChange) return;\n\n    const isSelected = selectedCavi.includes(cavoId);\n    let newSelection;\n\n    if (isSelected) {\n      // Rimuovi dalla selezione\n      newSelection = selectedCavi.filter(id => id !== cavoId);\n      console.log(`Cavo ${cavoId} deselezionato`);\n    } else {\n      // Aggiungi alla selezione\n      newSelection = [...selectedCavi, cavoId];\n      console.log(`Cavo ${cavoId} selezionato`);\n    }\n\n    onSelectionChange(newSelection);\n\n    // Feedback visivo rapido (opzionale - può essere rimosso se troppo invasivo)\n    // Potresti aggiungere qui un piccolo toast o animazione\n  };\n\n  // Seleziona tutti i cavi visibili (filtrati)\n  const handleSelectAll = () => {\n    if (!selectionEnabled || !onSelectionChange) return;\n\n    const visibleCaviIds = filteredCavi.map(cavo => cavo.id_cavo);\n    const allSelected = visibleCaviIds.every(id => selectedCavi.includes(id));\n\n    if (allSelected) {\n      // Deseleziona tutti i cavi visibili\n      const newSelection = selectedCavi.filter(id => !visibleCaviIds.includes(id));\n      onSelectionChange(newSelection);\n    } else {\n      // Seleziona tutti i cavi visibili\n      const newSelection = [...new Set([...selectedCavi, ...visibleCaviIds])];\n      onSelectionChange(newSelection);\n    }\n  };\n\n  // Deseleziona tutti i cavi\n  const handleClearSelection = () => {\n    if (!selectionEnabled || !onSelectionChange) return;\n    onSelectionChange([]);\n  };\n\n\n\n  // Definizione delle colonne\n  const columns = [\n    // Colonna di selezione (solo se abilitata)\n    ...(selectionEnabled ? [{\n      field: 'selection',\n      headerName: '',\n      disableFilter: true,\n      disableSort: true,\n      width: 50,\n      align: 'center',\n      headerStyle: { width: '50px', padding: '4px' },\n      cellStyle: { width: '50px', padding: '4px', textAlign: 'center' },\n      renderHeader: () => {\n        const visibleCaviIds = filteredCavi.map(cavo => cavo.id_cavo);\n        const allSelected = visibleCaviIds.length > 0 && visibleCaviIds.every(id => selectedCavi.includes(id));\n        const someSelected = visibleCaviIds.some(id => selectedCavi.includes(id));\n\n        return (\n          <Checkbox\n            checked={allSelected}\n            indeterminate={someSelected && !allSelected}\n            onChange={handleSelectAll}\n            size=\"small\"\n            title={allSelected ? \"Deseleziona tutti\" : \"Seleziona tutti\"}\n          />\n        );\n      },\n      renderCell: (row) => (\n        <Checkbox\n          checked={selectedCavi.includes(row.id_cavo)}\n          onChange={() => handleCavoToggle(row.id_cavo)}\n          size=\"small\"\n          onClick={(e) => e.stopPropagation()}\n        />\n      )\n    }] : []),\n    {\n      field: 'id_cavo',\n      headerName: 'ID Cavo',\n      dataType: 'text',\n      headerStyle: { fontWeight: 'bold' }\n    },\n    // Colonna Revisione rimossa e spostata nella tabella delle statistiche\n    {\n      field: 'sistema',\n      headerName: 'Sistema',\n      dataType: 'text'\n    },\n    {\n      field: 'utility',\n      headerName: 'Utility',\n      dataType: 'text'\n    },\n    {\n      field: 'tipologia',\n      headerName: 'Tipologia',\n      dataType: 'text'\n    },\n    // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n    {\n      field: 'sezione',\n      headerName: 'Formazione',\n      dataType: 'text',\n      align: 'right',\n      cellStyle: { textAlign: 'right' }\n    },\n    {\n      field: 'metri_teorici',\n      headerName: 'Metri Teorici',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'\n    },\n    {\n      field: 'metratura_reale',\n      headerName: 'Metri Reali',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metratura_reale ? row.metratura_reale.toFixed(1) : '0'\n    },\n    {\n      field: 'stato_installazione',\n      headerName: 'Stato',\n      dataType: 'text',\n      renderCell: (row) => {\n        // Determina colore, icona e azione in base allo stato\n        let color = 'default';\n        let icon = null;\n        let actionLabel = '';\n        let actionType = '';\n\n        // Normalizza lo stato per gestire diverse varianti\n        const statoNormalizzato = row.stato_installazione?.toUpperCase();\n\n        if (statoNormalizzato === 'INSTALLATO' || row.stato_installazione === 'Installato') {\n          color = 'success';\n          icon = <SettingsIcon fontSize=\"small\" />;\n          actionLabel = 'Modifica Bobina';\n          actionType = 'modify_reel';\n        } else if (statoNormalizzato === 'IN_CORSO' || row.stato_installazione === 'In corso') {\n          color = 'warning';\n          icon = <RulerIcon fontSize=\"small\" />;\n          actionLabel = 'Inserisci Metri Posati';\n          actionType = 'insert_meters';\n        } else if (statoNormalizzato === 'DA_INSTALLARE' || row.stato_installazione === 'Da installare' || !row.stato_installazione) {\n          color = 'error';\n          icon = <StartIcon fontSize=\"small\" />;\n          actionLabel = 'Inserisci Metri Posati';\n          actionType = 'insert_meters';\n        }\n\n        // Debug: verifica se onStatusAction è definito e se c'è un'azione per questo stato\n        console.log('🔍 Pulsante stato per cavo:', row.id_cavo, {\n          stato: row.stato_installazione,\n          actionType,\n          actionLabel,\n          willBeClickable: !!(onStatusAction && actionType && actionLabel)\n        });\n\n        // Determina se il pulsante deve essere cliccabile\n        const isClickable = onStatusAction && actionType && actionLabel;\n\n        return (\n          <Chip\n            label={row.stato_installazione || 'N/D'}\n            size=\"small\"\n            color={color}\n            variant=\"outlined\"\n            icon={icon}\n            onClick={isClickable ? (e) => {\n              e.stopPropagation();\n              console.log('🔥 CLICK su pulsante stato!', {\n                cavoId: row.id_cavo,\n                stato: row.stato_installazione,\n                actionType,\n                actionLabel\n              });\n              onStatusAction(row, actionType, actionLabel);\n            } : undefined}\n            sx={{\n              cursor: isClickable ? 'pointer' : 'default',\n              transition: 'all 0.2s ease',\n              '&:hover': isClickable ? {\n                transform: 'scale(1.05)',\n                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                backgroundColor: `${color}.light`\n              } : {},\n              // Aggiungi un bordo più marcato per i pulsanti cliccabili\n              border: isClickable ? '2px solid currentColor' : '1px solid currentColor'\n            }}\n            title={isClickable ? actionLabel : 'Nessuna azione disponibile'}\n          />\n        );\n      }\n    },\n    {\n      field: 'id_bobina',\n      headerName: 'Bobina',\n      dataType: 'text',\n      renderCell: (row) => {\n        // Gestione differenziata per null e BOBINA_VUOTA\n        if (row.id_bobina === null) {\n          // Per cavi non posati (id_bobina è null)\n          return '-';\n        } else if (row.id_bobina === 'BOBINA_VUOTA') {\n          // Per cavi posati senza bobina specifica\n          return 'BOBINA VUOTA';\n        } else if (!row.id_bobina) {\n          // Per altri casi in cui id_bobina è falsy (undefined, stringa vuota)\n          return '-';\n        }\n\n        // Estrai solo il numero della bobina (parte dopo '_B')\n        const match = row.id_bobina.match(/_B(.+)$/);\n        return match ? match[1] : row.id_bobina;\n      }\n    },\n    {\n      field: 'timestamp',\n      headerName: 'Data Modifica',\n      dataType: 'date',\n      renderCell: (row) => formatDate(row.timestamp)\n    },\n    {\n      field: 'collegamenti',\n      headerName: 'Collegamenti',\n      dataType: 'number',\n      align: 'center',\n      cellStyle: { textAlign: 'center' },\n      renderCell: (row) => {\n        let color = 'default';\n        if (row.collegamenti === 2) color = 'success';\n        else if (row.collegamenti === 1) color = 'warning';\n        else color = 'error';\n\n        return (\n          <Chip\n            label={row.collegamenti}\n            size=\"small\"\n            color={color}\n            variant=\"outlined\"\n          />\n        );\n      }\n    }\n  ];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    const statoNormalizzato = row.stato_installazione?.toUpperCase();\n    if (statoNormalizzato === 'INSTALLATO' || row.stato_installazione === 'Installato') {\n      bgColor = 'rgba(76, 175, 80, 0.1)';\n    } else if (statoNormalizzato === 'IN_CORSO' || row.stato_installazione === 'In corso') {\n      bgColor = 'rgba(255, 152, 0, 0.1)';\n    }\n\n    // Se la selezione è abilitata, evidenzia le righe selezionate\n    const isSelected = selectionEnabled && selectedCavi.includes(row.id_cavo);\n    if (isSelected) {\n      bgColor = 'rgba(25, 118, 210, 0.12)'; // Blu delicato per le righe selezionate\n    }\n\n    return (\n      <TableRow\n        key={index}\n        selected={isSelected}\n        hover\n        onClick={selectionEnabled ? () => handleCavoToggle(row.id_cavo) : undefined}\n        onContextMenu={(e) => contextMenuItems.length > 0 ? handleContextMenu(e, row) : undefined}\n        sx={{\n          backgroundColor: bgColor,\n          cursor: selectionEnabled ? 'pointer' : 'default',\n          transition: 'all 0.2s ease',\n          border: isSelected ? '2px solid #1976d2' : 'none',\n          '&:hover': {\n            backgroundColor: selectionEnabled\n              ? (isSelected ? 'rgba(25, 118, 210, 0.18)' : 'rgba(25, 118, 210, 0.08)')\n              : 'rgba(0, 0, 0, 0.04)',\n            transform: selectionEnabled ? 'scale(1.005)' : 'none',\n            boxShadow: selectionEnabled ? '0 1px 4px rgba(0,0,0,0.08)' : 'none'\n          }\n        }}\n      >\n        {columns.map((column) => (\n          <TableCell\n            key={column.field}\n            align={column.align || 'left'}\n            sx={column.cellStyle}\n          >\n            {column.renderCell ? column.renderCell(row) : row[column.field]}\n          </TableCell>\n        ))}\n      </TableRow>\n    );\n  };\n\n\n\n  return (\n    <Box>\n      {/* Filtro intelligente */}\n      <SmartCaviFilter\n        cavi={cavi}\n        onFilteredDataChange={handleSmartFilterChange}\n        loading={loading}\n      />\n\n      {/* Pannello di controllo selezione */}\n      {selectionEnabled && (\n        <Box sx={{\n          mb: 2,\n          p: 2,\n          backgroundColor: selectedCavi.length > 0 ? 'rgba(25, 118, 210, 0.15)' : 'rgba(25, 118, 210, 0.08)',\n          borderRadius: 2,\n          border: selectedCavi.length > 0 ? '2px solid rgba(25, 118, 210, 0.5)' : '2px solid rgba(25, 118, 210, 0.25)',\n          transition: 'all 0.3s ease',\n          boxShadow: selectedCavi.length > 0 ? '0 4px 12px rgba(25, 118, 210, 0.25)' : '0 2px 6px rgba(25, 118, 210, 0.1)'\n        }}>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n              {selectedCavi.length > 0 && (\n                <CheckBoxIcon color=\"primary\" fontSize=\"small\" />\n              )}\n              <Typography variant=\"body1\" sx={{ fontWeight: 600, color: selectedCavi.length > 0 ? 'primary.main' : 'text.primary' }}>\n                {selectedCavi.length > 0\n                  ? `${selectedCavi.length} cavi selezionati`\n                  : 'Modalità selezione attiva - Click sui cavi per selezionarli'\n                }\n              </Typography>\n            </Box>\n\n            <Box sx={{ display: 'flex', gap: 1 }}>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={handleSelectAll}\n                disabled={filteredCavi.length === 0}\n              >\n                {filteredCavi.length > 0 && filteredCavi.every(cavo => selectedCavi.includes(cavo.id_cavo))\n                  ? 'Deseleziona tutti'\n                  : 'Seleziona tutti'\n                }\n              </Button>\n\n              {selectedCavi.length > 0 && (\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<ClearIcon />}\n                  onClick={handleClearSelection}\n                  color=\"error\"\n                >\n                  Cancella selezione\n                </Button>\n              )}\n            </Box>\n          </Box>\n        </Box>\n      )}\n\n      {/* Tabella con filtri Excel-like sui dati già filtrati dal filtro intelligente */}\n      <FilterableTable\n        data={smartFilteredCavi}\n        columns={columns}\n        onFilteredDataChange={handleFilteredDataChange}\n        loading={loading}\n        emptyMessage=\"Nessun cavo disponibile\"\n        renderRow={renderRow}\n      />\n\n      {/* Menu contestuale */}\n      <ContextMenu\n        open={contextMenu.open}\n        anchorPosition={contextMenu.anchorPosition}\n        onClose={closeContextMenu}\n        menuItems={typeof contextMenuItems === 'function' ? contextMenuItems(contextMenu.contextData) : contextMenuItems}\n        contextData={contextMenu.contextData}\n      />\n    </Box>\n  );\n};\n\nexport default CaviFilterableTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,eAAe;AAC5F,SACEC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,SAAS,EACvBC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,SAAS,QACjB,qBAAqB;AAC5B,OAAOC,eAAe,MAAM,2BAA2B;AACvD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,UAAU,QAAQ,uBAAuB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,SAAAC,MAAA,IAAAC,OAAA;AAeA,MAAMC,mBAAmB,GAAGA,CAAC;EAC3BC,IAAI,GAAG,EAAE;EACTC,OAAO,GAAG,KAAK;EACfC,oBAAoB,GAAG,IAAI;EAC3BC,iBAAiB,GAAG,IAAI;EACxBC,gBAAgB,GAAG,KAAK;EACxBC,YAAY,GAAG,EAAE;EACjBC,iBAAiB,GAAG,IAAI;EACxBC,gBAAgB,GAAG,EAAE;EACrBC,mBAAmB,GAAG,IAAI;EAC1BC,cAAc,GAAG;AACnB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC2B,IAAI,CAAC;EACtD,MAAM,CAACa,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzC,QAAQ,CAAC2B,IAAI,CAAC;;EAEhE;EACA,MAAM;IAAEe,WAAW;IAAEC,iBAAiB;IAAEC;EAAiB,CAAC,GAAGtB,cAAc,CAAC,CAAC;;EAE7E;EACArB,SAAS,CAAC,MAAM;IACdsC,eAAe,CAACZ,IAAI,CAAC;IACrBc,oBAAoB,CAACd,IAAI,CAAC;EAC5B,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMkB,wBAAwB,GAAIC,IAAI,IAAK;IACzCP,eAAe,CAACO,IAAI,CAAC;IACrB,IAAIjB,oBAAoB,EAAE;MACxBA,oBAAoB,CAACiB,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAID,IAAI,IAAK;IACxCE,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE;MACxDC,aAAa,EAAEvB,IAAI,CAACwB,MAAM;MAC1BC,aAAa,EAAEN,IAAI,CAACK,MAAM;MAC1BE,WAAW,EAAEP,IAAI,CAACQ,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO;IACtC,CAAC,CAAC;IACFf,oBAAoB,CAACK,IAAI,CAAC;IAC1B;IACAP,eAAe,CAACO,IAAI,CAAC;IACrB,IAAIjB,oBAAoB,EAAE;MACxBA,oBAAoB,CAACiB,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMW,gBAAgB,GAAIC,MAAM,IAAK;IACnC,IAAI,CAAC3B,gBAAgB,IAAI,CAACE,iBAAiB,EAAE;IAE7C,MAAM0B,UAAU,GAAG3B,YAAY,CAAC4B,QAAQ,CAACF,MAAM,CAAC;IAChD,IAAIG,YAAY;IAEhB,IAAIF,UAAU,EAAE;MACd;MACAE,YAAY,GAAG7B,YAAY,CAAC8B,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKL,MAAM,CAAC;MACvDV,OAAO,CAACC,GAAG,CAAC,QAAQS,MAAM,gBAAgB,CAAC;IAC7C,CAAC,MAAM;MACL;MACAG,YAAY,GAAG,CAAC,GAAG7B,YAAY,EAAE0B,MAAM,CAAC;MACxCV,OAAO,CAACC,GAAG,CAAC,QAAQS,MAAM,cAAc,CAAC;IAC3C;IAEAzB,iBAAiB,CAAC4B,YAAY,CAAC;;IAE/B;IACA;EACF,CAAC;;EAED;EACA,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACjC,gBAAgB,IAAI,CAACE,iBAAiB,EAAE;IAE7C,MAAMgC,cAAc,GAAG3B,YAAY,CAACgB,GAAG,CAACY,IAAI,IAAIA,IAAI,CAACV,OAAO,CAAC;IAC7D,MAAMW,WAAW,GAAGF,cAAc,CAACG,KAAK,CAACL,EAAE,IAAI/B,YAAY,CAAC4B,QAAQ,CAACG,EAAE,CAAC,CAAC;IAEzE,IAAII,WAAW,EAAE;MACf;MACA,MAAMN,YAAY,GAAG7B,YAAY,CAAC8B,MAAM,CAACC,EAAE,IAAI,CAACE,cAAc,CAACL,QAAQ,CAACG,EAAE,CAAC,CAAC;MAC5E9B,iBAAiB,CAAC4B,YAAY,CAAC;IACjC,CAAC,MAAM;MACL;MACA,MAAMA,YAAY,GAAG,CAAC,GAAG,IAAIQ,GAAG,CAAC,CAAC,GAAGrC,YAAY,EAAE,GAAGiC,cAAc,CAAC,CAAC,CAAC;MACvEhC,iBAAiB,CAAC4B,YAAY,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMS,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAACvC,gBAAgB,IAAI,CAACE,iBAAiB,EAAE;IAC7CA,iBAAiB,CAAC,EAAE,CAAC;EACvB,CAAC;;EAID;EACA,MAAMsC,OAAO,GAAG;EACd;EACA,IAAIxC,gBAAgB,GAAG,CAAC;IACtByC,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,IAAI;IACjBC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE;MAAEF,KAAK,EAAE,MAAM;MAAEG,OAAO,EAAE;IAAM,CAAC;IAC9CC,SAAS,EAAE;MAAEJ,KAAK,EAAE,MAAM;MAAEG,OAAO,EAAE,KAAK;MAAEE,SAAS,EAAE;IAAS,CAAC;IACjEC,YAAY,EAAEA,CAAA,KAAM;MAClB,MAAMjB,cAAc,GAAG3B,YAAY,CAACgB,GAAG,CAACY,IAAI,IAAIA,IAAI,CAACV,OAAO,CAAC;MAC7D,MAAMW,WAAW,GAAGF,cAAc,CAACd,MAAM,GAAG,CAAC,IAAIc,cAAc,CAACG,KAAK,CAACL,EAAE,IAAI/B,YAAY,CAAC4B,QAAQ,CAACG,EAAE,CAAC,CAAC;MACtG,MAAMoB,YAAY,GAAGlB,cAAc,CAACmB,IAAI,CAACrB,EAAE,IAAI/B,YAAY,CAAC4B,QAAQ,CAACG,EAAE,CAAC,CAAC;MAEzE,oBACEtC,OAAA,CAAClB,QAAQ;QACP8E,OAAO,EAAElB,WAAY;QACrBmB,aAAa,EAAEH,YAAY,IAAI,CAAChB,WAAY;QAC5CoB,QAAQ,EAAEvB,eAAgB;QAC1BwB,IAAI,EAAC,OAAO;QACZC,KAAK,EAAEtB,WAAW,GAAG,mBAAmB,GAAG;MAAkB;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAEN,CAAC;IACDC,UAAU,EAAGC,GAAG,iBACdtE,OAAA,CAAClB,QAAQ;MACP8E,OAAO,EAAErD,YAAY,CAAC4B,QAAQ,CAACmC,GAAG,CAACvC,OAAO,CAAE;MAC5C+B,QAAQ,EAAEA,CAAA,KAAM9B,gBAAgB,CAACsC,GAAG,CAACvC,OAAO,CAAE;MAC9CgC,IAAI,EAAC,OAAO;MACZQ,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC;IAAE;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC;EAEL,CAAC,CAAC,GAAG,EAAE,CAAC,EACR;IACErB,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrB0B,QAAQ,EAAE,MAAM;IAChBrB,WAAW,EAAE;MAAEsB,UAAU,EAAE;IAAO;EACpC,CAAC;EACD;EACA;IACE5B,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrB0B,QAAQ,EAAE;EACZ,CAAC,EACD;IACE3B,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrB0B,QAAQ,EAAE;EACZ,CAAC,EACD;IACE3B,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,WAAW;IACvB0B,QAAQ,EAAE;EACZ,CAAC;EACD;EACA;IACE3B,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,YAAY;IACxB0B,QAAQ,EAAE,MAAM;IAChBtB,KAAK,EAAE,OAAO;IACdG,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ;EAClC,CAAC,EACD;IACET,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE,eAAe;IAC3B0B,QAAQ,EAAE,QAAQ;IAClBtB,KAAK,EAAE,OAAO;IACdG,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAC;IACjCa,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACM,aAAa,GAAGN,GAAG,CAACM,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG;EAC1E,CAAC,EACD;IACE9B,KAAK,EAAE,iBAAiB;IACxBC,UAAU,EAAE,aAAa;IACzB0B,QAAQ,EAAE,QAAQ;IAClBtB,KAAK,EAAE,OAAO;IACdG,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAC;IACjCa,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACQ,eAAe,GAAGR,GAAG,CAACQ,eAAe,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG;EAC9E,CAAC,EACD;IACE9B,KAAK,EAAE,qBAAqB;IAC5BC,UAAU,EAAE,OAAO;IACnB0B,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAGC,GAAG,IAAK;MAAA,IAAAS,qBAAA;MACnB;MACA,IAAIC,KAAK,GAAG,SAAS;MACrB,IAAIC,IAAI,GAAG,IAAI;MACf,IAAIC,WAAW,GAAG,EAAE;MACpB,IAAIC,UAAU,GAAG,EAAE;;MAEnB;MACA,MAAMC,iBAAiB,IAAAL,qBAAA,GAAGT,GAAG,CAACe,mBAAmB,cAAAN,qBAAA,uBAAvBA,qBAAA,CAAyBO,WAAW,CAAC,CAAC;MAEhE,IAAIF,iBAAiB,KAAK,YAAY,IAAId,GAAG,CAACe,mBAAmB,KAAK,YAAY,EAAE;QAClFL,KAAK,GAAG,SAAS;QACjBC,IAAI,gBAAGjF,OAAA,CAACT,YAAY;UAACgG,QAAQ,EAAC;QAAO;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACxCc,WAAW,GAAG,iBAAiB;QAC/BC,UAAU,GAAG,aAAa;MAC5B,CAAC,MAAM,IAAIC,iBAAiB,KAAK,UAAU,IAAId,GAAG,CAACe,mBAAmB,KAAK,UAAU,EAAE;QACrFL,KAAK,GAAG,SAAS;QACjBC,IAAI,gBAAGjF,OAAA,CAACX,SAAS;UAACkG,QAAQ,EAAC;QAAO;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACrCc,WAAW,GAAG,wBAAwB;QACtCC,UAAU,GAAG,eAAe;MAC9B,CAAC,MAAM,IAAIC,iBAAiB,KAAK,eAAe,IAAId,GAAG,CAACe,mBAAmB,KAAK,eAAe,IAAI,CAACf,GAAG,CAACe,mBAAmB,EAAE;QAC3HL,KAAK,GAAG,OAAO;QACfC,IAAI,gBAAGjF,OAAA,CAACP,SAAS;UAAC8F,QAAQ,EAAC;QAAO;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACrCc,WAAW,GAAG,wBAAwB;QACtCC,UAAU,GAAG,eAAe;MAC9B;;MAEA;MACA5D,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE8C,GAAG,CAACvC,OAAO,EAAE;QACtDyD,KAAK,EAAElB,GAAG,CAACe,mBAAmB;QAC9BF,UAAU;QACVD,WAAW;QACXO,eAAe,EAAE,CAAC,EAAE9E,cAAc,IAAIwE,UAAU,IAAID,WAAW;MACjE,CAAC,CAAC;;MAEF;MACA,MAAMQ,WAAW,GAAG/E,cAAc,IAAIwE,UAAU,IAAID,WAAW;MAE/D,oBACElF,OAAA,CAACrB,IAAI;QACHgH,KAAK,EAAErB,GAAG,CAACe,mBAAmB,IAAI,KAAM;QACxCtB,IAAI,EAAC,OAAO;QACZiB,KAAK,EAAEA,KAAM;QACbY,OAAO,EAAC,UAAU;QAClBX,IAAI,EAAEA,IAAK;QACXV,OAAO,EAAEmB,WAAW,GAAIlB,CAAC,IAAK;UAC5BA,CAAC,CAACC,eAAe,CAAC,CAAC;UACnBlD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;YACzCS,MAAM,EAAEqC,GAAG,CAACvC,OAAO;YACnByD,KAAK,EAAElB,GAAG,CAACe,mBAAmB;YAC9BF,UAAU;YACVD;UACF,CAAC,CAAC;UACFvE,cAAc,CAAC2D,GAAG,EAAEa,UAAU,EAAED,WAAW,CAAC;QAC9C,CAAC,GAAGW,SAAU;QACdC,EAAE,EAAE;UACFC,MAAM,EAAEL,WAAW,GAAG,SAAS,GAAG,SAAS;UAC3CM,UAAU,EAAE,eAAe;UAC3B,SAAS,EAAEN,WAAW,GAAG;YACvBO,SAAS,EAAE,aAAa;YACxBC,SAAS,EAAE,4BAA4B;YACvCC,eAAe,EAAE,GAAGnB,KAAK;UAC3B,CAAC,GAAG,CAAC,CAAC;UACN;UACAoB,MAAM,EAAEV,WAAW,GAAG,wBAAwB,GAAG;QACnD,CAAE;QACF1B,KAAK,EAAE0B,WAAW,GAAGR,WAAW,GAAG;MAA6B;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC;IAEN;EACF,CAAC,EACD;IACErB,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,QAAQ;IACpB0B,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAGC,GAAG,IAAK;MACnB;MACA,IAAIA,GAAG,CAAC+B,SAAS,KAAK,IAAI,EAAE;QAC1B;QACA,OAAO,GAAG;MACZ,CAAC,MAAM,IAAI/B,GAAG,CAAC+B,SAAS,KAAK,cAAc,EAAE;QAC3C;QACA,OAAO,cAAc;MACvB,CAAC,MAAM,IAAI,CAAC/B,GAAG,CAAC+B,SAAS,EAAE;QACzB;QACA,OAAO,GAAG;MACZ;;MAEA;MACA,MAAMC,KAAK,GAAGhC,GAAG,CAAC+B,SAAS,CAACC,KAAK,CAAC,SAAS,CAAC;MAC5C,OAAOA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGhC,GAAG,CAAC+B,SAAS;IACzC;EACF,CAAC,EACD;IACEtD,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,eAAe;IAC3B0B,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAGC,GAAG,IAAKxE,UAAU,CAACwE,GAAG,CAACiC,SAAS;EAC/C,CAAC,EACD;IACExD,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAE,cAAc;IAC1B0B,QAAQ,EAAE,QAAQ;IAClBtB,KAAK,EAAE,QAAQ;IACfG,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAS,CAAC;IAClCa,UAAU,EAAGC,GAAG,IAAK;MACnB,IAAIU,KAAK,GAAG,SAAS;MACrB,IAAIV,GAAG,CAACkC,YAAY,KAAK,CAAC,EAAExB,KAAK,GAAG,SAAS,CAAC,KACzC,IAAIV,GAAG,CAACkC,YAAY,KAAK,CAAC,EAAExB,KAAK,GAAG,SAAS,CAAC,KAC9CA,KAAK,GAAG,OAAO;MAEpB,oBACEhF,OAAA,CAACrB,IAAI;QACHgH,KAAK,EAAErB,GAAG,CAACkC,YAAa;QACxBzC,IAAI,EAAC,OAAO;QACZiB,KAAK,EAAEA,KAAM;QACbY,OAAO,EAAC;MAAU;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAEN;EACF,CAAC,CACF;;EAED;EACA,MAAMqC,SAAS,GAAGA,CAACnC,GAAG,EAAEoC,KAAK,KAAK;IAAA,IAAAC,sBAAA;IAChC;IACA,IAAIC,OAAO,GAAG,SAAS;IACvB,MAAMxB,iBAAiB,IAAAuB,sBAAA,GAAGrC,GAAG,CAACe,mBAAmB,cAAAsB,sBAAA,uBAAvBA,sBAAA,CAAyBrB,WAAW,CAAC,CAAC;IAChE,IAAIF,iBAAiB,KAAK,YAAY,IAAId,GAAG,CAACe,mBAAmB,KAAK,YAAY,EAAE;MAClFuB,OAAO,GAAG,wBAAwB;IACpC,CAAC,MAAM,IAAIxB,iBAAiB,KAAK,UAAU,IAAId,GAAG,CAACe,mBAAmB,KAAK,UAAU,EAAE;MACrFuB,OAAO,GAAG,wBAAwB;IACpC;;IAEA;IACA,MAAM1E,UAAU,GAAG5B,gBAAgB,IAAIC,YAAY,CAAC4B,QAAQ,CAACmC,GAAG,CAACvC,OAAO,CAAC;IACzE,IAAIG,UAAU,EAAE;MACd0E,OAAO,GAAG,0BAA0B,CAAC,CAAC;IACxC;IAEA,oBACE5G,OAAA,CAACpB,QAAQ;MAEPiI,QAAQ,EAAE3E,UAAW;MACrB4E,KAAK;MACLvC,OAAO,EAAEjE,gBAAgB,GAAG,MAAM0B,gBAAgB,CAACsC,GAAG,CAACvC,OAAO,CAAC,GAAG8D,SAAU;MAC5EkB,aAAa,EAAGvC,CAAC,IAAK/D,gBAAgB,CAACiB,MAAM,GAAG,CAAC,GAAGR,iBAAiB,CAACsD,CAAC,EAAEF,GAAG,CAAC,GAAGuB,SAAU;MAC1FC,EAAE,EAAE;QACFK,eAAe,EAAES,OAAO;QACxBb,MAAM,EAAEzF,gBAAgB,GAAG,SAAS,GAAG,SAAS;QAChD0F,UAAU,EAAE,eAAe;QAC3BI,MAAM,EAAElE,UAAU,GAAG,mBAAmB,GAAG,MAAM;QACjD,SAAS,EAAE;UACTiE,eAAe,EAAE7F,gBAAgB,GAC5B4B,UAAU,GAAG,0BAA0B,GAAG,0BAA0B,GACrE,qBAAqB;UACzB+D,SAAS,EAAE3F,gBAAgB,GAAG,cAAc,GAAG,MAAM;UACrD4F,SAAS,EAAE5F,gBAAgB,GAAG,4BAA4B,GAAG;QAC/D;MACF,CAAE;MAAA0G,QAAA,EAEDlE,OAAO,CAACjB,GAAG,CAAEoF,MAAM,iBAClBjH,OAAA,CAACnB,SAAS;QAERuE,KAAK,EAAE6D,MAAM,CAAC7D,KAAK,IAAI,MAAO;QAC9B0C,EAAE,EAAEmB,MAAM,CAAC1D,SAAU;QAAAyD,QAAA,EAEpBC,MAAM,CAAC5C,UAAU,GAAG4C,MAAM,CAAC5C,UAAU,CAACC,GAAG,CAAC,GAAGA,GAAG,CAAC2C,MAAM,CAAClE,KAAK;MAAC,GAJ1DkE,MAAM,CAAClE,KAAK;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKR,CACZ;IAAC,GA3BGsC,KAAK;MAAAzC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA4BF,CAAC;EAEf,CAAC;EAID,oBACEpE,OAAA,CAACvB,GAAG;IAAAuI,QAAA,gBAEFhH,OAAA,CAACL,eAAe;MACdO,IAAI,EAAEA,IAAK;MACXE,oBAAoB,EAAEkB,uBAAwB;MAC9CnB,OAAO,EAAEA;IAAQ;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,EAGD9D,gBAAgB,iBACfN,OAAA,CAACvB,GAAG;MAACqH,EAAE,EAAE;QACPoB,EAAE,EAAE,CAAC;QACLC,CAAC,EAAE,CAAC;QACJhB,eAAe,EAAE5F,YAAY,CAACmB,MAAM,GAAG,CAAC,GAAG,0BAA0B,GAAG,0BAA0B;QAClG0F,YAAY,EAAE,CAAC;QACfhB,MAAM,EAAE7F,YAAY,CAACmB,MAAM,GAAG,CAAC,GAAG,mCAAmC,GAAG,oCAAoC;QAC5GsE,UAAU,EAAE,eAAe;QAC3BE,SAAS,EAAE3F,YAAY,CAACmB,MAAM,GAAG,CAAC,GAAG,qCAAqC,GAAG;MAC/E,CAAE;MAAAsF,QAAA,eACAhH,OAAA,CAACvB,GAAG;QAACqH,EAAE,EAAE;UAAEuB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAP,QAAA,gBAClFhH,OAAA,CAACvB,GAAG;UAACqH,EAAE,EAAE;YAAEuB,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAR,QAAA,GACxDzG,YAAY,CAACmB,MAAM,GAAG,CAAC,iBACtB1B,OAAA,CAACf,YAAY;YAAC+F,KAAK,EAAC,SAAS;YAACO,QAAQ,EAAC;UAAO;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CACjD,eACDpE,OAAA,CAACtB,UAAU;YAACkH,OAAO,EAAC,OAAO;YAACE,EAAE,EAAE;cAAEnB,UAAU,EAAE,GAAG;cAAEK,KAAK,EAAEzE,YAAY,CAACmB,MAAM,GAAG,CAAC,GAAG,cAAc,GAAG;YAAe,CAAE;YAAAsF,QAAA,EACnHzG,YAAY,CAACmB,MAAM,GAAG,CAAC,GACpB,GAAGnB,YAAY,CAACmB,MAAM,mBAAmB,GACzC;UAA6D;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENpE,OAAA,CAACvB,GAAG;UAACqH,EAAE,EAAE;YAAEuB,OAAO,EAAE,MAAM;YAAEG,GAAG,EAAE;UAAE,CAAE;UAAAR,QAAA,gBACnChH,OAAA,CAACjB,MAAM;YACL6G,OAAO,EAAC,UAAU;YAClB7B,IAAI,EAAC,OAAO;YACZQ,OAAO,EAAEhC,eAAgB;YACzBkF,QAAQ,EAAE5G,YAAY,CAACa,MAAM,KAAK,CAAE;YAAAsF,QAAA,EAEnCnG,YAAY,CAACa,MAAM,GAAG,CAAC,IAAIb,YAAY,CAAC8B,KAAK,CAACF,IAAI,IAAIlC,YAAY,CAAC4B,QAAQ,CAACM,IAAI,CAACV,OAAO,CAAC,CAAC,GACvF,mBAAmB,GACnB;UAAiB;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CAAC,EAER7D,YAAY,CAACmB,MAAM,GAAG,CAAC,iBACtB1B,OAAA,CAACjB,MAAM;YACL6G,OAAO,EAAC,UAAU;YAClB7B,IAAI,EAAC,OAAO;YACZ2D,SAAS,eAAE1H,OAAA,CAACb,SAAS;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBG,OAAO,EAAE1B,oBAAqB;YAC9BmC,KAAK,EAAC,OAAO;YAAAgC,QAAA,EACd;UAED;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDpE,OAAA,CAACN,eAAe;MACd2B,IAAI,EAAEN,iBAAkB;MACxB+B,OAAO,EAAEA,OAAQ;MACjB1C,oBAAoB,EAAEgB,wBAAyB;MAC/CjB,OAAO,EAAEA,OAAQ;MACjBwH,YAAY,EAAC,yBAAyB;MACtClB,SAAS,EAAEA;IAAU;MAAAxC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,eAGFpE,OAAA,CAACJ,WAAW;MACVgI,IAAI,EAAE3G,WAAW,CAAC2G,IAAK;MACvBC,cAAc,EAAE5G,WAAW,CAAC4G,cAAe;MAC3CC,OAAO,EAAE3G,gBAAiB;MAC1B4G,SAAS,EAAE,OAAOtH,gBAAgB,KAAK,UAAU,GAAGA,gBAAgB,CAACQ,WAAW,CAAC+G,WAAW,CAAC,GAAGvH,gBAAiB;MACjHuH,WAAW,EAAE/G,WAAW,CAAC+G;IAAY;MAAA/D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACxD,EAAA,CAxbIX,mBAAmB;EAAA,QAgBsCJ,cAAc;AAAA;AAAAoI,EAAA,GAhBvEhI,mBAAmB;AA0bzB,eAAeA,mBAAmB;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}