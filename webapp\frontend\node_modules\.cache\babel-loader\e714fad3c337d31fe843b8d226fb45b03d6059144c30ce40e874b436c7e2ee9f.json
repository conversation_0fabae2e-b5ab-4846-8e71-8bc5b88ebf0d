{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\ModificaBobinaForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Paper, Typography, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, Divider, Alert, CircularProgress, Dialog, DialogTitle, DialogContent, DialogActions, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Radio, RadioGroup, FormControlLabel, IconButton, InputAdornment, List, ListItem, ListItemText, ListItemSecondaryAction, Chip } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, Cancel as CancelIcon, Warning as WarningIcon, Info as InfoIcon, AddCircleOutline as AddCircleOutlineIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoDetailsView from './CavoDetailsView';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Componente per la modifica della bobina di un cavo già posato\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModificaBobinaForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [selectedOption, setSelectedOption] = useState('');\n  const [selectedBobinaId, setSelectedBobinaId] = useState('');\n  const [bobinaSearchText, setBobinaSearchText] = useState('');\n  const [bobinaNumericInput, setBobinaNumericInput] = useState('');\n\n  // Stati per i dialoghi\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');\n  const [confirmDialogAction, setConfirmDialogAction] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica i cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica le bobine quando viene selezionato un cavo\n  useEffect(() => {\n    if (selectedCavo) {\n      loadBobine();\n    }\n  }, [selectedCavo]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica solo i cavi installati (con metratura_reale > 0)\n      const caviData = await caviService.getCavi(cantiereId, null, 'Installato');\n      console.log(`Caricati ${caviData.length} cavi installati`);\n\n      // Filtra i cavi che hanno metratura_reale > 0\n      const caviInstallati = caviData.filter(cavo => parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato');\n      setCavi(caviInstallati);\n      console.log(`Filtrati ${caviInstallati.length} cavi effettivamente installati`);\n    } catch (error) {\n      console.error('Errore durante il caricamento dei cavi:', error);\n      onError('Errore durante il caricamento dei cavi: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    if (!selectedCavo) return;\n    try {\n      setBobineLoading(true);\n      console.log(`Caricamento bobine per il cantiere ${cantiereId}...`);\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Caricati ${bobineData.length} bobine`);\n\n      // Filtra le bobine compatibili\n      const compatibleBobineData = bobineData.filter(bobina => bobina.tipologia === selectedCavo.tipologia && bobina.sezione === selectedCavo.sezione && (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso'));\n      setBobine(bobineData);\n      setCompatibleBobine(compatibleBobineData);\n      console.log(`Filtrate ${compatibleBobineData.length} bobine compatibili`);\n    } catch (error) {\n      console.error('Errore durante il caricamento delle bobine:', error);\n      onError('Errore durante il caricamento delle bobine: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setLoading(true);\n      const cavo = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica che il cavo sia installato\n      if (parseFloat(cavo.metratura_reale) <= 0 && cavo.stato_installazione !== 'Installato') {\n        onError('Il cavo selezionato non risulta installato');\n        setLoading(false);\n        return;\n      }\n      setSelectedCavo(cavo);\n      setSelectedOption(''); // Reset dell'opzione selezionata\n      setSelectedBobinaId(''); // Reset della bobina selezionata\n      setShowSearchResults(false);\n    } catch (error) {\n      console.error('Errore durante la ricerca del cavo:', error);\n      onError('Errore durante la ricerca del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo dalla lista\n  const handleSelectCavo = cavo => {\n    setSelectedCavo(cavo);\n    setSelectedOption(''); // Reset dell'opzione selezionata\n    setSelectedBobinaId(''); // Reset della bobina selezionata\n    setShowSearchResults(false);\n  };\n\n  // Gestisce il cambio dell'opzione selezionata\n  const handleOptionChange = event => {\n    setSelectedOption(event.target.value);\n    setSelectedBobinaId(''); // Reset della bobina selezionata quando cambia l'opzione\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = bobinaId => {\n    setSelectedBobinaId(bobinaId);\n    // Aggiorna anche l'input numerico con la parte numerica\n    setBobinaNumericInput(getBobinaNumber(bobinaId));\n  };\n\n  // Gestisce la selezione di una bobina tramite input numerico\n  const handleSelectBobinaByNumber = () => {\n    if (!bobinaNumericInput.trim()) {\n      onError('Inserisci un numero di bobina valido');\n      return;\n    }\n\n    // Costruisci l'ID completo della bobina\n    const fullBobinaId = `C${cantiereId}_B${bobinaNumericInput.trim()}`;\n\n    // Verifica che la bobina esista\n    const bobina = bobine.find(b => b.id_bobina === fullBobinaId);\n    if (!bobina) {\n      onError(`Bobina ${bobinaNumericInput} non trovata nel cantiere`);\n      return;\n    }\n\n    // Verifica compatibilità\n    const isCompatible = selectedCavo && String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim();\n    if (!isCompatible) {\n      onError(`Bobina ${bobinaNumericInput} non è compatibile con il cavo selezionato`);\n      return;\n    }\n\n    // Seleziona la bobina\n    setSelectedBobinaId(fullBobinaId);\n  };\n\n  // Gestisce il salvataggio delle modifiche\n  const handleSave = () => {\n    if (!selectedCavo) {\n      onError('Seleziona un cavo prima di procedere');\n      return;\n    }\n    if (!selectedOption) {\n      onError('Seleziona un\\'opzione prima di procedere');\n      return;\n    }\n\n    // Verifica che sia stata selezionata una bobina se l'opzione è \"assegnaNuova\"\n    if (selectedOption === 'assegnaNuova' && !selectedBobinaId) {\n      onError('Seleziona una bobina prima di procedere');\n      return;\n    }\n\n    // Prepara il messaggio di conferma in base all'opzione selezionata\n    let message = '';\n    let action = null;\n    if (selectedOption === 'assegnaNuova') {\n      const bobina = bobine.find(b => b.id_bobina === selectedBobinaId);\n      message = `Sei sicuro di voler assegnare la bobina ${getBobinaNumber(selectedBobinaId)} al cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina(selectedBobinaId);\n    } else if (selectedOption === 'rimuoviBobina') {\n      message = `Sei sicuro di voler rimuovere la bobina attuale dal cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina('BOBINA_VUOTA');\n    } else if (selectedOption === 'annullaInstallazione') {\n      message = `ATTENZIONE: Questa operazione annullerà l'installazione del cavo ${selectedCavo.id_cavo}. Tutti i metri posati saranno restituiti alla bobina originale. Sei sicuro di voler procedere?`;\n      action = () => annullaInstallazione();\n    } else if (selectedOption === 'annullaOperazione') {\n      // Per annulla operazione, chiudi semplicemente il form senza fare nulla\n      handleCloseForm();\n      return;\n    }\n    setConfirmDialogMessage(message);\n    setConfirmDialogAction(() => action);\n    setShowConfirmDialog(true);\n  };\n\n  // Funzione per aggiornare la bobina di un cavo\n  const updateBobina = async bobinaId => {\n    try {\n      setLoading(true);\n      await caviService.updateBobina(cantiereId, selectedCavo.id_cavo, bobinaId);\n      onSuccess(`Bobina ${bobinaId === 'BOBINA_VUOTA' ? 'vuota assegnata' : 'assegnata'} con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento della bobina:', error);\n      onError('Errore durante l\\'aggiornamento della bobina: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per annullare l'installazione di un cavo\n  const annullaInstallazione = async () => {\n    try {\n      setLoading(true);\n\n      // Chiamata all'API per annullare l'installazione\n      await caviService.cancelInstallation(cantiereId, selectedCavo.id_cavo);\n      onSuccess(`Installazione del cavo ${selectedCavo.id_cavo} annullata con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'annullamento dell\\'installazione:', error);\n      onError('Errore durante l\\'annullamento dell\\'installazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per chiudere il form e resettare tutto\n  const handleCloseForm = () => {\n    // Reset di tutti gli stati\n    setSelectedCavo(null);\n    setSelectedOption('');\n    setSelectedBobinaId('');\n    setCavoIdInput('');\n    setShowSearchResults(false);\n\n    // Messaggio di conferma\n    onSuccess('Operazione annullata');\n  };\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = idBobina => {\n    if (idBobina === 'BOBINA_VUOTA') return 'BOBINA VUOTA';\n\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Renderizza il form per la selezione del cavo\n  const renderCavoSelectionForm = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 3,\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"Seleziona un cavo posato\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(TextField, {\n        label: \"ID Cavo\",\n        value: cavoIdInput,\n        onChange: e => setCavoIdInput(e.target.value),\n        sx: {\n          width: '250px'\n        },\n        InputProps: {\n          endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n            position: \"end\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleSearchCavoById,\n              disabled: loading || !cavoIdInput.trim(),\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 15\n          }, this)\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        onClick: () => setShowSearchResults(!showSearchResults),\n        disabled: caviLoading || cavi.length === 0,\n        sx: {\n          width: '200px'\n        },\n        children: showSearchResults ? 'Nascondi lista cavi' : 'Mostra lista cavi'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 7\n    }, this), caviLoading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        mt: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 9\n    }, this), showSearchResults && cavi.length > 0 && /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      sx: {\n        mt: 2,\n        maxHeight: 300\n      },\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        stickyHeader: true,\n        size: \"small\",\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"ID Cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Tipologia\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Formazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Metri Posati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n            hover: true,\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: cavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: cavo.tipologia\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: cavo.sezione\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: cavo.metratura_reale\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: getBobinaNumber(cavo.id_bobina || '')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                onClick: () => handleSelectCavo(cavo),\n                variant: \"contained\",\n                color: \"primary\",\n                children: \"Seleziona\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 19\n            }, this)]\n          }, cavo.id_cavo, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 364,\n    columnNumber: 5\n  }, this);\n\n  // Renderizza i dettagli del cavo selezionato\n  const renderSelectedCavoDetails = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"Dettagli del cavo selezionato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          size: \"small\",\n          onClick: () => setShowCavoDetailsDialog(true),\n          children: \"Visualizza dettagli completi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"ID Cavo:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: selectedCavo.id_cavo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Tipologia:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: selectedCavo.tipologia\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Formazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: selectedCavo.sezione\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Metri Posati:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: selectedCavo.metratura_reale\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Bobina Attuale:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: selectedCavo.id_bobina ? getBobinaNumber(selectedCavo.id_bobina) : 'Nessuna bobina'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Stato:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: selectedCavo.stato_installazione\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza le opzioni di modifica\n  const renderModificaOptions = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Opzioni di modifica\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n        value: selectedOption,\n        onChange: handleOptionChange,\n        children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"assegnaNuova\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 22\n          }, this),\n          label: \"Assegna nuova bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"rimuoviBobina\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 22\n          }, this),\n          label: \"Rimuovi bobina attuale\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"annullaInstallazione\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 22\n          }, this),\n          label: \"Annulla installazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 9\n      }, this), selectedOption === 'assegnaNuova' && renderBobineSelection(), selectedOption === 'rimuoviBobina' && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mt: 2\n        },\n        children: \"Questa operazione rimuover\\xE0 l'associazione con la bobina attuale, assegnando una \\\"BOBINA_VUOTA\\\" al cavo. Il cavo rimarr\\xE0 nello stato posato e i metri posati rimarranno invariati. La bobina attuale (se presente) riavr\\xE0 i suoi metri restituiti.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 11\n      }, this), selectedOption === 'annullaInstallazione' && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        sx: {\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          fontWeight: \"bold\",\n          children: \"ATTENZIONE: Questa operazione annuller\\xE0 completamente l'installazione del cavo.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [\"- Il cavo torner\\xE0 allo stato \\\"Da installare\\\"\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 59\n          }, this), \"- La metratura reale sar\\xE0 azzerata\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 49\n          }, this), \"- L'associazione con la bobina sar\\xE0 rimossa\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 58\n          }, this), \"- I metri posati saranno restituiti alla bobina originale (se presente)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 11\n      }, this), selectedOption === 'annullaOperazione' && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mt: 2\n        },\n        children: \"Questa operazione terminer\\xE0 senza apportare alcuna modifica al cavo.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'flex-end'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: selectedOption === 'annullaOperazione' ? 'secondary' : 'primary',\n          startIcon: selectedOption === 'annullaOperazione' ? /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 65\n          }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 82\n          }, this),\n          onClick: handleSave,\n          disabled: loading || !selectedOption || selectedOption === 'assegnaNuova' && !selectedBobinaId,\n          children: selectedOption === 'annullaOperazione' ? 'Annulla operazione' : 'Salva modifiche'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 556,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza la selezione delle bobine\n  const renderBobineSelection = () => {\n    if (bobineLoading) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 575,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Filtra le bobine in base al testo di ricerca\n    const bobineFiltrate = bobine.filter(bobina => {\n      const searchLower = bobinaSearchText.toLowerCase();\n      return !bobinaSearchText || getBobinaNumber(bobina.id_bobina).toLowerCase().includes(searchLower) || String(bobina.tipologia || '').toLowerCase().includes(searchLower) || String(bobina.sezione || '').toLowerCase().includes(searchLower);\n    });\n\n    // Separa le bobine compatibili e non compatibili\n    const bobineCompatibili = selectedCavo ? bobineFiltrate.filter(bobina => String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim() && (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso')) : [];\n    const bobineNonCompatibili = selectedCavo ? bobineFiltrate.filter(bobina => !(String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim()) && (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso')) : [];\n\n    // Ordina per metri residui (decrescente)\n    bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n    bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Ricerca bobina (ID, tipologia, formazione)\",\n          value: bobinaSearchText,\n          onChange: e => setBobinaSearchText(e.target.value),\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 17\n            }, this)\n          },\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 613,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 612,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2,\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Inserisci numero bobina (es. 3)\",\n          value: bobinaNumericInput,\n          onChange: e => setBobinaNumericInput(e.target.value),\n          type: \"number\",\n          sx: {\n            width: '250px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          onClick: handleSelectBobinaByNumber,\n          disabled: !bobinaNumericInput.trim(),\n          children: \"Seleziona\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 630,\n        columnNumber: 9\n      }, this), selectedBobinaId && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"success\",\n        sx: {\n          mb: 2\n        },\n        children: [\"Bobina selezionata: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: getBobinaNumber(selectedBobinaId)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 33\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 649,\n        columnNumber: 11\n      }, this), bobineCompatibili.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 2,\n            bgcolor: '#e8f5e8'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              color: '#2e7d32',\n              fontWeight: 'bold'\n            },\n            children: [\"Bobine compatibili (\", bobineCompatibili.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"Formazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"Metri\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            maxHeight: '300px',\n            overflow: 'auto',\n            bgcolor: 'background.paper'\n          },\n          children: bobineCompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n            disablePadding: true,\n            secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n              edge: \"end\",\n              size: \"small\",\n              onClick: () => handleSelectBobina(bobina.id_bobina),\n              color: selectedBobinaId === bobina.id_bobina ? 'success' : 'primary',\n              children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 21\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: selectedBobinaId === bobina.id_bobina ? 'bold' : 'normal'\n                    },\n                    children: getBobinaNumber(bobina.id_bobina)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 699,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: bobina.tipologia\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: bobina.sezione\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 707,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [bobina.metri_residui, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 710,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 709,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: bobina.stato_bobina,\n                    size: \"small\",\n                    color: bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 713,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 23\n              }, this),\n              sx: {\n                bgcolor: selectedBobinaId === bobina.id_bobina ? '#e8f5e8' : 'transparent',\n                borderRadius: 1,\n                px: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 19\n            }, this)\n          }, bobina.id_bobina, false, {\n            fileName: _jsxFileName,\n            lineNumber: 681,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 679,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 656,\n        columnNumber: 11\n      }, this), bobineNonCompatibili.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 2,\n            bgcolor: '#fff3e0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              color: '#f57c00',\n              fontWeight: 'bold'\n            },\n            children: [\"Bobine non compatibili (\", bobineNonCompatibili.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 737,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Queste bobine non corrispondono alle caratteristiche del cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 745,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"Formazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 750,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"Metri\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 757,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 756,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 736,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            maxHeight: '300px',\n            overflow: 'auto',\n            bgcolor: 'background.paper'\n          },\n          children: bobineNonCompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n            disablePadding: true,\n            secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n              edge: \"end\",\n              size: \"small\",\n              onClick: () => {\n                onError(`Bobina ${getBobinaNumber(bobina.id_bobina)} non compatibile. Tipologia: ${bobina.tipologia}, Formazione: ${bobina.sezione}`);\n              },\n              color: \"warning\",\n              children: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 21\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: getBobinaNumber(bobina.id_bobina)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: bobina.tipologia !== (selectedCavo === null || selectedCavo === void 0 ? void 0 : selectedCavo.tipologia) ? 'error' : 'text.primary',\n                    children: bobina.tipologia\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 786,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 785,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: String(bobina.sezione) !== String(selectedCavo === null || selectedCavo === void 0 ? void 0 : selectedCavo.sezione) ? 'error' : 'text.primary',\n                    children: bobina.sezione\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 791,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 790,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [bobina.metri_residui, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 796,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 795,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: bobina.stato_bobina,\n                    size: \"small\",\n                    color: bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 799,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 798,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 19\n            }, this)\n          }, bobina.id_bobina, false, {\n            fileName: _jsxFileName,\n            lineNumber: 763,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 761,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 735,\n        columnNumber: 11\n      }, this), bobineCompatibili.length === 0 && bobineNonCompatibili.length === 0 && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mt: 2\n        },\n        children: bobinaSearchText ? 'Nessuna bobina trovata con i criteri di ricerca specificati.' : 'Non ci sono bobine disponibili.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 816,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 610,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [renderCavoSelectionForm(), renderSelectedCavoDetails(), renderModificaOptions(), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showCavoDetailsDialog,\n      onClose: () => setShowCavoDetailsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Dettagli completi del cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 842,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: selectedCavo && /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 844,\n          columnNumber: 28\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 843,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowCavoDetailsDialog(false),\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 847,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 846,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 836,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showConfirmDialog,\n      onClose: () => setShowConfirmDialog(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Conferma operazione\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 856,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: confirmDialogMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 858,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 857,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowConfirmDialog(false),\n          color: \"primary\",\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 861,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setShowConfirmDialog(false);\n            if (confirmDialogAction) confirmDialogAction();\n          },\n          color: \"primary\",\n          variant: \"contained\",\n          autoFocus: true,\n          children: \"Conferma\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 867,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 860,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 852,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 825,\n    columnNumber: 5\n  }, this);\n};\n_s(ModificaBobinaForm, \"PZ4fkXn+INwY5icGbHeyKjTAiNs=\", false, function () {\n  return [useNavigate];\n});\n_c = ModificaBobinaForm;\nexport default ModificaBobinaForm;\nvar _c;\n$RefreshReg$(_c, \"ModificaBobinaForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "<PERSON><PERSON>", "CircularProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Radio", "RadioGroup", "FormControlLabel", "IconButton", "InputAdornment", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Chip", "Search", "SearchIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "AddCircleOutline", "AddCircleOutlineIcon", "useNavigate", "caviService", "parcoCaviService", "CavoDetailsView", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "jsxDEV", "_jsxDEV", "ModificaBobinaForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "searchResults", "setSearchResults", "showSearchResults", "setShowSearchResults", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "compatibleBobine", "setCompatibleBobine", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "selectedOption", "setSelectedOption", "selectedBobinaId", "setSelectedBobinaId", "bobinaSearchText", "setBobinaSearchText", "bobinaNumericInput", "setBobinaNumericInput", "showConfirmDialog", "setShowConfirmDialog", "confirmDialogMessage", "setConfirmDialogMessage", "confirmDialogAction", "setConfirmDialogAction", "showCavoDetailsDialog", "setShowCavoDetailsDialog", "loadCavi", "loadBobine", "console", "log", "caviData", "get<PERSON><PERSON>", "length", "caviInstallati", "filter", "cavo", "parseFloat", "metratura_reale", "stato_installazione", "error", "detail", "message", "bobine<PERSON><PERSON>", "getBobine", "compatibleBobineData", "bobina", "tipologia", "sezione", "stato_bobina", "handleSearchCavoById", "trim", "getCavoById", "handleSelectCavo", "handleOptionChange", "event", "target", "value", "handleSelectBobina", "bobina<PERSON>d", "getBobinaNumber", "handleSelectBobinaByNumber", "fullBobinaId", "find", "b", "id_bobina", "isCompatible", "String", "handleSave", "action", "id_cavo", "updateBobina", "annullaInstallazione", "handleCloseForm", "cancelInstallation", "idBobina", "includes", "split", "renderCavoSelectionForm", "sx", "p", "mb", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "alignItems", "gap", "label", "onChange", "e", "width", "InputProps", "endAdornment", "position", "onClick", "disabled", "justifyContent", "mt", "component", "maxHeight", "<PERSON><PERSON><PERSON><PERSON>", "size", "map", "hover", "color", "renderSelectedCavoDetails", "container", "spacing", "item", "xs", "sm", "md", "renderModificaOptions", "control", "renderBobineSelection", "severity", "fontWeight", "startIcon", "bobineFiltrate", "searchLower", "toLowerCase", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "bobineNonCompatibili", "sort", "a", "metri_residui", "fullWidth", "startAdornment", "type", "bgcolor", "flex", "fontSize", "overflow", "disablePadding", "secondaryAction", "edge", "primary", "borderRadius", "px", "open", "onClose", "max<PERSON><PERSON><PERSON>", "autoFocus", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/ModificaBobinaForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Alert,\n  CircularProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Radio,\n  RadioGroup,\n  FormControlLabel,\n  IconButton,\n  InputAdornment,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Chip\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon,\n  AddCircleOutline as AddCircleOutlineIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoDetailsView from './CavoDetailsView';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\n\n/**\n * Componente per la modifica della bobina di un cavo già posato\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst ModificaBobinaForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [selectedOption, setSelectedOption] = useState('');\n  const [selectedBobinaId, setSelectedBobinaId] = useState('');\n  const [bobinaSearchText, setBobinaSearchText] = useState('');\n  const [bobinaNumericInput, setBobinaNumericInput] = useState('');\n\n  // Stati per i dialoghi\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');\n  const [confirmDialogAction, setConfirmDialogAction] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica i cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica le bobine quando viene selezionato un cavo\n  useEffect(() => {\n    if (selectedCavo) {\n      loadBobine();\n    }\n  }, [selectedCavo]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica solo i cavi installati (con metratura_reale > 0)\n      const caviData = await caviService.getCavi(cantiereId, null, 'Installato');\n      console.log(`Caricati ${caviData.length} cavi installati`);\n\n      // Filtra i cavi che hanno metratura_reale > 0\n      const caviInstallati = caviData.filter(cavo =>\n        parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato'\n      );\n\n      setCavi(caviInstallati);\n      console.log(`Filtrati ${caviInstallati.length} cavi effettivamente installati`);\n    } catch (error) {\n      console.error('Errore durante il caricamento dei cavi:', error);\n      onError('Errore durante il caricamento dei cavi: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    if (!selectedCavo) return;\n\n    try {\n      setBobineLoading(true);\n      console.log(`Caricamento bobine per il cantiere ${cantiereId}...`);\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Caricati ${bobineData.length} bobine`);\n\n      // Filtra le bobine compatibili\n      const compatibleBobineData = bobineData.filter(bobina =>\n        bobina.tipologia === selectedCavo.tipologia &&\n        bobina.sezione === selectedCavo.sezione &&\n        (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso')\n      );\n\n      setBobine(bobineData);\n      setCompatibleBobine(compatibleBobineData);\n      console.log(`Filtrate ${compatibleBobineData.length} bobine compatibili`);\n    } catch (error) {\n      console.error('Errore durante il caricamento delle bobine:', error);\n      onError('Errore durante il caricamento delle bobine: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      const cavo = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica che il cavo sia installato\n      if (parseFloat(cavo.metratura_reale) <= 0 && cavo.stato_installazione !== 'Installato') {\n        onError('Il cavo selezionato non risulta installato');\n        setLoading(false);\n        return;\n      }\n\n      setSelectedCavo(cavo);\n      setSelectedOption(''); // Reset dell'opzione selezionata\n      setSelectedBobinaId(''); // Reset della bobina selezionata\n      setShowSearchResults(false);\n    } catch (error) {\n      console.error('Errore durante la ricerca del cavo:', error);\n      onError('Errore durante la ricerca del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo dalla lista\n  const handleSelectCavo = (cavo) => {\n    setSelectedCavo(cavo);\n    setSelectedOption(''); // Reset dell'opzione selezionata\n    setSelectedBobinaId(''); // Reset della bobina selezionata\n    setShowSearchResults(false);\n  };\n\n  // Gestisce il cambio dell'opzione selezionata\n  const handleOptionChange = (event) => {\n    setSelectedOption(event.target.value);\n    setSelectedBobinaId(''); // Reset della bobina selezionata quando cambia l'opzione\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = (bobinaId) => {\n    setSelectedBobinaId(bobinaId);\n    // Aggiorna anche l'input numerico con la parte numerica\n    setBobinaNumericInput(getBobinaNumber(bobinaId));\n  };\n\n  // Gestisce la selezione di una bobina tramite input numerico\n  const handleSelectBobinaByNumber = () => {\n    if (!bobinaNumericInput.trim()) {\n      onError('Inserisci un numero di bobina valido');\n      return;\n    }\n\n    // Costruisci l'ID completo della bobina\n    const fullBobinaId = `C${cantiereId}_B${bobinaNumericInput.trim()}`;\n\n    // Verifica che la bobina esista\n    const bobina = bobine.find(b => b.id_bobina === fullBobinaId);\n    if (!bobina) {\n      onError(`Bobina ${bobinaNumericInput} non trovata nel cantiere`);\n      return;\n    }\n\n    // Verifica compatibilità\n    const isCompatible = selectedCavo &&\n      String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n      String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim();\n\n    if (!isCompatible) {\n      onError(`Bobina ${bobinaNumericInput} non è compatibile con il cavo selezionato`);\n      return;\n    }\n\n    // Seleziona la bobina\n    setSelectedBobinaId(fullBobinaId);\n  };\n\n  // Gestisce il salvataggio delle modifiche\n  const handleSave = () => {\n    if (!selectedCavo) {\n      onError('Seleziona un cavo prima di procedere');\n      return;\n    }\n\n    if (!selectedOption) {\n      onError('Seleziona un\\'opzione prima di procedere');\n      return;\n    }\n\n    // Verifica che sia stata selezionata una bobina se l'opzione è \"assegnaNuova\"\n    if (selectedOption === 'assegnaNuova' && !selectedBobinaId) {\n      onError('Seleziona una bobina prima di procedere');\n      return;\n    }\n\n    // Prepara il messaggio di conferma in base all'opzione selezionata\n    let message = '';\n    let action = null;\n\n    if (selectedOption === 'assegnaNuova') {\n      const bobina = bobine.find(b => b.id_bobina === selectedBobinaId);\n      message = `Sei sicuro di voler assegnare la bobina ${getBobinaNumber(selectedBobinaId)} al cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina(selectedBobinaId);\n    } else if (selectedOption === 'rimuoviBobina') {\n      message = `Sei sicuro di voler rimuovere la bobina attuale dal cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina('BOBINA_VUOTA');\n    } else if (selectedOption === 'annullaInstallazione') {\n      message = `ATTENZIONE: Questa operazione annullerà l'installazione del cavo ${selectedCavo.id_cavo}. Tutti i metri posati saranno restituiti alla bobina originale. Sei sicuro di voler procedere?`;\n      action = () => annullaInstallazione();\n    } else if (selectedOption === 'annullaOperazione') {\n      // Per annulla operazione, chiudi semplicemente il form senza fare nulla\n      handleCloseForm();\n      return;\n    }\n\n    setConfirmDialogMessage(message);\n    setConfirmDialogAction(() => action);\n    setShowConfirmDialog(true);\n  };\n\n  // Funzione per aggiornare la bobina di un cavo\n  const updateBobina = async (bobinaId) => {\n    try {\n      setLoading(true);\n      await caviService.updateBobina(cantiereId, selectedCavo.id_cavo, bobinaId);\n\n      onSuccess(`Bobina ${bobinaId === 'BOBINA_VUOTA' ? 'vuota assegnata' : 'assegnata'} con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento della bobina:', error);\n      onError('Errore durante l\\'aggiornamento della bobina: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per annullare l'installazione di un cavo\n  const annullaInstallazione = async () => {\n    try {\n      setLoading(true);\n\n      // Chiamata all'API per annullare l'installazione\n      await caviService.cancelInstallation(cantiereId, selectedCavo.id_cavo);\n\n      onSuccess(`Installazione del cavo ${selectedCavo.id_cavo} annullata con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'annullamento dell\\'installazione:', error);\n      onError('Errore durante l\\'annullamento dell\\'installazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per chiudere il form e resettare tutto\n  const handleCloseForm = () => {\n    // Reset di tutti gli stati\n    setSelectedCavo(null);\n    setSelectedOption('');\n    setSelectedBobinaId('');\n    setCavoIdInput('');\n    setShowSearchResults(false);\n\n    // Messaggio di conferma\n    onSuccess('Operazione annullata');\n  };\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina) => {\n    if (idBobina === 'BOBINA_VUOTA') return 'BOBINA VUOTA';\n\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Renderizza il form per la selezione del cavo\n  const renderCavoSelectionForm = () => (\n    <Paper sx={{ p: 3, mb: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        Seleziona un cavo posato\n      </Typography>\n\n      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n        <TextField\n          label=\"ID Cavo\"\n          value={cavoIdInput}\n          onChange={(e) => setCavoIdInput(e.target.value)}\n          sx={{ width: '250px' }}\n          InputProps={{\n            endAdornment: (\n              <InputAdornment position=\"end\">\n                <IconButton\n                  onClick={handleSearchCavoById}\n                  disabled={loading || !cavoIdInput.trim()}\n                >\n                  <SearchIcon />\n                </IconButton>\n              </InputAdornment>\n            )\n          }}\n        />\n        <Button\n          variant=\"outlined\"\n          onClick={() => setShowSearchResults(!showSearchResults)}\n          disabled={caviLoading || cavi.length === 0}\n          sx={{ width: '200px' }}\n        >\n          {showSearchResults ? 'Nascondi lista cavi' : 'Mostra lista cavi'}\n        </Button>\n      </Box>\n\n      {caviLoading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {showSearchResults && cavi.length > 0 && (\n        <TableContainer component={Paper} sx={{ mt: 2, maxHeight: 300 }}>\n          <Table stickyHeader size=\"small\">\n            <TableHead>\n              <TableRow>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>Formazione</TableCell>\n                <TableCell>Metri Posati</TableCell>\n                <TableCell>Bobina</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {cavi.map((cavo) => (\n                <TableRow key={cavo.id_cavo} hover>\n                  <TableCell>{cavo.id_cavo}</TableCell>\n                  <TableCell>{cavo.tipologia}</TableCell>\n                  <TableCell>{cavo.sezione}</TableCell>\n                  <TableCell>{cavo.metratura_reale}</TableCell>\n                  <TableCell>{getBobinaNumber(cavo.id_bobina || '')}</TableCell>\n                  <TableCell>\n                    <Button\n                      size=\"small\"\n                      onClick={() => handleSelectCavo(cavo)}\n                      variant=\"contained\"\n                      color=\"primary\"\n                    >\n                      Seleziona\n                    </Button>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      )}\n    </Paper>\n  );\n\n  // Renderizza i dettagli del cavo selezionato\n  const renderSelectedCavoDetails = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n          <Typography variant=\"h6\">\n            Dettagli del cavo selezionato\n          </Typography>\n          <Button\n            variant=\"outlined\"\n            size=\"small\"\n            onClick={() => setShowCavoDetailsDialog(true)}\n          >\n            Visualizza dettagli completi\n          </Button>\n        </Box>\n\n        <Grid container spacing={2}>\n          <Grid item xs={12} sm={6} md={4}>\n            <Typography variant=\"subtitle2\">ID Cavo:</Typography>\n            <Typography variant=\"body1\">{selectedCavo.id_cavo}</Typography>\n          </Grid>\n          <Grid item xs={12} sm={6} md={4}>\n            <Typography variant=\"subtitle2\">Tipologia:</Typography>\n            <Typography variant=\"body1\">{selectedCavo.tipologia}</Typography>\n          </Grid>\n          <Grid item xs={12} sm={6} md={4}>\n            <Typography variant=\"subtitle2\">Formazione:</Typography>\n            <Typography variant=\"body1\">{selectedCavo.sezione}</Typography>\n          </Grid>\n          <Grid item xs={12} sm={6} md={4}>\n            <Typography variant=\"subtitle2\">Metri Posati:</Typography>\n            <Typography variant=\"body1\">{selectedCavo.metratura_reale}</Typography>\n          </Grid>\n          <Grid item xs={12} sm={6} md={4}>\n            <Typography variant=\"subtitle2\">Bobina Attuale:</Typography>\n            <Typography variant=\"body1\">\n              {selectedCavo.id_bobina ? getBobinaNumber(selectedCavo.id_bobina) : 'Nessuna bobina'}\n            </Typography>\n          </Grid>\n          <Grid item xs={12} sm={6} md={4}>\n            <Typography variant=\"subtitle2\">Stato:</Typography>\n            <Typography variant=\"body1\">{selectedCavo.stato_installazione}</Typography>\n          </Grid>\n        </Grid>\n      </Paper>\n    );\n  };\n\n  // Renderizza le opzioni di modifica\n  const renderModificaOptions = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Opzioni di modifica\n        </Typography>\n\n        <RadioGroup\n          value={selectedOption}\n          onChange={handleOptionChange}\n        >\n          <FormControlLabel\n            value=\"assegnaNuova\"\n            control={<Radio />}\n            label=\"Assegna nuova bobina\"\n          />\n          <FormControlLabel\n            value=\"rimuoviBobina\"\n            control={<Radio />}\n            label=\"Rimuovi bobina attuale\"\n          />\n          <FormControlLabel\n            value=\"annullaInstallazione\"\n            control={<Radio />}\n            label=\"Annulla installazione\"\n          />\n        </RadioGroup>\n\n        {selectedOption === 'assegnaNuova' && renderBobineSelection()}\n\n        {selectedOption === 'rimuoviBobina' && (\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            Questa operazione rimuoverà l'associazione con la bobina attuale, assegnando una \"BOBINA_VUOTA\" al cavo.\n            Il cavo rimarrà nello stato posato e i metri posati rimarranno invariati.\n            La bobina attuale (se presente) riavrà i suoi metri restituiti.\n          </Alert>\n        )}\n\n        {selectedOption === 'annullaInstallazione' && (\n          <Alert severity=\"warning\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\" fontWeight=\"bold\">\n              ATTENZIONE: Questa operazione annullerà completamente l'installazione del cavo.\n            </Typography>\n            <Typography variant=\"body2\">\n              - Il cavo tornerà allo stato \"Da installare\"<br />\n              - La metratura reale sarà azzerata<br />\n              - L'associazione con la bobina sarà rimossa<br />\n              - I metri posati saranno restituiti alla bobina originale (se presente)\n            </Typography>\n          </Alert>\n        )}\n\n        {selectedOption === 'annullaOperazione' && (\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            Questa operazione terminerà senza apportare alcuna modifica al cavo.\n          </Alert>\n        )}\n\n        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>\n          <Button\n            variant=\"contained\"\n            color={selectedOption === 'annullaOperazione' ? 'secondary' : 'primary'}\n            startIcon={selectedOption === 'annullaOperazione' ? <CancelIcon /> : <SaveIcon />}\n            onClick={handleSave}\n            disabled={loading || !selectedOption || (selectedOption === 'assegnaNuova' && !selectedBobinaId)}\n          >\n            {selectedOption === 'annullaOperazione' ? 'Annulla operazione' : 'Salva modifiche'}\n          </Button>\n        </Box>\n      </Paper>\n    );\n  };\n\n  // Renderizza la selezione delle bobine\n  const renderBobineSelection = () => {\n    if (bobineLoading) {\n      return (\n        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n          <CircularProgress />\n        </Box>\n      );\n    }\n\n    // Filtra le bobine in base al testo di ricerca\n    const bobineFiltrate = bobine.filter(bobina => {\n      const searchLower = bobinaSearchText.toLowerCase();\n      return !bobinaSearchText ||\n        getBobinaNumber(bobina.id_bobina).toLowerCase().includes(searchLower) ||\n        String(bobina.tipologia || '').toLowerCase().includes(searchLower) ||\n        String(bobina.sezione || '').toLowerCase().includes(searchLower);\n    });\n\n    // Separa le bobine compatibili e non compatibili\n    const bobineCompatibili = selectedCavo\n      ? bobineFiltrate.filter(bobina =>\n          String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n          String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim() &&\n          (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso'))\n      : [];\n\n    const bobineNonCompatibili = selectedCavo\n      ? bobineFiltrate.filter(bobina =>\n          !(String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n            String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim()) &&\n          (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso'))\n      : [];\n\n    // Ordina per metri residui (decrescente)\n    bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n    bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n    return (\n      <Box sx={{ mt: 2 }}>\n        {/* Campo di ricerca rapida */}\n        <Box sx={{ mb: 2 }}>\n          <TextField\n            fullWidth\n            label=\"Ricerca bobina (ID, tipologia, formazione)\"\n            value={bobinaSearchText}\n            onChange={(e) => setBobinaSearchText(e.target.value)}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon />\n                </InputAdornment>\n              )\n            }}\n            sx={{ mb: 2 }}\n          />\n        </Box>\n\n        {/* Input numerico per selezione diretta */}\n        <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>\n          <TextField\n            label=\"Inserisci numero bobina (es. 3)\"\n            value={bobinaNumericInput}\n            onChange={(e) => setBobinaNumericInput(e.target.value)}\n            type=\"number\"\n            sx={{ width: '250px' }}\n          />\n          <Button\n            variant=\"outlined\"\n            onClick={handleSelectBobinaByNumber}\n            disabled={!bobinaNumericInput.trim()}\n          >\n            Seleziona\n          </Button>\n        </Box>\n\n        {/* Bobina selezionata */}\n        {selectedBobinaId && (\n          <Alert severity=\"success\" sx={{ mb: 2 }}>\n            Bobina selezionata: <strong>{getBobinaNumber(selectedBobinaId)}</strong>\n          </Alert>\n        )}\n\n        {/* Lista bobine compatibili */}\n        {bobineCompatibili.length > 0 && (\n          <Paper sx={{ mb: 2 }}>\n            <Box sx={{ p: 2, bgcolor: '#e8f5e8' }}>\n              <Typography variant=\"h6\" sx={{ color: '#2e7d32', fontWeight: 'bold' }}>\n                Bobine compatibili ({bobineCompatibili.length})\n              </Typography>\n              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>\n                </Box>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipologia</Typography>\n                </Box>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Formazione</Typography>\n                </Box>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Metri</Typography>\n                </Box>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>\n                </Box>\n              </Box>\n            </Box>\n            <List sx={{ maxHeight: '300px', overflow: 'auto', bgcolor: 'background.paper' }}>\n              {bobineCompatibili.map((bobina) => (\n                <ListItem\n                  key={bobina.id_bobina}\n                  disablePadding\n                  secondaryAction={\n                    <IconButton\n                      edge=\"end\"\n                      size=\"small\"\n                      onClick={() => handleSelectBobina(bobina.id_bobina)}\n                      color={selectedBobinaId === bobina.id_bobina ? 'success' : 'primary'}\n                    >\n                      <AddCircleOutlineIcon />\n                    </IconButton>\n                  }\n                >\n                  <ListItemText\n                    primary={\n                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography variant=\"body2\" sx={{ fontWeight: selectedBobinaId === bobina.id_bobina ? 'bold' : 'normal' }}>\n                            {getBobinaNumber(bobina.id_bobina)}\n                          </Typography>\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography variant=\"body2\">{bobina.tipologia}</Typography>\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography variant=\"body2\">{bobina.sezione}</Typography>\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography variant=\"body2\">{bobina.metri_residui}m</Typography>\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Chip\n                            label={bobina.stato_bobina}\n                            size=\"small\"\n                            color={bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning'}\n                          />\n                        </Box>\n                      </Box>\n                    }\n                    sx={{\n                      bgcolor: selectedBobinaId === bobina.id_bobina ? '#e8f5e8' : 'transparent',\n                      borderRadius: 1,\n                      px: 1\n                    }}\n                  />\n                </ListItem>\n              ))}\n            </List>\n          </Paper>\n        )}\n\n        {/* Lista bobine non compatibili */}\n        {bobineNonCompatibili.length > 0 && (\n          <Paper sx={{ mb: 2 }}>\n            <Box sx={{ p: 2, bgcolor: '#fff3e0' }}>\n              <Typography variant=\"h6\" sx={{ color: '#f57c00', fontWeight: 'bold' }}>\n                Bobine non compatibili ({bobineNonCompatibili.length})\n              </Typography>\n              <Typography variant=\"caption\" color=\"text.secondary\">\n                Queste bobine non corrispondono alle caratteristiche del cavo\n              </Typography>\n              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>\n                </Box>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipologia</Typography>\n                </Box>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Formazione</Typography>\n                </Box>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Metri</Typography>\n                </Box>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>\n                </Box>\n              </Box>\n            </Box>\n            <List sx={{ maxHeight: '300px', overflow: 'auto', bgcolor: 'background.paper' }}>\n              {bobineNonCompatibili.map((bobina) => (\n                <ListItem\n                  key={bobina.id_bobina}\n                  disablePadding\n                  secondaryAction={\n                    <IconButton\n                      edge=\"end\"\n                      size=\"small\"\n                      onClick={() => {\n                        onError(`Bobina ${getBobinaNumber(bobina.id_bobina)} non compatibile. Tipologia: ${bobina.tipologia}, Formazione: ${bobina.sezione}`);\n                      }}\n                      color=\"warning\"\n                    >\n                      <WarningIcon />\n                    </IconButton>\n                  }\n                >\n                  <ListItemText\n                    primary={\n                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography variant=\"body2\">{getBobinaNumber(bobina.id_bobina)}</Typography>\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography variant=\"body2\" color={bobina.tipologia !== selectedCavo?.tipologia ? 'error' : 'text.primary'}>\n                            {bobina.tipologia}\n                          </Typography>\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography variant=\"body2\" color={String(bobina.sezione) !== String(selectedCavo?.sezione) ? 'error' : 'text.primary'}>\n                            {bobina.sezione}\n                          </Typography>\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography variant=\"body2\">{bobina.metri_residui}m</Typography>\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Chip\n                            label={bobina.stato_bobina}\n                            size=\"small\"\n                            color={bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning'}\n                          />\n                        </Box>\n                      </Box>\n                    }\n                  />\n                </ListItem>\n              ))}\n            </List>\n          </Paper>\n        )}\n\n        {/* Messaggio se non ci sono bobine */}\n        {bobineCompatibili.length === 0 && bobineNonCompatibili.length === 0 && (\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            {bobinaSearchText ? 'Nessuna bobina trovata con i criteri di ricerca specificati.' : 'Non ci sono bobine disponibili.'}\n          </Alert>\n        )}\n      </Box>\n    );\n  };\n\n  return (\n    <Box>\n      {/* Form per la selezione del cavo */}\n      {renderCavoSelectionForm()}\n\n      {/* Dettagli del cavo selezionato */}\n      {renderSelectedCavoDetails()}\n\n      {/* Opzioni di modifica */}\n      {renderModificaOptions()}\n\n      {/* Dialog per la visualizzazione dei dettagli completi del cavo */}\n      <Dialog\n        open={showCavoDetailsDialog}\n        onClose={() => setShowCavoDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>Dettagli completi del cavo</DialogTitle>\n        <DialogContent>\n          {selectedCavo && <CavoDetailsView cavo={selectedCavo} />}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowCavoDetailsDialog(false)}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog di conferma */}\n      <Dialog\n        open={showConfirmDialog}\n        onClose={() => setShowConfirmDialog(false)}\n      >\n        <DialogTitle>Conferma operazione</DialogTitle>\n        <DialogContent>\n          <Typography>{confirmDialogMessage}</Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button\n            onClick={() => setShowConfirmDialog(false)}\n            color=\"primary\"\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={() => {\n              setShowConfirmDialog(false);\n              if (confirmDialogAction) confirmDialogAction();\n            }}\n            color=\"primary\"\n            variant=\"contained\"\n            autoFocus\n          >\n            Conferma\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ModificaBobinaForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,UAAU,EACVC,cAAc,EACdC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,IAAI,QACC,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,gBAAgB,IAAIC,oBAAoB,QACnC,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAC,MAAA,IAAAC,OAAA;AAQA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsE,WAAW,EAAEC,cAAc,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwE,aAAa,EAAEC,gBAAgB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0E,aAAa,EAAEC,gBAAgB,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC8E,IAAI,EAAEC,OAAO,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACgF,MAAM,EAAEC,SAAS,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACkF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACoF,YAAY,EAAEC,eAAe,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsF,WAAW,EAAEC,cAAc,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwF,cAAc,EAAEC,iBAAiB,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC4F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC8F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAM,CAACgG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACkG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnG,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACoG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACsG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACduG,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACzC,UAAU,CAAC,CAAC;;EAEhB;EACA9D,SAAS,CAAC,MAAM;IACd,IAAImF,YAAY,EAAE;MAChBqB,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACrB,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMoB,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFjC,cAAc,CAAC,IAAI,CAAC;MACpBmC,OAAO,CAACC,GAAG,CAAC,oCAAoC5C,UAAU,KAAK,CAAC;;MAEhE;MACA,MAAM6C,QAAQ,GAAG,MAAM5D,WAAW,CAAC6D,OAAO,CAAC9C,UAAU,EAAE,IAAI,EAAE,YAAY,CAAC;MAC1E2C,OAAO,CAACC,GAAG,CAAC,YAAYC,QAAQ,CAACE,MAAM,kBAAkB,CAAC;;MAE1D;MACA,MAAMC,cAAc,GAAGH,QAAQ,CAACI,MAAM,CAACC,IAAI,IACzCC,UAAU,CAACD,IAAI,CAACE,eAAe,CAAC,GAAG,CAAC,IAAIF,IAAI,CAACG,mBAAmB,KAAK,YACvE,CAAC;MAEDrC,OAAO,CAACgC,cAAc,CAAC;MACvBL,OAAO,CAACC,GAAG,CAAC,YAAYI,cAAc,CAACD,MAAM,iCAAiC,CAAC;IACjF,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/DpD,OAAO,CAAC,0CAA0C,IAAIoD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC/G,CAAC,SAAS;MACRhD,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMkC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACrB,YAAY,EAAE;IAEnB,IAAI;MACFX,gBAAgB,CAAC,IAAI,CAAC;MACtBiC,OAAO,CAACC,GAAG,CAAC,sCAAsC5C,UAAU,KAAK,CAAC;;MAElE;MACA,MAAMyD,UAAU,GAAG,MAAMvE,gBAAgB,CAACwE,SAAS,CAAC1D,UAAU,CAAC;MAC/D2C,OAAO,CAACC,GAAG,CAAC,YAAYa,UAAU,CAACV,MAAM,SAAS,CAAC;;MAEnD;MACA,MAAMY,oBAAoB,GAAGF,UAAU,CAACR,MAAM,CAACW,MAAM,IACnDA,MAAM,CAACC,SAAS,KAAKxC,YAAY,CAACwC,SAAS,IAC3CD,MAAM,CAACE,OAAO,KAAKzC,YAAY,CAACyC,OAAO,KACtCF,MAAM,CAACG,YAAY,KAAK,aAAa,IAAIH,MAAM,CAACG,YAAY,KAAK,QAAQ,CAC5E,CAAC;MAED7C,SAAS,CAACuC,UAAU,CAAC;MACrBrC,mBAAmB,CAACuC,oBAAoB,CAAC;MACzChB,OAAO,CAACC,GAAG,CAAC,YAAYe,oBAAoB,CAACZ,MAAM,qBAAqB,CAAC;IAC3E,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnEpD,OAAO,CAAC,8CAA8C,IAAIoD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACnH,CAAC,SAAS;MACR9C,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMsD,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACzC,WAAW,CAAC0C,IAAI,CAAC,CAAC,EAAE;MACvB/D,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFI,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM4C,IAAI,GAAG,MAAMjE,WAAW,CAACiF,WAAW,CAAClE,UAAU,EAAEuB,WAAW,CAAC0C,IAAI,CAAC,CAAC,CAAC;;MAE1E;MACA,IAAId,UAAU,CAACD,IAAI,CAACE,eAAe,CAAC,IAAI,CAAC,IAAIF,IAAI,CAACG,mBAAmB,KAAK,YAAY,EAAE;QACtFnD,OAAO,CAAC,4CAA4C,CAAC;QACrDI,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEAgB,eAAe,CAAC4B,IAAI,CAAC;MACrBxB,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;MACvBE,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;MACzBd,oBAAoB,CAAC,KAAK,CAAC;IAC7B,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3DpD,OAAO,CAAC,sCAAsC,IAAIoD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC3G,CAAC,SAAS;MACRlD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6D,gBAAgB,GAAIjB,IAAI,IAAK;IACjC5B,eAAe,CAAC4B,IAAI,CAAC;IACrBxB,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;IACvBE,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;IACzBd,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMsD,kBAAkB,GAAIC,KAAK,IAAK;IACpC3C,iBAAiB,CAAC2C,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;IACrC3C,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM4C,kBAAkB,GAAIC,QAAQ,IAAK;IACvC7C,mBAAmB,CAAC6C,QAAQ,CAAC;IAC7B;IACAzC,qBAAqB,CAAC0C,eAAe,CAACD,QAAQ,CAAC,CAAC;EAClD,CAAC;;EAED;EACA,MAAME,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAI,CAAC5C,kBAAkB,CAACkC,IAAI,CAAC,CAAC,EAAE;MAC9B/D,OAAO,CAAC,sCAAsC,CAAC;MAC/C;IACF;;IAEA;IACA,MAAM0E,YAAY,GAAG,IAAI5E,UAAU,KAAK+B,kBAAkB,CAACkC,IAAI,CAAC,CAAC,EAAE;;IAEnE;IACA,MAAML,MAAM,GAAG3C,MAAM,CAAC4D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKH,YAAY,CAAC;IAC7D,IAAI,CAAChB,MAAM,EAAE;MACX1D,OAAO,CAAC,UAAU6B,kBAAkB,2BAA2B,CAAC;MAChE;IACF;;IAEA;IACA,MAAMiD,YAAY,GAAG3D,YAAY,IAC/B4D,MAAM,CAACrB,MAAM,CAACC,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKgB,MAAM,CAAC5D,YAAY,CAACwC,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFgB,MAAM,CAACrB,MAAM,CAACE,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKgB,MAAM,CAAC5D,YAAY,CAACyC,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC;IAEnF,IAAI,CAACe,YAAY,EAAE;MACjB9E,OAAO,CAAC,UAAU6B,kBAAkB,4CAA4C,CAAC;MACjF;IACF;;IAEA;IACAH,mBAAmB,CAACgD,YAAY,CAAC;EACnC,CAAC;;EAED;EACA,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAAC7D,YAAY,EAAE;MACjBnB,OAAO,CAAC,sCAAsC,CAAC;MAC/C;IACF;IAEA,IAAI,CAACuB,cAAc,EAAE;MACnBvB,OAAO,CAAC,0CAA0C,CAAC;MACnD;IACF;;IAEA;IACA,IAAIuB,cAAc,KAAK,cAAc,IAAI,CAACE,gBAAgB,EAAE;MAC1DzB,OAAO,CAAC,yCAAyC,CAAC;MAClD;IACF;;IAEA;IACA,IAAIsD,OAAO,GAAG,EAAE;IAChB,IAAI2B,MAAM,GAAG,IAAI;IAEjB,IAAI1D,cAAc,KAAK,cAAc,EAAE;MACrC,MAAMmC,MAAM,GAAG3C,MAAM,CAAC4D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKpD,gBAAgB,CAAC;MACjE6B,OAAO,GAAG,2CAA2CkB,eAAe,CAAC/C,gBAAgB,CAAC,YAAYN,YAAY,CAAC+D,OAAO,GAAG;MACzHD,MAAM,GAAGA,CAAA,KAAME,YAAY,CAAC1D,gBAAgB,CAAC;IAC/C,CAAC,MAAM,IAAIF,cAAc,KAAK,eAAe,EAAE;MAC7C+B,OAAO,GAAG,4DAA4DnC,YAAY,CAAC+D,OAAO,GAAG;MAC7FD,MAAM,GAAGA,CAAA,KAAME,YAAY,CAAC,cAAc,CAAC;IAC7C,CAAC,MAAM,IAAI5D,cAAc,KAAK,sBAAsB,EAAE;MACpD+B,OAAO,GAAG,oEAAoEnC,YAAY,CAAC+D,OAAO,iGAAiG;MACnMD,MAAM,GAAGA,CAAA,KAAMG,oBAAoB,CAAC,CAAC;IACvC,CAAC,MAAM,IAAI7D,cAAc,KAAK,mBAAmB,EAAE;MACjD;MACA8D,eAAe,CAAC,CAAC;MACjB;IACF;IAEAnD,uBAAuB,CAACoB,OAAO,CAAC;IAChClB,sBAAsB,CAAC,MAAM6C,MAAM,CAAC;IACpCjD,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMmD,YAAY,GAAG,MAAOZ,QAAQ,IAAK;IACvC,IAAI;MACFnE,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMrB,WAAW,CAACoG,YAAY,CAACrF,UAAU,EAAEqB,YAAY,CAAC+D,OAAO,EAAEX,QAAQ,CAAC;MAE1ExE,SAAS,CAAC,UAAUwE,QAAQ,KAAK,cAAc,GAAG,iBAAiB,GAAG,WAAW,eAAe,CAAC;;MAEjG;MACAnD,eAAe,CAAC,IAAI,CAAC;MACrBI,iBAAiB,CAAC,EAAE,CAAC;MACrBE,mBAAmB,CAAC,EAAE,CAAC;MACvBJ,cAAc,CAAC,EAAE,CAAC;;MAElB;MACAiB,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrEpD,OAAO,CAAC,gDAAgD,IAAIoD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACrH,CAAC,SAAS;MACRlD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgF,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFhF,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMrB,WAAW,CAACuG,kBAAkB,CAACxF,UAAU,EAAEqB,YAAY,CAAC+D,OAAO,CAAC;MAEtEnF,SAAS,CAAC,0BAA0BoB,YAAY,CAAC+D,OAAO,yBAAyB,CAAC;;MAElF;MACA9D,eAAe,CAAC,IAAI,CAAC;MACrBI,iBAAiB,CAAC,EAAE,CAAC;MACrBE,mBAAmB,CAAC,EAAE,CAAC;MACvBJ,cAAc,CAAC,EAAE,CAAC;;MAElB;MACAiB,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;MAC3EpD,OAAO,CAAC,sDAAsD,IAAIoD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC3H,CAAC,SAAS;MACRlD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiF,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACAjE,eAAe,CAAC,IAAI,CAAC;IACrBI,iBAAiB,CAAC,EAAE,CAAC;IACrBE,mBAAmB,CAAC,EAAE,CAAC;IACvBJ,cAAc,CAAC,EAAE,CAAC;IAClBV,oBAAoB,CAAC,KAAK,CAAC;;IAE3B;IACAb,SAAS,CAAC,sBAAsB,CAAC;EACnC,CAAC;;EAED;EACA,MAAMyE,eAAe,GAAIe,QAAQ,IAAK;IACpC,IAAIA,QAAQ,KAAK,cAAc,EAAE,OAAO,cAAc;;IAEtD;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvC,OAAOD,QAAQ,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAOF,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMG,uBAAuB,GAAGA,CAAA,kBAC9B9F,OAAA,CAAC1D,KAAK;IAACyJ,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACzBlG,OAAA,CAACzD,UAAU;MAAC4J,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbxG,OAAA,CAAC3D,GAAG;MAAC0J,EAAE,EAAE;QAAEU,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAE,CAAE;MAAAT,QAAA,gBACzDlG,OAAA,CAACxD,SAAS;QACRoK,KAAK,EAAC,SAAS;QACfnC,KAAK,EAAEhD,WAAY;QACnBoF,QAAQ,EAAGC,CAAC,IAAKpF,cAAc,CAACoF,CAAC,CAACtC,MAAM,CAACC,KAAK,CAAE;QAChDsB,EAAE,EAAE;UAAEgB,KAAK,EAAE;QAAQ,CAAE;QACvBC,UAAU,EAAE;UACVC,YAAY,eACVjH,OAAA,CAAChC,cAAc;YAACkJ,QAAQ,EAAC,KAAK;YAAAhB,QAAA,eAC5BlG,OAAA,CAACjC,UAAU;cACToJ,OAAO,EAAEjD,oBAAqB;cAC9BkD,QAAQ,EAAE7G,OAAO,IAAI,CAACkB,WAAW,CAAC0C,IAAI,CAAC,CAAE;cAAA+B,QAAA,eAEzClG,OAAA,CAACzB,UAAU;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAEpB;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFxG,OAAA,CAACvD,MAAM;QACL0J,OAAO,EAAC,UAAU;QAClBgB,OAAO,EAAEA,CAAA,KAAMnG,oBAAoB,CAAC,CAACD,iBAAiB,CAAE;QACxDqG,QAAQ,EAAE3G,WAAW,IAAIQ,IAAI,CAACgC,MAAM,KAAK,CAAE;QAC3C8C,EAAE,EAAE;UAAEgB,KAAK,EAAE;QAAQ,CAAE;QAAAb,QAAA,EAEtBnF,iBAAiB,GAAG,qBAAqB,GAAG;MAAmB;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL/F,WAAW,iBACVT,OAAA,CAAC3D,GAAG;MAAC0J,EAAE,EAAE;QAAEU,OAAO,EAAE,MAAM;QAAEY,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAApB,QAAA,eAC5DlG,OAAA,CAAC/C,gBAAgB;QAAAoJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,EAEAzF,iBAAiB,IAAIE,IAAI,CAACgC,MAAM,GAAG,CAAC,iBACnCjD,OAAA,CAACvC,cAAc;MAAC8J,SAAS,EAAEjL,KAAM;MAACyJ,EAAE,EAAE;QAAEuB,EAAE,EAAE,CAAC;QAAEE,SAAS,EAAE;MAAI,CAAE;MAAAtB,QAAA,eAC9DlG,OAAA,CAAC1C,KAAK;QAACmK,YAAY;QAACC,IAAI,EAAC,OAAO;QAAAxB,QAAA,gBAC9BlG,OAAA,CAACtC,SAAS;UAAAwI,QAAA,eACRlG,OAAA,CAACrC,QAAQ;YAAAuI,QAAA,gBACPlG,OAAA,CAACxC,SAAS;cAAA0I,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC9BxG,OAAA,CAACxC,SAAS;cAAA0I,QAAA,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChCxG,OAAA,CAACxC,SAAS;cAAA0I,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACjCxG,OAAA,CAACxC,SAAS;cAAA0I,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACnCxG,OAAA,CAACxC,SAAS;cAAA0I,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7BxG,OAAA,CAACxC,SAAS;cAAA0I,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZxG,OAAA,CAACzC,SAAS;UAAA2I,QAAA,EACPjF,IAAI,CAAC0G,GAAG,CAAEvE,IAAI,iBACbpD,OAAA,CAACrC,QAAQ;YAAoBiK,KAAK;YAAA1B,QAAA,gBAChClG,OAAA,CAACxC,SAAS;cAAA0I,QAAA,EAAE9C,IAAI,CAACkC;YAAO;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrCxG,OAAA,CAACxC,SAAS;cAAA0I,QAAA,EAAE9C,IAAI,CAACW;YAAS;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvCxG,OAAA,CAACxC,SAAS;cAAA0I,QAAA,EAAE9C,IAAI,CAACY;YAAO;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrCxG,OAAA,CAACxC,SAAS;cAAA0I,QAAA,EAAE9C,IAAI,CAACE;YAAe;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7CxG,OAAA,CAACxC,SAAS;cAAA0I,QAAA,EAAEtB,eAAe,CAACxB,IAAI,CAAC6B,SAAS,IAAI,EAAE;YAAC;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9DxG,OAAA,CAACxC,SAAS;cAAA0I,QAAA,eACRlG,OAAA,CAACvD,MAAM;gBACLiL,IAAI,EAAC,OAAO;gBACZP,OAAO,EAAEA,CAAA,KAAM9C,gBAAgB,CAACjB,IAAI,CAAE;gBACtC+C,OAAO,EAAC,WAAW;gBACnB0B,KAAK,EAAC,SAAS;gBAAA3B,QAAA,EAChB;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA,GAfCpD,IAAI,CAACkC,OAAO;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBjB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACjB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACR;;EAED;EACA,MAAMsB,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI,CAACvG,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEvB,OAAA,CAAC1D,KAAK;MAACyJ,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACzBlG,OAAA,CAAC3D,GAAG;QAAC0J,EAAE,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEY,cAAc,EAAE,eAAe;UAAEX,UAAU,EAAE,QAAQ;UAAET,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACzFlG,OAAA,CAACzD,UAAU;UAAC4J,OAAO,EAAC,IAAI;UAAAD,QAAA,EAAC;QAEzB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxG,OAAA,CAACvD,MAAM;UACL0J,OAAO,EAAC,UAAU;UAClBuB,IAAI,EAAC,OAAO;UACZP,OAAO,EAAEA,CAAA,KAAMzE,wBAAwB,CAAC,IAAI,CAAE;UAAAwD,QAAA,EAC/C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENxG,OAAA,CAACtD,IAAI;QAACqL,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA9B,QAAA,gBACzBlG,OAAA,CAACtD,IAAI;UAACuL,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlC,QAAA,gBAC9BlG,OAAA,CAACzD,UAAU;YAAC4J,OAAO,EAAC,WAAW;YAAAD,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrDxG,OAAA,CAACzD,UAAU;YAAC4J,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAE3E,YAAY,CAAC+D;UAAO;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACPxG,OAAA,CAACtD,IAAI;UAACuL,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlC,QAAA,gBAC9BlG,OAAA,CAACzD,UAAU;YAAC4J,OAAO,EAAC,WAAW;YAAAD,QAAA,EAAC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACvDxG,OAAA,CAACzD,UAAU;YAAC4J,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAE3E,YAAY,CAACwC;UAAS;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACPxG,OAAA,CAACtD,IAAI;UAACuL,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlC,QAAA,gBAC9BlG,OAAA,CAACzD,UAAU;YAAC4J,OAAO,EAAC,WAAW;YAAAD,QAAA,EAAC;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxDxG,OAAA,CAACzD,UAAU;YAAC4J,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAE3E,YAAY,CAACyC;UAAO;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACPxG,OAAA,CAACtD,IAAI;UAACuL,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlC,QAAA,gBAC9BlG,OAAA,CAACzD,UAAU;YAAC4J,OAAO,EAAC,WAAW;YAAAD,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC1DxG,OAAA,CAACzD,UAAU;YAAC4J,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAE3E,YAAY,CAAC+B;UAAe;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACPxG,OAAA,CAACtD,IAAI;UAACuL,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlC,QAAA,gBAC9BlG,OAAA,CAACzD,UAAU;YAAC4J,OAAO,EAAC,WAAW;YAAAD,QAAA,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC5DxG,OAAA,CAACzD,UAAU;YAAC4J,OAAO,EAAC,OAAO;YAAAD,QAAA,EACxB3E,YAAY,CAAC0D,SAAS,GAAGL,eAAe,CAACrD,YAAY,CAAC0D,SAAS,CAAC,GAAG;UAAgB;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPxG,OAAA,CAACtD,IAAI;UAACuL,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlC,QAAA,gBAC9BlG,OAAA,CAACzD,UAAU;YAAC4J,OAAO,EAAC,WAAW;YAAAD,QAAA,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnDxG,OAAA,CAACzD,UAAU;YAAC4J,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAE3E,YAAY,CAACgC;UAAmB;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;;EAED;EACA,MAAM6B,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAAC9G,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEvB,OAAA,CAAC1D,KAAK;MAACyJ,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACzBlG,OAAA,CAACzD,UAAU;QAAC4J,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbxG,OAAA,CAACnC,UAAU;QACT4G,KAAK,EAAE9C,cAAe;QACtBkF,QAAQ,EAAEvC,kBAAmB;QAAA4B,QAAA,gBAE7BlG,OAAA,CAAClC,gBAAgB;UACf2G,KAAK,EAAC,cAAc;UACpB6D,OAAO,eAAEtI,OAAA,CAACpC,KAAK;YAAAyI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBI,KAAK,EAAC;QAAsB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACFxG,OAAA,CAAClC,gBAAgB;UACf2G,KAAK,EAAC,eAAe;UACrB6D,OAAO,eAAEtI,OAAA,CAACpC,KAAK;YAAAyI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBI,KAAK,EAAC;QAAwB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACFxG,OAAA,CAAClC,gBAAgB;UACf2G,KAAK,EAAC,sBAAsB;UAC5B6D,OAAO,eAAEtI,OAAA,CAACpC,KAAK;YAAAyI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBI,KAAK,EAAC;QAAuB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,EAEZ7E,cAAc,KAAK,cAAc,IAAI4G,qBAAqB,CAAC,CAAC,EAE5D5G,cAAc,KAAK,eAAe,iBACjC3B,OAAA,CAAChD,KAAK;QAACwL,QAAQ,EAAC,MAAM;QAACzC,EAAE,EAAE;UAAEuB,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,EAAC;MAItC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,EAEA7E,cAAc,KAAK,sBAAsB,iBACxC3B,OAAA,CAAChD,KAAK;QAACwL,QAAQ,EAAC,SAAS;QAACzC,EAAE,EAAE;UAAEuB,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,gBACtClG,OAAA,CAACzD,UAAU;UAAC4J,OAAO,EAAC,OAAO;UAACsC,UAAU,EAAC,MAAM;UAAAvC,QAAA,EAAC;QAE9C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxG,OAAA,CAACzD,UAAU;UAAC4J,OAAO,EAAC,OAAO;UAAAD,QAAA,GAAC,mDACkB,eAAAlG,OAAA;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,yCAChB,eAAAxG,OAAA;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,kDACG,eAAAxG,OAAA;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,2EAEnD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACR,EAEA7E,cAAc,KAAK,mBAAmB,iBACrC3B,OAAA,CAAChD,KAAK;QAACwL,QAAQ,EAAC,MAAM;QAACzC,EAAE,EAAE;UAAEuB,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,eAEDxG,OAAA,CAAC3D,GAAG;QAAC0J,EAAE,EAAE;UAAEuB,EAAE,EAAE,CAAC;UAAEb,OAAO,EAAE,MAAM;UAAEY,cAAc,EAAE;QAAW,CAAE;QAAAnB,QAAA,eAC9DlG,OAAA,CAACvD,MAAM;UACL0J,OAAO,EAAC,WAAW;UACnB0B,KAAK,EAAElG,cAAc,KAAK,mBAAmB,GAAG,WAAW,GAAG,SAAU;UACxE+G,SAAS,EAAE/G,cAAc,KAAK,mBAAmB,gBAAG3B,OAAA,CAACrB,UAAU;YAAA0H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGxG,OAAA,CAACvB,QAAQ;YAAA4H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAClFW,OAAO,EAAE/B,UAAW;UACpBgC,QAAQ,EAAE7G,OAAO,IAAI,CAACoB,cAAc,IAAKA,cAAc,KAAK,cAAc,IAAI,CAACE,gBAAkB;UAAAqE,QAAA,EAEhGvE,cAAc,KAAK,mBAAmB,GAAG,oBAAoB,GAAG;QAAiB;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEZ,CAAC;;EAED;EACA,MAAM+B,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI5H,aAAa,EAAE;MACjB,oBACEX,OAAA,CAAC3D,GAAG;QAAC0J,EAAE,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEY,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,eAC5DlG,OAAA,CAAC/C,gBAAgB;UAAAoJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAEV;;IAEA;IACA,MAAMmC,cAAc,GAAGxH,MAAM,CAACgC,MAAM,CAACW,MAAM,IAAI;MAC7C,MAAM8E,WAAW,GAAG7G,gBAAgB,CAAC8G,WAAW,CAAC,CAAC;MAClD,OAAO,CAAC9G,gBAAgB,IACtB6C,eAAe,CAACd,MAAM,CAACmB,SAAS,CAAC,CAAC4D,WAAW,CAAC,CAAC,CAACjD,QAAQ,CAACgD,WAAW,CAAC,IACrEzD,MAAM,CAACrB,MAAM,CAACC,SAAS,IAAI,EAAE,CAAC,CAAC8E,WAAW,CAAC,CAAC,CAACjD,QAAQ,CAACgD,WAAW,CAAC,IAClEzD,MAAM,CAACrB,MAAM,CAACE,OAAO,IAAI,EAAE,CAAC,CAAC6E,WAAW,CAAC,CAAC,CAACjD,QAAQ,CAACgD,WAAW,CAAC;IACpE,CAAC,CAAC;;IAEF;IACA,MAAME,iBAAiB,GAAGvH,YAAY,GAClCoH,cAAc,CAACxF,MAAM,CAACW,MAAM,IAC1BqB,MAAM,CAACrB,MAAM,CAACC,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKgB,MAAM,CAAC5D,YAAY,CAACwC,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFgB,MAAM,CAACrB,MAAM,CAACE,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKgB,MAAM,CAAC5D,YAAY,CAACyC,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAChFL,MAAM,CAACG,YAAY,KAAK,aAAa,IAAIH,MAAM,CAACG,YAAY,KAAK,QAAQ,CAAC,CAAC,GAC9E,EAAE;IAEN,MAAM8E,oBAAoB,GAAGxH,YAAY,GACrCoH,cAAc,CAACxF,MAAM,CAACW,MAAM,IAC1B,EAAEqB,MAAM,CAACrB,MAAM,CAACC,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKgB,MAAM,CAAC5D,YAAY,CAACwC,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFgB,MAAM,CAACrB,MAAM,CAACE,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKgB,MAAM,CAAC5D,YAAY,CAACyC,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC,KACnFL,MAAM,CAACG,YAAY,KAAK,aAAa,IAAIH,MAAM,CAACG,YAAY,KAAK,QAAQ,CAAC,CAAC,GAC9E,EAAE;;IAEN;IACA6E,iBAAiB,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEjE,CAAC,KAAKA,CAAC,CAACkE,aAAa,GAAGD,CAAC,CAACC,aAAa,CAAC;IACnEH,oBAAoB,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEjE,CAAC,KAAKA,CAAC,CAACkE,aAAa,GAAGD,CAAC,CAACC,aAAa,CAAC;IAEtE,oBACElJ,OAAA,CAAC3D,GAAG;MAAC0J,EAAE,EAAE;QAAEuB,EAAE,EAAE;MAAE,CAAE;MAAApB,QAAA,gBAEjBlG,OAAA,CAAC3D,GAAG;QAAC0J,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,eACjBlG,OAAA,CAACxD,SAAS;UACR2M,SAAS;UACTvC,KAAK,EAAC,4CAA4C;UAClDnC,KAAK,EAAE1C,gBAAiB;UACxB8E,QAAQ,EAAGC,CAAC,IAAK9E,mBAAmB,CAAC8E,CAAC,CAACtC,MAAM,CAACC,KAAK,CAAE;UACrDuC,UAAU,EAAE;YACVoC,cAAc,eACZpJ,OAAA,CAAChC,cAAc;cAACkJ,QAAQ,EAAC,OAAO;cAAAhB,QAAA,eAC9BlG,OAAA,CAACzB,UAAU;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAEpB,CAAE;UACFT,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNxG,OAAA,CAAC3D,GAAG;QAAC0J,EAAE,EAAE;UAAEE,EAAE,EAAE,CAAC;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAT,QAAA,gBAChElG,OAAA,CAACxD,SAAS;UACRoK,KAAK,EAAC,iCAAiC;UACvCnC,KAAK,EAAExC,kBAAmB;UAC1B4E,QAAQ,EAAGC,CAAC,IAAK5E,qBAAqB,CAAC4E,CAAC,CAACtC,MAAM,CAACC,KAAK,CAAE;UACvD4E,IAAI,EAAC,QAAQ;UACbtD,EAAE,EAAE;YAAEgB,KAAK,EAAE;UAAQ;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACFxG,OAAA,CAACvD,MAAM;UACL0J,OAAO,EAAC,UAAU;UAClBgB,OAAO,EAAEtC,0BAA2B;UACpCuC,QAAQ,EAAE,CAACnF,kBAAkB,CAACkC,IAAI,CAAC,CAAE;UAAA+B,QAAA,EACtC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL3E,gBAAgB,iBACf7B,OAAA,CAAChD,KAAK;QAACwL,QAAQ,EAAC,SAAS;QAACzC,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,GAAC,sBACnB,eAAAlG,OAAA;UAAAkG,QAAA,EAAStB,eAAe,CAAC/C,gBAAgB;QAAC;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CACR,EAGAsC,iBAAiB,CAAC7F,MAAM,GAAG,CAAC,iBAC3BjD,OAAA,CAAC1D,KAAK;QAACyJ,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACnBlG,OAAA,CAAC3D,GAAG;UAAC0J,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEsD,OAAO,EAAE;UAAU,CAAE;UAAApD,QAAA,gBACpClG,OAAA,CAACzD,UAAU;YAAC4J,OAAO,EAAC,IAAI;YAACJ,EAAE,EAAE;cAAE8B,KAAK,EAAE,SAAS;cAAEY,UAAU,EAAE;YAAO,CAAE;YAAAvC,QAAA,GAAC,sBACjD,EAAC4C,iBAAiB,CAAC7F,MAAM,EAAC,GAChD;UAAA;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxG,OAAA,CAAC3D,GAAG;YAAC0J,EAAE,EAAE;cAAEU,OAAO,EAAE,MAAM;cAAEY,cAAc,EAAE,eAAe;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAApB,QAAA,gBACnElG,OAAA,CAAC3D,GAAG;cAAC0J,EAAE,EAAE;gBAAEwD,IAAI,EAAE;cAAE,CAAE;cAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;gBAAC4J,OAAO,EAAC,SAAS;gBAACJ,EAAE,EAAE;kBAAE0C,UAAU,EAAE,MAAM;kBAAEe,QAAQ,EAAE;gBAAU,CAAE;gBAAAtD,QAAA,EAAC;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,eACNxG,OAAA,CAAC3D,GAAG;cAAC0J,EAAE,EAAE;gBAAEwD,IAAI,EAAE;cAAE,CAAE;cAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;gBAAC4J,OAAO,EAAC,SAAS;gBAACJ,EAAE,EAAE;kBAAE0C,UAAU,EAAE,MAAM;kBAAEe,QAAQ,EAAE;gBAAU,CAAE;gBAAAtD,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClG,CAAC,eACNxG,OAAA,CAAC3D,GAAG;cAAC0J,EAAE,EAAE;gBAAEwD,IAAI,EAAE;cAAE,CAAE;cAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;gBAAC4J,OAAO,EAAC,SAAS;gBAACJ,EAAE,EAAE;kBAAE0C,UAAU,EAAE,MAAM;kBAAEe,QAAQ,EAAE;gBAAU,CAAE;gBAAAtD,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnG,CAAC,eACNxG,OAAA,CAAC3D,GAAG;cAAC0J,EAAE,EAAE;gBAAEwD,IAAI,EAAE;cAAE,CAAE;cAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;gBAAC4J,OAAO,EAAC,SAAS;gBAACJ,EAAE,EAAE;kBAAE0C,UAAU,EAAE,MAAM;kBAAEe,QAAQ,EAAE;gBAAU,CAAE;gBAAAtD,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC,eACNxG,OAAA,CAAC3D,GAAG;cAAC0J,EAAE,EAAE;gBAAEwD,IAAI,EAAE;cAAE,CAAE;cAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;gBAAC4J,OAAO,EAAC,SAAS;gBAACJ,EAAE,EAAE;kBAAE0C,UAAU,EAAE,MAAM;kBAAEe,QAAQ,EAAE;gBAAU,CAAE;gBAAAtD,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxG,OAAA,CAAC/B,IAAI;UAAC8H,EAAE,EAAE;YAAEyB,SAAS,EAAE,OAAO;YAAEiC,QAAQ,EAAE,MAAM;YAAEH,OAAO,EAAE;UAAmB,CAAE;UAAApD,QAAA,EAC7E4C,iBAAiB,CAACnB,GAAG,CAAE7D,MAAM,iBAC5B9D,OAAA,CAAC9B,QAAQ;YAEPwL,cAAc;YACdC,eAAe,eACb3J,OAAA,CAACjC,UAAU;cACT6L,IAAI,EAAC,KAAK;cACVlC,IAAI,EAAC,OAAO;cACZP,OAAO,EAAEA,CAAA,KAAMzC,kBAAkB,CAACZ,MAAM,CAACmB,SAAS,CAAE;cACpD4C,KAAK,EAAEhG,gBAAgB,KAAKiC,MAAM,CAACmB,SAAS,GAAG,SAAS,GAAG,SAAU;cAAAiB,QAAA,eAErElG,OAAA,CAACf,oBAAoB;gBAAAoH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CACb;YAAAN,QAAA,eAEDlG,OAAA,CAAC7B,YAAY;cACX0L,OAAO,eACL7J,OAAA,CAAC3D,GAAG;gBAAC0J,EAAE,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEY,cAAc,EAAE,eAAe;kBAAEX,UAAU,EAAE;gBAAS,CAAE;gBAAAR,QAAA,gBAClFlG,OAAA,CAAC3D,GAAG;kBAAC0J,EAAE,EAAE;oBAAEwD,IAAI,EAAE;kBAAE,CAAE;kBAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;oBAAC4J,OAAO,EAAC,OAAO;oBAACJ,EAAE,EAAE;sBAAE0C,UAAU,EAAE5G,gBAAgB,KAAKiC,MAAM,CAACmB,SAAS,GAAG,MAAM,GAAG;oBAAS,CAAE;oBAAAiB,QAAA,EACvGtB,eAAe,CAACd,MAAM,CAACmB,SAAS;kBAAC;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNxG,OAAA,CAAC3D,GAAG;kBAAC0J,EAAE,EAAE;oBAAEwD,IAAI,EAAE;kBAAE,CAAE;kBAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;oBAAC4J,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAEpC,MAAM,CAACC;kBAAS;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACNxG,OAAA,CAAC3D,GAAG;kBAAC0J,EAAE,EAAE;oBAAEwD,IAAI,EAAE;kBAAE,CAAE;kBAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;oBAAC4J,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAEpC,MAAM,CAACE;kBAAO;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNxG,OAAA,CAAC3D,GAAG;kBAAC0J,EAAE,EAAE;oBAAEwD,IAAI,EAAE;kBAAE,CAAE;kBAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;oBAAC4J,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAEpC,MAAM,CAACoF,aAAa,EAAC,GAAC;kBAAA;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACNxG,OAAA,CAAC3D,GAAG;kBAAC0J,EAAE,EAAE;oBAAEwD,IAAI,EAAE;kBAAE,CAAE;kBAAArD,QAAA,eACnBlG,OAAA,CAAC3B,IAAI;oBACHuI,KAAK,EAAE9C,MAAM,CAACG,YAAa;oBAC3ByD,IAAI,EAAC,OAAO;oBACZG,KAAK,EAAE/D,MAAM,CAACG,YAAY,KAAK,aAAa,GAAG,SAAS,GAAG;kBAAU;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;cACDT,EAAE,EAAE;gBACFuD,OAAO,EAAEzH,gBAAgB,KAAKiC,MAAM,CAACmB,SAAS,GAAG,SAAS,GAAG,aAAa;gBAC1E6E,YAAY,EAAE,CAAC;gBACfC,EAAE,EAAE;cACN;YAAE;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GA5CG1C,MAAM,CAACmB,SAAS;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6Cb,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACR,EAGAuC,oBAAoB,CAAC9F,MAAM,GAAG,CAAC,iBAC9BjD,OAAA,CAAC1D,KAAK;QAACyJ,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACnBlG,OAAA,CAAC3D,GAAG;UAAC0J,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEsD,OAAO,EAAE;UAAU,CAAE;UAAApD,QAAA,gBACpClG,OAAA,CAACzD,UAAU;YAAC4J,OAAO,EAAC,IAAI;YAACJ,EAAE,EAAE;cAAE8B,KAAK,EAAE,SAAS;cAAEY,UAAU,EAAE;YAAO,CAAE;YAAAvC,QAAA,GAAC,0BAC7C,EAAC6C,oBAAoB,CAAC9F,MAAM,EAAC,GACvD;UAAA;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxG,OAAA,CAACzD,UAAU;YAAC4J,OAAO,EAAC,SAAS;YAAC0B,KAAK,EAAC,gBAAgB;YAAA3B,QAAA,EAAC;UAErD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxG,OAAA,CAAC3D,GAAG;YAAC0J,EAAE,EAAE;cAAEU,OAAO,EAAE,MAAM;cAAEY,cAAc,EAAE,eAAe;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAApB,QAAA,gBACnElG,OAAA,CAAC3D,GAAG;cAAC0J,EAAE,EAAE;gBAAEwD,IAAI,EAAE;cAAE,CAAE;cAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;gBAAC4J,OAAO,EAAC,SAAS;gBAACJ,EAAE,EAAE;kBAAE0C,UAAU,EAAE,MAAM;kBAAEe,QAAQ,EAAE;gBAAU,CAAE;gBAAAtD,QAAA,EAAC;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,eACNxG,OAAA,CAAC3D,GAAG;cAAC0J,EAAE,EAAE;gBAAEwD,IAAI,EAAE;cAAE,CAAE;cAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;gBAAC4J,OAAO,EAAC,SAAS;gBAACJ,EAAE,EAAE;kBAAE0C,UAAU,EAAE,MAAM;kBAAEe,QAAQ,EAAE;gBAAU,CAAE;gBAAAtD,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClG,CAAC,eACNxG,OAAA,CAAC3D,GAAG;cAAC0J,EAAE,EAAE;gBAAEwD,IAAI,EAAE;cAAE,CAAE;cAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;gBAAC4J,OAAO,EAAC,SAAS;gBAACJ,EAAE,EAAE;kBAAE0C,UAAU,EAAE,MAAM;kBAAEe,QAAQ,EAAE;gBAAU,CAAE;gBAAAtD,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnG,CAAC,eACNxG,OAAA,CAAC3D,GAAG;cAAC0J,EAAE,EAAE;gBAAEwD,IAAI,EAAE;cAAE,CAAE;cAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;gBAAC4J,OAAO,EAAC,SAAS;gBAACJ,EAAE,EAAE;kBAAE0C,UAAU,EAAE,MAAM;kBAAEe,QAAQ,EAAE;gBAAU,CAAE;gBAAAtD,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC,eACNxG,OAAA,CAAC3D,GAAG;cAAC0J,EAAE,EAAE;gBAAEwD,IAAI,EAAE;cAAE,CAAE;cAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;gBAAC4J,OAAO,EAAC,SAAS;gBAACJ,EAAE,EAAE;kBAAE0C,UAAU,EAAE,MAAM;kBAAEe,QAAQ,EAAE;gBAAU,CAAE;gBAAAtD,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxG,OAAA,CAAC/B,IAAI;UAAC8H,EAAE,EAAE;YAAEyB,SAAS,EAAE,OAAO;YAAEiC,QAAQ,EAAE,MAAM;YAAEH,OAAO,EAAE;UAAmB,CAAE;UAAApD,QAAA,EAC7E6C,oBAAoB,CAACpB,GAAG,CAAE7D,MAAM,iBAC/B9D,OAAA,CAAC9B,QAAQ;YAEPwL,cAAc;YACdC,eAAe,eACb3J,OAAA,CAACjC,UAAU;cACT6L,IAAI,EAAC,KAAK;cACVlC,IAAI,EAAC,OAAO;cACZP,OAAO,EAAEA,CAAA,KAAM;gBACb/G,OAAO,CAAC,UAAUwE,eAAe,CAACd,MAAM,CAACmB,SAAS,CAAC,gCAAgCnB,MAAM,CAACC,SAAS,iBAAiBD,MAAM,CAACE,OAAO,EAAE,CAAC;cACvI,CAAE;cACF6D,KAAK,EAAC,SAAS;cAAA3B,QAAA,eAEflG,OAAA,CAACnB,WAAW;gBAAAwH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACb;YAAAN,QAAA,eAEDlG,OAAA,CAAC7B,YAAY;cACX0L,OAAO,eACL7J,OAAA,CAAC3D,GAAG;gBAAC0J,EAAE,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEY,cAAc,EAAE,eAAe;kBAAEX,UAAU,EAAE;gBAAS,CAAE;gBAAAR,QAAA,gBAClFlG,OAAA,CAAC3D,GAAG;kBAAC0J,EAAE,EAAE;oBAAEwD,IAAI,EAAE;kBAAE,CAAE;kBAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;oBAAC4J,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAEtB,eAAe,CAACd,MAAM,CAACmB,SAAS;kBAAC;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC,eACNxG,OAAA,CAAC3D,GAAG;kBAAC0J,EAAE,EAAE;oBAAEwD,IAAI,EAAE;kBAAE,CAAE;kBAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;oBAAC4J,OAAO,EAAC,OAAO;oBAAC0B,KAAK,EAAE/D,MAAM,CAACC,SAAS,MAAKxC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEwC,SAAS,IAAG,OAAO,GAAG,cAAe;oBAAAmC,QAAA,EACxGpC,MAAM,CAACC;kBAAS;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNxG,OAAA,CAAC3D,GAAG;kBAAC0J,EAAE,EAAE;oBAAEwD,IAAI,EAAE;kBAAE,CAAE;kBAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;oBAAC4J,OAAO,EAAC,OAAO;oBAAC0B,KAAK,EAAE1C,MAAM,CAACrB,MAAM,CAACE,OAAO,CAAC,KAAKmB,MAAM,CAAC5D,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEyC,OAAO,CAAC,GAAG,OAAO,GAAG,cAAe;oBAAAkC,QAAA,EACpHpC,MAAM,CAACE;kBAAO;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNxG,OAAA,CAAC3D,GAAG;kBAAC0J,EAAE,EAAE;oBAAEwD,IAAI,EAAE;kBAAE,CAAE;kBAAArD,QAAA,eACnBlG,OAAA,CAACzD,UAAU;oBAAC4J,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAEpC,MAAM,CAACoF,aAAa,EAAC,GAAC;kBAAA;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACNxG,OAAA,CAAC3D,GAAG;kBAAC0J,EAAE,EAAE;oBAAEwD,IAAI,EAAE;kBAAE,CAAE;kBAAArD,QAAA,eACnBlG,OAAA,CAAC3B,IAAI;oBACHuI,KAAK,EAAE9C,MAAM,CAACG,YAAa;oBAC3ByD,IAAI,EAAC,OAAO;oBACZG,KAAK,EAAE/D,MAAM,CAACG,YAAY,KAAK,aAAa,GAAG,SAAS,GAAG;kBAAU;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC,GA3CG1C,MAAM,CAACmB,SAAS;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4Cb,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACR,EAGAsC,iBAAiB,CAAC7F,MAAM,KAAK,CAAC,IAAI8F,oBAAoB,CAAC9F,MAAM,KAAK,CAAC,iBAClEjD,OAAA,CAAChD,KAAK;QAACwL,QAAQ,EAAC,MAAM;QAACzC,EAAE,EAAE;UAAEuB,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,EAClCnE,gBAAgB,GAAG,8DAA8D,GAAG;MAAiC;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjH,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,oBACExG,OAAA,CAAC3D,GAAG;IAAA6J,QAAA,GAEDJ,uBAAuB,CAAC,CAAC,EAGzBgC,yBAAyB,CAAC,CAAC,EAG3BO,qBAAqB,CAAC,CAAC,eAGxBrI,OAAA,CAAC9C,MAAM;MACL8M,IAAI,EAAEvH,qBAAsB;MAC5BwH,OAAO,EAAEA,CAAA,KAAMvH,wBAAwB,CAAC,KAAK,CAAE;MAC/CwH,QAAQ,EAAC,IAAI;MACbf,SAAS;MAAAjD,QAAA,gBAETlG,OAAA,CAAC7C,WAAW;QAAA+I,QAAA,EAAC;MAA0B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACrDxG,OAAA,CAAC5C,aAAa;QAAA8I,QAAA,EACX3E,YAAY,iBAAIvB,OAAA,CAACX,eAAe;UAAC+D,IAAI,EAAE7B;QAAa;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAChBxG,OAAA,CAAC3C,aAAa;QAAA6I,QAAA,eACZlG,OAAA,CAACvD,MAAM;UAAC0K,OAAO,EAAEA,CAAA,KAAMzE,wBAAwB,CAAC,KAAK,CAAE;UAAAwD,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTxG,OAAA,CAAC9C,MAAM;MACL8M,IAAI,EAAE7H,iBAAkB;MACxB8H,OAAO,EAAEA,CAAA,KAAM7H,oBAAoB,CAAC,KAAK,CAAE;MAAA8D,QAAA,gBAE3ClG,OAAA,CAAC7C,WAAW;QAAA+I,QAAA,EAAC;MAAmB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC9CxG,OAAA,CAAC5C,aAAa;QAAA8I,QAAA,eACZlG,OAAA,CAACzD,UAAU;UAAA2J,QAAA,EAAE7D;QAAoB;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAChBxG,OAAA,CAAC3C,aAAa;QAAA6I,QAAA,gBACZlG,OAAA,CAACvD,MAAM;UACL0K,OAAO,EAAEA,CAAA,KAAM/E,oBAAoB,CAAC,KAAK,CAAE;UAC3CyF,KAAK,EAAC,SAAS;UAAA3B,QAAA,EAChB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxG,OAAA,CAACvD,MAAM;UACL0K,OAAO,EAAEA,CAAA,KAAM;YACb/E,oBAAoB,CAAC,KAAK,CAAC;YAC3B,IAAIG,mBAAmB,EAAEA,mBAAmB,CAAC,CAAC;UAChD,CAAE;UACFsF,KAAK,EAAC,SAAS;UACf1B,OAAO,EAAC,WAAW;UACnBgE,SAAS;UAAAjE,QAAA,EACV;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACnG,EAAA,CA7yBIJ,kBAAkB;EAAA,QACLf,WAAW;AAAA;AAAAkL,EAAA,GADxBnK,kBAAkB;AA+yBxB,eAAeA,kBAAkB;AAAC,IAAAmK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}