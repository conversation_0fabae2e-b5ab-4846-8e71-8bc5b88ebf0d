{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\TopNavbar.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { AppBar, Toolbar, Box, Button, Menu, MenuItem, Typography, IconButton, Divider, Avatar, Tooltip } from '@mui/material';\nimport { Home as HomeIcon, AdminPanelSettings as AdminIcon, Construction as ConstructionIcon, Cable as CableIcon, Description as ReportIcon, Logout as LogoutIcon, KeyboardArrowDown as ArrowDownIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { useGlobalContext } from '../context/GlobalContext';\nimport SelectedCantiereDisplay from './common/SelectedCantiereDisplay';\nimport './TopNavbar.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TopNavbar = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    logout,\n    isImpersonating,\n    impersonatedUser\n  } = useAuth();\n  const {\n    setOpenEliminaCavoDialog\n  } = useGlobalContext();\n\n  // Stati per i menu a tendina\n  const [homeAnchorEl, setHomeAnchorEl] = useState(null);\n  const [adminAnchorEl, setAdminAnchorEl] = useState(null);\n  const [cantieriAnchorEl, setCantieriAnchorEl] = useState(null);\n  const [caviAnchorEl, setCaviAnchorEl] = useState(null);\n  const [posaAnchorEl, setPosaAnchorEl] = useState(null);\n  const [parcoAnchorEl, setParcoAnchorEl] = useState(null);\n  const [excelAnchorEl, setExcelAnchorEl] = useState(null);\n  const [reportAnchorEl, setReportAnchorEl] = useState(null);\n  const [certificazioneAnchorEl, setCertificazioneAnchorEl] = useState(null);\n  const [comandeAnchorEl, setComandeAnchorEl] = useState(null);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Funzioni per aprire/chiudere i menu\n  const handleMenuOpen = (event, setAnchorEl) => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = setAnchorEl => {\n    setAnchorEl(null);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = path => {\n    // Gestione speciale per il percorso Home\n    if (path === '/dashboard') {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n        // Reindirizza direttamente alla pagina di visualizzazione cavi\n        navigate('/dashboard/cavi/visualizza');\n      }\n      // Fallback per altri tipi di utenti\n      else {\n        navigate(path);\n      }\n    } else {\n      navigate(path);\n    }\n\n    // Chiudi tutti i menu\n    handleMenuClose(setHomeAnchorEl);\n    handleMenuClose(setAdminAnchorEl);\n    handleMenuClose(setCantieriAnchorEl);\n    handleMenuClose(setCaviAnchorEl);\n    handleMenuClose(setPosaAnchorEl);\n    handleMenuClose(setParcoAnchorEl);\n    handleMenuClose(setExcelAnchorEl);\n    handleMenuClose(setReportAnchorEl);\n    handleMenuClose(setCertificazioneAnchorEl);\n    handleMenuClose(setComandeAnchorEl);\n  };\n  const handleLogout = () => {\n    logout();\n  };\n\n  // Verifica se un percorso è attivo\n  const isActive = path => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = path => {\n    return location.pathname.startsWith(path);\n  };\n  return /*#__PURE__*/_jsxDEV(AppBar, {\n    position: \"static\",\n    color: \"default\",\n    elevation: 2,\n    sx: {\n      zIndex: 1100,\n      width: '100%',\n      overflowX: 'hidden'\n    },\n    className: \"excel-style-menu\",\n    children: /*#__PURE__*/_jsxDEV(Toolbar, {\n      sx: {\n        overflowX: 'hidden',\n        height: '60px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        color: \"inherit\",\n        onClick: () => navigateTo('/dashboard'),\n        startIcon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 22\n        }, this),\n        sx: {\n          mr: 1\n        },\n        className: isActive('/dashboard') ? 'active-button' : '',\n        children: isImpersonating ? \"Torna al Menu Admin\" : (user === null || user === void 0 ? void 0 : user.role) === 'owner' ? \"Pannello Admin\" : (user === null || user === void 0 ? void 0 : user.role) === 'user' ? \"Lista Cantieri\" : (user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user' ? \"Gestione Cavi\" : \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        orientation: \"vertical\",\n        flexItem: true,\n        sx: {\n          mx: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), ((user === null || user === void 0 ? void 0 : user.role) !== 'owner' || (user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [isImpersonating && /*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          onClick: () => navigateTo('/dashboard/cantieri'),\n          sx: {\n            mr: 1\n          },\n          className: isActive('/dashboard/cantieri') ? 'active-button' : '',\n          children: isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"Lista Cantieri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 15\n        }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [(user === null || user === void 0 ? void 0 : user.role) !== 'cantieri_user' && /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            onClick: () => navigateTo('/dashboard/cavi/visualizza'),\n            sx: {\n              mr: 1\n            },\n            className: isActive('/dashboard/cavi/visualizza') ? 'active-button' : '',\n            children: \"Visualizza Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"posa-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setPosaAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/posa') ? 'active-button' : '',\n            children: \"Posa e Collegamenti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"parco-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: () => navigateTo('/dashboard/cavi/parco/visualizza'),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/parco') ? 'active-button' : '',\n            children: \"Parco Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"excel-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setExcelAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/excel') ? 'active-button' : '',\n            children: \"Gestione Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"report-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setReportAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/report') ? 'active-button' : '',\n            children: \"Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"certificazione-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setCertificazioneAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/certificazione') ? 'active-button' : '',\n            children: \"Certificazione Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"comande-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setComandeAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/comande') ? 'active-button' : '',\n            children: \"Gestione Comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"posa-menu\",\n            anchorEl: posaAnchorEl,\n            keepMounted: true,\n            open: Boolean(posaAnchorEl),\n            onClose: () => handleMenuClose(setPosaAnchorEl),\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'center'\n            },\n            className: \"excel-style-submenu\",\n            elevation: 3,\n            sx: {\n              mt: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/inserisci-metri'),\n              children: \"Inserisci metri posati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/modifica-cavo'),\n              children: \"Modifica cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/aggiungi-cavo'),\n              children: \"Aggiungi nuovo cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => {\n                // Apre il dialogo di eliminazione cavi invece di navigare alla pagina\n                setOpenEliminaCavoDialog(true);\n                // Chiude il menu\n                handleMenuClose(setPosaAnchorEl);\n                // Naviga alla pagina visualizza cavi\n                navigateTo('/dashboard/cavi/visualizza');\n              },\n              children: \"Elimina cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/modifica-bobina'),\n              children: \"Modifica bobina cavo posato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/collegamenti'),\n              children: \"Gestisci collegamenti cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"parco-menu\",\n            anchorEl: parcoAnchorEl,\n            keepMounted: true,\n            open: Boolean(parcoAnchorEl),\n            onClose: () => handleMenuClose(setParcoAnchorEl),\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'center'\n            },\n            className: \"excel-style-submenu\",\n            elevation: 3,\n            sx: {\n              mt: 0.5\n            },\n            children: /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/storico'),\n              children: \"Visualizza Storico Utilizzo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"excel-menu\",\n            anchorEl: excelAnchorEl,\n            keepMounted: true,\n            open: Boolean(excelAnchorEl),\n            onClose: () => handleMenuClose(setExcelAnchorEl),\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'center'\n            },\n            className: \"excel-style-submenu\",\n            elevation: 3,\n            sx: {\n              mt: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/excel/importa-cavi'),\n              children: \"Importa cavi da Excel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/excel/importa-bobine'),\n              children: \"Importa parco bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/excel/esporta-cavi'),\n              children: \"Esporta cavi in Excel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/excel/esporta-bobine'),\n              children: \"Esporta parco bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"report-menu\",\n            anchorEl: reportAnchorEl,\n            keepMounted: true,\n            open: Boolean(reportAnchorEl),\n            onClose: () => handleMenuClose(setReportAnchorEl),\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'center'\n            },\n            className: \"excel-style-submenu\",\n            elevation: 3,\n            sx: {\n              mt: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/avanzamento'),\n              children: \"Report Avanzamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/boq'),\n              children: \"Bill of Quantities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/utilizzo-bobine'),\n              children: \"Report Utilizzo Bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/statistiche'),\n              children: \"Statistiche Cantiere\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"certificazione-menu\",\n            anchorEl: certificazioneAnchorEl,\n            keepMounted: true,\n            open: Boolean(certificazioneAnchorEl),\n            onClose: () => handleMenuClose(setCertificazioneAnchorEl),\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'center'\n            },\n            className: \"excel-style-submenu\",\n            elevation: 3,\n            sx: {\n              mt: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/visualizza'),\n              children: \"Visualizza certificazioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/filtra'),\n              children: \"Filtra per cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/crea'),\n              children: \"Crea certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/modifica'),\n              children: \"Modifica certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/elimina'),\n              children: \"Elimina certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/strumenti'),\n              children: \"Gestione strumenti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"comande-menu\",\n            anchorEl: comandeAnchorEl,\n            keepMounted: true,\n            open: Boolean(comandeAnchorEl),\n            onClose: () => handleMenuClose(setComandeAnchorEl),\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'center'\n            },\n            className: \"excel-style-submenu\",\n            elevation: 3,\n            sx: {\n              mt: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/visualizza'),\n              children: \"Visualizza comande\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/crea'),\n              children: \"Crea nuova comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/modifica'),\n              children: \"Modifica comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/elimina'),\n              children: \"Elimina comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/stampa'),\n              children: \"Stampa comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/assegna'),\n              children: \"Assegna comanda a cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flexGrow: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          height: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(SelectedCantiereDisplay, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this), isImpersonating && impersonatedUser && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          sx: {\n            mr: 2.5,\n            fontSize: '1rem'\n          },\n          children: [\"Accesso come: \", /*#__PURE__*/_jsxDEV(\"b\", {\n            children: impersonatedUser.username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mr: 2.5,\n            fontWeight: 500,\n            fontSize: '1rem'\n          },\n          children: (user === null || user === void 0 ? void 0 : user.username) || ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Logout\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            color: \"inherit\",\n            onClick: handleLogout,\n            edge: \"end\",\n            sx: {\n              '&:hover': {\n                backgroundColor: '#e9ecef'\n              },\n              padding: '10px'\n            },\n            children: /*#__PURE__*/_jsxDEV(LogoutIcon, {\n              fontSize: \"medium\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 5\n  }, this);\n};\n_s(TopNavbar, \"uoTK4eUW7spPkA7PvXgAQ5VBA00=\", false, function () {\n  return [useNavigate, useLocation, useAuth, useGlobalContext];\n});\n_c = TopNavbar;\nexport default TopNavbar;\nvar _c;\n$RefreshReg$(_c, \"TopNavbar\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "AppBar", "<PERSON><PERSON><PERSON>", "Box", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Typography", "IconButton", "Divider", "Avatar", "<PERSON><PERSON><PERSON>", "Home", "HomeIcon", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "Logout", "LogoutIcon", "KeyboardArrowDown", "ArrowDownIcon", "useAuth", "useGlobalContext", "SelectedCantiereDisplay", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TopNavbar", "_s", "navigate", "location", "user", "logout", "isImpersonating", "impersonated<PERSON><PERSON>", "setOpenEliminaCavoDialog", "homeAnchorEl", "setHomeAnchorEl", "adminAnchorEl", "setAdminAnchorEl", "cantieriAnchorEl", "setCantieriAnchorEl", "caviAnchorEl", "setCaviAnchorEl", "posaAnchorEl", "setPosaAnchorEl", "parcoAnchorEl", "setParcoAnchorEl", "excelAnchorEl", "setExcelAnchorEl", "reportAnchorEl", "setReportAnchorEl", "certificazioneAnchorEl", "setCertificazioneAnchorEl", "comandeAnchorEl", "setComandeAnchorEl", "selectedCantiereId", "localStorage", "getItem", "selectedCantiereName", "handleMenuOpen", "event", "setAnchorEl", "currentTarget", "handleMenuClose", "navigateTo", "path", "role", "handleLogout", "isActive", "pathname", "isPartOfActive", "startsWith", "position", "color", "elevation", "sx", "zIndex", "width", "overflowX", "className", "children", "height", "onClick", "startIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mr", "orientation", "flexItem", "mx", "username", "e", "endIcon", "id", "anchorEl", "keepMounted", "open", "Boolean", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "mt", "flexGrow", "display", "alignItems", "variant", "fontSize", "fontWeight", "title", "edge", "backgroundColor", "padding", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/TopNavbar.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  AppBar,\n  Toolbar,\n  Box,\n  Button,\n  Menu,\n  MenuItem,\n  Typography,\n  IconButton,\n  Divider,\n  Avatar,\n  Tooltip\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon,\n  Logout as LogoutIcon,\n  KeyboardArrowDown as ArrowDownIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { useGlobalContext } from '../context/GlobalContext';\nimport SelectedCantiereDisplay from './common/SelectedCantiereDisplay';\nimport './TopNavbar.css';\n\nconst TopNavbar = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, logout, isImpersonating, impersonatedUser } = useAuth();\n  const { setOpenEliminaCavoDialog } = useGlobalContext();\n\n  // Stati per i menu a tendina\n  const [homeAnchorEl, setHomeAnchorEl] = useState(null);\n  const [adminAnchorEl, setAdminAnchorEl] = useState(null);\n  const [cantieriAnchorEl, setCantieriAnchorEl] = useState(null);\n  const [caviAnchorEl, setCaviAnchorEl] = useState(null);\n  const [posaAnchorEl, setPosaAnchorEl] = useState(null);\n  const [parcoAnchorEl, setParcoAnchorEl] = useState(null);\n  const [excelAnchorEl, setExcelAnchorEl] = useState(null);\n  const [reportAnchorEl, setReportAnchorEl] = useState(null);\n  const [certificazioneAnchorEl, setCertificazioneAnchorEl] = useState(null);\n  const [comandeAnchorEl, setComandeAnchorEl] = useState(null);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Funzioni per aprire/chiudere i menu\n  const handleMenuOpen = (event, setAnchorEl) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = (setAnchorEl) => {\n    setAnchorEl(null);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    // Gestione speciale per il percorso Home\n    if (path === '/dashboard') {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if (user?.role === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if (user?.role === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if (user?.role === 'cantieri_user') {\n        // Reindirizza direttamente alla pagina di visualizzazione cavi\n        navigate('/dashboard/cavi/visualizza');\n      }\n      // Fallback per altri tipi di utenti\n      else {\n        navigate(path);\n      }\n    } else {\n      navigate(path);\n    }\n\n    // Chiudi tutti i menu\n    handleMenuClose(setHomeAnchorEl);\n    handleMenuClose(setAdminAnchorEl);\n    handleMenuClose(setCantieriAnchorEl);\n    handleMenuClose(setCaviAnchorEl);\n    handleMenuClose(setPosaAnchorEl);\n    handleMenuClose(setParcoAnchorEl);\n    handleMenuClose(setExcelAnchorEl);\n    handleMenuClose(setReportAnchorEl);\n    handleMenuClose(setCertificazioneAnchorEl);\n    handleMenuClose(setComandeAnchorEl);\n  };\n\n  const handleLogout = () => {\n    logout();\n  };\n\n  // Verifica se un percorso è attivo\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = (path) => {\n    return location.pathname.startsWith(path);\n  };\n\n  return (\n    <AppBar position=\"static\" color=\"default\" elevation={2} sx={{ zIndex: 1100, width: '100%', overflowX: 'hidden' }} className=\"excel-style-menu\">\n      <Toolbar sx={{ overflowX: 'hidden', height: '60px' }}>\n        {/* Logo/Home - Testo personalizzato in base al tipo di utente */}\n        <Button\n          color=\"inherit\"\n          onClick={() => navigateTo('/dashboard')}\n          startIcon={<HomeIcon />}\n          sx={{ mr: 1 }}\n          className={isActive('/dashboard') ? 'active-button' : ''}\n        >\n          {isImpersonating ? \"Torna al Menu Admin\" :\n           user?.role === 'owner' ? \"Pannello Admin\" :\n           user?.role === 'user' ? \"Lista Cantieri\" :\n           user?.role === 'cantieri_user' ? \"Gestione Cavi\" : \"Home\"}\n        </Button>\n        <Divider orientation=\"vertical\" flexItem sx={{ mx: 0.5 }} />\n\n        {/* Il menu Amministratore è stato rimosso perché ridondante con il pulsante Home per gli amministratori */}\n\n        {/* Menu per utenti standard e cantieri */}\n        {(user?.role !== 'owner' || (user?.role === 'owner' && isImpersonating && impersonatedUser)) && (\n          <>\n            {/* Pulsante Lista Cantieri solo per utenti che impersonano */}\n            {isImpersonating && (\n              <Button\n                color=\"inherit\"\n                onClick={() => navigateTo('/dashboard/cantieri')}\n                sx={{ mr: 1 }}\n                className={isActive('/dashboard/cantieri') ? 'active-button' : ''}\n              >\n                {isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"Lista Cantieri\"}\n              </Button>\n            )}\n\n            {/* Il cantiere selezionato è stato spostato nella parte destra della barra di navigazione */}\n\n            {/* Menu di gestione cavi - visibile solo se un cantiere è selezionato */}\n            {selectedCantiereId && (\n              <>\n                {/* Visualizza Cavi - nascosto per utenti cantiere perché ridondante con il tasto Home */}\n                {user?.role !== 'cantieri_user' && (\n                  <Button\n                    color=\"inherit\"\n                    onClick={() => navigateTo('/dashboard/cavi/visualizza')}\n                    sx={{ mr: 1 }}\n                    className={isActive('/dashboard/cavi/visualizza') ? 'active-button' : ''}\n                  >\n                    Visualizza Cavi\n                  </Button>\n                )}\n\n                {/* Posa e Collegamenti */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"posa-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setPosaAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/posa') ? 'active-button' : ''}\n                >\n                  Posa e Collegamenti\n                </Button>\n\n                {/* Parco Cavi */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"parco-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={() => navigateTo('/dashboard/cavi/parco/visualizza')}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/parco') ? 'active-button' : ''}\n                >\n                  Parco Cavi\n                </Button>\n\n                {/* Gestione Excel */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"excel-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setExcelAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/excel') ? 'active-button' : ''}\n                >\n                  Gestione Excel\n                </Button>\n\n                {/* Report */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"report-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setReportAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/report') ? 'active-button' : ''}\n                >\n                  Report\n                </Button>\n\n                {/* Certificazione Cavi */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"certificazione-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setCertificazioneAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/certificazione') ? 'active-button' : ''}\n                >\n                  Certificazione Cavi\n                </Button>\n\n                {/* Gestione Comande */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"comande-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setComandeAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/comande') ? 'active-button' : ''}\n                >\n                  Gestione Comande\n                </Button>\n\n                {/* Sottomenu Posa e Collegamenti */}\n                <Menu\n                  id=\"posa-menu\"\n                  anchorEl={posaAnchorEl}\n                  keepMounted\n                  open={Boolean(posaAnchorEl)}\n                  onClose={() => handleMenuClose(setPosaAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/inserisci-metri')}>Inserisci metri posati</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/modifica-cavo')}>Modifica cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/aggiungi-cavo')}>Aggiungi nuovo cavo</MenuItem>\n                  <MenuItem onClick={() => {\n                    // Apre il dialogo di eliminazione cavi invece di navigare alla pagina\n                    setOpenEliminaCavoDialog(true);\n                    // Chiude il menu\n                    handleMenuClose(setPosaAnchorEl);\n                    // Naviga alla pagina visualizza cavi\n                    navigateTo('/dashboard/cavi/visualizza');\n                  }}>Elimina cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/modifica-bobina')}>Modifica bobina cavo posato</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/collegamenti')}>Gestisci collegamenti cavo</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Parco Cavi */}\n                <Menu\n                  id=\"parco-menu\"\n                  anchorEl={parcoAnchorEl}\n                  keepMounted\n                  open={Boolean(parcoAnchorEl)}\n                  onClose={() => handleMenuClose(setParcoAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/storico')}>Visualizza Storico Utilizzo</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Gestione Excel */}\n                <Menu\n                  id=\"excel-menu\"\n                  anchorEl={excelAnchorEl}\n                  keepMounted\n                  open={Boolean(excelAnchorEl)}\n                  onClose={() => handleMenuClose(setExcelAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/excel/importa-cavi')}>Importa cavi da Excel</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/excel/importa-bobine')}>Importa parco bobine</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/excel/esporta-cavi')}>Esporta cavi in Excel</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/excel/esporta-bobine')}>Esporta parco bobine</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Report */}\n                <Menu\n                  id=\"report-menu\"\n                  anchorEl={reportAnchorEl}\n                  keepMounted\n                  open={Boolean(reportAnchorEl)}\n                  onClose={() => handleMenuClose(setReportAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/avanzamento')}>Report Avanzamento</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/boq')}>Bill of Quantities</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/utilizzo-bobine')}>Report Utilizzo Bobine</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/statistiche')}>Statistiche Cantiere</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Certificazione Cavi */}\n                <Menu\n                  id=\"certificazione-menu\"\n                  anchorEl={certificazioneAnchorEl}\n                  keepMounted\n                  open={Boolean(certificazioneAnchorEl)}\n                  onClose={() => handleMenuClose(setCertificazioneAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/visualizza')}>Visualizza certificazioni</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/filtra')}>Filtra per cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/crea')}>Crea certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/modifica')}>Modifica certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/elimina')}>Elimina certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/strumenti')}>Gestione strumenti</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Gestione Comande */}\n                <Menu\n                  id=\"comande-menu\"\n                  anchorEl={comandeAnchorEl}\n                  keepMounted\n                  open={Boolean(comandeAnchorEl)}\n                  onClose={() => handleMenuClose(setComandeAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/visualizza')}>Visualizza comande</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/crea')}>Crea nuova comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/modifica')}>Modifica comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/elimina')}>Elimina comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/stampa')}>Stampa comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/assegna')}>Assegna comanda a cavo</MenuItem>\n                </Menu>\n              </>\n            )}\n          </>\n        )}\n\n        {/* Spacer */}\n        <Box sx={{ flexGrow: 1 }} />\n\n        {/* Informazioni utente e logout */}\n        <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>\n          {/* Mostra il cantiere selezionato */}\n          <SelectedCantiereDisplay />\n\n          {isImpersonating && impersonatedUser && (\n            <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mr: 2.5, fontSize: '1rem' }}>\n              Accesso come: <b>{impersonatedUser.username}</b>\n            </Typography>\n          )}\n          <Typography variant=\"body2\" sx={{ mr: 2.5, fontWeight: 500, fontSize: '1rem' }}>\n            {user?.username || ''}\n          </Typography>\n          <Tooltip title=\"Logout\">\n            <IconButton\n              color=\"inherit\"\n              onClick={handleLogout}\n              edge=\"end\"\n              sx={{ '&:hover': { backgroundColor: '#e9ecef' }, padding: '10px' }}\n            >\n              <LogoutIcon fontSize=\"medium\" />\n            </IconButton>\n          </Tooltip>\n        </Box>\n      </Toolbar>\n    </AppBar>\n  );\n};\n\nexport default TopNavbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,MAAM,EACNC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,UAAU,EACVC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,OAAO,QACF,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,kBAAkB,IAAIC,SAAS,EAC/BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,EACzBC,MAAM,IAAIC,UAAU,EACpBC,iBAAiB,IAAIC,aAAa,QAC7B,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,OAAOC,uBAAuB,MAAM,kCAAkC;AACtE,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAMqC,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEqC,IAAI;IAAEC,MAAM;IAAEC,eAAe;IAAEC;EAAiB,CAAC,GAAGd,OAAO,CAAC,CAAC;EACrE,MAAM;IAAEe;EAAyB,CAAC,GAAGd,gBAAgB,CAAC,CAAC;;EAEvD;EACA,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACgD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACwD,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC0D,cAAc,EAAEC,iBAAiB,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC4D,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EAC1E,MAAM,CAAC8D,eAAe,EAAEC,kBAAkB,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAMgE,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EACrE,MAAMC,oBAAoB,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEzE;EACA,MAAME,cAAc,GAAGA,CAACC,KAAK,EAAEC,WAAW,KAAK;IAC7CA,WAAW,CAACD,KAAK,CAACE,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,eAAe,GAAIF,WAAW,IAAK;IACvCA,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAIC,IAAI,IAAK;IAC3B;IACA,IAAIA,IAAI,KAAK,YAAY,EAAE;MACzB;MACA,IAAIjC,eAAe,EAAE;QACnBJ,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,IAAI,MAAK,OAAO,EAAE;QAC/BtC,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,IAAI,MAAK,MAAM,EAAE;QAC9BtC,QAAQ,CAAC,qBAAqB,CAAC;MACjC;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,IAAI,MAAK,eAAe,EAAE;QACvC;QACAtC,QAAQ,CAAC,4BAA4B,CAAC;MACxC;MACA;MAAA,KACK;QACHA,QAAQ,CAACqC,IAAI,CAAC;MAChB;IACF,CAAC,MAAM;MACLrC,QAAQ,CAACqC,IAAI,CAAC;IAChB;;IAEA;IACAF,eAAe,CAAC3B,eAAe,CAAC;IAChC2B,eAAe,CAACzB,gBAAgB,CAAC;IACjCyB,eAAe,CAACvB,mBAAmB,CAAC;IACpCuB,eAAe,CAACrB,eAAe,CAAC;IAChCqB,eAAe,CAACnB,eAAe,CAAC;IAChCmB,eAAe,CAACjB,gBAAgB,CAAC;IACjCiB,eAAe,CAACf,gBAAgB,CAAC;IACjCe,eAAe,CAACb,iBAAiB,CAAC;IAClCa,eAAe,CAACX,yBAAyB,CAAC;IAC1CW,eAAe,CAACT,kBAAkB,CAAC;EACrC,CAAC;EAED,MAAMa,YAAY,GAAGA,CAAA,KAAM;IACzBpC,MAAM,CAAC,CAAC;EACV,CAAC;;EAED;EACA,MAAMqC,QAAQ,GAAIH,IAAI,IAAK;IACzB,OAAOpC,QAAQ,CAACwC,QAAQ,KAAKJ,IAAI;EACnC,CAAC;;EAED;EACA,MAAMK,cAAc,GAAIL,IAAI,IAAK;IAC/B,OAAOpC,QAAQ,CAACwC,QAAQ,CAACE,UAAU,CAACN,IAAI,CAAC;EAC3C,CAAC;EAED,oBACE1C,OAAA,CAAC7B,MAAM;IAAC8E,QAAQ,EAAC,QAAQ;IAACC,KAAK,EAAC,SAAS;IAACC,SAAS,EAAE,CAAE;IAACC,EAAE,EAAE;MAAEC,MAAM,EAAE,IAAI;MAAEC,KAAK,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAS,CAAE;IAACC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eAC5IzD,OAAA,CAAC5B,OAAO;MAACgF,EAAE,EAAE;QAAEG,SAAS,EAAE,QAAQ;QAAEG,MAAM,EAAE;MAAO,CAAE;MAAAD,QAAA,gBAEnDzD,OAAA,CAAC1B,MAAM;QACL4E,KAAK,EAAC,SAAS;QACfS,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,YAAY,CAAE;QACxCmB,SAAS,eAAE5D,OAAA,CAACjB,QAAQ;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBZ,EAAE,EAAE;UAAEa,EAAE,EAAE;QAAE,CAAE;QACdT,SAAS,EAAEX,QAAQ,CAAC,YAAY,CAAC,GAAG,eAAe,GAAG,EAAG;QAAAY,QAAA,EAExDhD,eAAe,GAAG,qBAAqB,GACvC,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,IAAI,MAAK,OAAO,GAAG,gBAAgB,GACzC,CAAApC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,IAAI,MAAK,MAAM,GAAG,gBAAgB,GACxC,CAAApC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,IAAI,MAAK,eAAe,GAAG,eAAe,GAAG;MAAM;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACThE,OAAA,CAACrB,OAAO;QAACuF,WAAW,EAAC,UAAU;QAACC,QAAQ;QAACf,EAAE,EAAE;UAAEgB,EAAE,EAAE;QAAI;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAK3D,CAAC,CAAAzD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,IAAI,MAAK,OAAO,IAAK,CAAApC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,IAAI,MAAK,OAAO,IAAIlC,eAAe,IAAIC,gBAAiB,kBACzFV,OAAA,CAAAE,SAAA;QAAAuD,QAAA,GAEGhD,eAAe,iBACdT,OAAA,CAAC1B,MAAM;UACL4E,KAAK,EAAC,SAAS;UACfS,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,qBAAqB,CAAE;UACjDW,EAAE,EAAE;YAAEa,EAAE,EAAE;UAAE,CAAE;UACdT,SAAS,EAAEX,QAAQ,CAAC,qBAAqB,CAAC,GAAG,eAAe,GAAG,EAAG;UAAAY,QAAA,EAEjEhD,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAAC2D,QAAQ,EAAE,GAAG;QAAgB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F,CACT,EAKAhC,kBAAkB,iBACjBhC,OAAA,CAAAE,SAAA;UAAAuD,QAAA,GAEG,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,IAAI,MAAK,eAAe,iBAC7B3C,OAAA,CAAC1B,MAAM;YACL4E,KAAK,EAAC,SAAS;YACfS,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,4BAA4B,CAAE;YACxDW,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAEX,QAAQ,CAAC,4BAA4B,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAY,QAAA,EAC1E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eAGDhE,OAAA,CAAC1B,MAAM;YACL4E,KAAK,EAAC,SAAS;YACf,iBAAc,WAAW;YACzB,iBAAc,MAAM;YACpBS,OAAO,EAAGW,CAAC,IAAKlC,cAAc,CAACkC,CAAC,EAAEjD,eAAe,CAAE;YACnDkD,OAAO,eAAEvE,OAAA,CAACL,aAAa;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BZ,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,cAAc,CAAC,sBAAsB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC1E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGThE,OAAA,CAAC1B,MAAM;YACL4E,KAAK,EAAC,SAAS;YACf,iBAAc,YAAY;YAC1B,iBAAc,MAAM;YACpBS,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,kCAAkC,CAAE;YAC9DW,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,cAAc,CAAC,uBAAuB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC3E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGThE,OAAA,CAAC1B,MAAM;YACL4E,KAAK,EAAC,SAAS;YACf,iBAAc,YAAY;YAC1B,iBAAc,MAAM;YACpBS,OAAO,EAAGW,CAAC,IAAKlC,cAAc,CAACkC,CAAC,EAAE7C,gBAAgB,CAAE;YACpD8C,OAAO,eAAEvE,OAAA,CAACL,aAAa;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BZ,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,cAAc,CAAC,uBAAuB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC3E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGThE,OAAA,CAAC1B,MAAM;YACL4E,KAAK,EAAC,SAAS;YACf,iBAAc,aAAa;YAC3B,iBAAc,MAAM;YACpBS,OAAO,EAAGW,CAAC,IAAKlC,cAAc,CAACkC,CAAC,EAAE3C,iBAAiB,CAAE;YACrD4C,OAAO,eAAEvE,OAAA,CAACL,aAAa;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BZ,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,cAAc,CAAC,wBAAwB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC5E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGThE,OAAA,CAAC1B,MAAM;YACL4E,KAAK,EAAC,SAAS;YACf,iBAAc,qBAAqB;YACnC,iBAAc,MAAM;YACpBS,OAAO,EAAGW,CAAC,IAAKlC,cAAc,CAACkC,CAAC,EAAEzC,yBAAyB,CAAE;YAC7D0C,OAAO,eAAEvE,OAAA,CAACL,aAAa;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BZ,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,cAAc,CAAC,gCAAgC,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EACpF;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGThE,OAAA,CAAC1B,MAAM;YACL4E,KAAK,EAAC,SAAS;YACf,iBAAc,cAAc;YAC5B,iBAAc,MAAM;YACpBS,OAAO,EAAGW,CAAC,IAAKlC,cAAc,CAACkC,CAAC,EAAEvC,kBAAkB,CAAE;YACtDwC,OAAO,eAAEvE,OAAA,CAACL,aAAa;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BZ,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,cAAc,CAAC,yBAAyB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC7E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGThE,OAAA,CAACzB,IAAI;YACHiG,EAAE,EAAC,WAAW;YACdC,QAAQ,EAAErD,YAAa;YACvBsD,WAAW;YACXC,IAAI,EAAEC,OAAO,CAACxD,YAAY,CAAE;YAC5ByD,OAAO,EAAEA,CAAA,KAAMrC,eAAe,CAACnB,eAAe,CAAE;YAChDyD,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFxB,SAAS,EAAC,qBAAqB;YAC/BL,SAAS,EAAE,CAAE;YACbC,EAAE,EAAE;cAAE8B,EAAE,EAAE;YAAI,CAAE;YAAAzB,QAAA,gBAEhBzD,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,sCAAsC,CAAE;cAAAgB,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC9GhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,oCAAoC,CAAE;cAAAgB,QAAA,EAAC;YAAa;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACnGhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,oCAAoC,CAAE;cAAAgB,QAAA,EAAC;YAAmB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACzGhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAM;gBACvB;gBACAhD,wBAAwB,CAAC,IAAI,CAAC;gBAC9B;gBACA6B,eAAe,CAACnB,eAAe,CAAC;gBAChC;gBACAoB,UAAU,CAAC,4BAA4B,CAAC;cAC1C,CAAE;cAAAgB,QAAA,EAAC;YAAY;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1BhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,sCAAsC,CAAE;cAAAgB,QAAA,EAAC;YAA2B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACnHhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,mCAAmC,CAAE;cAAAgB,QAAA,EAAC;YAA0B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3G,CAAC,eAGPhE,OAAA,CAACzB,IAAI;YACHiG,EAAE,EAAC,YAAY;YACfC,QAAQ,EAAEnD,aAAc;YACxBoD,WAAW;YACXC,IAAI,EAAEC,OAAO,CAACtD,aAAa,CAAE;YAC7BuD,OAAO,EAAEA,CAAA,KAAMrC,eAAe,CAACjB,gBAAgB,CAAE;YACjDuD,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFxB,SAAS,EAAC,qBAAqB;YAC/BL,SAAS,EAAE,CAAE;YACbC,EAAE,EAAE;cAAE8B,EAAE,EAAE;YAAI,CAAE;YAAAzB,QAAA,eAEhBzD,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,+BAA+B,CAAE;cAAAgB,QAAA,EAAC;YAA2B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxG,CAAC,eAGPhE,OAAA,CAACzB,IAAI;YACHiG,EAAE,EAAC,YAAY;YACfC,QAAQ,EAAEjD,aAAc;YACxBkD,WAAW;YACXC,IAAI,EAAEC,OAAO,CAACpD,aAAa,CAAE;YAC7BqD,OAAO,EAAEA,CAAA,KAAMrC,eAAe,CAACf,gBAAgB,CAAE;YACjDqD,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFxB,SAAS,EAAC,qBAAqB;YAC/BL,SAAS,EAAE,CAAE;YACbC,EAAE,EAAE;cAAE8B,EAAE,EAAE;YAAI,CAAE;YAAAzB,QAAA,gBAEhBzD,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,oCAAoC,CAAE;cAAAgB,QAAA,EAAC;YAAqB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC3GhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,sCAAsC,CAAE;cAAAgB,QAAA,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC5GhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,oCAAoC,CAAE;cAAAgB,QAAA,EAAC;YAAqB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC3GhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,sCAAsC,CAAE;cAAAgB,QAAA,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxG,CAAC,eAGPhE,OAAA,CAACzB,IAAI;YACHiG,EAAE,EAAC,aAAa;YAChBC,QAAQ,EAAE/C,cAAe;YACzBgD,WAAW;YACXC,IAAI,EAAEC,OAAO,CAAClD,cAAc,CAAE;YAC9BmD,OAAO,EAAEA,CAAA,KAAMrC,eAAe,CAACb,iBAAiB,CAAE;YAClDmD,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFxB,SAAS,EAAC,qBAAqB;YAC/BL,SAAS,EAAE,CAAE;YACbC,EAAE,EAAE;cAAE8B,EAAE,EAAE;YAAI,CAAE;YAAAzB,QAAA,gBAEhBzD,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,oCAAoC,CAAE;cAAAgB,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxGhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,4BAA4B,CAAE;cAAAgB,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChGhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,wCAAwC,CAAE;cAAAgB,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChHhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,oCAAoC,CAAE;cAAAgB,QAAA,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CAAC,eAGPhE,OAAA,CAACzB,IAAI;YACHiG,EAAE,EAAC,qBAAqB;YACxBC,QAAQ,EAAE7C,sBAAuB;YACjC8C,WAAW;YACXC,IAAI,EAAEC,OAAO,CAAChD,sBAAsB,CAAE;YACtCiD,OAAO,EAAEA,CAAA,KAAMrC,eAAe,CAACX,yBAAyB,CAAE;YAC1DiD,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFxB,SAAS,EAAC,qBAAqB;YAC/BL,SAAS,EAAE,CAAE;YACbC,EAAE,EAAE;cAAE8B,EAAE,EAAE;YAAI,CAAE;YAAAzB,QAAA,gBAEhBzD,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,2CAA2C,CAAE;cAAAgB,QAAA,EAAC;YAAyB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtHhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,uCAAuC,CAAE;cAAAgB,QAAA,EAAC;YAAe;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxGhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,qCAAqC,CAAE;cAAAgB,QAAA,EAAC;YAAmB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1GhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,yCAAyC,CAAE;cAAAgB,QAAA,EAAC;YAAuB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClHhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,wCAAwC,CAAE;cAAAgB,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChHhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,0CAA0C,CAAE;cAAAgB,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1G,CAAC,eAGPhE,OAAA,CAACzB,IAAI;YACHiG,EAAE,EAAC,cAAc;YACjBC,QAAQ,EAAE3C,eAAgB;YAC1B4C,WAAW;YACXC,IAAI,EAAEC,OAAO,CAAC9C,eAAe,CAAE;YAC/B+C,OAAO,EAAEA,CAAA,KAAMrC,eAAe,CAACT,kBAAkB,CAAE;YACnD+C,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFxB,SAAS,EAAC,qBAAqB;YAC/BL,SAAS,EAAE,CAAE;YACbC,EAAE,EAAE;cAAE8B,EAAE,EAAE;YAAI,CAAE;YAAAzB,QAAA,gBAEhBzD,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,oCAAoC,CAAE;cAAAgB,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxGhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,8BAA8B,CAAE;cAAAgB,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClGhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,kCAAkC,CAAE;cAAAgB,QAAA,EAAC;YAAgB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACpGhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,iCAAiC,CAAE;cAAAgB,QAAA,EAAC;YAAe;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClGhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,gCAAgC,CAAE;cAAAgB,QAAA,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChGhE,OAAA,CAACxB,QAAQ;cAACmF,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,iCAAiC,CAAE;cAAAgB,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC;QAAA,eACP,CACH;MAAA,eACD,CACH,eAGDhE,OAAA,CAAC3B,GAAG;QAAC+E,EAAE,EAAE;UAAE+B,QAAQ,EAAE;QAAE;MAAE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG5BhE,OAAA,CAAC3B,GAAG;QAAC+E,EAAE,EAAE;UAAEgC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAE3B,MAAM,EAAE;QAAO,CAAE;QAAAD,QAAA,gBAEjEzD,OAAA,CAACF,uBAAuB;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAE1BvD,eAAe,IAAIC,gBAAgB,iBAClCV,OAAA,CAACvB,UAAU;UAAC6G,OAAO,EAAC,OAAO;UAACpC,KAAK,EAAC,eAAe;UAACE,EAAE,EAAE;YAAEa,EAAE,EAAE,GAAG;YAAEsB,QAAQ,EAAE;UAAO,CAAE;UAAA9B,QAAA,GAAC,gBACrE,eAAAzD,OAAA;YAAAyD,QAAA,EAAI/C,gBAAgB,CAAC2D;UAAQ;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CACb,eACDhE,OAAA,CAACvB,UAAU;UAAC6G,OAAO,EAAC,OAAO;UAAClC,EAAE,EAAE;YAAEa,EAAE,EAAE,GAAG;YAAEuB,UAAU,EAAE,GAAG;YAAED,QAAQ,EAAE;UAAO,CAAE;UAAA9B,QAAA,EAC5E,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D,QAAQ,KAAI;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACbhE,OAAA,CAACnB,OAAO;UAAC4G,KAAK,EAAC,QAAQ;UAAAhC,QAAA,eACrBzD,OAAA,CAACtB,UAAU;YACTwE,KAAK,EAAC,SAAS;YACfS,OAAO,EAAEf,YAAa;YACtB8C,IAAI,EAAC,KAAK;YACVtC,EAAE,EAAE;cAAE,SAAS,EAAE;gBAAEuC,eAAe,EAAE;cAAU,CAAC;cAAEC,OAAO,EAAE;YAAO,CAAE;YAAAnC,QAAA,eAEnEzD,OAAA,CAACP,UAAU;cAAC8F,QAAQ,EAAC;YAAQ;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEb,CAAC;AAAC5D,EAAA,CA1ZID,SAAS;EAAA,QACIlC,WAAW,EACXC,WAAW,EACgC0B,OAAO,EAC9BC,gBAAgB;AAAA;AAAAgG,EAAA,GAJjD1F,SAAS;AA4Zf,eAAeA,SAAS;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}