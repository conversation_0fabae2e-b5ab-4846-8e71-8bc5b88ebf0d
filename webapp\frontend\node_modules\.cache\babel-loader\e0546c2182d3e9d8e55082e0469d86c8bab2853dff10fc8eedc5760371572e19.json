{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'هەفتەی ڕابردوو' eeee 'کاتژمێر' p\",\n  yesterday: \"'دوێنێ کاتژمێر' p\",\n  today: \"'ئەمڕۆ کاتژمێر' p\",\n  tomorrow: \"'بەیانی کاتژمێر' p\",\n  nextWeek: \"eeee 'کاتژمێر' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/ckb/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'هەفتەی ڕابردوو' eeee 'کاتژمێر' p\",\n  yesterday: \"'دوێنێ کاتژمێر' p\",\n  today: \"'ئەمڕۆ کاتژمێر' p\",\n  tomorrow: \"'بەیانی کاتژمێر' p\",\n  nextWeek: \"eeee 'کاتژمێر' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,mCAAmC;EAC7CC,SAAS,EAAE,mBAAmB;EAC9BC,KAAK,EAAE,mBAAmB;EAC1BC,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAE,kBAAkB;EAC5BC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}