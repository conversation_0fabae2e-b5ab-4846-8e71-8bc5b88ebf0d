{"ast": null, "code": "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport Divider from '@mui/material/Divider';\nimport { PickersLayoutContentWrapper, PickersLayoutRoot, pickersLayoutClasses, usePickerLayout } from \"../PickersLayout/index.js\";\nimport { usePickerContext } from \"../hooks/usePickerContext.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * @ignore - internal component.\n */\nconst DesktopDateTimePickerLayout = /*#__PURE__*/React.forwardRef(function DesktopDateTimePickerLayout(props, ref) {\n  const {\n    toolbar,\n    tabs,\n    content,\n    actionBar,\n    shortcuts,\n    ownerState\n  } = usePickerLayout(props);\n  const {\n    orientation\n  } = usePickerContext();\n  const {\n    sx,\n    className,\n    classes\n  } = props;\n  const isActionBarVisible = actionBar && (actionBar.props.actions?.length ?? 0) > 0;\n  return /*#__PURE__*/_jsxs(PickersLayoutRoot, {\n    ref: ref,\n    className: clsx(pickersLayoutClasses.root, classes?.root, className),\n    sx: [{\n      [`& .${pickersLayoutClasses.tabs}`]: {\n        gridRow: 4,\n        gridColumn: '1 / 4'\n      },\n      [`& .${pickersLayoutClasses.actionBar}`]: {\n        gridRow: 5\n      }\n    }, ...(Array.isArray(sx) ? sx : [sx])],\n    ownerState: ownerState,\n    children: [orientation === 'landscape' ? shortcuts : toolbar, orientation === 'landscape' ? toolbar : shortcuts, /*#__PURE__*/_jsxs(PickersLayoutContentWrapper, {\n      className: clsx(pickersLayoutClasses.contentWrapper, classes?.contentWrapper),\n      ownerState: ownerState,\n      sx: {\n        display: 'grid'\n      },\n      children: [content, tabs, isActionBarVisible && /*#__PURE__*/_jsx(Divider, {\n        sx: {\n          gridRow: 3,\n          gridColumn: '1 / 4'\n        }\n      })]\n    }), actionBar]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? DesktopDateTimePickerLayout.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { DesktopDateTimePickerLayout };", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "Divider", "PickersLayoutContentWrapper", "PickersLayoutRoot", "pickersLayoutClasses", "usePickerLayout", "usePickerContext", "jsx", "_jsx", "jsxs", "_jsxs", "DesktopDateTimePickerLayout", "forwardRef", "props", "ref", "toolbar", "tabs", "content", "actionBar", "shortcuts", "ownerState", "orientation", "sx", "className", "classes", "isActionBarVisible", "actions", "length", "root", "gridRow", "gridColumn", "Array", "isArray", "children", "contentWrapper", "display", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "slotProps", "slots", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/DesktopDateTimePicker/DesktopDateTimePickerLayout.js"], "sourcesContent": ["import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport Divider from '@mui/material/Divider';\nimport { PickersLayoutContentWrapper, PickersLayoutRoot, pickersLayoutClasses, usePickerLayout } from \"../PickersLayout/index.js\";\nimport { usePickerContext } from \"../hooks/usePickerContext.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * @ignore - internal component.\n */\nconst DesktopDateTimePickerLayout = /*#__PURE__*/React.forwardRef(function DesktopDateTimePickerLayout(props, ref) {\n  const {\n    toolbar,\n    tabs,\n    content,\n    actionBar,\n    shortcuts,\n    ownerState\n  } = usePickerLayout(props);\n  const {\n    orientation\n  } = usePickerContext();\n  const {\n    sx,\n    className,\n    classes\n  } = props;\n  const isActionBarVisible = actionBar && (actionBar.props.actions?.length ?? 0) > 0;\n  return /*#__PURE__*/_jsxs(PickersLayoutRoot, {\n    ref: ref,\n    className: clsx(pickersLayoutClasses.root, classes?.root, className),\n    sx: [{\n      [`& .${pickersLayoutClasses.tabs}`]: {\n        gridRow: 4,\n        gridColumn: '1 / 4'\n      },\n      [`& .${pickersLayoutClasses.actionBar}`]: {\n        gridRow: 5\n      }\n    }, ...(Array.isArray(sx) ? sx : [sx])],\n    ownerState: ownerState,\n    children: [orientation === 'landscape' ? shortcuts : toolbar, orientation === 'landscape' ? toolbar : shortcuts, /*#__PURE__*/_jsxs(PickersLayoutContentWrapper, {\n      className: clsx(pickersLayoutClasses.contentWrapper, classes?.contentWrapper),\n      ownerState: ownerState,\n      sx: {\n        display: 'grid'\n      },\n      children: [content, tabs, isActionBarVisible && /*#__PURE__*/_jsx(Divider, {\n        sx: {\n          gridRow: 3,\n          gridColumn: '1 / 4'\n        }\n      })]\n    }), actionBar]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? DesktopDateTimePickerLayout.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { DesktopDateTimePickerLayout };"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,SAASC,2BAA2B,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEC,eAAe,QAAQ,2BAA2B;AACjI,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D;AACA;AACA;AACA,MAAMC,2BAA2B,GAAG,aAAab,KAAK,CAACc,UAAU,CAAC,SAASD,2BAA2BA,CAACE,KAAK,EAAEC,GAAG,EAAE;EACjH,MAAM;IACJC,OAAO;IACPC,IAAI;IACJC,OAAO;IACPC,SAAS;IACTC,SAAS;IACTC;EACF,CAAC,GAAGf,eAAe,CAACQ,KAAK,CAAC;EAC1B,MAAM;IACJQ;EACF,CAAC,GAAGf,gBAAgB,CAAC,CAAC;EACtB,MAAM;IACJgB,EAAE;IACFC,SAAS;IACTC;EACF,CAAC,GAAGX,KAAK;EACT,MAAMY,kBAAkB,GAAGP,SAAS,IAAI,CAACA,SAAS,CAACL,KAAK,CAACa,OAAO,EAAEC,MAAM,IAAI,CAAC,IAAI,CAAC;EAClF,OAAO,aAAajB,KAAK,CAACP,iBAAiB,EAAE;IAC3CW,GAAG,EAAEA,GAAG;IACRS,SAAS,EAAEvB,IAAI,CAACI,oBAAoB,CAACwB,IAAI,EAAEJ,OAAO,EAAEI,IAAI,EAAEL,SAAS,CAAC;IACpED,EAAE,EAAE,CAAC;MACH,CAAC,MAAMlB,oBAAoB,CAACY,IAAI,EAAE,GAAG;QACnCa,OAAO,EAAE,CAAC;QACVC,UAAU,EAAE;MACd,CAAC;MACD,CAAC,MAAM1B,oBAAoB,CAACc,SAAS,EAAE,GAAG;QACxCW,OAAO,EAAE;MACX;IACF,CAAC,EAAE,IAAIE,KAAK,CAACC,OAAO,CAACV,EAAE,CAAC,GAAGA,EAAE,GAAG,CAACA,EAAE,CAAC,CAAC,CAAC;IACtCF,UAAU,EAAEA,UAAU;IACtBa,QAAQ,EAAE,CAACZ,WAAW,KAAK,WAAW,GAAGF,SAAS,GAAGJ,OAAO,EAAEM,WAAW,KAAK,WAAW,GAAGN,OAAO,GAAGI,SAAS,EAAE,aAAaT,KAAK,CAACR,2BAA2B,EAAE;MAC/JqB,SAAS,EAAEvB,IAAI,CAACI,oBAAoB,CAAC8B,cAAc,EAAEV,OAAO,EAAEU,cAAc,CAAC;MAC7Ed,UAAU,EAAEA,UAAU;MACtBE,EAAE,EAAE;QACFa,OAAO,EAAE;MACX,CAAC;MACDF,QAAQ,EAAE,CAAChB,OAAO,EAAED,IAAI,EAAES,kBAAkB,IAAI,aAAajB,IAAI,CAACP,OAAO,EAAE;QACzEqB,EAAE,EAAE;UACFO,OAAO,EAAE,CAAC;UACVC,UAAU,EAAE;QACd;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,EAAEZ,SAAS;EACf,CAAC,CAAC;AACJ,CAAC,CAAC;AACFkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG3B,2BAA2B,CAAC4B,SAAS,GAAG;EAC9E;EACA;EACA;EACA;EACAN,QAAQ,EAAElC,SAAS,CAACyC,IAAI;EACxB;AACF;AACA;EACEhB,OAAO,EAAEzB,SAAS,CAAC0C,MAAM;EACzBlB,SAAS,EAAExB,SAAS,CAAC2C,MAAM;EAC3B;AACF;AACA;AACA;EACEC,SAAS,EAAE5C,SAAS,CAAC0C,MAAM;EAC3B;AACF;AACA;AACA;EACEG,KAAK,EAAE7C,SAAS,CAAC0C,MAAM;EACvB;AACF;AACA;EACEnB,EAAE,EAAEvB,SAAS,CAAC8C,SAAS,CAAC,CAAC9C,SAAS,CAAC+C,OAAO,CAAC/C,SAAS,CAAC8C,SAAS,CAAC,CAAC9C,SAAS,CAACgD,IAAI,EAAEhD,SAAS,CAAC0C,MAAM,EAAE1C,SAAS,CAACiD,IAAI,CAAC,CAAC,CAAC,EAAEjD,SAAS,CAACgD,IAAI,EAAEhD,SAAS,CAAC0C,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,SAAS9B,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}