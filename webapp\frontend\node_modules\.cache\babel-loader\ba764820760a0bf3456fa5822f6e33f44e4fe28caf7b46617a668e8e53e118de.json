{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"initialWidth\", \"width\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport getDisplayName from '@mui/utils/getDisplayName';\nimport { getThemeProps } from '@mui/system/useThemeProps';\nimport useTheme from '../styles/useTheme';\nimport useEnhancedEffect from '../utils/useEnhancedEffect';\nimport useMediaQuery from '../useMediaQuery';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst breakpointKeys = ['xs', 'sm', 'md', 'lg', 'xl'];\n\n// By default, returns true if screen width is the same or greater than the given breakpoint.\nexport const isWidthUp = function (breakpoint, width) {\n  let inclusive = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  if (inclusive) {\n    return breakpointKeys.indexOf(breakpoint) <= breakpointKeys.indexOf(width);\n  }\n  return breakpointKeys.indexOf(breakpoint) < breakpointKeys.indexOf(width);\n};\n\n// By default, returns true if screen width is less than the given breakpoint.\nexport const isWidthDown = function (breakpoint, width) {\n  let inclusive = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  if (inclusive) {\n    return breakpointKeys.indexOf(width) <= breakpointKeys.indexOf(breakpoint);\n  }\n  return breakpointKeys.indexOf(width) < breakpointKeys.indexOf(breakpoint);\n};\nconst withWidth = function () {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return Component => {\n    const {\n      withTheme: withThemeOption = false,\n      noSSR = false,\n      initialWidth: initialWidthOption\n    } = options;\n    function WithWidth(props) {\n      const contextTheme = useTheme();\n      const theme = props.theme || contextTheme;\n      const _getThemeProps = getThemeProps({\n          theme,\n          name: 'MuiWithWidth',\n          props\n        }),\n        {\n          initialWidth,\n          width\n        } = _getThemeProps,\n        other = _objectWithoutPropertiesLoose(_getThemeProps, _excluded);\n      const [mountedState, setMountedState] = React.useState(false);\n      useEnhancedEffect(() => {\n        setMountedState(true);\n      }, []);\n\n      /**\n       * innerWidth |xs      sm      md      lg      xl\n       *            |-------|-------|-------|-------|------>\n       * width      |  xs   |  sm   |  md   |  lg   |  xl\n       */\n      const keys = theme.breakpoints.keys.slice().reverse();\n      const widthComputed = keys.reduce((output, key) => {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const matches = useMediaQuery(theme.breakpoints.up(key));\n        return !output && matches ? key : output;\n      }, null);\n      const more = _extends({\n        width: width || (mountedState || noSSR ? widthComputed : undefined) || initialWidth || initialWidthOption\n      }, withThemeOption ? {\n        theme\n      } : {}, other);\n\n      // When rendering the component on the server,\n      // we have no idea about the client browser screen width.\n      // In order to prevent blinks and help the reconciliation of the React tree\n      // we are not rendering the child component.\n      //\n      // An alternative is to use the `initialWidth` property.\n      if (more.width === undefined) {\n        return null;\n      }\n      return /*#__PURE__*/_jsx(Component, _extends({}, more));\n    }\n    process.env.NODE_ENV !== \"production\" ? WithWidth.propTypes = {\n      /**\n       * As `window.innerWidth` is unavailable on the server,\n       * we default to rendering an empty component during the first mount.\n       * You might want to use a heuristic to approximate\n       * the screen width of the client browser screen width.\n       *\n       * For instance, you could be using the user-agent or the client-hints.\n       * https://caniuse.com/#search=client%20hint\n       */\n      initialWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),\n      /**\n       * @ignore\n       */\n      theme: PropTypes.object,\n      /**\n       * Bypass the width calculation logic.\n       */\n      width: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl'])\n    } : void 0;\n    if (process.env.NODE_ENV !== 'production') {\n      WithWidth.displayName = `WithWidth(${getDisplayName(Component)})`;\n    }\n    return WithWidth;\n  };\n};\nexport default withWidth;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "getDisplayName", "getThemeProps", "useTheme", "useEnhancedEffect", "useMediaQuery", "jsx", "_jsx", "breakpoint<PERSON><PERSON><PERSON>", "isWidthUp", "breakpoint", "width", "inclusive", "arguments", "length", "undefined", "indexOf", "isWidthDown", "with<PERSON><PERSON><PERSON>", "options", "Component", "withTheme", "withThemeOption", "noSSR", "initialWidth", "initialWidthOption", "<PERSON><PERSON><PERSON><PERSON>", "props", "contextTheme", "theme", "_getThemeProps", "name", "other", "mountedState", "setMountedState", "useState", "keys", "breakpoints", "slice", "reverse", "widthComputed", "reduce", "output", "key", "matches", "up", "more", "process", "env", "NODE_ENV", "propTypes", "oneOf", "object", "displayName"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/Hidden/withWidth.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"initialWidth\", \"width\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport getDisplayName from '@mui/utils/getDisplayName';\nimport { getThemeProps } from '@mui/system/useThemeProps';\nimport useTheme from '../styles/useTheme';\nimport useEnhancedEffect from '../utils/useEnhancedEffect';\nimport useMediaQuery from '../useMediaQuery';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst breakpointKeys = ['xs', 'sm', 'md', 'lg', 'xl'];\n\n// By default, returns true if screen width is the same or greater than the given breakpoint.\nexport const isWidthUp = (breakpoint, width, inclusive = true) => {\n  if (inclusive) {\n    return breakpointKeys.indexOf(breakpoint) <= breakpointKeys.indexOf(width);\n  }\n  return breakpointKeys.indexOf(breakpoint) < breakpointKeys.indexOf(width);\n};\n\n// By default, returns true if screen width is less than the given breakpoint.\nexport const isWidthDown = (breakpoint, width, inclusive = false) => {\n  if (inclusive) {\n    return breakpointKeys.indexOf(width) <= breakpointKeys.indexOf(breakpoint);\n  }\n  return breakpointKeys.indexOf(width) < breakpointKeys.indexOf(breakpoint);\n};\nconst withWidth = (options = {}) => Component => {\n  const {\n    withTheme: withThemeOption = false,\n    noSSR = false,\n    initialWidth: initialWidthOption\n  } = options;\n  function WithWidth(props) {\n    const contextTheme = useTheme();\n    const theme = props.theme || contextTheme;\n    const _getThemeProps = getThemeProps({\n        theme,\n        name: 'MuiWithWidth',\n        props\n      }),\n      {\n        initialWidth,\n        width\n      } = _getThemeProps,\n      other = _objectWithoutPropertiesLoose(_getThemeProps, _excluded);\n    const [mountedState, setMountedState] = React.useState(false);\n    useEnhancedEffect(() => {\n      setMountedState(true);\n    }, []);\n\n    /**\n     * innerWidth |xs      sm      md      lg      xl\n     *            |-------|-------|-------|-------|------>\n     * width      |  xs   |  sm   |  md   |  lg   |  xl\n     */\n    const keys = theme.breakpoints.keys.slice().reverse();\n    const widthComputed = keys.reduce((output, key) => {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      const matches = useMediaQuery(theme.breakpoints.up(key));\n      return !output && matches ? key : output;\n    }, null);\n    const more = _extends({\n      width: width || (mountedState || noSSR ? widthComputed : undefined) || initialWidth || initialWidthOption\n    }, withThemeOption ? {\n      theme\n    } : {}, other);\n\n    // When rendering the component on the server,\n    // we have no idea about the client browser screen width.\n    // In order to prevent blinks and help the reconciliation of the React tree\n    // we are not rendering the child component.\n    //\n    // An alternative is to use the `initialWidth` property.\n    if (more.width === undefined) {\n      return null;\n    }\n    return /*#__PURE__*/_jsx(Component, _extends({}, more));\n  }\n  process.env.NODE_ENV !== \"production\" ? WithWidth.propTypes = {\n    /**\n     * As `window.innerWidth` is unavailable on the server,\n     * we default to rendering an empty component during the first mount.\n     * You might want to use a heuristic to approximate\n     * the screen width of the client browser screen width.\n     *\n     * For instance, you could be using the user-agent or the client-hints.\n     * https://caniuse.com/#search=client%20hint\n     */\n    initialWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),\n    /**\n     * @ignore\n     */\n    theme: PropTypes.object,\n    /**\n     * Bypass the width calculation logic.\n     */\n    width: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl'])\n  } : void 0;\n  if (process.env.NODE_ENV !== 'production') {\n    WithWidth.displayName = `WithWidth(${getDisplayName(Component)})`;\n  }\n  return WithWidth;\n};\nexport default withWidth;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,aAAa,QAAQ,2BAA2B;AACzD,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;;AAErD;AACA,OAAO,MAAMC,SAAS,GAAG,SAAAA,CAACC,UAAU,EAAEC,KAAK,EAAuB;EAAA,IAArBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAC3D,IAAID,SAAS,EAAE;IACb,OAAOJ,cAAc,CAACQ,OAAO,CAACN,UAAU,CAAC,IAAIF,cAAc,CAACQ,OAAO,CAACL,KAAK,CAAC;EAC5E;EACA,OAAOH,cAAc,CAACQ,OAAO,CAACN,UAAU,CAAC,GAAGF,cAAc,CAACQ,OAAO,CAACL,KAAK,CAAC;AAC3E,CAAC;;AAED;AACA,OAAO,MAAMM,WAAW,GAAG,SAAAA,CAACP,UAAU,EAAEC,KAAK,EAAwB;EAAA,IAAtBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAC9D,IAAID,SAAS,EAAE;IACb,OAAOJ,cAAc,CAACQ,OAAO,CAACL,KAAK,CAAC,IAAIH,cAAc,CAACQ,OAAO,CAACN,UAAU,CAAC;EAC5E;EACA,OAAOF,cAAc,CAACQ,OAAO,CAACL,KAAK,CAAC,GAAGH,cAAc,CAACQ,OAAO,CAACN,UAAU,CAAC;AAC3E,CAAC;AACD,MAAMQ,SAAS,GAAG,SAAAA,CAAA;EAAA,IAACC,OAAO,GAAAN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,OAAKO,SAAS,IAAI;IAC/C,MAAM;MACJC,SAAS,EAAEC,eAAe,GAAG,KAAK;MAClCC,KAAK,GAAG,KAAK;MACbC,YAAY,EAAEC;IAChB,CAAC,GAAGN,OAAO;IACX,SAASO,SAASA,CAACC,KAAK,EAAE;MACxB,MAAMC,YAAY,GAAGzB,QAAQ,CAAC,CAAC;MAC/B,MAAM0B,KAAK,GAAGF,KAAK,CAACE,KAAK,IAAID,YAAY;MACzC,MAAME,cAAc,GAAG5B,aAAa,CAAC;UACjC2B,KAAK;UACLE,IAAI,EAAE,cAAc;UACpBJ;QACF,CAAC,CAAC;QACF;UACEH,YAAY;UACZb;QACF,CAAC,GAAGmB,cAAc;QAClBE,KAAK,GAAGnC,6BAA6B,CAACiC,cAAc,EAAEhC,SAAS,CAAC;MAClE,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,KAAK,CAACoC,QAAQ,CAAC,KAAK,CAAC;MAC7D/B,iBAAiB,CAAC,MAAM;QACtB8B,eAAe,CAAC,IAAI,CAAC;MACvB,CAAC,EAAE,EAAE,CAAC;;MAEN;AACJ;AACA;AACA;AACA;MACI,MAAME,IAAI,GAAGP,KAAK,CAACQ,WAAW,CAACD,IAAI,CAACE,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MACrD,MAAMC,aAAa,GAAGJ,IAAI,CAACK,MAAM,CAAC,CAACC,MAAM,EAAEC,GAAG,KAAK;QACjD;QACA,MAAMC,OAAO,GAAGvC,aAAa,CAACwB,KAAK,CAACQ,WAAW,CAACQ,EAAE,CAACF,GAAG,CAAC,CAAC;QACxD,OAAO,CAACD,MAAM,IAAIE,OAAO,GAAGD,GAAG,GAAGD,MAAM;MAC1C,CAAC,EAAE,IAAI,CAAC;MACR,MAAMI,IAAI,GAAGlD,QAAQ,CAAC;QACpBe,KAAK,EAAEA,KAAK,KAAKsB,YAAY,IAAIV,KAAK,GAAGiB,aAAa,GAAGzB,SAAS,CAAC,IAAIS,YAAY,IAAIC;MACzF,CAAC,EAAEH,eAAe,GAAG;QACnBO;MACF,CAAC,GAAG,CAAC,CAAC,EAAEG,KAAK,CAAC;;MAEd;MACA;MACA;MACA;MACA;MACA;MACA,IAAIc,IAAI,CAACnC,KAAK,KAAKI,SAAS,EAAE;QAC5B,OAAO,IAAI;MACb;MACA,OAAO,aAAaR,IAAI,CAACa,SAAS,EAAExB,QAAQ,CAAC,CAAC,CAAC,EAAEkD,IAAI,CAAC,CAAC;IACzD;IACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvB,SAAS,CAACwB,SAAS,GAAG;MAC5D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACI1B,YAAY,EAAExB,SAAS,CAACmD,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;MAC7D;AACJ;AACA;MACItB,KAAK,EAAE7B,SAAS,CAACoD,MAAM;MACvB;AACJ;AACA;MACIzC,KAAK,EAAEX,SAAS,CAACmD,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACvD,CAAC,GAAG,KAAK,CAAC;IACV,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCvB,SAAS,CAAC2B,WAAW,GAAG,aAAapD,cAAc,CAACmB,SAAS,CAAC,GAAG;IACnE;IACA,OAAOM,SAAS;EAClB,CAAC;AAAA;AACD,eAAeR,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}