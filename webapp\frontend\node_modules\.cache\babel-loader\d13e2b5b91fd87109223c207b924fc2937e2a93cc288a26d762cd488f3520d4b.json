{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\AggiungiCavoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, FormHelperText, Alert, CircularProgress, Typography, Paper } from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AggiungiCavoForm = ({\n  cantiereId,\n  onSuccess,\n  onError,\n  isDialog = false\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [loadingRevisione, setLoadingRevisione] = useState(true);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    revisione_ufficiale: '',\n    sistema: '',\n    utility: '',\n    colore_cavo: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    sh: '',\n    ubicazione_partenza: '',\n    utenza_partenza: '',\n    descrizione_utenza_partenza: '',\n    ubicazione_arrivo: '',\n    utenza_arrivo: '',\n    descrizione_utenza_arrivo: '',\n    metri_teorici: '',\n    metratura_reale: '0',\n    responsabile_posa: '',\n    id_bobina: '',\n    stato_installazione: 'Da installare'\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Carica la revisione corrente all'avvio\n  useEffect(() => {\n    const loadRevisioneCorrente = async () => {\n      try {\n        setLoadingRevisione(true);\n        const revisione = await caviService.getRevisioneCorrente(cantiereId);\n        setFormData(prev => ({\n          ...prev,\n          revisione_ufficiale: revisione\n        }));\n      } catch (error) {\n        console.error('Errore nel caricamento della revisione corrente:', error);\n        onError('Errore nel caricamento della revisione corrente');\n      } finally {\n        setLoadingRevisione(false);\n      }\n    };\n    loadRevisioneCorrente();\n  }, [cantiereId, onError]);\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    const additionalParams = {};\n    if (name === 'metratura_reale') {\n      additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n    }\n    const result = validateField(name, value, additionalParams);\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: !result.valid ? result.message : null\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: result.warning ? result.message : null\n    }));\n  };\n\n  // Gestisce l'annullamento dell'operazione\n  const handleCancel = () => {\n    if (isDialog) {\n      // Se è in un dialog, non fare nulla (il dialog ha il suo pulsante di annullamento)\n      return;\n    }\n    // Reindirizza alla visualizzazione dei cavi\n    navigate('/dashboard/cavi/visualizza');\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      // Validazione completa dei dati del cavo\n      const validation = validateCavoData(formData);\n      if (!validation.isValid) {\n        setFormErrors(validation.errors);\n        setFormWarnings(validation.warnings);\n        setLoading(false);\n        return;\n      }\n\n      // Usa i dati validati\n      const validatedData = validation.validatedData;\n\n      // Converti l'ID cavo in maiuscolo\n      validatedData.id_cavo = validatedData.id_cavo.toUpperCase();\n\n      // Invia i dati al server\n      await caviService.createCavo(cantiereId, validatedData);\n      onSuccess('Cavo aggiunto con successo');\n\n      // Reindirizza alla visualizzazione dei cavi solo se non è in un dialog\n      if (!isDialog) {\n        navigate('/dashboard/cavi/visualizza');\n      }\n\n      // Resetta il form (non necessario con il redirect, ma lo manteniamo per sicurezza)\n      setFormData({\n        id_cavo: '',\n        revisione_ufficiale: validatedData.revisione_ufficiale,\n        // Mantieni la revisione\n        sistema: '',\n        utility: '',\n        colore_cavo: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        sh: '',\n        ubicazione_partenza: '',\n        utenza_partenza: '',\n        descrizione_utenza_partenza: '',\n        ubicazione_arrivo: '',\n        utenza_arrivo: '',\n        descrizione_utenza_arrivo: '',\n        metri_teorici: '',\n        metratura_reale: '0',\n        responsabile_posa: '',\n        id_bobina: '',\n        stato_installazione: 'Da installare'\n      });\n      setFormErrors({});\n      setFormWarnings({});\n    } catch (error) {\n      console.error('Errore durante l\\'aggiunta del cavo:', error);\n      onError(error.detail || 'Errore durante l\\'aggiunta del cavo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mostra un avviso se ci sono warning\n  const hasWarnings = Object.keys(formWarnings).length > 0;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"form\",\n    onSubmit: handleSubmit,\n    noValidate: true,\n    children: loadingRevisione ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [hasWarnings && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 21\n        }, this),\n        sx: {\n          mb: isDialog ? 2 : 3,\n          py: isDialog ? 0.5 : 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          sx: {\n            fontSize: isDialog ? '0.8rem' : '0.875rem'\n          },\n          children: \"Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 2 : 3,\n          mb: isDialog ? 2 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '1.1rem' : '1.25rem'\n          },\n          children: \"Informazioni Generali\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"id_cavo\",\n              label: \"ID Cavo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.id_cavo,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.id_cavo,\n              helperText: formErrors.id_cavo,\n              inputProps: {\n                style: {\n                  textTransform: 'uppercase'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"revisione_ufficiale\",\n              label: \"Revisione Ufficiale\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.revisione_ufficiale,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.revisione_ufficiale,\n              helperText: formErrors.revisione_ufficiale\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"sistema\",\n              label: \"Sistema\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.sistema,\n              onChange: handleFormChange,\n              error: !!formErrors.sistema,\n              helperText: formErrors.sistema\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utility\",\n              label: \"Utility\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utility,\n              onChange: handleFormChange,\n              error: !!formErrors.utility,\n              helperText: formErrors.utility\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Caratteristiche Tecniche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"colore_cavo\",\n              label: \"Colore Cavo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.colore_cavo,\n              onChange: handleFormChange,\n              error: !!formErrors.colore_cavo,\n              helperText: formErrors.colore_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"tipologia\",\n              label: \"Tipologia\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.tipologia,\n              onChange: handleFormChange,\n              error: !!formErrors.tipologia,\n              helperText: formErrors.tipologia\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"n_conduttori\",\n              label: \"Numero Conduttori\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.n_conduttori,\n              onChange: handleFormChange,\n              error: !!formErrors.n_conduttori,\n              helperText: formErrors.n_conduttori || formWarnings.n_conduttori,\n              FormHelperTextProps: {\n                style: {\n                  color: formWarnings.n_conduttori ? 'orange' : undefined\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"sezione\",\n              label: \"Sezione\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.sezione,\n              onChange: handleFormChange,\n              error: !!formErrors.sezione,\n              helperText: formErrors.sezione || formWarnings.sezione,\n              FormHelperTextProps: {\n                style: {\n                  color: formWarnings.sezione ? 'orange' : undefined\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              error: !!formErrors.sh,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"sh-label\",\n                children: \"Schermato (S/N)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"sh-label\",\n                name: \"sh\",\n                value: formData.sh,\n                label: \"Schermato (S/N)\",\n                onChange: handleFormChange,\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"S\",\n                  children: \"S\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"N\",\n                  children: \"N\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this), formErrors.sh && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                children: formErrors.sh\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 2 : 3,\n          mb: isDialog ? 2 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '1.1rem' : '1.25rem'\n          },\n          children: \"Ubicazione Partenza\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"ubicazione_partenza\",\n              label: \"Ubicazione Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.ubicazione_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.ubicazione_partenza,\n              helperText: formErrors.ubicazione_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utenza_partenza\",\n              label: \"Utenza Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utenza_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.utenza_partenza,\n              helperText: formErrors.utenza_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"descrizione_utenza_partenza\",\n              label: \"Descrizione Utenza Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.descrizione_utenza_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.descrizione_utenza_partenza,\n              helperText: formErrors.descrizione_utenza_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 2 : 3,\n          mb: isDialog ? 2 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '1.1rem' : '1.25rem'\n          },\n          children: \"Ubicazione Arrivo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"ubicazione_arrivo\",\n              label: \"Ubicazione Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.ubicazione_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.ubicazione_arrivo,\n              helperText: formErrors.ubicazione_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utenza_arrivo\",\n              label: \"Utenza Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utenza_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.utenza_arrivo,\n              helperText: formErrors.utenza_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"descrizione_utenza_arrivo\",\n              label: \"Descrizione Utenza Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.descrizione_utenza_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.descrizione_utenza_arrivo,\n              helperText: formErrors.descrizione_utenza_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 2 : 3,\n          mb: isDialog ? 2 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '1.1rem' : '1.25rem'\n          },\n          children: \"Metratura\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"metri_teorici\",\n              label: \"Metri Teorici\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.metri_teorici,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.metri_teorici,\n              helperText: formErrors.metri_teorici\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 11\n      }, this), !isDialog && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          size: \"large\",\n          onClick: handleCancel,\n          disabled: loading,\n          sx: {\n            minWidth: 150\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          color: \"primary\",\n          size: \"large\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 28\n          }, this),\n          disabled: loading,\n          sx: {\n            minWidth: 150\n          },\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 28\n          }, this) : 'Salva Cavo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 13\n      }, this), isDialog && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          justifyContent: 'flex-end',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          color: \"primary\",\n          size: \"medium\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 28\n          }, this),\n          disabled: loading,\n          sx: {\n            minWidth: 120\n          },\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 28\n          }, this) : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 470,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this);\n};\n_s(AggiungiCavoForm, \"Muxm1Wb8jDN/87yWsjtE8U4F0S8=\", false, function () {\n  return [useNavigate];\n});\n_c = AggiungiCavoForm;\nexport default AggiungiCavoForm;\nvar _c;\n$RefreshReg$(_c, \"AggiungiCavoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "FormHelperText", "<PERSON><PERSON>", "CircularProgress", "Typography", "Paper", "Save", "SaveIcon", "Warning", "WarningIcon", "useNavigate", "caviService", "validateCavoData", "validateField", "isEmpty", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AggiungiCavoForm", "cantiereId", "onSuccess", "onError", "isDialog", "_s", "navigate", "loading", "setLoading", "loadingRevisione", "setLoadingRevisione", "formData", "setFormData", "id_cavo", "revisione_ufficiale", "sistema", "utility", "colore_cavo", "tipologia", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "metri_te<PERSON>ci", "metratura_reale", "responsabile_posa", "id_bobina", "stato_installazione", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "loadRevisioneCorrente", "revisione", "getRevisioneCorrente", "prev", "error", "console", "handleFormChange", "e", "name", "value", "target", "additionalParams", "metriTeorici", "parseFloat", "result", "valid", "message", "warning", "handleCancel", "handleSubmit", "preventDefault", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "validatedData", "toUpperCase", "createCavo", "detail", "hasWarnings", "Object", "keys", "length", "component", "onSubmit", "noValidate", "children", "sx", "display", "justifyContent", "my", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "icon", "mb", "py", "variant", "fontSize", "p", "boxShadow", "gutterBottom", "container", "spacing", "item", "xs", "sm", "label", "fullWidth", "onChange", "required", "helperText", "inputProps", "style", "textTransform", "FormHelperTextProps", "color", "undefined", "id", "labelId", "mt", "gap", "size", "onClick", "disabled", "min<PERSON><PERSON><PERSON>", "type", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/AggiungiCavoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  FormHelperText,\n  Alert,\n  CircularProgress,\n  Typography,\n  Paper\n} from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\n\nconst AggiungiCavoForm = ({ cantiereId, onSuccess, onError, isDialog = false }) => {\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [loadingRevisione, setLoadingRevisione] = useState(true);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    revisione_ufficiale: '',\n    sistema: '',\n    utility: '',\n    colore_cavo: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    sh: '',\n    ubicazione_partenza: '',\n    utenza_partenza: '',\n    descrizione_utenza_partenza: '',\n    ubicazione_arrivo: '',\n    utenza_arrivo: '',\n    descrizione_utenza_arrivo: '',\n    metri_teorici: '',\n    metratura_reale: '0',\n    responsabile_posa: '',\n    id_bobina: '',\n    stato_installazione: 'Da installare'\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Carica la revisione corrente all'avvio\n  useEffect(() => {\n    const loadRevisioneCorrente = async () => {\n      try {\n        setLoadingRevisione(true);\n        const revisione = await caviService.getRevisioneCorrente(cantiereId);\n        setFormData(prev => ({ ...prev, revisione_ufficiale: revisione }));\n      } catch (error) {\n        console.error('Errore nel caricamento della revisione corrente:', error);\n        onError('Errore nel caricamento della revisione corrente');\n      } finally {\n        setLoadingRevisione(false);\n      }\n    };\n\n    loadRevisioneCorrente();\n  }, [cantiereId, onError]);\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    const additionalParams = {};\n    if (name === 'metratura_reale') {\n      additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n    }\n\n    const result = validateField(name, value, additionalParams);\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: !result.valid ? result.message : null\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: result.warning ? result.message : null\n    }));\n  };\n\n  // Gestisce l'annullamento dell'operazione\n  const handleCancel = () => {\n    if (isDialog) {\n      // Se è in un dialog, non fare nulla (il dialog ha il suo pulsante di annullamento)\n      return;\n    }\n    // Reindirizza alla visualizzazione dei cavi\n    navigate('/dashboard/cavi/visualizza');\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      // Validazione completa dei dati del cavo\n      const validation = validateCavoData(formData);\n\n      if (!validation.isValid) {\n        setFormErrors(validation.errors);\n        setFormWarnings(validation.warnings);\n        setLoading(false);\n        return;\n      }\n\n      // Usa i dati validati\n      const validatedData = validation.validatedData;\n\n      // Converti l'ID cavo in maiuscolo\n      validatedData.id_cavo = validatedData.id_cavo.toUpperCase();\n\n      // Invia i dati al server\n      await caviService.createCavo(cantiereId, validatedData);\n      onSuccess('Cavo aggiunto con successo');\n\n      // Reindirizza alla visualizzazione dei cavi solo se non è in un dialog\n      if (!isDialog) {\n        navigate('/dashboard/cavi/visualizza');\n      }\n\n      // Resetta il form (non necessario con il redirect, ma lo manteniamo per sicurezza)\n      setFormData({\n        id_cavo: '',\n        revisione_ufficiale: validatedData.revisione_ufficiale, // Mantieni la revisione\n        sistema: '',\n        utility: '',\n        colore_cavo: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        sh: '',\n        ubicazione_partenza: '',\n        utenza_partenza: '',\n        descrizione_utenza_partenza: '',\n        ubicazione_arrivo: '',\n        utenza_arrivo: '',\n        descrizione_utenza_arrivo: '',\n        metri_teorici: '',\n        metratura_reale: '0',\n        responsabile_posa: '',\n        id_bobina: '',\n        stato_installazione: 'Da installare'\n      });\n      setFormErrors({});\n      setFormWarnings({});\n    } catch (error) {\n      console.error('Errore durante l\\'aggiunta del cavo:', error);\n      onError(error.detail || 'Errore durante l\\'aggiunta del cavo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mostra un avviso se ci sono warning\n  const hasWarnings = Object.keys(formWarnings).length > 0;\n\n  return (\n    <Box component=\"form\" onSubmit={handleSubmit} noValidate>\n      {loadingRevisione ? (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      ) : (\n        <>\n          {hasWarnings && (\n            <Alert\n              severity=\"warning\"\n              icon={<WarningIcon />}\n              sx={{ mb: isDialog ? 2 : 3, py: isDialog ? 0.5 : 1 }}\n            >\n              <Typography variant=\"subtitle2\" sx={{ fontSize: isDialog ? '0.8rem' : '0.875rem' }}>\n                Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\n              </Typography>\n            </Alert>\n          )}\n\n          <Paper sx={{ p: isDialog ? 2 : 3, mb: isDialog ? 2 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '1.1rem' : '1.25rem' }}>\n              Informazioni Generali\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_cavo\"\n                  label=\"ID Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_cavo}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.id_cavo}\n                  helperText={formErrors.id_cavo}\n                  inputProps={{ style: { textTransform: 'uppercase' } }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"revisione_ufficiale\"\n                  label=\"Revisione Ufficiale\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.revisione_ufficiale}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.revisione_ufficiale}\n                  helperText={formErrors.revisione_ufficiale}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sistema\"\n                  label=\"Sistema\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sistema}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sistema}\n                  helperText={formErrors.sistema}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utility}\n                  helperText={formErrors.utility}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: 3, mb: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Caratteristiche Tecniche\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"colore_cavo\"\n                  label=\"Colore Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.colore_cavo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.colore_cavo}\n                  helperText={formErrors.colore_cavo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                  error={!!formErrors.tipologia}\n                  helperText={formErrors.tipologia}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"Numero Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                  error={!!formErrors.n_conduttori}\n                  helperText={formErrors.n_conduttori || formWarnings.n_conduttori}\n                  FormHelperTextProps={{\n                    style: { color: formWarnings.n_conduttori ? 'orange' : undefined }\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sezione}\n                  helperText={formErrors.sezione || formWarnings.sezione}\n                  FormHelperTextProps={{\n                    style: { color: formWarnings.sezione ? 'orange' : undefined }\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <FormControl fullWidth error={!!formErrors.sh}>\n                  <InputLabel id=\"sh-label\">Schermato (S/N)</InputLabel>\n                  <Select\n                    labelId=\"sh-label\"\n                    name=\"sh\"\n                    value={formData.sh}\n                    label=\"Schermato (S/N)\"\n                    onChange={handleFormChange}\n                  >\n                    <MenuItem value=\"S\">S</MenuItem>\n                    <MenuItem value=\"N\">N</MenuItem>\n                  </Select>\n                  {formErrors.sh && <FormHelperText>{formErrors.sh}</FormHelperText>}\n                </FormControl>\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 2 : 3, mb: isDialog ? 2 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '1.1rem' : '1.25rem' }}>\n              Ubicazione Partenza\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"ubicazione_partenza\"\n                  label=\"Ubicazione Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_partenza}\n                  helperText={formErrors.ubicazione_partenza}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"utenza_partenza\"\n                  label=\"Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utenza_partenza}\n                  helperText={formErrors.utenza_partenza}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"descrizione_utenza_partenza\"\n                  label=\"Descrizione Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.descrizione_utenza_partenza}\n                  helperText={formErrors.descrizione_utenza_partenza}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 2 : 3, mb: isDialog ? 2 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '1.1rem' : '1.25rem' }}>\n              Ubicazione Arrivo\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"ubicazione_arrivo\"\n                  label=\"Ubicazione Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_arrivo}\n                  helperText={formErrors.ubicazione_arrivo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"utenza_arrivo\"\n                  label=\"Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utenza_arrivo}\n                  helperText={formErrors.utenza_arrivo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"descrizione_utenza_arrivo\"\n                  label=\"Descrizione Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.descrizione_utenza_arrivo}\n                  helperText={formErrors.descrizione_utenza_arrivo}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 2 : 3, mb: isDialog ? 2 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '1.1rem' : '1.25rem' }}>\n              Metratura\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_teorici\"\n                  label=\"Metri Teorici\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_teorici}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_teorici}\n                  helperText={formErrors.metri_teorici}\n                />\n              </Grid>\n\n            </Grid>\n          </Paper>\n\n          {!isDialog && (\n            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center', gap: 2 }}>\n              <Button\n                variant=\"outlined\"\n                color=\"secondary\"\n                size=\"large\"\n                onClick={handleCancel}\n                disabled={loading}\n                sx={{ minWidth: 150 }}\n              >\n                Annulla\n              </Button>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                color=\"primary\"\n                size=\"large\"\n                startIcon={<SaveIcon />}\n                disabled={loading}\n                sx={{ minWidth: 150 }}\n              >\n                {loading ? <CircularProgress size={24} /> : 'Salva Cavo'}\n              </Button>\n            </Box>\n          )}\n          {isDialog && (\n            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                color=\"primary\"\n                size=\"medium\"\n                startIcon={<SaveIcon />}\n                disabled={loading}\n                sx={{ minWidth: 120 }}\n              >\n                {loading ? <CircularProgress size={20} /> : 'Salva'}\n              </Button>\n            </Box>\n          )}\n        </>\n      )}\n    </Box>\n  );\n};\n\nexport default AggiungiCavoForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SAASC,IAAI,IAAIC,QAAQ,EAAEC,OAAO,IAAIC,WAAW,QAAQ,qBAAqB;AAC9E,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvF,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC,OAAO;EAAEC,QAAQ,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACjF,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC;IACvCyC,OAAO,EAAE,EAAE;IACXC,mBAAmB,EAAE,EAAE;IACvBC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,EAAE,EAAE,EAAE;IACNC,mBAAmB,EAAE,EAAE;IACvBC,eAAe,EAAE,EAAE;IACnBC,2BAA2B,EAAE,EAAE;IAC/BC,iBAAiB,EAAE,EAAE;IACrBC,aAAa,EAAE,EAAE;IACjBC,yBAAyB,EAAE,EAAE;IAC7BC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,GAAG;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,EAAE;IACbC,mBAAmB,EAAE;EACvB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMgE,qBAAqB,GAAG,MAAAA,CAAA,KAAY;MACxC,IAAI;QACF3B,mBAAmB,CAAC,IAAI,CAAC;QACzB,MAAM4B,SAAS,GAAG,MAAM9C,WAAW,CAAC+C,oBAAoB,CAACtC,UAAU,CAAC;QACpEW,WAAW,CAAC4B,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE1B,mBAAmB,EAAEwB;QAAU,CAAC,CAAC,CAAC;MACpE,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;QACxEtC,OAAO,CAAC,iDAAiD,CAAC;MAC5D,CAAC,SAAS;QACRO,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC;IAED2B,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,CAACpC,UAAU,EAAEE,OAAO,CAAC,CAAC;;EAEzB;EACA,MAAMwC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACAnC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACkC,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,MAAME,gBAAgB,GAAG,CAAC,CAAC;IAC3B,IAAIH,IAAI,KAAK,iBAAiB,EAAE;MAC9BG,gBAAgB,CAACC,YAAY,GAAGC,UAAU,CAACvC,QAAQ,CAACiB,aAAa,IAAI,CAAC,CAAC;IACzE;IAEA,MAAMuB,MAAM,GAAGzD,aAAa,CAACmD,IAAI,EAAEC,KAAK,EAAEE,gBAAgB,CAAC;;IAE3D;IACAd,aAAa,CAACM,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACK,IAAI,GAAG,CAACM,MAAM,CAACC,KAAK,GAAGD,MAAM,CAACE,OAAO,GAAG;IAC3C,CAAC,CAAC,CAAC;;IAEH;IACAjB,eAAe,CAACI,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACK,IAAI,GAAGM,MAAM,CAACG,OAAO,GAAGH,MAAM,CAACE,OAAO,GAAG;IAC5C,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAInD,QAAQ,EAAE;MACZ;MACA;IACF;IACA;IACAE,QAAQ,CAAC,4BAA4B,CAAC;EACxC,CAAC;;EAED;EACA,MAAMkD,YAAY,GAAG,MAAOZ,CAAC,IAAK;IAChCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAClBjD,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,MAAMkD,UAAU,GAAGjE,gBAAgB,CAACkB,QAAQ,CAAC;MAE7C,IAAI,CAAC+C,UAAU,CAACC,OAAO,EAAE;QACvBzB,aAAa,CAACwB,UAAU,CAACE,MAAM,CAAC;QAChCxB,eAAe,CAACsB,UAAU,CAACG,QAAQ,CAAC;QACpCrD,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAMsD,aAAa,GAAGJ,UAAU,CAACI,aAAa;;MAE9C;MACAA,aAAa,CAACjD,OAAO,GAAGiD,aAAa,CAACjD,OAAO,CAACkD,WAAW,CAAC,CAAC;;MAE3D;MACA,MAAMvE,WAAW,CAACwE,UAAU,CAAC/D,UAAU,EAAE6D,aAAa,CAAC;MACvD5D,SAAS,CAAC,4BAA4B,CAAC;;MAEvC;MACA,IAAI,CAACE,QAAQ,EAAE;QACbE,QAAQ,CAAC,4BAA4B,CAAC;MACxC;;MAEA;MACAM,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,mBAAmB,EAAEgD,aAAa,CAAChD,mBAAmB;QAAE;QACxDC,OAAO,EAAE,EAAE;QACXC,OAAO,EAAE,EAAE;QACXC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACbC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAE,EAAE;QACXC,EAAE,EAAE,EAAE;QACNC,mBAAmB,EAAE,EAAE;QACvBC,eAAe,EAAE,EAAE;QACnBC,2BAA2B,EAAE,EAAE;QAC/BC,iBAAiB,EAAE,EAAE;QACrBC,aAAa,EAAE,EAAE;QACjBC,yBAAyB,EAAE,EAAE;QAC7BC,aAAa,EAAE,EAAE;QACjBC,eAAe,EAAE,GAAG;QACpBC,iBAAiB,EAAE,EAAE;QACrBC,SAAS,EAAE,EAAE;QACbC,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACFE,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DtC,OAAO,CAACsC,KAAK,CAACwB,MAAM,IAAI,qCAAqC,CAAC;IAChE,CAAC,SAAS;MACRzD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0D,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACjC,YAAY,CAAC,CAACkC,MAAM,GAAG,CAAC;EAExD,oBACExE,OAAA,CAACvB,GAAG;IAACgG,SAAS,EAAC,MAAM;IAACC,QAAQ,EAAEf,YAAa;IAACgB,UAAU;IAAAC,QAAA,EACrDhE,gBAAgB,gBACfZ,OAAA,CAACvB,GAAG;MAACoG,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAC5D5E,OAAA,CAACb,gBAAgB;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,gBAENpF,OAAA,CAAAE,SAAA;MAAA0E,QAAA,GACGP,WAAW,iBACVrE,OAAA,CAACd,KAAK;QACJmG,QAAQ,EAAC,SAAS;QAClBC,IAAI,eAAEtF,OAAA,CAACP,WAAW;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBP,EAAE,EAAE;UAAEU,EAAE,EAAEhF,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEiF,EAAE,EAAEjF,QAAQ,GAAG,GAAG,GAAG;QAAE,CAAE;QAAAqE,QAAA,eAErD5E,OAAA,CAACZ,UAAU;UAACqG,OAAO,EAAC,WAAW;UAACZ,EAAE,EAAE;YAAEa,QAAQ,EAAEnF,QAAQ,GAAG,QAAQ,GAAG;UAAW,CAAE;UAAAqE,QAAA,EAAC;QAEpF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACR,eAEDpF,OAAA,CAACX,KAAK;QAACwF,EAAE,EAAE;UAAEc,CAAC,EAAEpF,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEgF,EAAE,EAAEhF,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEqF,SAAS,EAAErF,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAqE,QAAA,gBACpF5E,OAAA,CAACZ,UAAU;UAACqG,OAAO,EAAC,IAAI;UAACI,YAAY;UAAChB,EAAE,EAAE;YAAEa,QAAQ,EAAEnF,QAAQ,GAAG,QAAQ,GAAG;UAAU,CAAE;UAAAqE,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpF,OAAA,CAACpB,IAAI;UAACkH,SAAS;UAACC,OAAO,EAAExF,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAqE,QAAA,gBACxC5E,OAAA,CAACpB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB5E,OAAA,CAACtB,SAAS;cACRsE,IAAI,EAAC,SAAS;cACdmD,KAAK,EAAC,SAAS;cACfC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAEnC,QAAQ,CAACE,OAAQ;cACxBqF,QAAQ,EAAEvD,gBAAiB;cAC3BwD,QAAQ;cACR1D,KAAK,EAAE,CAAC,CAACR,UAAU,CAACpB,OAAQ;cAC5BuF,UAAU,EAAEnE,UAAU,CAACpB,OAAQ;cAC/BwF,UAAU,EAAE;gBAAEC,KAAK,EAAE;kBAAEC,aAAa,EAAE;gBAAY;cAAE;YAAE;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPpF,OAAA,CAACpB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB5E,OAAA,CAACtB,SAAS;cACRsE,IAAI,EAAC,qBAAqB;cAC1BmD,KAAK,EAAC,qBAAqB;cAC3BC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAEnC,QAAQ,CAACG,mBAAoB;cACpCoF,QAAQ,EAAEvD,gBAAiB;cAC3BwD,QAAQ;cACR1D,KAAK,EAAE,CAAC,CAACR,UAAU,CAACnB,mBAAoB;cACxCsF,UAAU,EAAEnE,UAAU,CAACnB;YAAoB;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPpF,OAAA,CAACpB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB5E,OAAA,CAACtB,SAAS;cACRsE,IAAI,EAAC,SAAS;cACdmD,KAAK,EAAC,SAAS;cACfC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAEnC,QAAQ,CAACI,OAAQ;cACxBmF,QAAQ,EAAEvD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAAClB,OAAQ;cAC5BqF,UAAU,EAAEnE,UAAU,CAAClB;YAAQ;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPpF,OAAA,CAACpB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB5E,OAAA,CAACtB,SAAS;cACRsE,IAAI,EAAC,SAAS;cACdmD,KAAK,EAAC,SAAS;cACfC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAEnC,QAAQ,CAACK,OAAQ;cACxBkF,QAAQ,EAAEvD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACjB,OAAQ;cAC5BoF,UAAU,EAAEnE,UAAU,CAACjB;YAAQ;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERpF,OAAA,CAACX,KAAK;QAACwF,EAAE,EAAE;UAAEc,CAAC,EAAE,CAAC;UAAEJ,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,gBACzB5E,OAAA,CAACZ,UAAU;UAACqG,OAAO,EAAC,IAAI;UAACI,YAAY;UAAAjB,QAAA,EAAC;QAEtC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpF,OAAA,CAACpB,IAAI;UAACkH,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAnB,QAAA,gBACzB5E,OAAA,CAACpB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB5E,OAAA,CAACtB,SAAS;cACRsE,IAAI,EAAC,aAAa;cAClBmD,KAAK,EAAC,aAAa;cACnBC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAEnC,QAAQ,CAACM,WAAY;cAC5BiF,QAAQ,EAAEvD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAAChB,WAAY;cAChCmF,UAAU,EAAEnE,UAAU,CAAChB;YAAY;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPpF,OAAA,CAACpB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB5E,OAAA,CAACtB,SAAS;cACRsE,IAAI,EAAC,WAAW;cAChBmD,KAAK,EAAC,WAAW;cACjBC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAEnC,QAAQ,CAACO,SAAU;cAC1BgF,QAAQ,EAAEvD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACf,SAAU;cAC9BkF,UAAU,EAAEnE,UAAU,CAACf;YAAU;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPpF,OAAA,CAACpB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB5E,OAAA,CAACtB,SAAS;cACRsE,IAAI,EAAC,cAAc;cACnBmD,KAAK,EAAC,mBAAmB;cACzBC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAEnC,QAAQ,CAACQ,YAAa;cAC7B+E,QAAQ,EAAEvD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACd,YAAa;cACjCiF,UAAU,EAAEnE,UAAU,CAACd,YAAY,IAAIgB,YAAY,CAAChB,YAAa;cACjEqF,mBAAmB,EAAE;gBACnBF,KAAK,EAAE;kBAAEG,KAAK,EAAEtE,YAAY,CAAChB,YAAY,GAAG,QAAQ,GAAGuF;gBAAU;cACnE;YAAE;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPpF,OAAA,CAACpB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB5E,OAAA,CAACtB,SAAS;cACRsE,IAAI,EAAC,SAAS;cACdmD,KAAK,EAAC,SAAS;cACfC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAEnC,QAAQ,CAACS,OAAQ;cACxB8E,QAAQ,EAAEvD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACb,OAAQ;cAC5BgF,UAAU,EAAEnE,UAAU,CAACb,OAAO,IAAIe,YAAY,CAACf,OAAQ;cACvDoF,mBAAmB,EAAE;gBACnBF,KAAK,EAAE;kBAAEG,KAAK,EAAEtE,YAAY,CAACf,OAAO,GAAG,QAAQ,GAAGsF;gBAAU;cAC9D;YAAE;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPpF,OAAA,CAACpB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB5E,OAAA,CAACnB,WAAW;cAACuH,SAAS;cAACxD,KAAK,EAAE,CAAC,CAACR,UAAU,CAACZ,EAAG;cAAAoD,QAAA,gBAC5C5E,OAAA,CAAClB,UAAU;gBAACgI,EAAE,EAAC,UAAU;gBAAAlC,QAAA,EAAC;cAAe;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtDpF,OAAA,CAACjB,MAAM;gBACLgI,OAAO,EAAC,UAAU;gBAClB/D,IAAI,EAAC,IAAI;gBACTC,KAAK,EAAEnC,QAAQ,CAACU,EAAG;gBACnB2E,KAAK,EAAC,iBAAiB;gBACvBE,QAAQ,EAAEvD,gBAAiB;gBAAA8B,QAAA,gBAE3B5E,OAAA,CAAChB,QAAQ;kBAACiE,KAAK,EAAC,GAAG;kBAAA2B,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAChCpF,OAAA,CAAChB,QAAQ;kBAACiE,KAAK,EAAC,GAAG;kBAAA2B,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,EACRhD,UAAU,CAACZ,EAAE,iBAAIxB,OAAA,CAACf,cAAc;gBAAA2F,QAAA,EAAExC,UAAU,CAACZ;cAAE;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERpF,OAAA,CAACX,KAAK;QAACwF,EAAE,EAAE;UAAEc,CAAC,EAAEpF,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEgF,EAAE,EAAEhF,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEqF,SAAS,EAAErF,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAqE,QAAA,gBACpF5E,OAAA,CAACZ,UAAU;UAACqG,OAAO,EAAC,IAAI;UAACI,YAAY;UAAChB,EAAE,EAAE;YAAEa,QAAQ,EAAEnF,QAAQ,GAAG,QAAQ,GAAG;UAAU,CAAE;UAAAqE,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpF,OAAA,CAACpB,IAAI;UAACkH,SAAS;UAACC,OAAO,EAAExF,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAqE,QAAA,gBACxC5E,OAAA,CAACpB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB5E,OAAA,CAACtB,SAAS;cACRsE,IAAI,EAAC,qBAAqB;cAC1BmD,KAAK,EAAC,qBAAqB;cAC3BC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAEnC,QAAQ,CAACW,mBAAoB;cACpC4E,QAAQ,EAAEvD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACX,mBAAoB;cACxC8E,UAAU,EAAEnE,UAAU,CAACX;YAAoB;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPpF,OAAA,CAACpB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB5E,OAAA,CAACtB,SAAS;cACRsE,IAAI,EAAC,iBAAiB;cACtBmD,KAAK,EAAC,iBAAiB;cACvBC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAEnC,QAAQ,CAACY,eAAgB;cAChC2E,QAAQ,EAAEvD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACV,eAAgB;cACpC6E,UAAU,EAAEnE,UAAU,CAACV;YAAgB;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPpF,OAAA,CAACpB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB5E,OAAA,CAACtB,SAAS;cACRsE,IAAI,EAAC,6BAA6B;cAClCmD,KAAK,EAAC,6BAA6B;cACnCC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAEnC,QAAQ,CAACa,2BAA4B;cAC5C0E,QAAQ,EAAEvD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACT,2BAA4B;cAChD4E,UAAU,EAAEnE,UAAU,CAACT;YAA4B;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERpF,OAAA,CAACX,KAAK;QAACwF,EAAE,EAAE;UAAEc,CAAC,EAAEpF,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEgF,EAAE,EAAEhF,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEqF,SAAS,EAAErF,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAqE,QAAA,gBACpF5E,OAAA,CAACZ,UAAU;UAACqG,OAAO,EAAC,IAAI;UAACI,YAAY;UAAChB,EAAE,EAAE;YAAEa,QAAQ,EAAEnF,QAAQ,GAAG,QAAQ,GAAG;UAAU,CAAE;UAAAqE,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpF,OAAA,CAACpB,IAAI;UAACkH,SAAS;UAACC,OAAO,EAAExF,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAqE,QAAA,gBACxC5E,OAAA,CAACpB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB5E,OAAA,CAACtB,SAAS;cACRsE,IAAI,EAAC,mBAAmB;cACxBmD,KAAK,EAAC,mBAAmB;cACzBC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAEnC,QAAQ,CAACc,iBAAkB;cAClCyE,QAAQ,EAAEvD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACR,iBAAkB;cACtC2E,UAAU,EAAEnE,UAAU,CAACR;YAAkB;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPpF,OAAA,CAACpB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB5E,OAAA,CAACtB,SAAS;cACRsE,IAAI,EAAC,eAAe;cACpBmD,KAAK,EAAC,eAAe;cACrBC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAEnC,QAAQ,CAACe,aAAc;cAC9BwE,QAAQ,EAAEvD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACP,aAAc;cAClC0E,UAAU,EAAEnE,UAAU,CAACP;YAAc;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPpF,OAAA,CAACpB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB5E,OAAA,CAACtB,SAAS;cACRsE,IAAI,EAAC,2BAA2B;cAChCmD,KAAK,EAAC,2BAA2B;cACjCC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAEnC,QAAQ,CAACgB,yBAA0B;cAC1CuE,QAAQ,EAAEvD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACN,yBAA0B;cAC9CyE,UAAU,EAAEnE,UAAU,CAACN;YAA0B;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERpF,OAAA,CAACX,KAAK;QAACwF,EAAE,EAAE;UAAEc,CAAC,EAAEpF,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEgF,EAAE,EAAEhF,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEqF,SAAS,EAAErF,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAqE,QAAA,gBACpF5E,OAAA,CAACZ,UAAU;UAACqG,OAAO,EAAC,IAAI;UAACI,YAAY;UAAChB,EAAE,EAAE;YAAEa,QAAQ,EAAEnF,QAAQ,GAAG,QAAQ,GAAG;UAAU,CAAE;UAAAqE,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpF,OAAA,CAACpB,IAAI;UAACkH,SAAS;UAACC,OAAO,EAAExF,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAqE,QAAA,eACxC5E,OAAA,CAACpB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB5E,OAAA,CAACtB,SAAS;cACRsE,IAAI,EAAC,eAAe;cACpBmD,KAAK,EAAC,eAAe;cACrBC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAEnC,QAAQ,CAACiB,aAAc;cAC9BsE,QAAQ,EAAEvD,gBAAiB;cAC3BwD,QAAQ;cACR1D,KAAK,EAAE,CAAC,CAACR,UAAU,CAACL,aAAc;cAClCwE,UAAU,EAAEnE,UAAU,CAACL;YAAc;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAEP,CAAC7E,QAAQ,iBACRP,OAAA,CAACvB,GAAG;QAACoG,EAAE,EAAE;UAAEmC,EAAE,EAAE,CAAC;UAAElC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEkC,GAAG,EAAE;QAAE,CAAE;QAAArC,QAAA,gBACpE5E,OAAA,CAACrB,MAAM;UACL8G,OAAO,EAAC,UAAU;UAClBmB,KAAK,EAAC,WAAW;UACjBM,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEzD,YAAa;UACtB0D,QAAQ,EAAE1G,OAAQ;UAClBmE,EAAE,EAAE;YAAEwC,QAAQ,EAAE;UAAI,CAAE;UAAAzC,QAAA,EACvB;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpF,OAAA,CAACrB,MAAM;UACL2I,IAAI,EAAC,QAAQ;UACb7B,OAAO,EAAC,WAAW;UACnBmB,KAAK,EAAC,SAAS;UACfM,IAAI,EAAC,OAAO;UACZK,SAAS,eAAEvH,OAAA,CAACT,QAAQ;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBgC,QAAQ,EAAE1G,OAAQ;UAClBmE,EAAE,EAAE;YAAEwC,QAAQ,EAAE;UAAI,CAAE;UAAAzC,QAAA,EAErBlE,OAAO,gBAAGV,OAAA,CAACb,gBAAgB;YAAC+H,IAAI,EAAE;UAAG;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAY;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EACA7E,QAAQ,iBACPP,OAAA,CAACvB,GAAG;QAACoG,EAAE,EAAE;UAAEmC,EAAE,EAAE,CAAC;UAAElC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,UAAU;UAAEkC,GAAG,EAAE;QAAE,CAAE;QAAArC,QAAA,eACtE5E,OAAA,CAACrB,MAAM;UACL2I,IAAI,EAAC,QAAQ;UACb7B,OAAO,EAAC,WAAW;UACnBmB,KAAK,EAAC,SAAS;UACfM,IAAI,EAAC,QAAQ;UACbK,SAAS,eAAEvH,OAAA,CAACT,QAAQ;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBgC,QAAQ,EAAE1G,OAAQ;UAClBmE,EAAE,EAAE;YAAEwC,QAAQ,EAAE;UAAI,CAAE;UAAAzC,QAAA,EAErBlE,OAAO,gBAAGV,OAAA,CAACb,gBAAgB;YAAC+H,IAAI,EAAE;UAAG;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA,eACD;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC5E,EAAA,CAldIL,gBAAgB;EAAA,QACHT,WAAW;AAAA;AAAA8H,EAAA,GADxBrH,gBAAgB;AAodtB,eAAeA,gBAAgB;AAAC,IAAAqH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}