{"ast": null, "code": "'use client';\n\nexport { default } from './useScrollTrigger';", "map": {"version": 3, "names": ["default"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/material/useScrollTrigger/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './useScrollTrigger';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}