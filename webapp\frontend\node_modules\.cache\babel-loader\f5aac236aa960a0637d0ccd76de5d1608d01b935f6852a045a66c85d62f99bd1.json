{"ast": null, "code": "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name lastDayOfMonth\n * @category Month Helpers\n * @summary Return the last day of a month for the given date.\n *\n * @description\n * Return the last day of a month for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n *\n * @returns The last day of a month\n *\n * @example\n * // The last day of a month for 2 September 2014 11:55:00:\n * const result = lastDayOfMonth(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 00:00:00\n */\nexport function lastDayOfMonth(date) {\n  const _date = toDate(date);\n  const month = _date.getMonth();\n  _date.setFullYear(_date.getFullYear(), month + 1, 0);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfMonth;", "map": {"version": 3, "names": ["toDate", "lastDayOfMonth", "date", "_date", "month", "getMonth", "setFullYear", "getFullYear", "setHours"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/lastDayOfMonth.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name lastDayOfMonth\n * @category Month Helpers\n * @summary Return the last day of a month for the given date.\n *\n * @description\n * Return the last day of a month for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n *\n * @returns The last day of a month\n *\n * @example\n * // The last day of a month for 2 September 2014 11:55:00:\n * const result = lastDayOfMonth(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 00:00:00\n */\nexport function lastDayOfMonth(date) {\n  const _date = toDate(date);\n  const month = _date.getMonth();\n  _date.setFullYear(_date.getFullYear(), month + 1, 0);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfMonth;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,IAAI,EAAE;EACnC,MAAMC,KAAK,GAAGH,MAAM,CAACE,IAAI,CAAC;EAC1B,MAAME,KAAK,GAAGD,KAAK,CAACE,QAAQ,CAAC,CAAC;EAC9BF,KAAK,CAACG,WAAW,CAACH,KAAK,CAACI,WAAW,CAAC,CAAC,EAAEH,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;EACpDD,KAAK,CAACK,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1B,OAAOL,KAAK;AACd;;AAEA;AACA,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}