{"ast": null, "code": "import React from'react';import{Navigate}from'react-router-dom';import{useAuth}from'../context/AuthContext';import{CircularProgress,Box}from'@mui/material';import{jsx as _jsx}from\"react/jsx-runtime\";const ProtectedRoute=_ref=>{let{children,requiredRole}=_ref;const{isAuthenticated,user,loading}=useAuth();// Mostra un indicatore di caricamento mentre verifichiamo l'autenticazione\nif(loading){return/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"100vh\",children:/*#__PURE__*/_jsx(CircularProgress,{})});}// Se l'utente non è autenticato, reindirizza alla pagina di login\nif(!isAuthenticated){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});}// Se è richiesto un ruolo specifico, verifica che l'utente abbia quel ruolo\nif(requiredRole&&user.role!==requiredRole){// Reindirizza alla dashboard o a una pagina di accesso negato\nreturn/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true});}// Se l'utente è autenticato e ha il ruolo richiesto, mostra il contenuto protetto\nreturn children;};export default ProtectedRoute;", "map": {"version": 3, "names": ["React", "Navigate", "useAuth", "CircularProgress", "Box", "jsx", "_jsx", "ProtectedRoute", "_ref", "children", "requiredRole", "isAuthenticated", "user", "loading", "display", "justifyContent", "alignItems", "minHeight", "to", "replace", "role"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { CircularProgress, Box } from '@mui/material';\n\nconst ProtectedRoute = ({ children, requiredRole }) => {\n  const { isAuthenticated, user, loading } = useAuth();\n\n  // Mostra un indicatore di caricamento mentre verifichiamo l'autenticazione\n  if (loading) {\n    return (\n      <Box\n        display=\"flex\"\n        justifyContent=\"center\"\n        alignItems=\"center\"\n        minHeight=\"100vh\"\n      >\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  // Se l'utente non è autenticato, reindirizza alla pagina di login\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  // Se è richiesto un ruolo specifico, verifica che l'utente abbia quel ruolo\n  if (requiredRole && user.role !== requiredRole) {\n    // Reindirizza alla dashboard o a una pagina di accesso negato\n    return <Navigate to=\"/dashboard\" replace />;\n  }\n\n  // Se l'utente è autenticato e ha il ruolo richiesto, mostra il contenuto protetto\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,QAAQ,KAAQ,kBAAkB,CAC3C,OAASC,OAAO,KAAQ,wBAAwB,CAChD,OAASC,gBAAgB,CAAEC,GAAG,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEtD,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAAgC,IAA/B,CAAEC,QAAQ,CAAEC,YAAa,CAAC,CAAAF,IAAA,CAChD,KAAM,CAAEG,eAAe,CAAEC,IAAI,CAAEC,OAAQ,CAAC,CAAGX,OAAO,CAAC,CAAC,CAEpD;AACA,GAAIW,OAAO,CAAE,CACX,mBACEP,IAAA,CAACF,GAAG,EACFU,OAAO,CAAC,MAAM,CACdC,cAAc,CAAC,QAAQ,CACvBC,UAAU,CAAC,QAAQ,CACnBC,SAAS,CAAC,OAAO,CAAAR,QAAA,cAEjBH,IAAA,CAACH,gBAAgB,GAAE,CAAC,CACjB,CAAC,CAEV,CAEA;AACA,GAAI,CAACQ,eAAe,CAAE,CACpB,mBAAOL,IAAA,CAACL,QAAQ,EAACiB,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAC,CACzC,CAEA;AACA,GAAIT,YAAY,EAAIE,IAAI,CAACQ,IAAI,GAAKV,YAAY,CAAE,CAC9C;AACA,mBAAOJ,IAAA,CAACL,QAAQ,EAACiB,EAAE,CAAC,YAAY,CAACC,OAAO,MAAE,CAAC,CAC7C,CAEA;AACA,MAAO,CAAAV,QAAQ,CACjB,CAAC,CAED,cAAe,CAAAF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}