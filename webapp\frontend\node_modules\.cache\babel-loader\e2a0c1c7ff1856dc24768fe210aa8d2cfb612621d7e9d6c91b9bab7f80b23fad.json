{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\UserPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, CardActions, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, TextField, Snackbar, Alert } from '@mui/material';\nimport { Construction as ConstructionIcon, Delete as DeleteIcon, Add as AddIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport cantieriService from '../services/cantieriService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [cantieri, setCantieri] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openCreateDialog, setOpenCreateDialog] = useState(false);\n  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);\n  const [selectedCantiere, setSelectedCantiere] = useState(null);\n  const [newCantiereData, setNewCantiereData] = useState({\n    nome: '',\n    descrizione: '',\n    password_cantiere: '',\n    conferma_password: ''\n  });\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Carica i cantieri dell'utente corrente\n  useEffect(() => {\n    const fetchCantieri = async () => {\n      try {\n        setLoading(true);\n        const data = await cantieriService.getMyCantieri();\n        setCantieri(data);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cantieri:', err);\n        setError('Impossibile caricare i cantieri. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchCantieri();\n  }, []);\n\n  // Gestisce l'apertura del dialog per creare un nuovo cantiere\n  const handleOpenCreateDialog = () => {\n    setNewCantiereData({\n      nome: '',\n      descrizione: '',\n      password_cantiere: '',\n      conferma_password: ''\n    });\n    setOpenCreateDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per creare un nuovo cantiere\n  const handleCloseCreateDialog = () => {\n    setOpenCreateDialog(false);\n  };\n\n  // Gestisce l'apertura del dialog per eliminare un cantiere\n  const handleOpenDeleteDialog = cantiere => {\n    setSelectedCantiere(cantiere);\n    setOpenDeleteDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per eliminare un cantiere\n  const handleCloseDeleteDialog = () => {\n    setOpenDeleteDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce la modifica dei campi del form per creare un nuovo cantiere\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewCantiereData({\n      ...newCantiereData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la creazione di un nuovo cantiere\n  const handleCreateCantiere = async () => {\n    // Verifica che i campi obbligatori siano compilati\n    if (!newCantiereData.nome || !newCantiereData.password_cantiere) {\n      setNotification({\n        open: true,\n        message: 'Il nome e la password sono obbligatori',\n        severity: 'error'\n      });\n      return;\n    }\n\n    // Verifica che le password coincidano\n    if (newCantiereData.password_cantiere !== newCantiereData.conferma_password) {\n      setNotification({\n        open: true,\n        message: 'Le password non coincidono',\n        severity: 'error'\n      });\n      return;\n    }\n    try {\n      const createdCantiere = await cantieriService.createCantiere({\n        nome: newCantiereData.nome,\n        descrizione: newCantiereData.descrizione,\n        password_cantiere: newCantiereData.password_cantiere\n      });\n\n      // Aggiorna la lista dei cantieri\n      setCantieri([...cantieri, createdCantiere]);\n\n      // Chiudi il dialog\n      handleCloseCreateDialog();\n\n      // Mostra una notifica di successo\n      setNotification({\n        open: true,\n        message: `Cantiere ${createdCantiere.nome} creato con successo! Codice univoco: ${createdCantiere.codice_univoco}`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nella creazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nella creazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce l'eliminazione di un cantiere\n  const handleDeleteCantiere = async () => {\n    if (!selectedCantiere) return;\n    try {\n      await cantieriService.deleteCantiere(selectedCantiere.id_cantiere);\n\n      // Aggiorna la lista dei cantieri\n      setCantieri(cantieri.filter(c => c.id_cantiere !== selectedCantiere.id_cantiere));\n\n      // Chiudi il dialog\n      handleCloseDeleteDialog();\n\n      // Mostra una notifica di successo\n      setNotification({\n        open: true,\n        message: `Cantiere ${selectedCantiere.nome} eliminato con successo!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nell\\'eliminazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce la selezione di un cantiere per la gestione dei cavi\n  const handleSelectCantiere = cantiere => {\n    // Salva l'ID del cantiere selezionato nel localStorage\n    localStorage.setItem('selectedCantiereId', cantiere.id_cantiere);\n    localStorage.setItem('selectedCantiereName', cantiere.nome);\n\n    // Naviga alla pagina di gestione dei cavi\n    window.location.href = '/dashboard/cavi';\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"I Miei Cantieri\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: \"Visualizza e gestisci i tuoi cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 22\n        }, this),\n        onClick: handleOpenCreateDialog,\n        children: \"Nuovo Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Typography, {\n      children: \"Caricamento cantieri...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 9\n    }, this) : cantieri.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Nessun cantiere trovato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: \"Crea un nuovo cantiere per iniziare\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 24\n        }, this),\n        onClick: handleOpenCreateDialog,\n        sx: {\n          mt: 2\n        },\n        children: \"Nuovo Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: cantieri.map(cantiere => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(ConstructionIcon, {\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"div\",\n                children: cantiere.nome\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 1\n              },\n              children: [\"ID: \", cantiere.id_cantiere]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 1\n              },\n              children: [\"Codice Univoco: \", cantiere.codice_univoco]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 19\n            }, this), cantiere.descrizione && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: cantiere.descrizione\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              color: \"primary\",\n              onClick: () => handleSelectCantiere(cantiere),\n              children: \"Gestione Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              color: \"error\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 32\n              }, this),\n              onClick: () => handleOpenDeleteDialog(cantiere),\n              children: \"Elimina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 15\n        }, this)\n      }, cantiere.id_cantiere, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openCreateDialog,\n      onClose: handleCloseCreateDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Crea Nuovo Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: \"Inserisci i dati per creare un nuovo cantiere.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          name: \"nome\",\n          label: \"Nome Cantiere\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.nome,\n          onChange: handleInputChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"descrizione\",\n          label: \"Descrizione\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.descrizione,\n          onChange: handleInputChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"password_cantiere\",\n          label: \"Password\",\n          type: \"password\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.password_cantiere,\n          onChange: handleInputChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"conferma_password\",\n          label: \"Conferma Password\",\n          type: \"password\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.conferma_password,\n          onChange: handleInputChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseCreateDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCreateCantiere,\n          variant: \"contained\",\n          color: \"primary\",\n          children: \"Crea\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDeleteDialog,\n      onClose: handleCloseDeleteDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Elimina Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: [\"ATTENZIONE: Sei sicuro di voler eliminare il cantiere \\\"\", selectedCantiere === null || selectedCantiere === void 0 ? void 0 : selectedCantiere.nome, \"\\\" e tutti i suoi dati? Questa operazione non pu\\xF2 essere annullata.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDeleteDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteCantiere,\n          variant: \"contained\",\n          color: \"error\",\n          children: \"Elimina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: notification.open,\n      autoHideDuration: 6000,\n      onClose: handleCloseNotification,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseNotification,\n        severity: notification.severity,\n        sx: {\n          width: '100%'\n        },\n        children: notification.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 202,\n    columnNumber: 5\n  }, this);\n};\n_s(UserPage, \"NAtMfSLzctBgjYIOrl7HTA97wro=\", false, function () {\n  return [useAuth];\n});\n_c = UserPage;\nexport default UserPage;\nvar _c;\n$RefreshReg$(_c, \"UserPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "TextField", "Snackbar", "<PERSON><PERSON>", "Construction", "ConstructionIcon", "Delete", "DeleteIcon", "Add", "AddIcon", "useAuth", "cantieriService", "jsxDEV", "_jsxDEV", "UserPage", "_s", "user", "cantieri", "set<PERSON><PERSON><PERSON>", "loading", "setLoading", "error", "setError", "openCreateDialog", "setOpenCreateDialog", "openDeleteDialog", "setOpenDeleteDialog", "selected<PERSON><PERSON><PERSON>", "setSelectedCantiere", "newCantiereData", "setNewCantiereData", "nome", "descrizione", "password_cantiere", "conferma_password", "notification", "setNotification", "open", "message", "severity", "fetchCantieri", "data", "getMyCantieri", "err", "console", "handleOpenCreateDialog", "handleCloseCreateDialog", "handleOpenDeleteDialog", "cantiere", "handleCloseDeleteDialog", "handleInputChange", "e", "name", "value", "target", "handleCreateCantiere", "createdCantiere", "createCantiere", "codice_univoco", "handleDeleteCantiere", "deleteCantiere", "id_cantiere", "filter", "c", "handleSelectCantiere", "localStorage", "setItem", "window", "location", "href", "handleCloseNotification", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mb", "display", "justifyContent", "alignItems", "color", "startIcon", "onClick", "length", "p", "textAlign", "mt", "container", "spacing", "map", "item", "xs", "sm", "md", "mr", "component", "size", "onClose", "autoFocus", "margin", "label", "type", "fullWidth", "onChange", "required", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/UserPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Dialog,\n  DialogActions,\n  DialogContent,\n  DialogContentText,\n  DialogTitle,\n  TextField,\n  Snackbar,\n  Alert\n} from '@mui/material';\nimport {\n  Construction as ConstructionIcon,\n  Delete as DeleteIcon,\n  Add as AddIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport cantieriService from '../services/cantieriService';\n\nconst UserPage = () => {\n  const { user } = useAuth();\n  const [cantieri, setCantieri] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openCreateDialog, setOpenCreateDialog] = useState(false);\n  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);\n  const [selectedCantiere, setSelectedCantiere] = useState(null);\n  const [newCantiereData, setNewCantiereData] = useState({\n    nome: '',\n    descrizione: '',\n    password_cantiere: '',\n    conferma_password: ''\n  });\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Carica i cantieri dell'utente corrente\n  useEffect(() => {\n    const fetchCantieri = async () => {\n      try {\n        setLoading(true);\n        const data = await cantieriService.getMyCantieri();\n        setCantieri(data);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cantieri:', err);\n        setError('Impossibile caricare i cantieri. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchCantieri();\n  }, []);\n\n  // Gestisce l'apertura del dialog per creare un nuovo cantiere\n  const handleOpenCreateDialog = () => {\n    setNewCantiereData({\n      nome: '',\n      descrizione: '',\n      password_cantiere: '',\n      conferma_password: ''\n    });\n    setOpenCreateDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per creare un nuovo cantiere\n  const handleCloseCreateDialog = () => {\n    setOpenCreateDialog(false);\n  };\n\n  // Gestisce l'apertura del dialog per eliminare un cantiere\n  const handleOpenDeleteDialog = (cantiere) => {\n    setSelectedCantiere(cantiere);\n    setOpenDeleteDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per eliminare un cantiere\n  const handleCloseDeleteDialog = () => {\n    setOpenDeleteDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce la modifica dei campi del form per creare un nuovo cantiere\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setNewCantiereData({\n      ...newCantiereData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la creazione di un nuovo cantiere\n  const handleCreateCantiere = async () => {\n    // Verifica che i campi obbligatori siano compilati\n    if (!newCantiereData.nome || !newCantiereData.password_cantiere) {\n      setNotification({\n        open: true,\n        message: 'Il nome e la password sono obbligatori',\n        severity: 'error'\n      });\n      return;\n    }\n\n    // Verifica che le password coincidano\n    if (newCantiereData.password_cantiere !== newCantiereData.conferma_password) {\n      setNotification({\n        open: true,\n        message: 'Le password non coincidono',\n        severity: 'error'\n      });\n      return;\n    }\n\n    try {\n      const createdCantiere = await cantieriService.createCantiere({\n        nome: newCantiereData.nome,\n        descrizione: newCantiereData.descrizione,\n        password_cantiere: newCantiereData.password_cantiere\n      });\n\n      // Aggiorna la lista dei cantieri\n      setCantieri([...cantieri, createdCantiere]);\n\n      // Chiudi il dialog\n      handleCloseCreateDialog();\n\n      // Mostra una notifica di successo\n      setNotification({\n        open: true,\n        message: `Cantiere ${createdCantiere.nome} creato con successo! Codice univoco: ${createdCantiere.codice_univoco}`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nella creazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nella creazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce l'eliminazione di un cantiere\n  const handleDeleteCantiere = async () => {\n    if (!selectedCantiere) return;\n\n    try {\n      await cantieriService.deleteCantiere(selectedCantiere.id_cantiere);\n\n      // Aggiorna la lista dei cantieri\n      setCantieri(cantieri.filter(c => c.id_cantiere !== selectedCantiere.id_cantiere));\n\n      // Chiudi il dialog\n      handleCloseDeleteDialog();\n\n      // Mostra una notifica di successo\n      setNotification({\n        open: true,\n        message: `Cantiere ${selectedCantiere.nome} eliminato con successo!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nell\\'eliminazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce la selezione di un cantiere per la gestione dei cavi\n  const handleSelectCantiere = (cantiere) => {\n    // Salva l'ID del cantiere selezionato nel localStorage\n    localStorage.setItem('selectedCantiereId', cantiere.id_cantiere);\n    localStorage.setItem('selectedCantiereName', cantiere.nome);\n\n    // Naviga alla pagina di gestione dei cavi\n    window.location.href = '/dashboard/cavi';\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>\n        I Miei Cantieri\n      </Typography>\n\n      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <Typography variant=\"body1\">\n          Visualizza e gestisci i tuoi cantieri\n        </Typography>\n        <Button\n          variant=\"contained\"\n          color=\"primary\"\n          startIcon={<AddIcon />}\n          onClick={handleOpenCreateDialog}\n        >\n          Nuovo Cantiere\n        </Button>\n      </Box>\n\n      {loading ? (\n        <Typography>Caricamento cantieri...</Typography>\n      ) : error ? (\n        <Alert severity=\"error\">{error}</Alert>\n      ) : cantieri.length === 0 ? (\n        <Paper sx={{ p: 3, textAlign: 'center' }}>\n          <Typography variant=\"h6\">Nessun cantiere trovato</Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Crea un nuovo cantiere per iniziare\n          </Typography>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<AddIcon />}\n            onClick={handleOpenCreateDialog}\n            sx={{ mt: 2 }}\n          >\n            Nuovo Cantiere\n          </Button>\n        </Paper>\n      ) : (\n        <Grid container spacing={3}>\n          {cantieri.map((cantiere) => (\n            <Grid item xs={12} sm={6} md={4} key={cantiere.id_cantiere}>\n              <Card>\n                <CardContent>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                    <ConstructionIcon color=\"primary\" sx={{ mr: 1 }} />\n                    <Typography variant=\"h6\" component=\"div\">\n                      {cantiere.nome}\n                    </Typography>\n                  </Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                    ID: {cantiere.id_cantiere}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                    Codice Univoco: {cantiere.codice_univoco}\n                  </Typography>\n                  {cantiere.descrizione && (\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {cantiere.descrizione}\n                    </Typography>\n                  )}\n                </CardContent>\n                <CardActions>\n                  <Button\n                    size=\"small\"\n                    color=\"primary\"\n                    onClick={() => handleSelectCantiere(cantiere)}\n                  >\n                    Gestione Cavi\n                  </Button>\n                  <Button\n                    size=\"small\"\n                    color=\"error\"\n                    startIcon={<DeleteIcon />}\n                    onClick={() => handleOpenDeleteDialog(cantiere)}\n                  >\n                    Elimina\n                  </Button>\n                </CardActions>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      )}\n\n      {/* Dialog per creare un nuovo cantiere */}\n      <Dialog open={openCreateDialog} onClose={handleCloseCreateDialog}>\n        <DialogTitle>Crea Nuovo Cantiere</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Inserisci i dati per creare un nuovo cantiere.\n          </DialogContentText>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            name=\"nome\"\n            label=\"Nome Cantiere\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.nome}\n            onChange={handleInputChange}\n            required\n          />\n          <TextField\n            margin=\"dense\"\n            name=\"descrizione\"\n            label=\"Descrizione\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.descrizione}\n            onChange={handleInputChange}\n          />\n          <TextField\n            margin=\"dense\"\n            name=\"password_cantiere\"\n            label=\"Password\"\n            type=\"password\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.password_cantiere}\n            onChange={handleInputChange}\n            required\n          />\n          <TextField\n            margin=\"dense\"\n            name=\"conferma_password\"\n            label=\"Conferma Password\"\n            type=\"password\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.conferma_password}\n            onChange={handleInputChange}\n            required\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseCreateDialog}>Annulla</Button>\n          <Button onClick={handleCreateCantiere} variant=\"contained\" color=\"primary\">\n            Crea\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per eliminare un cantiere */}\n      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>\n        <DialogTitle>Elimina Cantiere</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            ATTENZIONE: Sei sicuro di voler eliminare il cantiere \"{selectedCantiere?.nome}\" e tutti i suoi dati?\n            Questa operazione non può essere annullata.\n          </DialogContentText>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDeleteDialog}>Annulla</Button>\n          <Button onClick={handleDeleteCantiere} variant=\"contained\" color=\"error\">\n            Elimina\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Notifica */}\n      <Snackbar\n        open={notification.open}\n        autoHideDuration={6000}\n        onClose={handleCloseNotification}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n          {notification.message}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default UserPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,WAAW,EACXC,SAAS,EACTC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SACEC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,GAAG,IAAIC,OAAO,QACT,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,eAAe,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAK,CAAC,GAAGN,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACuC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACyC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC;IACrD6C,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC;IAC/CmD,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACApD,SAAS,CAAC,MAAM;IACd,MAAMqD,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFpB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMqB,IAAI,GAAG,MAAM9B,eAAe,CAAC+B,aAAa,CAAC,CAAC;QAClDxB,WAAW,CAACuB,IAAI,CAAC;MACnB,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZC,OAAO,CAACvB,KAAK,CAAC,sCAAsC,EAAEsB,GAAG,CAAC;QAC1DrB,QAAQ,CAAC,qDAAqD,CAAC;MACjE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDoB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,sBAAsB,GAAGA,CAAA,KAAM;IACnCf,kBAAkB,CAAC;MACjBC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,iBAAiB,EAAE,EAAE;MACrBC,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACFV,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMsB,uBAAuB,GAAGA,CAAA,KAAM;IACpCtB,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMuB,sBAAsB,GAAIC,QAAQ,IAAK;IAC3CpB,mBAAmB,CAACoB,QAAQ,CAAC;IAC7BtB,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMuB,uBAAuB,GAAGA,CAAA,KAAM;IACpCvB,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMsB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCxB,kBAAkB,CAAC;MACjB,GAAGD,eAAe;MAClB,CAACuB,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC;IACA,IAAI,CAAC1B,eAAe,CAACE,IAAI,IAAI,CAACF,eAAe,CAACI,iBAAiB,EAAE;MAC/DG,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,wCAAwC;QACjDC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;IACF;;IAEA;IACA,IAAIV,eAAe,CAACI,iBAAiB,KAAKJ,eAAe,CAACK,iBAAiB,EAAE;MAC3EE,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,4BAA4B;QACrCC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;IACF;IAEA,IAAI;MACF,MAAMiB,eAAe,GAAG,MAAM7C,eAAe,CAAC8C,cAAc,CAAC;QAC3D1B,IAAI,EAAEF,eAAe,CAACE,IAAI;QAC1BC,WAAW,EAAEH,eAAe,CAACG,WAAW;QACxCC,iBAAiB,EAAEJ,eAAe,CAACI;MACrC,CAAC,CAAC;;MAEF;MACAf,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAEuC,eAAe,CAAC,CAAC;;MAE3C;MACAV,uBAAuB,CAAC,CAAC;;MAEzB;MACAV,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,YAAYkB,eAAe,CAACzB,IAAI,yCAAyCyB,eAAe,CAACE,cAAc,EAAE;QAClHnB,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAACvB,KAAK,CAAC,sCAAsC,EAAEsB,GAAG,CAAC;MAC1DP,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,qCAAqC;QAC9CC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMoB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAAChC,gBAAgB,EAAE;IAEvB,IAAI;MACF,MAAMhB,eAAe,CAACiD,cAAc,CAACjC,gBAAgB,CAACkC,WAAW,CAAC;;MAElE;MACA3C,WAAW,CAACD,QAAQ,CAAC6C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,WAAW,KAAKlC,gBAAgB,CAACkC,WAAW,CAAC,CAAC;;MAEjF;MACAZ,uBAAuB,CAAC,CAAC;;MAEzB;MACAb,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,YAAYX,gBAAgB,CAACI,IAAI,0BAA0B;QACpEQ,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAACvB,KAAK,CAAC,yCAAyC,EAAEsB,GAAG,CAAC;MAC7DP,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,wCAAwC;QACjDC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMyB,oBAAoB,GAAIhB,QAAQ,IAAK;IACzC;IACAiB,YAAY,CAACC,OAAO,CAAC,oBAAoB,EAAElB,QAAQ,CAACa,WAAW,CAAC;IAChEI,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAElB,QAAQ,CAACjB,IAAI,CAAC;;IAE3D;IACAoC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,iBAAiB;EAC1C,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpClC,eAAe,CAAC;MACd,GAAGD,YAAY;MACfE,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;EAED,oBACExB,OAAA,CAACzB,GAAG;IAAAmF,QAAA,gBACF1D,OAAA,CAACxB,UAAU;MAACmF,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbhE,OAAA,CAACzB,GAAG;MAAC0F,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAX,QAAA,gBACzF1D,OAAA,CAACxB,UAAU;QAACmF,OAAO,EAAC,OAAO;QAAAD,QAAA,EAAC;MAE5B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbhE,OAAA,CAACtB,MAAM;QACLiF,OAAO,EAAC,WAAW;QACnBW,KAAK,EAAC,SAAS;QACfC,SAAS,eAAEvE,OAAA,CAACJ,OAAO;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBQ,OAAO,EAAExC,sBAAuB;QAAA0B,QAAA,EACjC;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL1D,OAAO,gBACNN,OAAA,CAACxB,UAAU;MAAAkF,QAAA,EAAC;IAAuB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,GAC9CxD,KAAK,gBACPR,OAAA,CAACV,KAAK;MAACoC,QAAQ,EAAC,OAAO;MAAAgC,QAAA,EAAElD;IAAK;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,GACrC5D,QAAQ,CAACqE,MAAM,KAAK,CAAC,gBACvBzE,OAAA,CAACvB,KAAK;MAACwF,EAAE,EAAE;QAAES,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAjB,QAAA,gBACvC1D,OAAA,CAACxB,UAAU;QAACmF,OAAO,EAAC,IAAI;QAAAD,QAAA,EAAC;MAAuB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC7DhE,OAAA,CAACxB,UAAU;QAACmF,OAAO,EAAC,OAAO;QAACW,KAAK,EAAC,gBAAgB;QAAAZ,QAAA,EAAC;MAEnD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbhE,OAAA,CAACtB,MAAM;QACLiF,OAAO,EAAC,WAAW;QACnBW,KAAK,EAAC,SAAS;QACfC,SAAS,eAAEvE,OAAA,CAACJ,OAAO;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBQ,OAAO,EAAExC,sBAAuB;QAChCiC,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAlB,QAAA,EACf;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,gBAERhE,OAAA,CAACrB,IAAI;MAACkG,SAAS;MAACC,OAAO,EAAE,CAAE;MAAApB,QAAA,EACxBtD,QAAQ,CAAC2E,GAAG,CAAE5C,QAAQ,iBACrBnC,OAAA,CAACrB,IAAI;QAACqG,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eAC9B1D,OAAA,CAACpB,IAAI;UAAA8E,QAAA,gBACH1D,OAAA,CAACnB,WAAW;YAAA6E,QAAA,gBACV1D,OAAA,CAACzB,GAAG;cAAC0F,EAAE,EAAE;gBAAEE,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEH,EAAE,EAAE;cAAE,CAAE;cAAAR,QAAA,gBACxD1D,OAAA,CAACR,gBAAgB;gBAAC8E,KAAK,EAAC,SAAS;gBAACL,EAAE,EAAE;kBAAEmB,EAAE,EAAE;gBAAE;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnDhE,OAAA,CAACxB,UAAU;gBAACmF,OAAO,EAAC,IAAI;gBAAC0B,SAAS,EAAC,KAAK;gBAAA3B,QAAA,EACrCvB,QAAQ,CAACjB;cAAI;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNhE,OAAA,CAACxB,UAAU;cAACmF,OAAO,EAAC,OAAO;cAACW,KAAK,EAAC,gBAAgB;cAACL,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAR,QAAA,GAAC,MAC5D,EAACvB,QAAQ,CAACa,WAAW;YAAA;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACbhE,OAAA,CAACxB,UAAU;cAACmF,OAAO,EAAC,OAAO;cAACW,KAAK,EAAC,gBAAgB;cAACL,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAR,QAAA,GAAC,kBAChD,EAACvB,QAAQ,CAACU,cAAc;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,EACZ7B,QAAQ,CAAChB,WAAW,iBACnBnB,OAAA,CAACxB,UAAU;cAACmF,OAAO,EAAC,OAAO;cAACW,KAAK,EAAC,gBAAgB;cAAAZ,QAAA,EAC/CvB,QAAQ,CAAChB;YAAW;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eACdhE,OAAA,CAAClB,WAAW;YAAA4E,QAAA,gBACV1D,OAAA,CAACtB,MAAM;cACL4G,IAAI,EAAC,OAAO;cACZhB,KAAK,EAAC,SAAS;cACfE,OAAO,EAAEA,CAAA,KAAMrB,oBAAoB,CAAChB,QAAQ,CAAE;cAAAuB,QAAA,EAC/C;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThE,OAAA,CAACtB,MAAM;cACL4G,IAAI,EAAC,OAAO;cACZhB,KAAK,EAAC,OAAO;cACbC,SAAS,eAAEvE,OAAA,CAACN,UAAU;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BQ,OAAO,EAAEA,CAAA,KAAMtC,sBAAsB,CAACC,QAAQ,CAAE;cAAAuB,QAAA,EACjD;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GAtC6B7B,QAAQ,CAACa,WAAW;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuCpD,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAGDhE,OAAA,CAACjB,MAAM;MAACyC,IAAI,EAAEd,gBAAiB;MAAC6E,OAAO,EAAEtD,uBAAwB;MAAAyB,QAAA,gBAC/D1D,OAAA,CAACb,WAAW;QAAAuE,QAAA,EAAC;MAAmB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC9ChE,OAAA,CAACf,aAAa;QAAAyE,QAAA,gBACZ1D,OAAA,CAACd,iBAAiB;UAAAwE,QAAA,EAAC;QAEnB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBhE,OAAA,CAACZ,SAAS;UACRoG,SAAS;UACTC,MAAM,EAAC,OAAO;UACdlD,IAAI,EAAC,MAAM;UACXmD,KAAK,EAAC,eAAe;UACrBC,IAAI,EAAC,MAAM;UACXC,SAAS;UACTjC,OAAO,EAAC,UAAU;UAClBnB,KAAK,EAAExB,eAAe,CAACE,IAAK;UAC5B2E,QAAQ,EAAExD,iBAAkB;UAC5ByD,QAAQ;QAAA;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFhE,OAAA,CAACZ,SAAS;UACRqG,MAAM,EAAC,OAAO;UACdlD,IAAI,EAAC,aAAa;UAClBmD,KAAK,EAAC,aAAa;UACnBC,IAAI,EAAC,MAAM;UACXC,SAAS;UACTjC,OAAO,EAAC,UAAU;UAClBnB,KAAK,EAAExB,eAAe,CAACG,WAAY;UACnC0E,QAAQ,EAAExD;QAAkB;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACFhE,OAAA,CAACZ,SAAS;UACRqG,MAAM,EAAC,OAAO;UACdlD,IAAI,EAAC,mBAAmB;UACxBmD,KAAK,EAAC,UAAU;UAChBC,IAAI,EAAC,UAAU;UACfC,SAAS;UACTjC,OAAO,EAAC,UAAU;UAClBnB,KAAK,EAAExB,eAAe,CAACI,iBAAkB;UACzCyE,QAAQ,EAAExD,iBAAkB;UAC5ByD,QAAQ;QAAA;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFhE,OAAA,CAACZ,SAAS;UACRqG,MAAM,EAAC,OAAO;UACdlD,IAAI,EAAC,mBAAmB;UACxBmD,KAAK,EAAC,mBAAmB;UACzBC,IAAI,EAAC,UAAU;UACfC,SAAS;UACTjC,OAAO,EAAC,UAAU;UAClBnB,KAAK,EAAExB,eAAe,CAACK,iBAAkB;UACzCwE,QAAQ,EAAExD,iBAAkB;UAC5ByD,QAAQ;QAAA;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBhE,OAAA,CAAChB,aAAa;QAAA0E,QAAA,gBACZ1D,OAAA,CAACtB,MAAM;UAAC8F,OAAO,EAAEvC,uBAAwB;UAAAyB,QAAA,EAAC;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC1DhE,OAAA,CAACtB,MAAM;UAAC8F,OAAO,EAAE9B,oBAAqB;UAACiB,OAAO,EAAC,WAAW;UAACW,KAAK,EAAC,SAAS;UAAAZ,QAAA,EAAC;QAE3E;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGThE,OAAA,CAACjB,MAAM;MAACyC,IAAI,EAAEZ,gBAAiB;MAAC2E,OAAO,EAAEnD,uBAAwB;MAAAsB,QAAA,gBAC/D1D,OAAA,CAACb,WAAW;QAAAuE,QAAA,EAAC;MAAgB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC3ChE,OAAA,CAACf,aAAa;QAAAyE,QAAA,eACZ1D,OAAA,CAACd,iBAAiB;UAAAwE,QAAA,GAAC,0DACsC,EAAC5C,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEI,IAAI,EAAC,wEAEjF;QAAA;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChBhE,OAAA,CAAChB,aAAa;QAAA0E,QAAA,gBACZ1D,OAAA,CAACtB,MAAM;UAAC8F,OAAO,EAAEpC,uBAAwB;UAAAsB,QAAA,EAAC;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC1DhE,OAAA,CAACtB,MAAM;UAAC8F,OAAO,EAAE1B,oBAAqB;UAACa,OAAO,EAAC,WAAW;UAACW,KAAK,EAAC,OAAO;UAAAZ,QAAA,EAAC;QAEzE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGThE,OAAA,CAACX,QAAQ;MACPmC,IAAI,EAAEF,YAAY,CAACE,IAAK;MACxBuE,gBAAgB,EAAE,IAAK;MACvBR,OAAO,EAAE9B,uBAAwB;MACjCuC,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAxC,QAAA,eAE3D1D,OAAA,CAACV,KAAK;QAACiG,OAAO,EAAE9B,uBAAwB;QAAC/B,QAAQ,EAAEJ,YAAY,CAACI,QAAS;QAACuC,EAAE,EAAE;UAAEkC,KAAK,EAAE;QAAO,CAAE;QAAAzC,QAAA,EAC7FpC,YAAY,CAACG;MAAO;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAC9D,EAAA,CA9VID,QAAQ;EAAA,QACKJ,OAAO;AAAA;AAAAuG,EAAA,GADpBnG,QAAQ;AAgWd,eAAeA,QAAQ;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}