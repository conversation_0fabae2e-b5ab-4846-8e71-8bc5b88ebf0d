{"ast": null, "code": "var _Pie;\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _callSuper(t, o, e) {\n  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n/**\n * @fileOverview Render sectors of a pie\n */\nimport React, { PureComponent } from 'react';\nimport Animate from 'react-smooth';\nimport get from 'lodash/get';\nimport isEqual from 'lodash/isEqual';\nimport isNil from 'lodash/isNil';\nimport isFunction from 'lodash/isFunction';\nimport clsx from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { Curve } from '../shape/Curve';\nimport { Text } from '../component/Text';\nimport { Label } from '../component/Label';\nimport { LabelList } from '../component/LabelList';\nimport { Cell } from '../component/Cell';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { polarToCartesian, getMaxRadius } from '../util/PolarUtils';\nimport { isNumber, getPercentValue, mathSign, interpolateNumber, uniqueId } from '../util/DataUtils';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { warn } from '../util/LogUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { Shape } from '../util/ActiveShapeUtils';\nexport var Pie = /*#__PURE__*/function (_PureComponent) {\n  function Pie(props) {\n    var _this;\n    _classCallCheck(this, Pie);\n    _this = _callSuper(this, Pie, [props]);\n    _defineProperty(_this, \"pieRef\", null);\n    _defineProperty(_this, \"sectorRefs\", []);\n    _defineProperty(_this, \"id\", uniqueId('recharts-pie-'));\n    _defineProperty(_this, \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (isFunction(onAnimationEnd)) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_this, \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (isFunction(onAnimationStart)) {\n        onAnimationStart();\n      }\n    });\n    _this.state = {\n      isAnimationFinished: !props.isAnimationActive,\n      prevIsAnimationActive: props.isAnimationActive,\n      prevAnimationId: props.animationId,\n      sectorToFocus: 0\n    };\n    return _this;\n  }\n  _inherits(Pie, _PureComponent);\n  return _createClass(Pie, [{\n    key: \"isActiveIndex\",\n    value: function isActiveIndex(i) {\n      var activeIndex = this.props.activeIndex;\n      if (Array.isArray(activeIndex)) {\n        return activeIndex.indexOf(i) !== -1;\n      }\n      return i === activeIndex;\n    }\n  }, {\n    key: \"hasActiveIndex\",\n    value: function hasActiveIndex() {\n      var activeIndex = this.props.activeIndex;\n      return Array.isArray(activeIndex) ? activeIndex.length !== 0 : activeIndex || activeIndex === 0;\n    }\n  }, {\n    key: \"renderLabels\",\n    value: function renderLabels(sectors) {\n      var isAnimationActive = this.props.isAnimationActive;\n      if (isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props = this.props,\n        label = _this$props.label,\n        labelLine = _this$props.labelLine,\n        dataKey = _this$props.dataKey,\n        valueKey = _this$props.valueKey;\n      var pieProps = filterProps(this.props, false);\n      var customLabelProps = filterProps(label, false);\n      var customLabelLineProps = filterProps(labelLine, false);\n      var offsetRadius = label && label.offsetRadius || 20;\n      var labels = sectors.map(function (entry, i) {\n        var midAngle = (entry.startAngle + entry.endAngle) / 2;\n        var endPoint = polarToCartesian(entry.cx, entry.cy, entry.outerRadius + offsetRadius, midAngle);\n        var labelProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, pieProps), entry), {}, {\n          stroke: 'none'\n        }, customLabelProps), {}, {\n          index: i,\n          textAnchor: Pie.getTextAnchor(endPoint.x, entry.cx)\n        }, endPoint);\n        var lineProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, pieProps), entry), {}, {\n          fill: 'none',\n          stroke: entry.fill\n        }, customLabelLineProps), {}, {\n          index: i,\n          points: [polarToCartesian(entry.cx, entry.cy, entry.outerRadius, midAngle), endPoint]\n        });\n        var realDataKey = dataKey;\n        // TODO: compatible to lower versions\n        if (isNil(dataKey) && isNil(valueKey)) {\n          realDataKey = 'value';\n        } else if (isNil(dataKey)) {\n          realDataKey = valueKey;\n        }\n        return (/*#__PURE__*/\n          // eslint-disable-next-line react/no-array-index-key\n          React.createElement(Layer, {\n            key: \"label-\".concat(entry.startAngle, \"-\").concat(entry.endAngle, \"-\").concat(entry.midAngle, \"-\").concat(i)\n          }, labelLine && Pie.renderLabelLineItem(labelLine, lineProps, 'line'), Pie.renderLabelItem(label, labelProps, getValueByDataKey(entry, realDataKey)))\n        );\n      });\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-pie-labels\"\n      }, labels);\n    }\n  }, {\n    key: \"renderSectorsStatically\",\n    value: function renderSectorsStatically(sectors) {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        activeShape = _this$props2.activeShape,\n        blendStroke = _this$props2.blendStroke,\n        inactiveShapeProp = _this$props2.inactiveShape;\n      return sectors.map(function (entry, i) {\n        if ((entry === null || entry === void 0 ? void 0 : entry.startAngle) === 0 && (entry === null || entry === void 0 ? void 0 : entry.endAngle) === 0 && sectors.length !== 1) return null;\n        var isActive = _this2.isActiveIndex(i);\n        var inactiveShape = inactiveShapeProp && _this2.hasActiveIndex() ? inactiveShapeProp : null;\n        var sectorOptions = isActive ? activeShape : inactiveShape;\n        var sectorProps = _objectSpread(_objectSpread({}, entry), {}, {\n          stroke: blendStroke ? entry.fill : entry.stroke,\n          tabIndex: -1\n        });\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          ref: function ref(_ref) {\n            if (_ref && !_this2.sectorRefs.includes(_ref)) {\n              _this2.sectorRefs.push(_ref);\n            }\n          },\n          tabIndex: -1,\n          className: \"recharts-pie-sector\"\n        }, adaptEventsOfChild(_this2.props, entry, i), {\n          // eslint-disable-next-line react/no-array-index-key\n          key: \"sector-\".concat(entry === null || entry === void 0 ? void 0 : entry.startAngle, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.endAngle, \"-\").concat(entry.midAngle, \"-\").concat(i)\n        }), /*#__PURE__*/React.createElement(Shape, _extends({\n          option: sectorOptions,\n          isActive: isActive,\n          shapeType: \"sector\"\n        }, sectorProps)));\n      });\n    }\n  }, {\n    key: \"renderSectorsWithAnimation\",\n    value: function renderSectorsWithAnimation() {\n      var _this3 = this;\n      var _this$props3 = this.props,\n        sectors = _this$props3.sectors,\n        isAnimationActive = _this$props3.isAnimationActive,\n        animationBegin = _this$props3.animationBegin,\n        animationDuration = _this$props3.animationDuration,\n        animationEasing = _this$props3.animationEasing,\n        animationId = _this$props3.animationId;\n      var _this$state = this.state,\n        prevSectors = _this$state.prevSectors,\n        prevIsAnimationActive = _this$state.prevIsAnimationActive;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"pie-\".concat(animationId, \"-\").concat(prevIsAnimationActive),\n        onAnimationStart: this.handleAnimationStart,\n        onAnimationEnd: this.handleAnimationEnd\n      }, function (_ref2) {\n        var t = _ref2.t;\n        var stepData = [];\n        var first = sectors && sectors[0];\n        var curAngle = first.startAngle;\n        sectors.forEach(function (entry, index) {\n          var prev = prevSectors && prevSectors[index];\n          var paddingAngle = index > 0 ? get(entry, 'paddingAngle', 0) : 0;\n          if (prev) {\n            var angleIp = interpolateNumber(prev.endAngle - prev.startAngle, entry.endAngle - entry.startAngle);\n            var latest = _objectSpread(_objectSpread({}, entry), {}, {\n              startAngle: curAngle + paddingAngle,\n              endAngle: curAngle + angleIp(t) + paddingAngle\n            });\n            stepData.push(latest);\n            curAngle = latest.endAngle;\n          } else {\n            var endAngle = entry.endAngle,\n              startAngle = entry.startAngle;\n            var interpolatorAngle = interpolateNumber(0, endAngle - startAngle);\n            var deltaAngle = interpolatorAngle(t);\n            var _latest = _objectSpread(_objectSpread({}, entry), {}, {\n              startAngle: curAngle + paddingAngle,\n              endAngle: curAngle + deltaAngle + paddingAngle\n            });\n            stepData.push(_latest);\n            curAngle = _latest.endAngle;\n          }\n        });\n        return /*#__PURE__*/React.createElement(Layer, null, _this3.renderSectorsStatically(stepData));\n      });\n    }\n  }, {\n    key: \"attachKeyboardHandlers\",\n    value: function attachKeyboardHandlers(pieRef) {\n      var _this4 = this;\n      // eslint-disable-next-line no-param-reassign\n      pieRef.onkeydown = function (e) {\n        if (!e.altKey) {\n          switch (e.key) {\n            case 'ArrowLeft':\n              {\n                var next = ++_this4.state.sectorToFocus % _this4.sectorRefs.length;\n                _this4.sectorRefs[next].focus();\n                _this4.setState({\n                  sectorToFocus: next\n                });\n                break;\n              }\n            case 'ArrowRight':\n              {\n                var _next = --_this4.state.sectorToFocus < 0 ? _this4.sectorRefs.length - 1 : _this4.state.sectorToFocus % _this4.sectorRefs.length;\n                _this4.sectorRefs[_next].focus();\n                _this4.setState({\n                  sectorToFocus: _next\n                });\n                break;\n              }\n            case 'Escape':\n              {\n                _this4.sectorRefs[_this4.state.sectorToFocus].blur();\n                _this4.setState({\n                  sectorToFocus: 0\n                });\n                break;\n              }\n            default:\n              {\n                // There is nothing to do here\n              }\n          }\n        }\n      };\n    }\n  }, {\n    key: \"renderSectors\",\n    value: function renderSectors() {\n      var _this$props4 = this.props,\n        sectors = _this$props4.sectors,\n        isAnimationActive = _this$props4.isAnimationActive;\n      var prevSectors = this.state.prevSectors;\n      if (isAnimationActive && sectors && sectors.length && (!prevSectors || !isEqual(prevSectors, sectors))) {\n        return this.renderSectorsWithAnimation();\n      }\n      return this.renderSectorsStatically(sectors);\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (this.pieRef) {\n        this.attachKeyboardHandlers(this.pieRef);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this5 = this;\n      var _this$props5 = this.props,\n        hide = _this$props5.hide,\n        sectors = _this$props5.sectors,\n        className = _this$props5.className,\n        label = _this$props5.label,\n        cx = _this$props5.cx,\n        cy = _this$props5.cy,\n        innerRadius = _this$props5.innerRadius,\n        outerRadius = _this$props5.outerRadius,\n        isAnimationActive = _this$props5.isAnimationActive;\n      var isAnimationFinished = this.state.isAnimationFinished;\n      if (hide || !sectors || !sectors.length || !isNumber(cx) || !isNumber(cy) || !isNumber(innerRadius) || !isNumber(outerRadius)) {\n        return null;\n      }\n      var layerClass = clsx('recharts-pie', className);\n      return /*#__PURE__*/React.createElement(Layer, {\n        tabIndex: this.props.rootTabIndex,\n        className: layerClass,\n        ref: function ref(_ref3) {\n          _this5.pieRef = _ref3;\n        }\n      }, this.renderSectors(), label && this.renderLabels(sectors), Label.renderCallByParent(this.props, null, false), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, sectors, false));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (prevState.prevIsAnimationActive !== nextProps.isAnimationActive) {\n        return {\n          prevIsAnimationActive: nextProps.isAnimationActive,\n          prevAnimationId: nextProps.animationId,\n          curSectors: nextProps.sectors,\n          prevSectors: [],\n          isAnimationFinished: true\n        };\n      }\n      if (nextProps.isAnimationActive && nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curSectors: nextProps.sectors,\n          prevSectors: prevState.curSectors,\n          isAnimationFinished: true\n        };\n      }\n      if (nextProps.sectors !== prevState.curSectors) {\n        return {\n          curSectors: nextProps.sectors,\n          isAnimationFinished: true\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"getTextAnchor\",\n    value: function getTextAnchor(x, cx) {\n      if (x > cx) {\n        return 'start';\n      }\n      if (x < cx) {\n        return 'end';\n      }\n      return 'middle';\n    }\n  }, {\n    key: \"renderLabelLineItem\",\n    value: function renderLabelLineItem(option, props, key) {\n      if (/*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      if (isFunction(option)) {\n        return option(props);\n      }\n      var className = clsx('recharts-pie-label-line', typeof option !== 'boolean' ? option.className : '');\n      return /*#__PURE__*/React.createElement(Curve, _extends({}, props, {\n        key: key,\n        type: \"linear\",\n        className: className\n      }));\n    }\n  }, {\n    key: \"renderLabelItem\",\n    value: function renderLabelItem(option, props, value) {\n      if (/*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      var label = value;\n      if (isFunction(option)) {\n        label = option(props);\n        if (/*#__PURE__*/React.isValidElement(label)) {\n          return label;\n        }\n      }\n      var className = clsx('recharts-pie-label-text', typeof option !== 'boolean' && !isFunction(option) ? option.className : '');\n      return /*#__PURE__*/React.createElement(Text, _extends({}, props, {\n        alignmentBaseline: \"middle\",\n        className: className\n      }), label);\n    }\n  }]);\n}(PureComponent);\n_Pie = Pie;\n_defineProperty(Pie, \"displayName\", 'Pie');\n_defineProperty(Pie, \"defaultProps\", {\n  stroke: '#fff',\n  fill: '#808080',\n  legendType: 'rect',\n  cx: '50%',\n  cy: '50%',\n  startAngle: 0,\n  endAngle: 360,\n  innerRadius: 0,\n  outerRadius: '80%',\n  paddingAngle: 0,\n  labelLine: true,\n  hide: false,\n  minAngle: 0,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 400,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  nameKey: 'name',\n  blendStroke: false,\n  rootTabIndex: 0\n});\n_defineProperty(Pie, \"parseDeltaAngle\", function (startAngle, endAngle) {\n  var sign = mathSign(endAngle - startAngle);\n  var deltaAngle = Math.min(Math.abs(endAngle - startAngle), 360);\n  return sign * deltaAngle;\n});\n_defineProperty(Pie, \"getRealPieData\", function (itemProps) {\n  var data = itemProps.data,\n    children = itemProps.children;\n  var presentationProps = filterProps(itemProps, false);\n  var cells = findAllByType(children, Cell);\n  if (data && data.length) {\n    return data.map(function (entry, index) {\n      return _objectSpread(_objectSpread(_objectSpread({\n        payload: entry\n      }, presentationProps), entry), cells && cells[index] && cells[index].props);\n    });\n  }\n  if (cells && cells.length) {\n    return cells.map(function (cell) {\n      return _objectSpread(_objectSpread({}, presentationProps), cell.props);\n    });\n  }\n  return [];\n});\n_defineProperty(Pie, \"parseCoordinateOfPie\", function (itemProps, offset) {\n  var top = offset.top,\n    left = offset.left,\n    width = offset.width,\n    height = offset.height;\n  var maxPieRadius = getMaxRadius(width, height);\n  var cx = left + getPercentValue(itemProps.cx, width, width / 2);\n  var cy = top + getPercentValue(itemProps.cy, height, height / 2);\n  var innerRadius = getPercentValue(itemProps.innerRadius, maxPieRadius, 0);\n  var outerRadius = getPercentValue(itemProps.outerRadius, maxPieRadius, maxPieRadius * 0.8);\n  var maxRadius = itemProps.maxRadius || Math.sqrt(width * width + height * height) / 2;\n  return {\n    cx: cx,\n    cy: cy,\n    innerRadius: innerRadius,\n    outerRadius: outerRadius,\n    maxRadius: maxRadius\n  };\n});\n_defineProperty(Pie, \"getComposedData\", function (_ref4) {\n  var item = _ref4.item,\n    offset = _ref4.offset;\n  var itemProps = item.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, item.type.defaultProps), item.props) : item.props;\n  var pieData = _Pie.getRealPieData(itemProps);\n  if (!pieData || !pieData.length) {\n    return null;\n  }\n  var cornerRadius = itemProps.cornerRadius,\n    startAngle = itemProps.startAngle,\n    endAngle = itemProps.endAngle,\n    paddingAngle = itemProps.paddingAngle,\n    dataKey = itemProps.dataKey,\n    nameKey = itemProps.nameKey,\n    valueKey = itemProps.valueKey,\n    tooltipType = itemProps.tooltipType;\n  var minAngle = Math.abs(itemProps.minAngle);\n  var coordinate = _Pie.parseCoordinateOfPie(itemProps, offset);\n  var deltaAngle = _Pie.parseDeltaAngle(startAngle, endAngle);\n  var absDeltaAngle = Math.abs(deltaAngle);\n  var realDataKey = dataKey;\n  if (isNil(dataKey) && isNil(valueKey)) {\n    warn(false, \"Use \\\"dataKey\\\" to specify the value of pie,\\n      the props \\\"valueKey\\\" will be deprecated in 1.1.0\");\n    realDataKey = 'value';\n  } else if (isNil(dataKey)) {\n    warn(false, \"Use \\\"dataKey\\\" to specify the value of pie,\\n      the props \\\"valueKey\\\" will be deprecated in 1.1.0\");\n    realDataKey = valueKey;\n  }\n  var notZeroItemCount = pieData.filter(function (entry) {\n    return getValueByDataKey(entry, realDataKey, 0) !== 0;\n  }).length;\n  var totalPadingAngle = (absDeltaAngle >= 360 ? notZeroItemCount : notZeroItemCount - 1) * paddingAngle;\n  var realTotalAngle = absDeltaAngle - notZeroItemCount * minAngle - totalPadingAngle;\n  var sum = pieData.reduce(function (result, entry) {\n    var val = getValueByDataKey(entry, realDataKey, 0);\n    return result + (isNumber(val) ? val : 0);\n  }, 0);\n  var sectors;\n  if (sum > 0) {\n    var prev;\n    sectors = pieData.map(function (entry, i) {\n      var val = getValueByDataKey(entry, realDataKey, 0);\n      var name = getValueByDataKey(entry, nameKey, i);\n      var percent = (isNumber(val) ? val : 0) / sum;\n      var tempStartAngle;\n      if (i) {\n        tempStartAngle = prev.endAngle + mathSign(deltaAngle) * paddingAngle * (val !== 0 ? 1 : 0);\n      } else {\n        tempStartAngle = startAngle;\n      }\n      var tempEndAngle = tempStartAngle + mathSign(deltaAngle) * ((val !== 0 ? minAngle : 0) + percent * realTotalAngle);\n      var midAngle = (tempStartAngle + tempEndAngle) / 2;\n      var middleRadius = (coordinate.innerRadius + coordinate.outerRadius) / 2;\n      var tooltipPayload = [{\n        name: name,\n        value: val,\n        payload: entry,\n        dataKey: realDataKey,\n        type: tooltipType\n      }];\n      var tooltipPosition = polarToCartesian(coordinate.cx, coordinate.cy, middleRadius, midAngle);\n      prev = _objectSpread(_objectSpread(_objectSpread({\n        percent: percent,\n        cornerRadius: cornerRadius,\n        name: name,\n        tooltipPayload: tooltipPayload,\n        midAngle: midAngle,\n        middleRadius: middleRadius,\n        tooltipPosition: tooltipPosition\n      }, entry), coordinate), {}, {\n        value: getValueByDataKey(entry, realDataKey),\n        startAngle: tempStartAngle,\n        endAngle: tempEndAngle,\n        payload: entry,\n        paddingAngle: mathSign(deltaAngle) * paddingAngle\n      });\n      return prev;\n    });\n  }\n  return _objectSpread(_objectSpread({}, coordinate), {}, {\n    sectors: sectors,\n    data: pieData\n  });\n});", "map": {"version": 3, "names": ["_Pie", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "hasOwnProperty", "call", "apply", "ownKeys", "e", "r", "t", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_callSuper", "_getPrototypeOf", "_possibleConstructorReturn", "_isNativeReflectConstruct", "Reflect", "construct", "self", "_assertThisInitialized", "ReferenceError", "Boolean", "valueOf", "setPrototypeOf", "getPrototypeOf", "__proto__", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "p", "obj", "_toPrimitive", "toPrimitive", "String", "Number", "React", "PureComponent", "Animate", "get", "isEqual", "isNil", "isFunction", "clsx", "Layer", "Curve", "Text", "Label", "LabelList", "Cell", "findAllByType", "filterProps", "Global", "polarToCartesian", "getMaxRadius", "isNumber", "getPercentValue", "mathSign", "interpolateNumber", "uniqueId", "getValueByDataKey", "warn", "adaptEventsOfChild", "<PERSON><PERSON><PERSON>", "Pie", "_PureComponent", "_this", "onAnimationEnd", "setState", "isAnimationFinished", "onAnimationStart", "state", "isAnimationActive", "prevIsAnimationActive", "prevAnimationId", "animationId", "sectorToFocus", "isActiveIndex", "activeIndex", "Array", "isArray", "indexOf", "hasActiveIndex", "renderLabels", "sectors", "_this$props", "label", "labelLine", "dataKey", "valueKey", "pieProps", "customLabelProps", "customLabelLineProps", "offsetRadius", "labels", "map", "entry", "midAngle", "startAngle", "endAngle", "endPoint", "cx", "cy", "outerRadius", "labelProps", "stroke", "index", "textAnchor", "getTextAnchor", "x", "lineProps", "fill", "points", "realDataKey", "createElement", "concat", "renderLabelLineItem", "renderLabelItem", "className", "renderSectorsStatically", "_this2", "_this$props2", "activeShape", "blendStroke", "inactiveShapeProp", "inactiveShape", "isActive", "sectorOptions", "sectorProps", "tabIndex", "ref", "_ref", "sectorRefs", "includes", "option", "shapeType", "renderSectorsWithAnimation", "_this3", "_this$props3", "animationBegin", "animationDuration", "animationEasing", "_this$state", "prevSectors", "begin", "duration", "easing", "from", "to", "handleAnimationStart", "handleAnimationEnd", "_ref2", "stepData", "first", "curAngle", "prev", "paddingAngle", "angleIp", "latest", "interpolatorAngle", "deltaAngle", "_latest", "attachKeyboardHandlers", "pieRef", "_this4", "onkeydown", "altKey", "next", "focus", "_next", "blur", "renderSectors", "_this$props4", "componentDidMount", "render", "_this5", "_this$props5", "hide", "innerRadius", "layerClass", "rootTabIndex", "_ref3", "renderCallByParent", "getDerivedStateFromProps", "nextProps", "prevState", "curSectors", "isValidElement", "cloneElement", "type", "alignmentBaseline", "legendType", "minAngle", "isSsr", "<PERSON><PERSON><PERSON>", "sign", "Math", "min", "abs", "itemProps", "data", "children", "presentationProps", "cells", "payload", "cell", "offset", "top", "left", "width", "height", "maxPieRadius", "maxRadius", "sqrt", "_ref4", "item", "defaultProps", "undefined", "pieData", "getRealPieData", "cornerRadius", "tooltipType", "coordinate", "parseCoordinateOfPie", "parseDeltaAngle", "absDeltaAngle", "notZeroItemCount", "totalPadingAngle", "realTotalAngle", "sum", "reduce", "result", "val", "name", "percent", "tempStartAngle", "tempEndAngle", "middleRadius", "tooltipPayload", "tooltipPosition"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/recharts/es6/polar/Pie.js"], "sourcesContent": ["var _Pie;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @fileOverview Render sectors of a pie\n */\nimport React, { PureComponent } from 'react';\nimport Animate from 'react-smooth';\nimport get from 'lodash/get';\nimport isEqual from 'lodash/isEqual';\nimport isNil from 'lodash/isNil';\nimport isFunction from 'lodash/isFunction';\nimport clsx from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { Curve } from '../shape/Curve';\nimport { Text } from '../component/Text';\nimport { Label } from '../component/Label';\nimport { LabelList } from '../component/LabelList';\nimport { Cell } from '../component/Cell';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { polarToCartesian, getMaxRadius } from '../util/PolarUtils';\nimport { isNumber, getPercentValue, mathSign, interpolateNumber, uniqueId } from '../util/DataUtils';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { warn } from '../util/LogUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { Shape } from '../util/ActiveShapeUtils';\nexport var Pie = /*#__PURE__*/function (_PureComponent) {\n  function Pie(props) {\n    var _this;\n    _classCallCheck(this, Pie);\n    _this = _callSuper(this, Pie, [props]);\n    _defineProperty(_this, \"pieRef\", null);\n    _defineProperty(_this, \"sectorRefs\", []);\n    _defineProperty(_this, \"id\", uniqueId('recharts-pie-'));\n    _defineProperty(_this, \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (isFunction(onAnimationEnd)) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_this, \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (isFunction(onAnimationStart)) {\n        onAnimationStart();\n      }\n    });\n    _this.state = {\n      isAnimationFinished: !props.isAnimationActive,\n      prevIsAnimationActive: props.isAnimationActive,\n      prevAnimationId: props.animationId,\n      sectorToFocus: 0\n    };\n    return _this;\n  }\n  _inherits(Pie, _PureComponent);\n  return _createClass(Pie, [{\n    key: \"isActiveIndex\",\n    value: function isActiveIndex(i) {\n      var activeIndex = this.props.activeIndex;\n      if (Array.isArray(activeIndex)) {\n        return activeIndex.indexOf(i) !== -1;\n      }\n      return i === activeIndex;\n    }\n  }, {\n    key: \"hasActiveIndex\",\n    value: function hasActiveIndex() {\n      var activeIndex = this.props.activeIndex;\n      return Array.isArray(activeIndex) ? activeIndex.length !== 0 : activeIndex || activeIndex === 0;\n    }\n  }, {\n    key: \"renderLabels\",\n    value: function renderLabels(sectors) {\n      var isAnimationActive = this.props.isAnimationActive;\n      if (isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props = this.props,\n        label = _this$props.label,\n        labelLine = _this$props.labelLine,\n        dataKey = _this$props.dataKey,\n        valueKey = _this$props.valueKey;\n      var pieProps = filterProps(this.props, false);\n      var customLabelProps = filterProps(label, false);\n      var customLabelLineProps = filterProps(labelLine, false);\n      var offsetRadius = label && label.offsetRadius || 20;\n      var labels = sectors.map(function (entry, i) {\n        var midAngle = (entry.startAngle + entry.endAngle) / 2;\n        var endPoint = polarToCartesian(entry.cx, entry.cy, entry.outerRadius + offsetRadius, midAngle);\n        var labelProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, pieProps), entry), {}, {\n          stroke: 'none'\n        }, customLabelProps), {}, {\n          index: i,\n          textAnchor: Pie.getTextAnchor(endPoint.x, entry.cx)\n        }, endPoint);\n        var lineProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, pieProps), entry), {}, {\n          fill: 'none',\n          stroke: entry.fill\n        }, customLabelLineProps), {}, {\n          index: i,\n          points: [polarToCartesian(entry.cx, entry.cy, entry.outerRadius, midAngle), endPoint]\n        });\n        var realDataKey = dataKey;\n        // TODO: compatible to lower versions\n        if (isNil(dataKey) && isNil(valueKey)) {\n          realDataKey = 'value';\n        } else if (isNil(dataKey)) {\n          realDataKey = valueKey;\n        }\n        return (\n          /*#__PURE__*/\n          // eslint-disable-next-line react/no-array-index-key\n          React.createElement(Layer, {\n            key: \"label-\".concat(entry.startAngle, \"-\").concat(entry.endAngle, \"-\").concat(entry.midAngle, \"-\").concat(i)\n          }, labelLine && Pie.renderLabelLineItem(labelLine, lineProps, 'line'), Pie.renderLabelItem(label, labelProps, getValueByDataKey(entry, realDataKey)))\n        );\n      });\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-pie-labels\"\n      }, labels);\n    }\n  }, {\n    key: \"renderSectorsStatically\",\n    value: function renderSectorsStatically(sectors) {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        activeShape = _this$props2.activeShape,\n        blendStroke = _this$props2.blendStroke,\n        inactiveShapeProp = _this$props2.inactiveShape;\n      return sectors.map(function (entry, i) {\n        if ((entry === null || entry === void 0 ? void 0 : entry.startAngle) === 0 && (entry === null || entry === void 0 ? void 0 : entry.endAngle) === 0 && sectors.length !== 1) return null;\n        var isActive = _this2.isActiveIndex(i);\n        var inactiveShape = inactiveShapeProp && _this2.hasActiveIndex() ? inactiveShapeProp : null;\n        var sectorOptions = isActive ? activeShape : inactiveShape;\n        var sectorProps = _objectSpread(_objectSpread({}, entry), {}, {\n          stroke: blendStroke ? entry.fill : entry.stroke,\n          tabIndex: -1\n        });\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          ref: function ref(_ref) {\n            if (_ref && !_this2.sectorRefs.includes(_ref)) {\n              _this2.sectorRefs.push(_ref);\n            }\n          },\n          tabIndex: -1,\n          className: \"recharts-pie-sector\"\n        }, adaptEventsOfChild(_this2.props, entry, i), {\n          // eslint-disable-next-line react/no-array-index-key\n          key: \"sector-\".concat(entry === null || entry === void 0 ? void 0 : entry.startAngle, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.endAngle, \"-\").concat(entry.midAngle, \"-\").concat(i)\n        }), /*#__PURE__*/React.createElement(Shape, _extends({\n          option: sectorOptions,\n          isActive: isActive,\n          shapeType: \"sector\"\n        }, sectorProps)));\n      });\n    }\n  }, {\n    key: \"renderSectorsWithAnimation\",\n    value: function renderSectorsWithAnimation() {\n      var _this3 = this;\n      var _this$props3 = this.props,\n        sectors = _this$props3.sectors,\n        isAnimationActive = _this$props3.isAnimationActive,\n        animationBegin = _this$props3.animationBegin,\n        animationDuration = _this$props3.animationDuration,\n        animationEasing = _this$props3.animationEasing,\n        animationId = _this$props3.animationId;\n      var _this$state = this.state,\n        prevSectors = _this$state.prevSectors,\n        prevIsAnimationActive = _this$state.prevIsAnimationActive;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"pie-\".concat(animationId, \"-\").concat(prevIsAnimationActive),\n        onAnimationStart: this.handleAnimationStart,\n        onAnimationEnd: this.handleAnimationEnd\n      }, function (_ref2) {\n        var t = _ref2.t;\n        var stepData = [];\n        var first = sectors && sectors[0];\n        var curAngle = first.startAngle;\n        sectors.forEach(function (entry, index) {\n          var prev = prevSectors && prevSectors[index];\n          var paddingAngle = index > 0 ? get(entry, 'paddingAngle', 0) : 0;\n          if (prev) {\n            var angleIp = interpolateNumber(prev.endAngle - prev.startAngle, entry.endAngle - entry.startAngle);\n            var latest = _objectSpread(_objectSpread({}, entry), {}, {\n              startAngle: curAngle + paddingAngle,\n              endAngle: curAngle + angleIp(t) + paddingAngle\n            });\n            stepData.push(latest);\n            curAngle = latest.endAngle;\n          } else {\n            var endAngle = entry.endAngle,\n              startAngle = entry.startAngle;\n            var interpolatorAngle = interpolateNumber(0, endAngle - startAngle);\n            var deltaAngle = interpolatorAngle(t);\n            var _latest = _objectSpread(_objectSpread({}, entry), {}, {\n              startAngle: curAngle + paddingAngle,\n              endAngle: curAngle + deltaAngle + paddingAngle\n            });\n            stepData.push(_latest);\n            curAngle = _latest.endAngle;\n          }\n        });\n        return /*#__PURE__*/React.createElement(Layer, null, _this3.renderSectorsStatically(stepData));\n      });\n    }\n  }, {\n    key: \"attachKeyboardHandlers\",\n    value: function attachKeyboardHandlers(pieRef) {\n      var _this4 = this;\n      // eslint-disable-next-line no-param-reassign\n      pieRef.onkeydown = function (e) {\n        if (!e.altKey) {\n          switch (e.key) {\n            case 'ArrowLeft':\n              {\n                var next = ++_this4.state.sectorToFocus % _this4.sectorRefs.length;\n                _this4.sectorRefs[next].focus();\n                _this4.setState({\n                  sectorToFocus: next\n                });\n                break;\n              }\n            case 'ArrowRight':\n              {\n                var _next = --_this4.state.sectorToFocus < 0 ? _this4.sectorRefs.length - 1 : _this4.state.sectorToFocus % _this4.sectorRefs.length;\n                _this4.sectorRefs[_next].focus();\n                _this4.setState({\n                  sectorToFocus: _next\n                });\n                break;\n              }\n            case 'Escape':\n              {\n                _this4.sectorRefs[_this4.state.sectorToFocus].blur();\n                _this4.setState({\n                  sectorToFocus: 0\n                });\n                break;\n              }\n            default:\n              {\n                // There is nothing to do here\n              }\n          }\n        }\n      };\n    }\n  }, {\n    key: \"renderSectors\",\n    value: function renderSectors() {\n      var _this$props4 = this.props,\n        sectors = _this$props4.sectors,\n        isAnimationActive = _this$props4.isAnimationActive;\n      var prevSectors = this.state.prevSectors;\n      if (isAnimationActive && sectors && sectors.length && (!prevSectors || !isEqual(prevSectors, sectors))) {\n        return this.renderSectorsWithAnimation();\n      }\n      return this.renderSectorsStatically(sectors);\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (this.pieRef) {\n        this.attachKeyboardHandlers(this.pieRef);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this5 = this;\n      var _this$props5 = this.props,\n        hide = _this$props5.hide,\n        sectors = _this$props5.sectors,\n        className = _this$props5.className,\n        label = _this$props5.label,\n        cx = _this$props5.cx,\n        cy = _this$props5.cy,\n        innerRadius = _this$props5.innerRadius,\n        outerRadius = _this$props5.outerRadius,\n        isAnimationActive = _this$props5.isAnimationActive;\n      var isAnimationFinished = this.state.isAnimationFinished;\n      if (hide || !sectors || !sectors.length || !isNumber(cx) || !isNumber(cy) || !isNumber(innerRadius) || !isNumber(outerRadius)) {\n        return null;\n      }\n      var layerClass = clsx('recharts-pie', className);\n      return /*#__PURE__*/React.createElement(Layer, {\n        tabIndex: this.props.rootTabIndex,\n        className: layerClass,\n        ref: function ref(_ref3) {\n          _this5.pieRef = _ref3;\n        }\n      }, this.renderSectors(), label && this.renderLabels(sectors), Label.renderCallByParent(this.props, null, false), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, sectors, false));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (prevState.prevIsAnimationActive !== nextProps.isAnimationActive) {\n        return {\n          prevIsAnimationActive: nextProps.isAnimationActive,\n          prevAnimationId: nextProps.animationId,\n          curSectors: nextProps.sectors,\n          prevSectors: [],\n          isAnimationFinished: true\n        };\n      }\n      if (nextProps.isAnimationActive && nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curSectors: nextProps.sectors,\n          prevSectors: prevState.curSectors,\n          isAnimationFinished: true\n        };\n      }\n      if (nextProps.sectors !== prevState.curSectors) {\n        return {\n          curSectors: nextProps.sectors,\n          isAnimationFinished: true\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"getTextAnchor\",\n    value: function getTextAnchor(x, cx) {\n      if (x > cx) {\n        return 'start';\n      }\n      if (x < cx) {\n        return 'end';\n      }\n      return 'middle';\n    }\n  }, {\n    key: \"renderLabelLineItem\",\n    value: function renderLabelLineItem(option, props, key) {\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      if (isFunction(option)) {\n        return option(props);\n      }\n      var className = clsx('recharts-pie-label-line', typeof option !== 'boolean' ? option.className : '');\n      return /*#__PURE__*/React.createElement(Curve, _extends({}, props, {\n        key: key,\n        type: \"linear\",\n        className: className\n      }));\n    }\n  }, {\n    key: \"renderLabelItem\",\n    value: function renderLabelItem(option, props, value) {\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      var label = value;\n      if (isFunction(option)) {\n        label = option(props);\n        if ( /*#__PURE__*/React.isValidElement(label)) {\n          return label;\n        }\n      }\n      var className = clsx('recharts-pie-label-text', typeof option !== 'boolean' && !isFunction(option) ? option.className : '');\n      return /*#__PURE__*/React.createElement(Text, _extends({}, props, {\n        alignmentBaseline: \"middle\",\n        className: className\n      }), label);\n    }\n  }]);\n}(PureComponent);\n_Pie = Pie;\n_defineProperty(Pie, \"displayName\", 'Pie');\n_defineProperty(Pie, \"defaultProps\", {\n  stroke: '#fff',\n  fill: '#808080',\n  legendType: 'rect',\n  cx: '50%',\n  cy: '50%',\n  startAngle: 0,\n  endAngle: 360,\n  innerRadius: 0,\n  outerRadius: '80%',\n  paddingAngle: 0,\n  labelLine: true,\n  hide: false,\n  minAngle: 0,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 400,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  nameKey: 'name',\n  blendStroke: false,\n  rootTabIndex: 0\n});\n_defineProperty(Pie, \"parseDeltaAngle\", function (startAngle, endAngle) {\n  var sign = mathSign(endAngle - startAngle);\n  var deltaAngle = Math.min(Math.abs(endAngle - startAngle), 360);\n  return sign * deltaAngle;\n});\n_defineProperty(Pie, \"getRealPieData\", function (itemProps) {\n  var data = itemProps.data,\n    children = itemProps.children;\n  var presentationProps = filterProps(itemProps, false);\n  var cells = findAllByType(children, Cell);\n  if (data && data.length) {\n    return data.map(function (entry, index) {\n      return _objectSpread(_objectSpread(_objectSpread({\n        payload: entry\n      }, presentationProps), entry), cells && cells[index] && cells[index].props);\n    });\n  }\n  if (cells && cells.length) {\n    return cells.map(function (cell) {\n      return _objectSpread(_objectSpread({}, presentationProps), cell.props);\n    });\n  }\n  return [];\n});\n_defineProperty(Pie, \"parseCoordinateOfPie\", function (itemProps, offset) {\n  var top = offset.top,\n    left = offset.left,\n    width = offset.width,\n    height = offset.height;\n  var maxPieRadius = getMaxRadius(width, height);\n  var cx = left + getPercentValue(itemProps.cx, width, width / 2);\n  var cy = top + getPercentValue(itemProps.cy, height, height / 2);\n  var innerRadius = getPercentValue(itemProps.innerRadius, maxPieRadius, 0);\n  var outerRadius = getPercentValue(itemProps.outerRadius, maxPieRadius, maxPieRadius * 0.8);\n  var maxRadius = itemProps.maxRadius || Math.sqrt(width * width + height * height) / 2;\n  return {\n    cx: cx,\n    cy: cy,\n    innerRadius: innerRadius,\n    outerRadius: outerRadius,\n    maxRadius: maxRadius\n  };\n});\n_defineProperty(Pie, \"getComposedData\", function (_ref4) {\n  var item = _ref4.item,\n    offset = _ref4.offset;\n  var itemProps = item.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, item.type.defaultProps), item.props) : item.props;\n  var pieData = _Pie.getRealPieData(itemProps);\n  if (!pieData || !pieData.length) {\n    return null;\n  }\n  var cornerRadius = itemProps.cornerRadius,\n    startAngle = itemProps.startAngle,\n    endAngle = itemProps.endAngle,\n    paddingAngle = itemProps.paddingAngle,\n    dataKey = itemProps.dataKey,\n    nameKey = itemProps.nameKey,\n    valueKey = itemProps.valueKey,\n    tooltipType = itemProps.tooltipType;\n  var minAngle = Math.abs(itemProps.minAngle);\n  var coordinate = _Pie.parseCoordinateOfPie(itemProps, offset);\n  var deltaAngle = _Pie.parseDeltaAngle(startAngle, endAngle);\n  var absDeltaAngle = Math.abs(deltaAngle);\n  var realDataKey = dataKey;\n  if (isNil(dataKey) && isNil(valueKey)) {\n    warn(false, \"Use \\\"dataKey\\\" to specify the value of pie,\\n      the props \\\"valueKey\\\" will be deprecated in 1.1.0\");\n    realDataKey = 'value';\n  } else if (isNil(dataKey)) {\n    warn(false, \"Use \\\"dataKey\\\" to specify the value of pie,\\n      the props \\\"valueKey\\\" will be deprecated in 1.1.0\");\n    realDataKey = valueKey;\n  }\n  var notZeroItemCount = pieData.filter(function (entry) {\n    return getValueByDataKey(entry, realDataKey, 0) !== 0;\n  }).length;\n  var totalPadingAngle = (absDeltaAngle >= 360 ? notZeroItemCount : notZeroItemCount - 1) * paddingAngle;\n  var realTotalAngle = absDeltaAngle - notZeroItemCount * minAngle - totalPadingAngle;\n  var sum = pieData.reduce(function (result, entry) {\n    var val = getValueByDataKey(entry, realDataKey, 0);\n    return result + (isNumber(val) ? val : 0);\n  }, 0);\n  var sectors;\n  if (sum > 0) {\n    var prev;\n    sectors = pieData.map(function (entry, i) {\n      var val = getValueByDataKey(entry, realDataKey, 0);\n      var name = getValueByDataKey(entry, nameKey, i);\n      var percent = (isNumber(val) ? val : 0) / sum;\n      var tempStartAngle;\n      if (i) {\n        tempStartAngle = prev.endAngle + mathSign(deltaAngle) * paddingAngle * (val !== 0 ? 1 : 0);\n      } else {\n        tempStartAngle = startAngle;\n      }\n      var tempEndAngle = tempStartAngle + mathSign(deltaAngle) * ((val !== 0 ? minAngle : 0) + percent * realTotalAngle);\n      var midAngle = (tempStartAngle + tempEndAngle) / 2;\n      var middleRadius = (coordinate.innerRadius + coordinate.outerRadius) / 2;\n      var tooltipPayload = [{\n        name: name,\n        value: val,\n        payload: entry,\n        dataKey: realDataKey,\n        type: tooltipType\n      }];\n      var tooltipPosition = polarToCartesian(coordinate.cx, coordinate.cy, middleRadius, midAngle);\n      prev = _objectSpread(_objectSpread(_objectSpread({\n        percent: percent,\n        cornerRadius: cornerRadius,\n        name: name,\n        tooltipPayload: tooltipPayload,\n        midAngle: midAngle,\n        middleRadius: middleRadius,\n        tooltipPosition: tooltipPosition\n      }, entry), coordinate), {}, {\n        value: getValueByDataKey(entry, realDataKey),\n        startAngle: tempStartAngle,\n        endAngle: tempEndAngle,\n        payload: entry,\n        paddingAngle: mathSign(deltaAngle) * paddingAngle\n      });\n      return prev;\n    });\n  }\n  return _objectSpread(_objectSpread({}, coordinate), {}, {\n    sectors: sectors,\n    data: pieData\n  });\n});"], "mappings": "AAAA,IAAIA,IAAI;AACR,SAASC,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AAClV,SAASO,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGf,MAAM,CAACgB,IAAI,CAACH,CAAC,CAAC;EAAE,IAAIb,MAAM,CAACiB,qBAAqB,EAAE;IAAE,IAAIvB,CAAC,GAAGM,MAAM,CAACiB,qBAAqB,CAACJ,CAAC,CAAC;IAAEC,CAAC,KAAKpB,CAAC,GAAGA,CAAC,CAACwB,MAAM,CAAC,UAAUJ,CAAC,EAAE;MAAE,OAAOd,MAAM,CAACmB,wBAAwB,CAACN,CAAC,EAAEC,CAAC,CAAC,CAACM,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEL,CAAC,CAACM,IAAI,CAACV,KAAK,CAACI,CAAC,EAAErB,CAAC,CAAC;EAAE;EAAE,OAAOqB,CAAC;AAAE;AAC9P,SAASO,aAAaA,CAACT,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,SAAS,CAACC,MAAM,EAAEQ,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIV,SAAS,CAACS,CAAC,CAAC,GAAGT,SAAS,CAACS,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACZ,MAAM,CAACe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACQ,OAAO,CAAC,UAAUT,CAAC,EAAE;MAAEU,eAAe,CAACX,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGd,MAAM,CAACyB,yBAAyB,GAAGzB,MAAM,CAAC0B,gBAAgB,CAACb,CAAC,EAAEb,MAAM,CAACyB,yBAAyB,CAACV,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACZ,MAAM,CAACe,CAAC,CAAC,CAAC,CAACQ,OAAO,CAAC,UAAUT,CAAC,EAAE;MAAEd,MAAM,CAAC2B,cAAc,CAACd,CAAC,EAAEC,CAAC,EAAEd,MAAM,CAACmB,wBAAwB,CAACJ,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASe,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAAC7B,MAAM,EAAE8B,KAAK,EAAE;EAAE,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6B,KAAK,CAAC3B,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAI8B,UAAU,GAAGD,KAAK,CAAC7B,CAAC,CAAC;IAAE8B,UAAU,CAACd,UAAU,GAAGc,UAAU,CAACd,UAAU,IAAI,KAAK;IAAEc,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAEpC,MAAM,CAAC2B,cAAc,CAACxB,MAAM,EAAEkC,cAAc,CAACH,UAAU,CAAC1B,GAAG,CAAC,EAAE0B,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASI,YAAYA,CAACR,WAAW,EAAES,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACF,WAAW,CAAChC,SAAS,EAAEyC,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACF,WAAW,EAAEU,WAAW,CAAC;EAAExC,MAAM,CAAC2B,cAAc,CAACG,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASW,UAAUA,CAAC1B,CAAC,EAAErB,CAAC,EAAEmB,CAAC,EAAE;EAAE,OAAOnB,CAAC,GAAGgD,eAAe,CAAChD,CAAC,CAAC,EAAEiD,0BAA0B,CAAC5B,CAAC,EAAE6B,yBAAyB,CAAC,CAAC,GAAGC,OAAO,CAACC,SAAS,CAACpD,CAAC,EAAEmB,CAAC,IAAI,EAAE,EAAE6B,eAAe,CAAC3B,CAAC,CAAC,CAAClB,WAAW,CAAC,GAAGH,CAAC,CAACiB,KAAK,CAACI,CAAC,EAAEF,CAAC,CAAC,CAAC;AAAE;AAC1M,SAAS8B,0BAA0BA,CAACI,IAAI,EAAErC,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKjB,OAAO,CAACiB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIqB,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOiB,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI;IAAE,IAAI7B,CAAC,GAAG,CAACmC,OAAO,CAACpD,SAAS,CAACqD,OAAO,CAACzC,IAAI,CAACmC,OAAO,CAACC,SAAS,CAACI,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC,OAAOnC,CAAC,EAAE,CAAC;EAAE,OAAO,CAAC6B,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IAAE,OAAO,CAAC,CAAC7B,CAAC;EAAE,CAAC,EAAE,CAAC;AAAE;AAClP,SAAS2B,eAAeA,CAAChD,CAAC,EAAE;EAAEgD,eAAe,GAAG1C,MAAM,CAACoD,cAAc,GAAGpD,MAAM,CAACqD,cAAc,CAACnD,IAAI,CAAC,CAAC,GAAG,SAASwC,eAAeA,CAAChD,CAAC,EAAE;IAAE,OAAOA,CAAC,CAAC4D,SAAS,IAAItD,MAAM,CAACqD,cAAc,CAAC3D,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOgD,eAAe,CAAChD,CAAC,CAAC;AAAE;AACnN,SAAS6D,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAI1B,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEyB,QAAQ,CAAC1D,SAAS,GAAGE,MAAM,CAAC0D,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAC3D,SAAS,EAAE;IAAED,WAAW,EAAE;MAAE8D,KAAK,EAAEH,QAAQ;MAAEpB,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEnC,MAAM,CAAC2B,cAAc,CAAC6B,QAAQ,EAAE,WAAW,EAAE;IAAEpB,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIqB,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAAClE,CAAC,EAAEmE,CAAC,EAAE;EAAED,eAAe,GAAG5D,MAAM,CAACoD,cAAc,GAAGpD,MAAM,CAACoD,cAAc,CAAClD,IAAI,CAAC,CAAC,GAAG,SAAS0D,eAAeA,CAAClE,CAAC,EAAEmE,CAAC,EAAE;IAAEnE,CAAC,CAAC4D,SAAS,GAAGO,CAAC;IAAE,OAAOnE,CAAC;EAAE,CAAC;EAAE,OAAOkE,eAAe,CAAClE,CAAC,EAAEmE,CAAC,CAAC;AAAE;AACvM,SAASrC,eAAeA,CAACsC,GAAG,EAAEtD,GAAG,EAAEmD,KAAK,EAAE;EAAEnD,GAAG,GAAG6B,cAAc,CAAC7B,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIsD,GAAG,EAAE;IAAE9D,MAAM,CAAC2B,cAAc,CAACmC,GAAG,EAAEtD,GAAG,EAAE;MAAEmD,KAAK,EAAEA,KAAK;MAAEvC,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAE0B,GAAG,CAACtD,GAAG,CAAC,GAAGmD,KAAK;EAAE;EAAE,OAAOG,GAAG;AAAE;AAC3O,SAASzB,cAAcA,CAACtB,CAAC,EAAE;EAAE,IAAIX,CAAC,GAAG2D,YAAY,CAAChD,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAItB,OAAO,CAACW,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAAS2D,YAAYA,CAAChD,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIrB,OAAO,CAACsB,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACpB,MAAM,CAACqE,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKnD,CAAC,EAAE;IAAE,IAAIT,CAAC,GAAGS,CAAC,CAACH,IAAI,CAACK,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIrB,OAAO,CAACW,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAI2B,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKjB,CAAC,GAAGmD,MAAM,GAAGC,MAAM,EAAEnD,CAAC,CAAC;AAAE;AAC3T;AACA;AACA;AACA,OAAOoD,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,GAAG,MAAM,YAAY;AAC5B,OAAOC,OAAO,MAAM,gBAAgB;AACpC,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,aAAa,EAAEC,WAAW,QAAQ,oBAAoB;AAC/D,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,oBAAoB;AACnE,SAASC,QAAQ,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,QAAQ,QAAQ,mBAAmB;AACpG,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,KAAK,QAAQ,0BAA0B;AAChD,OAAO,IAAIC,GAAG,GAAG,aAAa,UAAUC,cAAc,EAAE;EACtD,SAASD,GAAGA,CAAC9D,KAAK,EAAE;IAClB,IAAIgE,KAAK;IACTrE,eAAe,CAAC,IAAI,EAAEmE,GAAG,CAAC;IAC1BE,KAAK,GAAGxD,UAAU,CAAC,IAAI,EAAEsD,GAAG,EAAE,CAAC9D,KAAK,CAAC,CAAC;IACtCT,eAAe,CAACyE,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC;IACtCzE,eAAe,CAACyE,KAAK,EAAE,YAAY,EAAE,EAAE,CAAC;IACxCzE,eAAe,CAACyE,KAAK,EAAE,IAAI,EAAEP,QAAQ,CAAC,eAAe,CAAC,CAAC;IACvDlE,eAAe,CAACyE,KAAK,EAAE,oBAAoB,EAAE,YAAY;MACvD,IAAIC,cAAc,GAAGD,KAAK,CAAChE,KAAK,CAACiE,cAAc;MAC/CD,KAAK,CAACE,QAAQ,CAAC;QACbC,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAI3B,UAAU,CAACyB,cAAc,CAAC,EAAE;QAC9BA,cAAc,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IACF1E,eAAe,CAACyE,KAAK,EAAE,sBAAsB,EAAE,YAAY;MACzD,IAAII,gBAAgB,GAAGJ,KAAK,CAAChE,KAAK,CAACoE,gBAAgB;MACnDJ,KAAK,CAACE,QAAQ,CAAC;QACbC,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAI3B,UAAU,CAAC4B,gBAAgB,CAAC,EAAE;QAChCA,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IACFJ,KAAK,CAACK,KAAK,GAAG;MACZF,mBAAmB,EAAE,CAACnE,KAAK,CAACsE,iBAAiB;MAC7CC,qBAAqB,EAAEvE,KAAK,CAACsE,iBAAiB;MAC9CE,eAAe,EAAExE,KAAK,CAACyE,WAAW;MAClCC,aAAa,EAAE;IACjB,CAAC;IACD,OAAOV,KAAK;EACd;EACA1C,SAAS,CAACwC,GAAG,EAAEC,cAAc,CAAC;EAC9B,OAAO1D,YAAY,CAACyD,GAAG,EAAE,CAAC;IACxBvF,GAAG,EAAE,eAAe;IACpBmD,KAAK,EAAE,SAASiD,aAAaA,CAACxG,CAAC,EAAE;MAC/B,IAAIyG,WAAW,GAAG,IAAI,CAAC5E,KAAK,CAAC4E,WAAW;MACxC,IAAIC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;QAC9B,OAAOA,WAAW,CAACG,OAAO,CAAC5G,CAAC,CAAC,KAAK,CAAC,CAAC;MACtC;MACA,OAAOA,CAAC,KAAKyG,WAAW;IAC1B;EACF,CAAC,EAAE;IACDrG,GAAG,EAAE,gBAAgB;IACrBmD,KAAK,EAAE,SAASsD,cAAcA,CAAA,EAAG;MAC/B,IAAIJ,WAAW,GAAG,IAAI,CAAC5E,KAAK,CAAC4E,WAAW;MACxC,OAAOC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,GAAGA,WAAW,CAACvG,MAAM,KAAK,CAAC,GAAGuG,WAAW,IAAIA,WAAW,KAAK,CAAC;IACjG;EACF,CAAC,EAAE;IACDrG,GAAG,EAAE,cAAc;IACnBmD,KAAK,EAAE,SAASuD,YAAYA,CAACC,OAAO,EAAE;MACpC,IAAIZ,iBAAiB,GAAG,IAAI,CAACtE,KAAK,CAACsE,iBAAiB;MACpD,IAAIA,iBAAiB,IAAI,CAAC,IAAI,CAACD,KAAK,CAACF,mBAAmB,EAAE;QACxD,OAAO,IAAI;MACb;MACA,IAAIgB,WAAW,GAAG,IAAI,CAACnF,KAAK;QAC1BoF,KAAK,GAAGD,WAAW,CAACC,KAAK;QACzBC,SAAS,GAAGF,WAAW,CAACE,SAAS;QACjCC,OAAO,GAAGH,WAAW,CAACG,OAAO;QAC7BC,QAAQ,GAAGJ,WAAW,CAACI,QAAQ;MACjC,IAAIC,QAAQ,GAAGvC,WAAW,CAAC,IAAI,CAACjD,KAAK,EAAE,KAAK,CAAC;MAC7C,IAAIyF,gBAAgB,GAAGxC,WAAW,CAACmC,KAAK,EAAE,KAAK,CAAC;MAChD,IAAIM,oBAAoB,GAAGzC,WAAW,CAACoC,SAAS,EAAE,KAAK,CAAC;MACxD,IAAIM,YAAY,GAAGP,KAAK,IAAIA,KAAK,CAACO,YAAY,IAAI,EAAE;MACpD,IAAIC,MAAM,GAAGV,OAAO,CAACW,GAAG,CAAC,UAAUC,KAAK,EAAE3H,CAAC,EAAE;QAC3C,IAAI4H,QAAQ,GAAG,CAACD,KAAK,CAACE,UAAU,GAAGF,KAAK,CAACG,QAAQ,IAAI,CAAC;QACtD,IAAIC,QAAQ,GAAG/C,gBAAgB,CAAC2C,KAAK,CAACK,EAAE,EAAEL,KAAK,CAACM,EAAE,EAAEN,KAAK,CAACO,WAAW,GAAGV,YAAY,EAAEI,QAAQ,CAAC;QAC/F,IAAIO,UAAU,GAAGjH,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmG,QAAQ,CAAC,EAAEM,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UAClGS,MAAM,EAAE;QACV,CAAC,EAAEd,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;UACxBe,KAAK,EAAErI,CAAC;UACRsI,UAAU,EAAE3C,GAAG,CAAC4C,aAAa,CAACR,QAAQ,CAACS,CAAC,EAAEb,KAAK,CAACK,EAAE;QACpD,CAAC,EAAED,QAAQ,CAAC;QACZ,IAAIU,SAAS,GAAGvH,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmG,QAAQ,CAAC,EAAEM,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjGe,IAAI,EAAE,MAAM;UACZN,MAAM,EAAET,KAAK,CAACe;QAChB,CAAC,EAAEnB,oBAAoB,CAAC,EAAE,CAAC,CAAC,EAAE;UAC5Bc,KAAK,EAAErI,CAAC;UACR2I,MAAM,EAAE,CAAC3D,gBAAgB,CAAC2C,KAAK,CAACK,EAAE,EAAEL,KAAK,CAACM,EAAE,EAAEN,KAAK,CAACO,WAAW,EAAEN,QAAQ,CAAC,EAAEG,QAAQ;QACtF,CAAC,CAAC;QACF,IAAIa,WAAW,GAAGzB,OAAO;QACzB;QACA,IAAI/C,KAAK,CAAC+C,OAAO,CAAC,IAAI/C,KAAK,CAACgD,QAAQ,CAAC,EAAE;UACrCwB,WAAW,GAAG,OAAO;QACvB,CAAC,MAAM,IAAIxE,KAAK,CAAC+C,OAAO,CAAC,EAAE;UACzByB,WAAW,GAAGxB,QAAQ;QACxB;QACA,QACE;UACA;UACArD,KAAK,CAAC8E,aAAa,CAACtE,KAAK,EAAE;YACzBnE,GAAG,EAAE,QAAQ,CAAC0I,MAAM,CAACnB,KAAK,CAACE,UAAU,EAAE,GAAG,CAAC,CAACiB,MAAM,CAACnB,KAAK,CAACG,QAAQ,EAAE,GAAG,CAAC,CAACgB,MAAM,CAACnB,KAAK,CAACC,QAAQ,EAAE,GAAG,CAAC,CAACkB,MAAM,CAAC9I,CAAC;UAC9G,CAAC,EAAEkH,SAAS,IAAIvB,GAAG,CAACoD,mBAAmB,CAAC7B,SAAS,EAAEuB,SAAS,EAAE,MAAM,CAAC,EAAE9C,GAAG,CAACqD,eAAe,CAAC/B,KAAK,EAAEkB,UAAU,EAAE5C,iBAAiB,CAACoC,KAAK,EAAEiB,WAAW,CAAC,CAAC;QAAC;MAEzJ,CAAC,CAAC;MACF,OAAO,aAAa7E,KAAK,CAAC8E,aAAa,CAACtE,KAAK,EAAE;QAC7C0E,SAAS,EAAE;MACb,CAAC,EAAExB,MAAM,CAAC;IACZ;EACF,CAAC,EAAE;IACDrH,GAAG,EAAE,yBAAyB;IAC9BmD,KAAK,EAAE,SAAS2F,uBAAuBA,CAACnC,OAAO,EAAE;MAC/C,IAAIoC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAACvH,KAAK;QAC3BwH,WAAW,GAAGD,YAAY,CAACC,WAAW;QACtCC,WAAW,GAAGF,YAAY,CAACE,WAAW;QACtCC,iBAAiB,GAAGH,YAAY,CAACI,aAAa;MAChD,OAAOzC,OAAO,CAACW,GAAG,CAAC,UAAUC,KAAK,EAAE3H,CAAC,EAAE;QACrC,IAAI,CAAC2H,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACE,UAAU,MAAM,CAAC,IAAI,CAACF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACG,QAAQ,MAAM,CAAC,IAAIf,OAAO,CAAC7G,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;QACvL,IAAIuJ,QAAQ,GAAGN,MAAM,CAAC3C,aAAa,CAACxG,CAAC,CAAC;QACtC,IAAIwJ,aAAa,GAAGD,iBAAiB,IAAIJ,MAAM,CAACtC,cAAc,CAAC,CAAC,GAAG0C,iBAAiB,GAAG,IAAI;QAC3F,IAAIG,aAAa,GAAGD,QAAQ,GAAGJ,WAAW,GAAGG,aAAa;QAC1D,IAAIG,WAAW,GAAGzI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UAC5DS,MAAM,EAAEkB,WAAW,GAAG3B,KAAK,CAACe,IAAI,GAAGf,KAAK,CAACS,MAAM;UAC/CwB,QAAQ,EAAE,CAAC;QACb,CAAC,CAAC;QACF,OAAO,aAAa7F,KAAK,CAAC8E,aAAa,CAACtE,KAAK,EAAE5E,QAAQ,CAAC;UACtDkK,GAAG,EAAE,SAASA,GAAGA,CAACC,IAAI,EAAE;YACtB,IAAIA,IAAI,IAAI,CAACX,MAAM,CAACY,UAAU,CAACC,QAAQ,CAACF,IAAI,CAAC,EAAE;cAC7CX,MAAM,CAACY,UAAU,CAAC9I,IAAI,CAAC6I,IAAI,CAAC;YAC9B;UACF,CAAC;UACDF,QAAQ,EAAE,CAAC,CAAC;UACZX,SAAS,EAAE;QACb,CAAC,EAAExD,kBAAkB,CAAC0D,MAAM,CAACtH,KAAK,EAAE8F,KAAK,EAAE3H,CAAC,CAAC,EAAE;UAC7C;UACAI,GAAG,EAAE,SAAS,CAAC0I,MAAM,CAACnB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACE,UAAU,EAAE,GAAG,CAAC,CAACiB,MAAM,CAACnB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACG,QAAQ,EAAE,GAAG,CAAC,CAACgB,MAAM,CAACnB,KAAK,CAACC,QAAQ,EAAE,GAAG,CAAC,CAACkB,MAAM,CAAC9I,CAAC;QAC3M,CAAC,CAAC,EAAE,aAAa+D,KAAK,CAAC8E,aAAa,CAACnD,KAAK,EAAE/F,QAAQ,CAAC;UACnDsK,MAAM,EAAEP,aAAa;UACrBD,QAAQ,EAAEA,QAAQ;UAClBS,SAAS,EAAE;QACb,CAAC,EAAEP,WAAW,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDvJ,GAAG,EAAE,4BAA4B;IACjCmD,KAAK,EAAE,SAAS4G,0BAA0BA,CAAA,EAAG;MAC3C,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAACxI,KAAK;QAC3BkF,OAAO,GAAGsD,YAAY,CAACtD,OAAO;QAC9BZ,iBAAiB,GAAGkE,YAAY,CAAClE,iBAAiB;QAClDmE,cAAc,GAAGD,YAAY,CAACC,cAAc;QAC5CC,iBAAiB,GAAGF,YAAY,CAACE,iBAAiB;QAClDC,eAAe,GAAGH,YAAY,CAACG,eAAe;QAC9ClE,WAAW,GAAG+D,YAAY,CAAC/D,WAAW;MACxC,IAAImE,WAAW,GAAG,IAAI,CAACvE,KAAK;QAC1BwE,WAAW,GAAGD,WAAW,CAACC,WAAW;QACrCtE,qBAAqB,GAAGqE,WAAW,CAACrE,qBAAqB;MAC3D,OAAO,aAAarC,KAAK,CAAC8E,aAAa,CAAC5E,OAAO,EAAE;QAC/C0G,KAAK,EAAEL,cAAc;QACrBM,QAAQ,EAAEL,iBAAiB;QAC3Bd,QAAQ,EAAEtD,iBAAiB;QAC3B0E,MAAM,EAAEL,eAAe;QACvBM,IAAI,EAAE;UACJnK,CAAC,EAAE;QACL,CAAC;QACDoK,EAAE,EAAE;UACFpK,CAAC,EAAE;QACL,CAAC;QACDP,GAAG,EAAE,MAAM,CAAC0I,MAAM,CAACxC,WAAW,EAAE,GAAG,CAAC,CAACwC,MAAM,CAAC1C,qBAAqB,CAAC;QAClEH,gBAAgB,EAAE,IAAI,CAAC+E,oBAAoB;QAC3ClF,cAAc,EAAE,IAAI,CAACmF;MACvB,CAAC,EAAE,UAAUC,KAAK,EAAE;QAClB,IAAIvK,CAAC,GAAGuK,KAAK,CAACvK,CAAC;QACf,IAAIwK,QAAQ,GAAG,EAAE;QACjB,IAAIC,KAAK,GAAGrE,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;QACjC,IAAIsE,QAAQ,GAAGD,KAAK,CAACvD,UAAU;QAC/Bd,OAAO,CAAC5F,OAAO,CAAC,UAAUwG,KAAK,EAAEU,KAAK,EAAE;UACtC,IAAIiD,IAAI,GAAGZ,WAAW,IAAIA,WAAW,CAACrC,KAAK,CAAC;UAC5C,IAAIkD,YAAY,GAAGlD,KAAK,GAAG,CAAC,GAAGnE,GAAG,CAACyD,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC,GAAG,CAAC;UAChE,IAAI2D,IAAI,EAAE;YACR,IAAIE,OAAO,GAAGnG,iBAAiB,CAACiG,IAAI,CAACxD,QAAQ,GAAGwD,IAAI,CAACzD,UAAU,EAAEF,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACE,UAAU,CAAC;YACnG,IAAI4D,MAAM,GAAGvK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACvDE,UAAU,EAAEwD,QAAQ,GAAGE,YAAY;cACnCzD,QAAQ,EAAEuD,QAAQ,GAAGG,OAAO,CAAC7K,CAAC,CAAC,GAAG4K;YACpC,CAAC,CAAC;YACFJ,QAAQ,CAAClK,IAAI,CAACwK,MAAM,CAAC;YACrBJ,QAAQ,GAAGI,MAAM,CAAC3D,QAAQ;UAC5B,CAAC,MAAM;YACL,IAAIA,QAAQ,GAAGH,KAAK,CAACG,QAAQ;cAC3BD,UAAU,GAAGF,KAAK,CAACE,UAAU;YAC/B,IAAI6D,iBAAiB,GAAGrG,iBAAiB,CAAC,CAAC,EAAEyC,QAAQ,GAAGD,UAAU,CAAC;YACnE,IAAI8D,UAAU,GAAGD,iBAAiB,CAAC/K,CAAC,CAAC;YACrC,IAAIiL,OAAO,GAAG1K,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACxDE,UAAU,EAAEwD,QAAQ,GAAGE,YAAY;cACnCzD,QAAQ,EAAEuD,QAAQ,GAAGM,UAAU,GAAGJ;YACpC,CAAC,CAAC;YACFJ,QAAQ,CAAClK,IAAI,CAAC2K,OAAO,CAAC;YACtBP,QAAQ,GAAGO,OAAO,CAAC9D,QAAQ;UAC7B;QACF,CAAC,CAAC;QACF,OAAO,aAAa/D,KAAK,CAAC8E,aAAa,CAACtE,KAAK,EAAE,IAAI,EAAE6F,MAAM,CAAClB,uBAAuB,CAACiC,QAAQ,CAAC,CAAC;MAChG,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD/K,GAAG,EAAE,wBAAwB;IAC7BmD,KAAK,EAAE,SAASsI,sBAAsBA,CAACC,MAAM,EAAE;MAC7C,IAAIC,MAAM,GAAG,IAAI;MACjB;MACAD,MAAM,CAACE,SAAS,GAAG,UAAUvL,CAAC,EAAE;QAC9B,IAAI,CAACA,CAAC,CAACwL,MAAM,EAAE;UACb,QAAQxL,CAAC,CAACL,GAAG;YACX,KAAK,WAAW;cACd;gBACE,IAAI8L,IAAI,GAAG,EAAEH,MAAM,CAAC7F,KAAK,CAACK,aAAa,GAAGwF,MAAM,CAAChC,UAAU,CAAC7J,MAAM;gBAClE6L,MAAM,CAAChC,UAAU,CAACmC,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC;gBAC/BJ,MAAM,CAAChG,QAAQ,CAAC;kBACdQ,aAAa,EAAE2F;gBACjB,CAAC,CAAC;gBACF;cACF;YACF,KAAK,YAAY;cACf;gBACE,IAAIE,KAAK,GAAG,EAAEL,MAAM,CAAC7F,KAAK,CAACK,aAAa,GAAG,CAAC,GAAGwF,MAAM,CAAChC,UAAU,CAAC7J,MAAM,GAAG,CAAC,GAAG6L,MAAM,CAAC7F,KAAK,CAACK,aAAa,GAAGwF,MAAM,CAAChC,UAAU,CAAC7J,MAAM;gBACnI6L,MAAM,CAAChC,UAAU,CAACqC,KAAK,CAAC,CAACD,KAAK,CAAC,CAAC;gBAChCJ,MAAM,CAAChG,QAAQ,CAAC;kBACdQ,aAAa,EAAE6F;gBACjB,CAAC,CAAC;gBACF;cACF;YACF,KAAK,QAAQ;cACX;gBACEL,MAAM,CAAChC,UAAU,CAACgC,MAAM,CAAC7F,KAAK,CAACK,aAAa,CAAC,CAAC8F,IAAI,CAAC,CAAC;gBACpDN,MAAM,CAAChG,QAAQ,CAAC;kBACdQ,aAAa,EAAE;gBACjB,CAAC,CAAC;gBACF;cACF;YACF;cACE;gBACE;cAAA;UAEN;QACF;MACF,CAAC;IACH;EACF,CAAC,EAAE;IACDnG,GAAG,EAAE,eAAe;IACpBmD,KAAK,EAAE,SAAS+I,aAAaA,CAAA,EAAG;MAC9B,IAAIC,YAAY,GAAG,IAAI,CAAC1K,KAAK;QAC3BkF,OAAO,GAAGwF,YAAY,CAACxF,OAAO;QAC9BZ,iBAAiB,GAAGoG,YAAY,CAACpG,iBAAiB;MACpD,IAAIuE,WAAW,GAAG,IAAI,CAACxE,KAAK,CAACwE,WAAW;MACxC,IAAIvE,iBAAiB,IAAIY,OAAO,IAAIA,OAAO,CAAC7G,MAAM,KAAK,CAACwK,WAAW,IAAI,CAACvG,OAAO,CAACuG,WAAW,EAAE3D,OAAO,CAAC,CAAC,EAAE;QACtG,OAAO,IAAI,CAACoD,0BAA0B,CAAC,CAAC;MAC1C;MACA,OAAO,IAAI,CAACjB,uBAAuB,CAACnC,OAAO,CAAC;IAC9C;EACF,CAAC,EAAE;IACD3G,GAAG,EAAE,mBAAmB;IACxBmD,KAAK,EAAE,SAASiJ,iBAAiBA,CAAA,EAAG;MAClC,IAAI,IAAI,CAACV,MAAM,EAAE;QACf,IAAI,CAACD,sBAAsB,CAAC,IAAI,CAACC,MAAM,CAAC;MAC1C;IACF;EACF,CAAC,EAAE;IACD1L,GAAG,EAAE,QAAQ;IACbmD,KAAK,EAAE,SAASkJ,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAAC9K,KAAK;QAC3B+K,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxB7F,OAAO,GAAG4F,YAAY,CAAC5F,OAAO;QAC9BkC,SAAS,GAAG0D,YAAY,CAAC1D,SAAS;QAClChC,KAAK,GAAG0F,YAAY,CAAC1F,KAAK;QAC1Be,EAAE,GAAG2E,YAAY,CAAC3E,EAAE;QACpBC,EAAE,GAAG0E,YAAY,CAAC1E,EAAE;QACpB4E,WAAW,GAAGF,YAAY,CAACE,WAAW;QACtC3E,WAAW,GAAGyE,YAAY,CAACzE,WAAW;QACtC/B,iBAAiB,GAAGwG,YAAY,CAACxG,iBAAiB;MACpD,IAAIH,mBAAmB,GAAG,IAAI,CAACE,KAAK,CAACF,mBAAmB;MACxD,IAAI4G,IAAI,IAAI,CAAC7F,OAAO,IAAI,CAACA,OAAO,CAAC7G,MAAM,IAAI,CAACgF,QAAQ,CAAC8C,EAAE,CAAC,IAAI,CAAC9C,QAAQ,CAAC+C,EAAE,CAAC,IAAI,CAAC/C,QAAQ,CAAC2H,WAAW,CAAC,IAAI,CAAC3H,QAAQ,CAACgD,WAAW,CAAC,EAAE;QAC7H,OAAO,IAAI;MACb;MACA,IAAI4E,UAAU,GAAGxI,IAAI,CAAC,cAAc,EAAE2E,SAAS,CAAC;MAChD,OAAO,aAAalF,KAAK,CAAC8E,aAAa,CAACtE,KAAK,EAAE;QAC7CqF,QAAQ,EAAE,IAAI,CAAC/H,KAAK,CAACkL,YAAY;QACjC9D,SAAS,EAAE6D,UAAU;QACrBjD,GAAG,EAAE,SAASA,GAAGA,CAACmD,KAAK,EAAE;UACvBN,MAAM,CAACZ,MAAM,GAAGkB,KAAK;QACvB;MACF,CAAC,EAAE,IAAI,CAACV,aAAa,CAAC,CAAC,EAAErF,KAAK,IAAI,IAAI,CAACH,YAAY,CAACC,OAAO,CAAC,EAAErC,KAAK,CAACuI,kBAAkB,CAAC,IAAI,CAACpL,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,CAACsE,iBAAiB,IAAIH,mBAAmB,KAAKrB,SAAS,CAACsI,kBAAkB,CAAC,IAAI,CAACpL,KAAK,EAAEkF,OAAO,EAAE,KAAK,CAAC,CAAC;IAC3N;EACF,CAAC,CAAC,EAAE,CAAC;IACH3G,GAAG,EAAE,0BAA0B;IAC/BmD,KAAK,EAAE,SAAS2J,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAIA,SAAS,CAAChH,qBAAqB,KAAK+G,SAAS,CAAChH,iBAAiB,EAAE;QACnE,OAAO;UACLC,qBAAqB,EAAE+G,SAAS,CAAChH,iBAAiB;UAClDE,eAAe,EAAE8G,SAAS,CAAC7G,WAAW;UACtC+G,UAAU,EAAEF,SAAS,CAACpG,OAAO;UAC7B2D,WAAW,EAAE,EAAE;UACf1E,mBAAmB,EAAE;QACvB,CAAC;MACH;MACA,IAAImH,SAAS,CAAChH,iBAAiB,IAAIgH,SAAS,CAAC7G,WAAW,KAAK8G,SAAS,CAAC/G,eAAe,EAAE;QACtF,OAAO;UACLA,eAAe,EAAE8G,SAAS,CAAC7G,WAAW;UACtC+G,UAAU,EAAEF,SAAS,CAACpG,OAAO;UAC7B2D,WAAW,EAAE0C,SAAS,CAACC,UAAU;UACjCrH,mBAAmB,EAAE;QACvB,CAAC;MACH;MACA,IAAImH,SAAS,CAACpG,OAAO,KAAKqG,SAAS,CAACC,UAAU,EAAE;QAC9C,OAAO;UACLA,UAAU,EAAEF,SAAS,CAACpG,OAAO;UAC7Bf,mBAAmB,EAAE;QACvB,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACD5F,GAAG,EAAE,eAAe;IACpBmD,KAAK,EAAE,SAASgF,aAAaA,CAACC,CAAC,EAAER,EAAE,EAAE;MACnC,IAAIQ,CAAC,GAAGR,EAAE,EAAE;QACV,OAAO,OAAO;MAChB;MACA,IAAIQ,CAAC,GAAGR,EAAE,EAAE;QACV,OAAO,KAAK;MACd;MACA,OAAO,QAAQ;IACjB;EACF,CAAC,EAAE;IACD5H,GAAG,EAAE,qBAAqB;IAC1BmD,KAAK,EAAE,SAASwF,mBAAmBA,CAACkB,MAAM,EAAEpI,KAAK,EAAEzB,GAAG,EAAE;MACtD,IAAK,aAAa2D,KAAK,CAACuJ,cAAc,CAACrD,MAAM,CAAC,EAAE;QAC9C,OAAO,aAAalG,KAAK,CAACwJ,YAAY,CAACtD,MAAM,EAAEpI,KAAK,CAAC;MACvD;MACA,IAAIwC,UAAU,CAAC4F,MAAM,CAAC,EAAE;QACtB,OAAOA,MAAM,CAACpI,KAAK,CAAC;MACtB;MACA,IAAIoH,SAAS,GAAG3E,IAAI,CAAC,yBAAyB,EAAE,OAAO2F,MAAM,KAAK,SAAS,GAAGA,MAAM,CAAChB,SAAS,GAAG,EAAE,CAAC;MACpG,OAAO,aAAalF,KAAK,CAAC8E,aAAa,CAACrE,KAAK,EAAE7E,QAAQ,CAAC,CAAC,CAAC,EAAEkC,KAAK,EAAE;QACjEzB,GAAG,EAAEA,GAAG;QACRoN,IAAI,EAAE,QAAQ;QACdvE,SAAS,EAAEA;MACb,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACD7I,GAAG,EAAE,iBAAiB;IACtBmD,KAAK,EAAE,SAASyF,eAAeA,CAACiB,MAAM,EAAEpI,KAAK,EAAE0B,KAAK,EAAE;MACpD,IAAK,aAAaQ,KAAK,CAACuJ,cAAc,CAACrD,MAAM,CAAC,EAAE;QAC9C,OAAO,aAAalG,KAAK,CAACwJ,YAAY,CAACtD,MAAM,EAAEpI,KAAK,CAAC;MACvD;MACA,IAAIoF,KAAK,GAAG1D,KAAK;MACjB,IAAIc,UAAU,CAAC4F,MAAM,CAAC,EAAE;QACtBhD,KAAK,GAAGgD,MAAM,CAACpI,KAAK,CAAC;QACrB,IAAK,aAAakC,KAAK,CAACuJ,cAAc,CAACrG,KAAK,CAAC,EAAE;UAC7C,OAAOA,KAAK;QACd;MACF;MACA,IAAIgC,SAAS,GAAG3E,IAAI,CAAC,yBAAyB,EAAE,OAAO2F,MAAM,KAAK,SAAS,IAAI,CAAC5F,UAAU,CAAC4F,MAAM,CAAC,GAAGA,MAAM,CAAChB,SAAS,GAAG,EAAE,CAAC;MAC3H,OAAO,aAAalF,KAAK,CAAC8E,aAAa,CAACpE,IAAI,EAAE9E,QAAQ,CAAC,CAAC,CAAC,EAAEkC,KAAK,EAAE;QAChE4L,iBAAiB,EAAE,QAAQ;QAC3BxE,SAAS,EAAEA;MACb,CAAC,CAAC,EAAEhC,KAAK,CAAC;IACZ;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAACjD,aAAa,CAAC;AAChB5E,IAAI,GAAGuG,GAAG;AACVvE,eAAe,CAACuE,GAAG,EAAE,aAAa,EAAE,KAAK,CAAC;AAC1CvE,eAAe,CAACuE,GAAG,EAAE,cAAc,EAAE;EACnCyC,MAAM,EAAE,MAAM;EACdM,IAAI,EAAE,SAAS;EACfgF,UAAU,EAAE,MAAM;EAClB1F,EAAE,EAAE,KAAK;EACTC,EAAE,EAAE,KAAK;EACTJ,UAAU,EAAE,CAAC;EACbC,QAAQ,EAAE,GAAG;EACb+E,WAAW,EAAE,CAAC;EACd3E,WAAW,EAAE,KAAK;EAClBqD,YAAY,EAAE,CAAC;EACfrE,SAAS,EAAE,IAAI;EACf0F,IAAI,EAAE,KAAK;EACXe,QAAQ,EAAE,CAAC;EACXxH,iBAAiB,EAAE,CAACpB,MAAM,CAAC6I,KAAK;EAChCtD,cAAc,EAAE,GAAG;EACnBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,MAAM;EACvBqD,OAAO,EAAE,MAAM;EACfvE,WAAW,EAAE,KAAK;EAClByD,YAAY,EAAE;AAChB,CAAC,CAAC;AACF3L,eAAe,CAACuE,GAAG,EAAE,iBAAiB,EAAE,UAAUkC,UAAU,EAAEC,QAAQ,EAAE;EACtE,IAAIgG,IAAI,GAAG1I,QAAQ,CAAC0C,QAAQ,GAAGD,UAAU,CAAC;EAC1C,IAAI8D,UAAU,GAAGoC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACnG,QAAQ,GAAGD,UAAU,CAAC,EAAE,GAAG,CAAC;EAC/D,OAAOiG,IAAI,GAAGnC,UAAU;AAC1B,CAAC,CAAC;AACFvK,eAAe,CAACuE,GAAG,EAAE,gBAAgB,EAAE,UAAUuI,SAAS,EAAE;EAC1D,IAAIC,IAAI,GAAGD,SAAS,CAACC,IAAI;IACvBC,QAAQ,GAAGF,SAAS,CAACE,QAAQ;EAC/B,IAAIC,iBAAiB,GAAGvJ,WAAW,CAACoJ,SAAS,EAAE,KAAK,CAAC;EACrD,IAAII,KAAK,GAAGzJ,aAAa,CAACuJ,QAAQ,EAAExJ,IAAI,CAAC;EACzC,IAAIuJ,IAAI,IAAIA,IAAI,CAACjO,MAAM,EAAE;IACvB,OAAOiO,IAAI,CAACzG,GAAG,CAAC,UAAUC,KAAK,EAAEU,KAAK,EAAE;MACtC,OAAOnH,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;QAC/CqN,OAAO,EAAE5G;MACX,CAAC,EAAE0G,iBAAiB,CAAC,EAAE1G,KAAK,CAAC,EAAE2G,KAAK,IAAIA,KAAK,CAACjG,KAAK,CAAC,IAAIiG,KAAK,CAACjG,KAAK,CAAC,CAACxG,KAAK,CAAC;IAC7E,CAAC,CAAC;EACJ;EACA,IAAIyM,KAAK,IAAIA,KAAK,CAACpO,MAAM,EAAE;IACzB,OAAOoO,KAAK,CAAC5G,GAAG,CAAC,UAAU8G,IAAI,EAAE;MAC/B,OAAOtN,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmN,iBAAiB,CAAC,EAAEG,IAAI,CAAC3M,KAAK,CAAC;IACxE,CAAC,CAAC;EACJ;EACA,OAAO,EAAE;AACX,CAAC,CAAC;AACFT,eAAe,CAACuE,GAAG,EAAE,sBAAsB,EAAE,UAAUuI,SAAS,EAAEO,MAAM,EAAE;EACxE,IAAIC,GAAG,GAAGD,MAAM,CAACC,GAAG;IAClBC,IAAI,GAAGF,MAAM,CAACE,IAAI;IAClBC,KAAK,GAAGH,MAAM,CAACG,KAAK;IACpBC,MAAM,GAAGJ,MAAM,CAACI,MAAM;EACxB,IAAIC,YAAY,GAAG7J,YAAY,CAAC2J,KAAK,EAAEC,MAAM,CAAC;EAC9C,IAAI7G,EAAE,GAAG2G,IAAI,GAAGxJ,eAAe,CAAC+I,SAAS,CAAClG,EAAE,EAAE4G,KAAK,EAAEA,KAAK,GAAG,CAAC,CAAC;EAC/D,IAAI3G,EAAE,GAAGyG,GAAG,GAAGvJ,eAAe,CAAC+I,SAAS,CAACjG,EAAE,EAAE4G,MAAM,EAAEA,MAAM,GAAG,CAAC,CAAC;EAChE,IAAIhC,WAAW,GAAG1H,eAAe,CAAC+I,SAAS,CAACrB,WAAW,EAAEiC,YAAY,EAAE,CAAC,CAAC;EACzE,IAAI5G,WAAW,GAAG/C,eAAe,CAAC+I,SAAS,CAAChG,WAAW,EAAE4G,YAAY,EAAEA,YAAY,GAAG,GAAG,CAAC;EAC1F,IAAIC,SAAS,GAAGb,SAAS,CAACa,SAAS,IAAIhB,IAAI,CAACiB,IAAI,CAACJ,KAAK,GAAGA,KAAK,GAAGC,MAAM,GAAGA,MAAM,CAAC,GAAG,CAAC;EACrF,OAAO;IACL7G,EAAE,EAAEA,EAAE;IACNC,EAAE,EAAEA,EAAE;IACN4E,WAAW,EAAEA,WAAW;IACxB3E,WAAW,EAAEA,WAAW;IACxB6G,SAAS,EAAEA;EACb,CAAC;AACH,CAAC,CAAC;AACF3N,eAAe,CAACuE,GAAG,EAAE,iBAAiB,EAAE,UAAUsJ,KAAK,EAAE;EACvD,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACnBT,MAAM,GAAGQ,KAAK,CAACR,MAAM;EACvB,IAAIP,SAAS,GAAGgB,IAAI,CAAC1B,IAAI,CAAC2B,YAAY,KAAKC,SAAS,GAAGlO,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgO,IAAI,CAAC1B,IAAI,CAAC2B,YAAY,CAAC,EAAED,IAAI,CAACrN,KAAK,CAAC,GAAGqN,IAAI,CAACrN,KAAK;EACxI,IAAIwN,OAAO,GAAGjQ,IAAI,CAACkQ,cAAc,CAACpB,SAAS,CAAC;EAC5C,IAAI,CAACmB,OAAO,IAAI,CAACA,OAAO,CAACnP,MAAM,EAAE;IAC/B,OAAO,IAAI;EACb;EACA,IAAIqP,YAAY,GAAGrB,SAAS,CAACqB,YAAY;IACvC1H,UAAU,GAAGqG,SAAS,CAACrG,UAAU;IACjCC,QAAQ,GAAGoG,SAAS,CAACpG,QAAQ;IAC7ByD,YAAY,GAAG2C,SAAS,CAAC3C,YAAY;IACrCpE,OAAO,GAAG+G,SAAS,CAAC/G,OAAO;IAC3B0G,OAAO,GAAGK,SAAS,CAACL,OAAO;IAC3BzG,QAAQ,GAAG8G,SAAS,CAAC9G,QAAQ;IAC7BoI,WAAW,GAAGtB,SAAS,CAACsB,WAAW;EACrC,IAAI7B,QAAQ,GAAGI,IAAI,CAACE,GAAG,CAACC,SAAS,CAACP,QAAQ,CAAC;EAC3C,IAAI8B,UAAU,GAAGrQ,IAAI,CAACsQ,oBAAoB,CAACxB,SAAS,EAAEO,MAAM,CAAC;EAC7D,IAAI9C,UAAU,GAAGvM,IAAI,CAACuQ,eAAe,CAAC9H,UAAU,EAAEC,QAAQ,CAAC;EAC3D,IAAI8H,aAAa,GAAG7B,IAAI,CAACE,GAAG,CAACtC,UAAU,CAAC;EACxC,IAAI/C,WAAW,GAAGzB,OAAO;EACzB,IAAI/C,KAAK,CAAC+C,OAAO,CAAC,IAAI/C,KAAK,CAACgD,QAAQ,CAAC,EAAE;IACrC5B,IAAI,CAAC,KAAK,EAAE,wGAAwG,CAAC;IACrHoD,WAAW,GAAG,OAAO;EACvB,CAAC,MAAM,IAAIxE,KAAK,CAAC+C,OAAO,CAAC,EAAE;IACzB3B,IAAI,CAAC,KAAK,EAAE,wGAAwG,CAAC;IACrHoD,WAAW,GAAGxB,QAAQ;EACxB;EACA,IAAIyI,gBAAgB,GAAGR,OAAO,CAACvO,MAAM,CAAC,UAAU6G,KAAK,EAAE;IACrD,OAAOpC,iBAAiB,CAACoC,KAAK,EAAEiB,WAAW,EAAE,CAAC,CAAC,KAAK,CAAC;EACvD,CAAC,CAAC,CAAC1I,MAAM;EACT,IAAI4P,gBAAgB,GAAG,CAACF,aAAa,IAAI,GAAG,GAAGC,gBAAgB,GAAGA,gBAAgB,GAAG,CAAC,IAAItE,YAAY;EACtG,IAAIwE,cAAc,GAAGH,aAAa,GAAGC,gBAAgB,GAAGlC,QAAQ,GAAGmC,gBAAgB;EACnF,IAAIE,GAAG,GAAGX,OAAO,CAACY,MAAM,CAAC,UAAUC,MAAM,EAAEvI,KAAK,EAAE;IAChD,IAAIwI,GAAG,GAAG5K,iBAAiB,CAACoC,KAAK,EAAEiB,WAAW,EAAE,CAAC,CAAC;IAClD,OAAOsH,MAAM,IAAIhL,QAAQ,CAACiL,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC,CAAC;EAC3C,CAAC,EAAE,CAAC,CAAC;EACL,IAAIpJ,OAAO;EACX,IAAIiJ,GAAG,GAAG,CAAC,EAAE;IACX,IAAI1E,IAAI;IACRvE,OAAO,GAAGsI,OAAO,CAAC3H,GAAG,CAAC,UAAUC,KAAK,EAAE3H,CAAC,EAAE;MACxC,IAAImQ,GAAG,GAAG5K,iBAAiB,CAACoC,KAAK,EAAEiB,WAAW,EAAE,CAAC,CAAC;MAClD,IAAIwH,IAAI,GAAG7K,iBAAiB,CAACoC,KAAK,EAAEkG,OAAO,EAAE7N,CAAC,CAAC;MAC/C,IAAIqQ,OAAO,GAAG,CAACnL,QAAQ,CAACiL,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC,IAAIH,GAAG;MAC7C,IAAIM,cAAc;MAClB,IAAItQ,CAAC,EAAE;QACLsQ,cAAc,GAAGhF,IAAI,CAACxD,QAAQ,GAAG1C,QAAQ,CAACuG,UAAU,CAAC,GAAGJ,YAAY,IAAI4E,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAC5F,CAAC,MAAM;QACLG,cAAc,GAAGzI,UAAU;MAC7B;MACA,IAAI0I,YAAY,GAAGD,cAAc,GAAGlL,QAAQ,CAACuG,UAAU,CAAC,IAAI,CAACwE,GAAG,KAAK,CAAC,GAAGxC,QAAQ,GAAG,CAAC,IAAI0C,OAAO,GAAGN,cAAc,CAAC;MAClH,IAAInI,QAAQ,GAAG,CAAC0I,cAAc,GAAGC,YAAY,IAAI,CAAC;MAClD,IAAIC,YAAY,GAAG,CAACf,UAAU,CAAC5C,WAAW,GAAG4C,UAAU,CAACvH,WAAW,IAAI,CAAC;MACxE,IAAIuI,cAAc,GAAG,CAAC;QACpBL,IAAI,EAAEA,IAAI;QACV7M,KAAK,EAAE4M,GAAG;QACV5B,OAAO,EAAE5G,KAAK;QACdR,OAAO,EAAEyB,WAAW;QACpB4E,IAAI,EAAEgC;MACR,CAAC,CAAC;MACF,IAAIkB,eAAe,GAAG1L,gBAAgB,CAACyK,UAAU,CAACzH,EAAE,EAAEyH,UAAU,CAACxH,EAAE,EAAEuI,YAAY,EAAE5I,QAAQ,CAAC;MAC5F0D,IAAI,GAAGpK,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;QAC/CmP,OAAO,EAAEA,OAAO;QAChBd,YAAY,EAAEA,YAAY;QAC1Ba,IAAI,EAAEA,IAAI;QACVK,cAAc,EAAEA,cAAc;QAC9B7I,QAAQ,EAAEA,QAAQ;QAClB4I,YAAY,EAAEA,YAAY;QAC1BE,eAAe,EAAEA;MACnB,CAAC,EAAE/I,KAAK,CAAC,EAAE8H,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;QAC1BlM,KAAK,EAAEgC,iBAAiB,CAACoC,KAAK,EAAEiB,WAAW,CAAC;QAC5Cf,UAAU,EAAEyI,cAAc;QAC1BxI,QAAQ,EAAEyI,YAAY;QACtBhC,OAAO,EAAE5G,KAAK;QACd4D,YAAY,EAAEnG,QAAQ,CAACuG,UAAU,CAAC,GAAGJ;MACvC,CAAC,CAAC;MACF,OAAOD,IAAI;IACb,CAAC,CAAC;EACJ;EACA,OAAOpK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuO,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;IACtD1I,OAAO,EAAEA,OAAO;IAChBoH,IAAI,EAAEkB;EACR,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}