{"ast": null, "code": "var _excluded = [\"layout\", \"type\", \"stroke\", \"connectNulls\", \"isRange\", \"ref\"],\n  _excluded2 = [\"key\"];\nvar _Area;\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _callSuper(t, o, e) {\n  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n/**\n * @fileOverview Area\n */\nimport React, { PureComponent } from 'react';\nimport clsx from 'clsx';\nimport Animate from 'react-smooth';\nimport isFunction from 'lodash/isFunction';\nimport max from 'lodash/max';\nimport isNil from 'lodash/isNil';\nimport isNan from 'lodash/isNaN';\nimport isEqual from 'lodash/isEqual';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { Global } from '../util/Global';\nimport { isNumber, uniqueId, interpolateNumber } from '../util/DataUtils';\nimport { getCateCoordinateOfLine, getValueByDataKey } from '../util/ChartUtils';\nimport { filterProps, hasClipDot } from '../util/ReactUtils';\nexport var Area = /*#__PURE__*/function (_PureComponent) {\n  function Area() {\n    var _this;\n    _classCallCheck(this, Area);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Area, [].concat(args));\n    _defineProperty(_this, \"state\", {\n      isAnimationFinished: true\n    });\n    _defineProperty(_this, \"id\", uniqueId('recharts-area-'));\n    _defineProperty(_this, \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (isFunction(onAnimationEnd)) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_this, \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (isFunction(onAnimationStart)) {\n        onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _inherits(Area, _PureComponent);\n  return _createClass(Area, [{\n    key: \"renderDots\",\n    value: function renderDots(needClip, clipDot, clipPathId) {\n      var isAnimationActive = this.props.isAnimationActive;\n      var isAnimationFinished = this.state.isAnimationFinished;\n      if (isAnimationActive && !isAnimationFinished) {\n        return null;\n      }\n      var _this$props = this.props,\n        dot = _this$props.dot,\n        points = _this$props.points,\n        dataKey = _this$props.dataKey;\n      var areaProps = filterProps(this.props, false);\n      var customDotProps = filterProps(dot, true);\n      var dots = points.map(function (entry, i) {\n        var dotProps = _objectSpread(_objectSpread(_objectSpread({\n          key: \"dot-\".concat(i),\n          r: 3\n        }, areaProps), customDotProps), {}, {\n          index: i,\n          cx: entry.x,\n          cy: entry.y,\n          dataKey: dataKey,\n          value: entry.value,\n          payload: entry.payload,\n          points: points\n        });\n        return Area.renderDotItem(dot, dotProps);\n      });\n      var dotsProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipDot ? '' : 'dots-').concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, _extends({\n        className: \"recharts-area-dots\"\n      }, dotsProps), dots);\n    }\n  }, {\n    key: \"renderHorizontalRect\",\n    value: function renderHorizontalRect(alpha) {\n      var _this$props2 = this.props,\n        baseLine = _this$props2.baseLine,\n        points = _this$props2.points,\n        strokeWidth = _this$props2.strokeWidth;\n      var startX = points[0].x;\n      var endX = points[points.length - 1].x;\n      var width = alpha * Math.abs(startX - endX);\n      var maxY = max(points.map(function (entry) {\n        return entry.y || 0;\n      }));\n      if (isNumber(baseLine) && typeof baseLine === 'number') {\n        maxY = Math.max(baseLine, maxY);\n      } else if (baseLine && Array.isArray(baseLine) && baseLine.length) {\n        maxY = Math.max(max(baseLine.map(function (entry) {\n          return entry.y || 0;\n        })), maxY);\n      }\n      if (isNumber(maxY)) {\n        return /*#__PURE__*/React.createElement(\"rect\", {\n          x: startX < endX ? startX : startX - width,\n          y: 0,\n          width: width,\n          height: Math.floor(maxY + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1))\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderVerticalRect\",\n    value: function renderVerticalRect(alpha) {\n      var _this$props3 = this.props,\n        baseLine = _this$props3.baseLine,\n        points = _this$props3.points,\n        strokeWidth = _this$props3.strokeWidth;\n      var startY = points[0].y;\n      var endY = points[points.length - 1].y;\n      var height = alpha * Math.abs(startY - endY);\n      var maxX = max(points.map(function (entry) {\n        return entry.x || 0;\n      }));\n      if (isNumber(baseLine) && typeof baseLine === 'number') {\n        maxX = Math.max(baseLine, maxX);\n      } else if (baseLine && Array.isArray(baseLine) && baseLine.length) {\n        maxX = Math.max(max(baseLine.map(function (entry) {\n          return entry.x || 0;\n        })), maxX);\n      }\n      if (isNumber(maxX)) {\n        return /*#__PURE__*/React.createElement(\"rect\", {\n          x: 0,\n          y: startY < endY ? startY : startY - height,\n          width: maxX + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1),\n          height: Math.floor(height)\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderClipRect\",\n    value: function renderClipRect(alpha) {\n      var layout = this.props.layout;\n      if (layout === 'vertical') {\n        return this.renderVerticalRect(alpha);\n      }\n      return this.renderHorizontalRect(alpha);\n    }\n  }, {\n    key: \"renderAreaStatically\",\n    value: function renderAreaStatically(points, baseLine, needClip, clipPathId) {\n      var _this$props4 = this.props,\n        layout = _this$props4.layout,\n        type = _this$props4.type,\n        stroke = _this$props4.stroke,\n        connectNulls = _this$props4.connectNulls,\n        isRange = _this$props4.isRange,\n        ref = _this$props4.ref,\n        others = _objectWithoutProperties(_this$props4, _excluded);\n      return /*#__PURE__*/React.createElement(Layer, {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      }, /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(others, true), {\n        points: points,\n        connectNulls: connectNulls,\n        type: type,\n        baseLine: baseLine,\n        layout: layout,\n        stroke: \"none\",\n        className: \"recharts-area-area\"\n      })), stroke !== 'none' && /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(this.props, false), {\n        className: \"recharts-area-curve\",\n        layout: layout,\n        type: type,\n        connectNulls: connectNulls,\n        fill: \"none\",\n        points: points\n      })), stroke !== 'none' && isRange && /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(this.props, false), {\n        className: \"recharts-area-curve\",\n        layout: layout,\n        type: type,\n        connectNulls: connectNulls,\n        fill: \"none\",\n        points: baseLine\n      })));\n    }\n  }, {\n    key: \"renderAreaWithAnimation\",\n    value: function renderAreaWithAnimation(needClip, clipPathId) {\n      var _this2 = this;\n      var _this$props5 = this.props,\n        points = _this$props5.points,\n        baseLine = _this$props5.baseLine,\n        isAnimationActive = _this$props5.isAnimationActive,\n        animationBegin = _this$props5.animationBegin,\n        animationDuration = _this$props5.animationDuration,\n        animationEasing = _this$props5.animationEasing,\n        animationId = _this$props5.animationId;\n      var _this$state = this.state,\n        prevPoints = _this$state.prevPoints,\n        prevBaseLine = _this$state.prevBaseLine;\n      // const clipPathId = isNil(id) ? this.id : id;\n\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"area-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        if (prevPoints) {\n          var prevPointsDiffFactor = prevPoints.length / points.length;\n          // update animtaion\n          var stepPoints = points.map(function (entry, index) {\n            var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n            if (prevPoints[prevPointIndex]) {\n              var prev = prevPoints[prevPointIndex];\n              var interpolatorX = interpolateNumber(prev.x, entry.x);\n              var interpolatorY = interpolateNumber(prev.y, entry.y);\n              return _objectSpread(_objectSpread({}, entry), {}, {\n                x: interpolatorX(t),\n                y: interpolatorY(t)\n              });\n            }\n            return entry;\n          });\n          var stepBaseLine;\n          if (isNumber(baseLine) && typeof baseLine === 'number') {\n            var interpolator = interpolateNumber(prevBaseLine, baseLine);\n            stepBaseLine = interpolator(t);\n          } else if (isNil(baseLine) || isNan(baseLine)) {\n            var _interpolator = interpolateNumber(prevBaseLine, 0);\n            stepBaseLine = _interpolator(t);\n          } else {\n            stepBaseLine = baseLine.map(function (entry, index) {\n              var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n              if (prevBaseLine[prevPointIndex]) {\n                var prev = prevBaseLine[prevPointIndex];\n                var interpolatorX = interpolateNumber(prev.x, entry.x);\n                var interpolatorY = interpolateNumber(prev.y, entry.y);\n                return _objectSpread(_objectSpread({}, entry), {}, {\n                  x: interpolatorX(t),\n                  y: interpolatorY(t)\n                });\n              }\n              return entry;\n            });\n          }\n          return _this2.renderAreaStatically(stepPoints, stepBaseLine, needClip, clipPathId);\n        }\n        return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n          id: \"animationClipPath-\".concat(clipPathId)\n        }, _this2.renderClipRect(t))), /*#__PURE__*/React.createElement(Layer, {\n          clipPath: \"url(#animationClipPath-\".concat(clipPathId, \")\")\n        }, _this2.renderAreaStatically(points, baseLine, needClip, clipPathId)));\n      });\n    }\n  }, {\n    key: \"renderArea\",\n    value: function renderArea(needClip, clipPathId) {\n      var _this$props6 = this.props,\n        points = _this$props6.points,\n        baseLine = _this$props6.baseLine,\n        isAnimationActive = _this$props6.isAnimationActive;\n      var _this$state2 = this.state,\n        prevPoints = _this$state2.prevPoints,\n        prevBaseLine = _this$state2.prevBaseLine,\n        totalLength = _this$state2.totalLength;\n      if (isAnimationActive && points && points.length && (!prevPoints && totalLength > 0 || !isEqual(prevPoints, points) || !isEqual(prevBaseLine, baseLine))) {\n        return this.renderAreaWithAnimation(needClip, clipPathId);\n      }\n      return this.renderAreaStatically(points, baseLine, needClip, clipPathId);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _filterProps;\n      var _this$props7 = this.props,\n        hide = _this$props7.hide,\n        dot = _this$props7.dot,\n        points = _this$props7.points,\n        className = _this$props7.className,\n        top = _this$props7.top,\n        left = _this$props7.left,\n        xAxis = _this$props7.xAxis,\n        yAxis = _this$props7.yAxis,\n        width = _this$props7.width,\n        height = _this$props7.height,\n        isAnimationActive = _this$props7.isAnimationActive,\n        id = _this$props7.id;\n      if (hide || !points || !points.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var hasSinglePoint = points.length === 1;\n      var layerClass = clsx('recharts-area', className);\n      var needClipX = xAxis && xAxis.allowDataOverflow;\n      var needClipY = yAxis && yAxis.allowDataOverflow;\n      var needClip = needClipX || needClipY;\n      var clipPathId = isNil(id) ? this.id : id;\n      var _ref2 = (_filterProps = filterProps(dot, false)) !== null && _filterProps !== void 0 ? _filterProps : {\n          r: 3,\n          strokeWidth: 2\n        },\n        _ref2$r = _ref2.r,\n        r = _ref2$r === void 0 ? 3 : _ref2$r,\n        _ref2$strokeWidth = _ref2.strokeWidth,\n        strokeWidth = _ref2$strokeWidth === void 0 ? 2 : _ref2$strokeWidth;\n      var _ref3 = hasClipDot(dot) ? dot : {},\n        _ref3$clipDot = _ref3.clipDot,\n        clipDot = _ref3$clipDot === void 0 ? true : _ref3$clipDot;\n      var dotSize = r * 2 + strokeWidth;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass\n      }, needClipX || needClipY ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: needClipX ? left : left - width / 2,\n        y: needClipY ? top : top - height / 2,\n        width: needClipX ? width : width * 2,\n        height: needClipY ? height : height * 2\n      })), !clipDot && /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-dots-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: left - dotSize / 2,\n        y: top - dotSize / 2,\n        width: width + dotSize,\n        height: height + dotSize\n      }))) : null, !hasSinglePoint ? this.renderArea(needClip, clipPathId) : null, (dot || hasSinglePoint) && this.renderDots(needClip, clipDot, clipPathId), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, points));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curPoints: nextProps.points,\n          curBaseLine: nextProps.baseLine,\n          prevPoints: prevState.curPoints,\n          prevBaseLine: prevState.curBaseLine\n        };\n      }\n      if (nextProps.points !== prevState.curPoints || nextProps.baseLine !== prevState.curBaseLine) {\n        return {\n          curPoints: nextProps.points,\n          curBaseLine: nextProps.baseLine\n        };\n      }\n      return null;\n    }\n  }]);\n}(PureComponent);\n_Area = Area;\n_defineProperty(Area, \"displayName\", 'Area');\n_defineProperty(Area, \"defaultProps\", {\n  stroke: '#3182bd',\n  fill: '#3182bd',\n  fillOpacity: 0.6,\n  xAxisId: 0,\n  yAxisId: 0,\n  legendType: 'line',\n  connectNulls: false,\n  // points of area\n  points: [],\n  dot: false,\n  activeDot: true,\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease'\n});\n_defineProperty(Area, \"getBaseValue\", function (props, item, xAxis, yAxis) {\n  var layout = props.layout,\n    chartBaseValue = props.baseValue;\n  var itemBaseValue = item.props.baseValue;\n\n  // The baseValue can be defined both on the AreaChart as well as on the Area.\n  // The value for the item takes precedence.\n  var baseValue = itemBaseValue !== null && itemBaseValue !== void 0 ? itemBaseValue : chartBaseValue;\n  if (isNumber(baseValue) && typeof baseValue === 'number') {\n    return baseValue;\n  }\n  var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n  var domain = numericAxis.scale.domain();\n  if (numericAxis.type === 'number') {\n    var domainMax = Math.max(domain[0], domain[1]);\n    var domainMin = Math.min(domain[0], domain[1]);\n    if (baseValue === 'dataMin') {\n      return domainMin;\n    }\n    if (baseValue === 'dataMax') {\n      return domainMax;\n    }\n    return domainMax < 0 ? domainMax : Math.max(Math.min(domain[0], domain[1]), 0);\n  }\n  if (baseValue === 'dataMin') {\n    return domain[0];\n  }\n  if (baseValue === 'dataMax') {\n    return domain[1];\n  }\n  return domain[0];\n});\n_defineProperty(Area, \"getComposedData\", function (_ref4) {\n  var props = _ref4.props,\n    item = _ref4.item,\n    xAxis = _ref4.xAxis,\n    yAxis = _ref4.yAxis,\n    xAxisTicks = _ref4.xAxisTicks,\n    yAxisTicks = _ref4.yAxisTicks,\n    bandSize = _ref4.bandSize,\n    dataKey = _ref4.dataKey,\n    stackedData = _ref4.stackedData,\n    dataStartIndex = _ref4.dataStartIndex,\n    displayedData = _ref4.displayedData,\n    offset = _ref4.offset;\n  var layout = props.layout;\n  var hasStack = stackedData && stackedData.length;\n  var baseValue = _Area.getBaseValue(props, item, xAxis, yAxis);\n  var isHorizontalLayout = layout === 'horizontal';\n  var isRange = false;\n  var points = displayedData.map(function (entry, index) {\n    var value;\n    if (hasStack) {\n      value = stackedData[dataStartIndex + index];\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!Array.isArray(value)) {\n        value = [baseValue, value];\n      } else {\n        isRange = true;\n      }\n    }\n    var isBreakPoint = value[1] == null || hasStack && getValueByDataKey(entry, dataKey) == null;\n    if (isHorizontalLayout) {\n      return {\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize: bandSize,\n          entry: entry,\n          index: index\n        }),\n        y: isBreakPoint ? null : yAxis.scale(value[1]),\n        value: value,\n        payload: entry\n      };\n    }\n    return {\n      x: isBreakPoint ? null : xAxis.scale(value[1]),\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize: bandSize,\n        entry: entry,\n        index: index\n      }),\n      value: value,\n      payload: entry\n    };\n  });\n  var baseLine;\n  if (hasStack || isRange) {\n    baseLine = points.map(function (entry) {\n      var x = Array.isArray(entry.value) ? entry.value[0] : null;\n      if (isHorizontalLayout) {\n        return {\n          x: entry.x,\n          y: x != null && entry.y != null ? yAxis.scale(x) : null\n        };\n      }\n      return {\n        x: x != null ? xAxis.scale(x) : null,\n        y: entry.y\n      };\n    });\n  } else {\n    baseLine = isHorizontalLayout ? yAxis.scale(baseValue) : xAxis.scale(baseValue);\n  }\n  return _objectSpread({\n    points: points,\n    baseLine: baseLine,\n    layout: layout,\n    isRange: isRange\n  }, offset);\n});\n_defineProperty(Area, \"renderDotItem\", function (option, props) {\n  var dotItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    dotItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (isFunction(option)) {\n    dotItem = option(props);\n  } else {\n    var className = clsx('recharts-area-dot', typeof option !== 'boolean' ? option.className : '');\n    var key = props.key,\n      rest = _objectWithoutProperties(props, _excluded2);\n    dotItem = /*#__PURE__*/React.createElement(Dot, _extends({}, rest, {\n      key: key,\n      className: className\n    }));\n  }\n  return dotItem;\n});", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_Area", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_objectWithoutProperties", "source", "excluded", "target", "_objectWithoutPropertiesLoose", "key", "i", "Object", "getOwnPropertySymbols", "sourceSymbolKeys", "length", "indexOf", "propertyIsEnumerable", "call", "hasOwnProperty", "_extends", "assign", "bind", "arguments", "apply", "ownKeys", "e", "r", "t", "keys", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_callSuper", "_getPrototypeOf", "_possibleConstructorReturn", "_isNativeReflectConstruct", "Reflect", "construct", "self", "_assertThisInitialized", "ReferenceError", "Boolean", "valueOf", "setPrototypeOf", "getPrototypeOf", "__proto__", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "p", "obj", "_toPrimitive", "toPrimitive", "String", "Number", "React", "PureComponent", "clsx", "Animate", "isFunction", "max", "isNil", "isNan", "isEqual", "Curve", "Dot", "Layer", "LabelList", "Global", "isNumber", "uniqueId", "interpolateNumber", "getCateCoordinateOfLine", "getValueByDataKey", "filterProps", "hasClipDot", "Area", "_PureComponent", "_this", "_len", "args", "Array", "_key", "concat", "isAnimationFinished", "onAnimationEnd", "setState", "onAnimationStart", "renderDots", "needClip", "clipDot", "clipPathId", "isAnimationActive", "state", "_this$props", "dot", "points", "dataKey", "areaProps", "customDotProps", "dots", "map", "entry", "dotProps", "index", "cx", "x", "cy", "y", "payload", "renderDotItem", "dotsProps", "clipPath", "createElement", "className", "renderHorizontalRect", "alpha", "_this$props2", "baseLine", "strokeWidth", "startX", "endX", "width", "Math", "abs", "maxY", "isArray", "height", "floor", "parseInt", "renderVerticalRect", "_this$props3", "startY", "endY", "maxX", "renderClipRect", "layout", "renderAreaStatically", "_this$props4", "type", "stroke", "connectNulls", "isRange", "ref", "others", "fill", "renderAreaWithAnimation", "_this2", "_this$props5", "animationBegin", "animationDuration", "animationEasing", "animationId", "_this$state", "prevPoints", "prevBaseLine", "begin", "duration", "isActive", "easing", "from", "to", "handleAnimationEnd", "handleAnimationStart", "_ref", "prevPointsDiffFactor", "stepPoints", "prevPointIndex", "prev", "interpolatorX", "interpolatorY", "stepBaseLine", "interpolator", "_interpolator", "id", "renderArea", "_this$props6", "_this$state2", "totalLength", "render", "_filterProps", "_this$props7", "hide", "top", "left", "xAxis", "yAxis", "hasSinglePoint", "layerClass", "needClipX", "allowDataOverflow", "needClipY", "_ref2", "_ref2$r", "_ref2$strokeWidth", "_ref3", "_ref3$clipDot", "dotSize", "renderCallByParent", "getDerivedStateFromProps", "nextProps", "prevState", "prevAnimationId", "curPoints", "curBaseLine", "fillOpacity", "xAxisId", "yAxisId", "legendType", "activeDot", "isSsr", "item", "chartBaseValue", "baseValue", "itemBaseValue", "numericAxis", "domain", "scale", "domainMax", "domainMin", "min", "_ref4", "xAxisTicks", "yAxisTicks", "bandSize", "stackedData", "dataStartIndex", "displayedData", "offset", "hasStack", "getBaseValue", "isHorizontalLayout", "isBreakPoint", "axis", "ticks", "option", "dotItem", "isValidElement", "cloneElement", "rest"], "sources": ["C:/CMS/webapp/frontend/node_modules/recharts/es6/cartesian/Area.js"], "sourcesContent": ["var _excluded = [\"layout\", \"type\", \"stroke\", \"connectNulls\", \"isRange\", \"ref\"],\n  _excluded2 = [\"key\"];\nvar _Area;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @fileOverview Area\n */\nimport React, { PureComponent } from 'react';\nimport clsx from 'clsx';\nimport Animate from 'react-smooth';\nimport isFunction from 'lodash/isFunction';\nimport max from 'lodash/max';\nimport isNil from 'lodash/isNil';\nimport isNan from 'lodash/isNaN';\nimport isEqual from 'lodash/isEqual';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { Global } from '../util/Global';\nimport { isNumber, uniqueId, interpolateNumber } from '../util/DataUtils';\nimport { getCateCoordinateOfLine, getValueByDataKey } from '../util/ChartUtils';\nimport { filterProps, hasClipDot } from '../util/ReactUtils';\nexport var Area = /*#__PURE__*/function (_PureComponent) {\n  function Area() {\n    var _this;\n    _classCallCheck(this, Area);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Area, [].concat(args));\n    _defineProperty(_this, \"state\", {\n      isAnimationFinished: true\n    });\n    _defineProperty(_this, \"id\", uniqueId('recharts-area-'));\n    _defineProperty(_this, \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (isFunction(onAnimationEnd)) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_this, \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (isFunction(onAnimationStart)) {\n        onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _inherits(Area, _PureComponent);\n  return _createClass(Area, [{\n    key: \"renderDots\",\n    value: function renderDots(needClip, clipDot, clipPathId) {\n      var isAnimationActive = this.props.isAnimationActive;\n      var isAnimationFinished = this.state.isAnimationFinished;\n      if (isAnimationActive && !isAnimationFinished) {\n        return null;\n      }\n      var _this$props = this.props,\n        dot = _this$props.dot,\n        points = _this$props.points,\n        dataKey = _this$props.dataKey;\n      var areaProps = filterProps(this.props, false);\n      var customDotProps = filterProps(dot, true);\n      var dots = points.map(function (entry, i) {\n        var dotProps = _objectSpread(_objectSpread(_objectSpread({\n          key: \"dot-\".concat(i),\n          r: 3\n        }, areaProps), customDotProps), {}, {\n          index: i,\n          cx: entry.x,\n          cy: entry.y,\n          dataKey: dataKey,\n          value: entry.value,\n          payload: entry.payload,\n          points: points\n        });\n        return Area.renderDotItem(dot, dotProps);\n      });\n      var dotsProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipDot ? '' : 'dots-').concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, _extends({\n        className: \"recharts-area-dots\"\n      }, dotsProps), dots);\n    }\n  }, {\n    key: \"renderHorizontalRect\",\n    value: function renderHorizontalRect(alpha) {\n      var _this$props2 = this.props,\n        baseLine = _this$props2.baseLine,\n        points = _this$props2.points,\n        strokeWidth = _this$props2.strokeWidth;\n      var startX = points[0].x;\n      var endX = points[points.length - 1].x;\n      var width = alpha * Math.abs(startX - endX);\n      var maxY = max(points.map(function (entry) {\n        return entry.y || 0;\n      }));\n      if (isNumber(baseLine) && typeof baseLine === 'number') {\n        maxY = Math.max(baseLine, maxY);\n      } else if (baseLine && Array.isArray(baseLine) && baseLine.length) {\n        maxY = Math.max(max(baseLine.map(function (entry) {\n          return entry.y || 0;\n        })), maxY);\n      }\n      if (isNumber(maxY)) {\n        return /*#__PURE__*/React.createElement(\"rect\", {\n          x: startX < endX ? startX : startX - width,\n          y: 0,\n          width: width,\n          height: Math.floor(maxY + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1))\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderVerticalRect\",\n    value: function renderVerticalRect(alpha) {\n      var _this$props3 = this.props,\n        baseLine = _this$props3.baseLine,\n        points = _this$props3.points,\n        strokeWidth = _this$props3.strokeWidth;\n      var startY = points[0].y;\n      var endY = points[points.length - 1].y;\n      var height = alpha * Math.abs(startY - endY);\n      var maxX = max(points.map(function (entry) {\n        return entry.x || 0;\n      }));\n      if (isNumber(baseLine) && typeof baseLine === 'number') {\n        maxX = Math.max(baseLine, maxX);\n      } else if (baseLine && Array.isArray(baseLine) && baseLine.length) {\n        maxX = Math.max(max(baseLine.map(function (entry) {\n          return entry.x || 0;\n        })), maxX);\n      }\n      if (isNumber(maxX)) {\n        return /*#__PURE__*/React.createElement(\"rect\", {\n          x: 0,\n          y: startY < endY ? startY : startY - height,\n          width: maxX + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1),\n          height: Math.floor(height)\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderClipRect\",\n    value: function renderClipRect(alpha) {\n      var layout = this.props.layout;\n      if (layout === 'vertical') {\n        return this.renderVerticalRect(alpha);\n      }\n      return this.renderHorizontalRect(alpha);\n    }\n  }, {\n    key: \"renderAreaStatically\",\n    value: function renderAreaStatically(points, baseLine, needClip, clipPathId) {\n      var _this$props4 = this.props,\n        layout = _this$props4.layout,\n        type = _this$props4.type,\n        stroke = _this$props4.stroke,\n        connectNulls = _this$props4.connectNulls,\n        isRange = _this$props4.isRange,\n        ref = _this$props4.ref,\n        others = _objectWithoutProperties(_this$props4, _excluded);\n      return /*#__PURE__*/React.createElement(Layer, {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      }, /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(others, true), {\n        points: points,\n        connectNulls: connectNulls,\n        type: type,\n        baseLine: baseLine,\n        layout: layout,\n        stroke: \"none\",\n        className: \"recharts-area-area\"\n      })), stroke !== 'none' && /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(this.props, false), {\n        className: \"recharts-area-curve\",\n        layout: layout,\n        type: type,\n        connectNulls: connectNulls,\n        fill: \"none\",\n        points: points\n      })), stroke !== 'none' && isRange && /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(this.props, false), {\n        className: \"recharts-area-curve\",\n        layout: layout,\n        type: type,\n        connectNulls: connectNulls,\n        fill: \"none\",\n        points: baseLine\n      })));\n    }\n  }, {\n    key: \"renderAreaWithAnimation\",\n    value: function renderAreaWithAnimation(needClip, clipPathId) {\n      var _this2 = this;\n      var _this$props5 = this.props,\n        points = _this$props5.points,\n        baseLine = _this$props5.baseLine,\n        isAnimationActive = _this$props5.isAnimationActive,\n        animationBegin = _this$props5.animationBegin,\n        animationDuration = _this$props5.animationDuration,\n        animationEasing = _this$props5.animationEasing,\n        animationId = _this$props5.animationId;\n      var _this$state = this.state,\n        prevPoints = _this$state.prevPoints,\n        prevBaseLine = _this$state.prevBaseLine;\n      // const clipPathId = isNil(id) ? this.id : id;\n\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"area-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        if (prevPoints) {\n          var prevPointsDiffFactor = prevPoints.length / points.length;\n          // update animtaion\n          var stepPoints = points.map(function (entry, index) {\n            var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n            if (prevPoints[prevPointIndex]) {\n              var prev = prevPoints[prevPointIndex];\n              var interpolatorX = interpolateNumber(prev.x, entry.x);\n              var interpolatorY = interpolateNumber(prev.y, entry.y);\n              return _objectSpread(_objectSpread({}, entry), {}, {\n                x: interpolatorX(t),\n                y: interpolatorY(t)\n              });\n            }\n            return entry;\n          });\n          var stepBaseLine;\n          if (isNumber(baseLine) && typeof baseLine === 'number') {\n            var interpolator = interpolateNumber(prevBaseLine, baseLine);\n            stepBaseLine = interpolator(t);\n          } else if (isNil(baseLine) || isNan(baseLine)) {\n            var _interpolator = interpolateNumber(prevBaseLine, 0);\n            stepBaseLine = _interpolator(t);\n          } else {\n            stepBaseLine = baseLine.map(function (entry, index) {\n              var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n              if (prevBaseLine[prevPointIndex]) {\n                var prev = prevBaseLine[prevPointIndex];\n                var interpolatorX = interpolateNumber(prev.x, entry.x);\n                var interpolatorY = interpolateNumber(prev.y, entry.y);\n                return _objectSpread(_objectSpread({}, entry), {}, {\n                  x: interpolatorX(t),\n                  y: interpolatorY(t)\n                });\n              }\n              return entry;\n            });\n          }\n          return _this2.renderAreaStatically(stepPoints, stepBaseLine, needClip, clipPathId);\n        }\n        return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n          id: \"animationClipPath-\".concat(clipPathId)\n        }, _this2.renderClipRect(t))), /*#__PURE__*/React.createElement(Layer, {\n          clipPath: \"url(#animationClipPath-\".concat(clipPathId, \")\")\n        }, _this2.renderAreaStatically(points, baseLine, needClip, clipPathId)));\n      });\n    }\n  }, {\n    key: \"renderArea\",\n    value: function renderArea(needClip, clipPathId) {\n      var _this$props6 = this.props,\n        points = _this$props6.points,\n        baseLine = _this$props6.baseLine,\n        isAnimationActive = _this$props6.isAnimationActive;\n      var _this$state2 = this.state,\n        prevPoints = _this$state2.prevPoints,\n        prevBaseLine = _this$state2.prevBaseLine,\n        totalLength = _this$state2.totalLength;\n      if (isAnimationActive && points && points.length && (!prevPoints && totalLength > 0 || !isEqual(prevPoints, points) || !isEqual(prevBaseLine, baseLine))) {\n        return this.renderAreaWithAnimation(needClip, clipPathId);\n      }\n      return this.renderAreaStatically(points, baseLine, needClip, clipPathId);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _filterProps;\n      var _this$props7 = this.props,\n        hide = _this$props7.hide,\n        dot = _this$props7.dot,\n        points = _this$props7.points,\n        className = _this$props7.className,\n        top = _this$props7.top,\n        left = _this$props7.left,\n        xAxis = _this$props7.xAxis,\n        yAxis = _this$props7.yAxis,\n        width = _this$props7.width,\n        height = _this$props7.height,\n        isAnimationActive = _this$props7.isAnimationActive,\n        id = _this$props7.id;\n      if (hide || !points || !points.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var hasSinglePoint = points.length === 1;\n      var layerClass = clsx('recharts-area', className);\n      var needClipX = xAxis && xAxis.allowDataOverflow;\n      var needClipY = yAxis && yAxis.allowDataOverflow;\n      var needClip = needClipX || needClipY;\n      var clipPathId = isNil(id) ? this.id : id;\n      var _ref2 = (_filterProps = filterProps(dot, false)) !== null && _filterProps !== void 0 ? _filterProps : {\n          r: 3,\n          strokeWidth: 2\n        },\n        _ref2$r = _ref2.r,\n        r = _ref2$r === void 0 ? 3 : _ref2$r,\n        _ref2$strokeWidth = _ref2.strokeWidth,\n        strokeWidth = _ref2$strokeWidth === void 0 ? 2 : _ref2$strokeWidth;\n      var _ref3 = hasClipDot(dot) ? dot : {},\n        _ref3$clipDot = _ref3.clipDot,\n        clipDot = _ref3$clipDot === void 0 ? true : _ref3$clipDot;\n      var dotSize = r * 2 + strokeWidth;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass\n      }, needClipX || needClipY ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: needClipX ? left : left - width / 2,\n        y: needClipY ? top : top - height / 2,\n        width: needClipX ? width : width * 2,\n        height: needClipY ? height : height * 2\n      })), !clipDot && /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-dots-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: left - dotSize / 2,\n        y: top - dotSize / 2,\n        width: width + dotSize,\n        height: height + dotSize\n      }))) : null, !hasSinglePoint ? this.renderArea(needClip, clipPathId) : null, (dot || hasSinglePoint) && this.renderDots(needClip, clipDot, clipPathId), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, points));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curPoints: nextProps.points,\n          curBaseLine: nextProps.baseLine,\n          prevPoints: prevState.curPoints,\n          prevBaseLine: prevState.curBaseLine\n        };\n      }\n      if (nextProps.points !== prevState.curPoints || nextProps.baseLine !== prevState.curBaseLine) {\n        return {\n          curPoints: nextProps.points,\n          curBaseLine: nextProps.baseLine\n        };\n      }\n      return null;\n    }\n  }]);\n}(PureComponent);\n_Area = Area;\n_defineProperty(Area, \"displayName\", 'Area');\n_defineProperty(Area, \"defaultProps\", {\n  stroke: '#3182bd',\n  fill: '#3182bd',\n  fillOpacity: 0.6,\n  xAxisId: 0,\n  yAxisId: 0,\n  legendType: 'line',\n  connectNulls: false,\n  // points of area\n  points: [],\n  dot: false,\n  activeDot: true,\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease'\n});\n_defineProperty(Area, \"getBaseValue\", function (props, item, xAxis, yAxis) {\n  var layout = props.layout,\n    chartBaseValue = props.baseValue;\n  var itemBaseValue = item.props.baseValue;\n\n  // The baseValue can be defined both on the AreaChart as well as on the Area.\n  // The value for the item takes precedence.\n  var baseValue = itemBaseValue !== null && itemBaseValue !== void 0 ? itemBaseValue : chartBaseValue;\n  if (isNumber(baseValue) && typeof baseValue === 'number') {\n    return baseValue;\n  }\n  var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n  var domain = numericAxis.scale.domain();\n  if (numericAxis.type === 'number') {\n    var domainMax = Math.max(domain[0], domain[1]);\n    var domainMin = Math.min(domain[0], domain[1]);\n    if (baseValue === 'dataMin') {\n      return domainMin;\n    }\n    if (baseValue === 'dataMax') {\n      return domainMax;\n    }\n    return domainMax < 0 ? domainMax : Math.max(Math.min(domain[0], domain[1]), 0);\n  }\n  if (baseValue === 'dataMin') {\n    return domain[0];\n  }\n  if (baseValue === 'dataMax') {\n    return domain[1];\n  }\n  return domain[0];\n});\n_defineProperty(Area, \"getComposedData\", function (_ref4) {\n  var props = _ref4.props,\n    item = _ref4.item,\n    xAxis = _ref4.xAxis,\n    yAxis = _ref4.yAxis,\n    xAxisTicks = _ref4.xAxisTicks,\n    yAxisTicks = _ref4.yAxisTicks,\n    bandSize = _ref4.bandSize,\n    dataKey = _ref4.dataKey,\n    stackedData = _ref4.stackedData,\n    dataStartIndex = _ref4.dataStartIndex,\n    displayedData = _ref4.displayedData,\n    offset = _ref4.offset;\n  var layout = props.layout;\n  var hasStack = stackedData && stackedData.length;\n  var baseValue = _Area.getBaseValue(props, item, xAxis, yAxis);\n  var isHorizontalLayout = layout === 'horizontal';\n  var isRange = false;\n  var points = displayedData.map(function (entry, index) {\n    var value;\n    if (hasStack) {\n      value = stackedData[dataStartIndex + index];\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!Array.isArray(value)) {\n        value = [baseValue, value];\n      } else {\n        isRange = true;\n      }\n    }\n    var isBreakPoint = value[1] == null || hasStack && getValueByDataKey(entry, dataKey) == null;\n    if (isHorizontalLayout) {\n      return {\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize: bandSize,\n          entry: entry,\n          index: index\n        }),\n        y: isBreakPoint ? null : yAxis.scale(value[1]),\n        value: value,\n        payload: entry\n      };\n    }\n    return {\n      x: isBreakPoint ? null : xAxis.scale(value[1]),\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize: bandSize,\n        entry: entry,\n        index: index\n      }),\n      value: value,\n      payload: entry\n    };\n  });\n  var baseLine;\n  if (hasStack || isRange) {\n    baseLine = points.map(function (entry) {\n      var x = Array.isArray(entry.value) ? entry.value[0] : null;\n      if (isHorizontalLayout) {\n        return {\n          x: entry.x,\n          y: x != null && entry.y != null ? yAxis.scale(x) : null\n        };\n      }\n      return {\n        x: x != null ? xAxis.scale(x) : null,\n        y: entry.y\n      };\n    });\n  } else {\n    baseLine = isHorizontalLayout ? yAxis.scale(baseValue) : xAxis.scale(baseValue);\n  }\n  return _objectSpread({\n    points: points,\n    baseLine: baseLine,\n    layout: layout,\n    isRange: isRange\n  }, offset);\n});\n_defineProperty(Area, \"renderDotItem\", function (option, props) {\n  var dotItem;\n  if ( /*#__PURE__*/React.isValidElement(option)) {\n    dotItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (isFunction(option)) {\n    dotItem = option(props);\n  } else {\n    var className = clsx('recharts-area-dot', typeof option !== 'boolean' ? option.className : '');\n    var key = props.key,\n      rest = _objectWithoutProperties(props, _excluded2);\n    dotItem = /*#__PURE__*/React.createElement(Dot, _extends({}, rest, {\n      key: key,\n      className: className\n    }));\n  }\n  return dotItem;\n});"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,SAAS,EAAE,KAAK,CAAC;EAC5EC,UAAU,GAAG,CAAC,KAAK,CAAC;AACtB,IAAIC,KAAK;AACT,SAASC,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,wBAAwBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAGC,6BAA6B,CAACH,MAAM,EAAEC,QAAQ,CAAC;EAAE,IAAIG,GAAG,EAAEC,CAAC;EAAE,IAAIC,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGF,MAAM,CAACC,qBAAqB,CAACP,MAAM,CAAC;IAAE,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,gBAAgB,CAACC,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAED,GAAG,GAAGI,gBAAgB,CAACH,CAAC,CAAC;MAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACE,MAAM,CAACR,SAAS,CAACa,oBAAoB,CAACC,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AAC3e,SAASC,6BAA6BA,CAACH,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIE,GAAG,IAAIJ,MAAM,EAAE;IAAE,IAAIM,MAAM,CAACR,SAAS,CAACe,cAAc,CAACD,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAE,IAAIH,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AACtR,SAASY,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGR,MAAM,CAACS,MAAM,GAAGT,MAAM,CAACS,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUd,MAAM,EAAE;IAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACR,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAE,IAAIL,MAAM,GAAGiB,SAAS,CAACZ,CAAC,CAAC;MAAE,KAAK,IAAID,GAAG,IAAIJ,MAAM,EAAE;QAAE,IAAIM,MAAM,CAACR,SAAS,CAACe,cAAc,CAACD,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;UAAEF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOF,MAAM;EAAE,CAAC;EAAE,OAAOY,QAAQ,CAACI,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;AAAE;AAClV,SAASE,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGhB,MAAM,CAACiB,IAAI,CAACH,CAAC,CAAC;EAAE,IAAId,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIb,CAAC,GAAGY,MAAM,CAACC,qBAAqB,CAACa,CAAC,CAAC;IAAEC,CAAC,KAAK3B,CAAC,GAAGA,CAAC,CAAC8B,MAAM,CAAC,UAAUH,CAAC,EAAE;MAAE,OAAOf,MAAM,CAACmB,wBAAwB,CAACL,CAAC,EAAEC,CAAC,CAAC,CAACK,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEJ,CAAC,CAACK,IAAI,CAACT,KAAK,CAACI,CAAC,EAAE5B,CAAC,CAAC;EAAE;EAAE,OAAO4B,CAAC;AAAE;AAC9P,SAASM,aAAaA,CAACR,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,CAACR,MAAM,EAAEY,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIL,SAAS,CAACI,CAAC,CAAC,GAAGJ,SAAS,CAACI,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACb,MAAM,CAACgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACO,OAAO,CAAC,UAAUR,CAAC,EAAE;MAAES,eAAe,CAACV,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGf,MAAM,CAACyB,yBAAyB,GAAGzB,MAAM,CAAC0B,gBAAgB,CAACZ,CAAC,EAAEd,MAAM,CAACyB,yBAAyB,CAACT,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACb,MAAM,CAACgB,CAAC,CAAC,CAAC,CAACO,OAAO,CAAC,UAAUR,CAAC,EAAE;MAAEf,MAAM,CAAC2B,cAAc,CAACb,CAAC,EAAEC,CAAC,EAAEf,MAAM,CAACmB,wBAAwB,CAACH,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASc,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACpC,MAAM,EAAEqC,KAAK,EAAE;EAAE,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkC,KAAK,CAAC9B,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAE,IAAImC,UAAU,GAAGD,KAAK,CAAClC,CAAC,CAAC;IAAEmC,UAAU,CAACd,UAAU,GAAGc,UAAU,CAACd,UAAU,IAAI,KAAK;IAAEc,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAEpC,MAAM,CAAC2B,cAAc,CAAC/B,MAAM,EAAEyC,cAAc,CAACH,UAAU,CAACpC,GAAG,CAAC,EAAEoC,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASI,YAAYA,CAACR,WAAW,EAAES,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACF,WAAW,CAACtC,SAAS,EAAE+C,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACF,WAAW,EAAEU,WAAW,CAAC;EAAExC,MAAM,CAAC2B,cAAc,CAACG,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASW,UAAUA,CAACzB,CAAC,EAAE5B,CAAC,EAAE0B,CAAC,EAAE;EAAE,OAAO1B,CAAC,GAAGsD,eAAe,CAACtD,CAAC,CAAC,EAAEuD,0BAA0B,CAAC3B,CAAC,EAAE4B,yBAAyB,CAAC,CAAC,GAAGC,OAAO,CAACC,SAAS,CAAC1D,CAAC,EAAE0B,CAAC,IAAI,EAAE,EAAE4B,eAAe,CAAC1B,CAAC,CAAC,CAACzB,WAAW,CAAC,GAAGH,CAAC,CAACwB,KAAK,CAACI,CAAC,EAAEF,CAAC,CAAC,CAAC;AAAE;AAC1M,SAAS6B,0BAA0BA,CAACI,IAAI,EAAEzC,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKnB,OAAO,CAACmB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIyB,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOiB,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI;IAAE,IAAI5B,CAAC,GAAG,CAACkC,OAAO,CAAC1D,SAAS,CAAC2D,OAAO,CAAC7C,IAAI,CAACuC,OAAO,CAACC,SAAS,CAACI,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC,OAAOlC,CAAC,EAAE,CAAC;EAAE,OAAO,CAAC4B,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IAAE,OAAO,CAAC,CAAC5B,CAAC;EAAE,CAAC,EAAE,CAAC;AAAE;AAClP,SAAS0B,eAAeA,CAACtD,CAAC,EAAE;EAAEsD,eAAe,GAAG1C,MAAM,CAACoD,cAAc,GAAGpD,MAAM,CAACqD,cAAc,CAAC3C,IAAI,CAAC,CAAC,GAAG,SAASgC,eAAeA,CAACtD,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACkE,SAAS,IAAItD,MAAM,CAACqD,cAAc,CAACjE,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOsD,eAAe,CAACtD,CAAC,CAAC;AAAE;AACnN,SAASmE,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAI1B,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEyB,QAAQ,CAAChE,SAAS,GAAGQ,MAAM,CAAC0D,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACjE,SAAS,EAAE;IAAED,WAAW,EAAE;MAAEoE,KAAK,EAAEH,QAAQ;MAAEpB,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEnC,MAAM,CAAC2B,cAAc,CAAC6B,QAAQ,EAAE,WAAW,EAAE;IAAEpB,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIqB,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAACxE,CAAC,EAAEyE,CAAC,EAAE;EAAED,eAAe,GAAG5D,MAAM,CAACoD,cAAc,GAAGpD,MAAM,CAACoD,cAAc,CAAC1C,IAAI,CAAC,CAAC,GAAG,SAASkD,eAAeA,CAACxE,CAAC,EAAEyE,CAAC,EAAE;IAAEzE,CAAC,CAACkE,SAAS,GAAGO,CAAC;IAAE,OAAOzE,CAAC;EAAE,CAAC;EAAE,OAAOwE,eAAe,CAACxE,CAAC,EAAEyE,CAAC,CAAC;AAAE;AACvM,SAASrC,eAAeA,CAACsC,GAAG,EAAEhE,GAAG,EAAE6D,KAAK,EAAE;EAAE7D,GAAG,GAAGuC,cAAc,CAACvC,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIgE,GAAG,EAAE;IAAE9D,MAAM,CAAC2B,cAAc,CAACmC,GAAG,EAAEhE,GAAG,EAAE;MAAE6D,KAAK,EAAEA,KAAK;MAAEvC,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAE0B,GAAG,CAAChE,GAAG,CAAC,GAAG6D,KAAK;EAAE;EAAE,OAAOG,GAAG;AAAE;AAC3O,SAASzB,cAAcA,CAACrB,CAAC,EAAE;EAAE,IAAIjB,CAAC,GAAGgE,YAAY,CAAC/C,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI7B,OAAO,CAACY,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASgE,YAAYA,CAAC/C,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI5B,OAAO,CAAC6B,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAAC3B,MAAM,CAAC2E,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKlD,CAAC,EAAE;IAAE,IAAIf,CAAC,GAAGe,CAAC,CAACR,IAAI,CAACU,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI5B,OAAO,CAACY,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIgC,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKhB,CAAC,GAAGkD,MAAM,GAAGC,MAAM,EAAElD,CAAC,CAAC;AAAE;AAC3T;AACA;AACA;AACA,OAAOmD,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,GAAG,MAAM,YAAY;AAC5B,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,OAAO,MAAM,gBAAgB;AACpC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,GAAG,QAAQ,cAAc;AAClC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,iBAAiB,QAAQ,mBAAmB;AACzE,SAASC,uBAAuB,EAAEC,iBAAiB,QAAQ,oBAAoB;AAC/E,SAASC,WAAW,EAAEC,UAAU,QAAQ,oBAAoB;AAC5D,OAAO,IAAIC,IAAI,GAAG,aAAa,UAAUC,cAAc,EAAE;EACvD,SAASD,IAAIA,CAAA,EAAG;IACd,IAAIE,KAAK;IACT9D,eAAe,CAAC,IAAI,EAAE4D,IAAI,CAAC;IAC3B,KAAK,IAAIG,IAAI,GAAGhF,SAAS,CAACR,MAAM,EAAEyF,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGnF,SAAS,CAACmF,IAAI,CAAC;IAC9B;IACAJ,KAAK,GAAGjD,UAAU,CAAC,IAAI,EAAE+C,IAAI,EAAE,EAAE,CAACO,MAAM,CAACH,IAAI,CAAC,CAAC;IAC/CpE,eAAe,CAACkE,KAAK,EAAE,OAAO,EAAE;MAC9BM,mBAAmB,EAAE;IACvB,CAAC,CAAC;IACFxE,eAAe,CAACkE,KAAK,EAAE,IAAI,EAAER,QAAQ,CAAC,gBAAgB,CAAC,CAAC;IACxD1D,eAAe,CAACkE,KAAK,EAAE,oBAAoB,EAAE,YAAY;MACvD,IAAIO,cAAc,GAAGP,KAAK,CAACzD,KAAK,CAACgE,cAAc;MAC/CP,KAAK,CAACQ,QAAQ,CAAC;QACbF,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAIzB,UAAU,CAAC0B,cAAc,CAAC,EAAE;QAC9BA,cAAc,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IACFzE,eAAe,CAACkE,KAAK,EAAE,sBAAsB,EAAE,YAAY;MACzD,IAAIS,gBAAgB,GAAGT,KAAK,CAACzD,KAAK,CAACkE,gBAAgB;MACnDT,KAAK,CAACQ,QAAQ,CAAC;QACbF,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAIzB,UAAU,CAAC4B,gBAAgB,CAAC,EAAE;QAChCA,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IACF,OAAOT,KAAK;EACd;EACAnC,SAAS,CAACiC,IAAI,EAAEC,cAAc,CAAC;EAC/B,OAAOnD,YAAY,CAACkD,IAAI,EAAE,CAAC;IACzB1F,GAAG,EAAE,YAAY;IACjB6D,KAAK,EAAE,SAASyC,UAAUA,CAACC,QAAQ,EAAEC,OAAO,EAAEC,UAAU,EAAE;MACxD,IAAIC,iBAAiB,GAAG,IAAI,CAACvE,KAAK,CAACuE,iBAAiB;MACpD,IAAIR,mBAAmB,GAAG,IAAI,CAACS,KAAK,CAACT,mBAAmB;MACxD,IAAIQ,iBAAiB,IAAI,CAACR,mBAAmB,EAAE;QAC7C,OAAO,IAAI;MACb;MACA,IAAIU,WAAW,GAAG,IAAI,CAACzE,KAAK;QAC1B0E,GAAG,GAAGD,WAAW,CAACC,GAAG;QACrBC,MAAM,GAAGF,WAAW,CAACE,MAAM;QAC3BC,OAAO,GAAGH,WAAW,CAACG,OAAO;MAC/B,IAAIC,SAAS,GAAGxB,WAAW,CAAC,IAAI,CAACrD,KAAK,EAAE,KAAK,CAAC;MAC9C,IAAI8E,cAAc,GAAGzB,WAAW,CAACqB,GAAG,EAAE,IAAI,CAAC;MAC3C,IAAIK,IAAI,GAAGJ,MAAM,CAACK,GAAG,CAAC,UAAUC,KAAK,EAAEnH,CAAC,EAAE;QACxC,IAAIoH,QAAQ,GAAG7F,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;UACvDxB,GAAG,EAAE,MAAM,CAACiG,MAAM,CAAChG,CAAC,CAAC;UACrBgB,CAAC,EAAE;QACL,CAAC,EAAE+F,SAAS,CAAC,EAAEC,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;UAClCK,KAAK,EAAErH,CAAC;UACRsH,EAAE,EAAEH,KAAK,CAACI,CAAC;UACXC,EAAE,EAAEL,KAAK,CAACM,CAAC;UACXX,OAAO,EAAEA,OAAO;UAChBlD,KAAK,EAAEuD,KAAK,CAACvD,KAAK;UAClB8D,OAAO,EAAEP,KAAK,CAACO,OAAO;UACtBb,MAAM,EAAEA;QACV,CAAC,CAAC;QACF,OAAOpB,IAAI,CAACkC,aAAa,CAACf,GAAG,EAAEQ,QAAQ,CAAC;MAC1C,CAAC,CAAC;MACF,IAAIQ,SAAS,GAAG;QACdC,QAAQ,EAAEvB,QAAQ,GAAG,gBAAgB,CAACN,MAAM,CAACO,OAAO,GAAG,EAAE,GAAG,OAAO,CAAC,CAACP,MAAM,CAACQ,UAAU,EAAE,GAAG,CAAC,GAAG;MACjG,CAAC;MACD,OAAO,aAAapC,KAAK,CAAC0D,aAAa,CAAC/C,KAAK,EAAEtE,QAAQ,CAAC;QACtDsH,SAAS,EAAE;MACb,CAAC,EAAEH,SAAS,CAAC,EAAEX,IAAI,CAAC;IACtB;EACF,CAAC,EAAE;IACDlH,GAAG,EAAE,sBAAsB;IAC3B6D,KAAK,EAAE,SAASoE,oBAAoBA,CAACC,KAAK,EAAE;MAC1C,IAAIC,YAAY,GAAG,IAAI,CAAChG,KAAK;QAC3BiG,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChCtB,MAAM,GAAGqB,YAAY,CAACrB,MAAM;QAC5BuB,WAAW,GAAGF,YAAY,CAACE,WAAW;MACxC,IAAIC,MAAM,GAAGxB,MAAM,CAAC,CAAC,CAAC,CAACU,CAAC;MACxB,IAAIe,IAAI,GAAGzB,MAAM,CAACA,MAAM,CAACzG,MAAM,GAAG,CAAC,CAAC,CAACmH,CAAC;MACtC,IAAIgB,KAAK,GAAGN,KAAK,GAAGO,IAAI,CAACC,GAAG,CAACJ,MAAM,GAAGC,IAAI,CAAC;MAC3C,IAAII,IAAI,GAAGjE,GAAG,CAACoC,MAAM,CAACK,GAAG,CAAC,UAAUC,KAAK,EAAE;QACzC,OAAOA,KAAK,CAACM,CAAC,IAAI,CAAC;MACrB,CAAC,CAAC,CAAC;MACH,IAAIvC,QAAQ,CAACiD,QAAQ,CAAC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QACtDO,IAAI,GAAGF,IAAI,CAAC/D,GAAG,CAAC0D,QAAQ,EAAEO,IAAI,CAAC;MACjC,CAAC,MAAM,IAAIP,QAAQ,IAAIrC,KAAK,CAAC6C,OAAO,CAACR,QAAQ,CAAC,IAAIA,QAAQ,CAAC/H,MAAM,EAAE;QACjEsI,IAAI,GAAGF,IAAI,CAAC/D,GAAG,CAACA,GAAG,CAAC0D,QAAQ,CAACjB,GAAG,CAAC,UAAUC,KAAK,EAAE;UAChD,OAAOA,KAAK,CAACM,CAAC,IAAI,CAAC;QACrB,CAAC,CAAC,CAAC,EAAEiB,IAAI,CAAC;MACZ;MACA,IAAIxD,QAAQ,CAACwD,IAAI,CAAC,EAAE;QAClB,OAAO,aAAatE,KAAK,CAAC0D,aAAa,CAAC,MAAM,EAAE;UAC9CP,CAAC,EAAEc,MAAM,GAAGC,IAAI,GAAGD,MAAM,GAAGA,MAAM,GAAGE,KAAK;UAC1Cd,CAAC,EAAE,CAAC;UACJc,KAAK,EAAEA,KAAK;UACZK,MAAM,EAAEJ,IAAI,CAACK,KAAK,CAACH,IAAI,IAAIN,WAAW,GAAGU,QAAQ,CAAC,EAAE,CAAC9C,MAAM,CAACoC,WAAW,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;QACpF,CAAC,CAAC;MACJ;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDrI,GAAG,EAAE,oBAAoB;IACzB6D,KAAK,EAAE,SAASmF,kBAAkBA,CAACd,KAAK,EAAE;MACxC,IAAIe,YAAY,GAAG,IAAI,CAAC9G,KAAK;QAC3BiG,QAAQ,GAAGa,YAAY,CAACb,QAAQ;QAChCtB,MAAM,GAAGmC,YAAY,CAACnC,MAAM;QAC5BuB,WAAW,GAAGY,YAAY,CAACZ,WAAW;MACxC,IAAIa,MAAM,GAAGpC,MAAM,CAAC,CAAC,CAAC,CAACY,CAAC;MACxB,IAAIyB,IAAI,GAAGrC,MAAM,CAACA,MAAM,CAACzG,MAAM,GAAG,CAAC,CAAC,CAACqH,CAAC;MACtC,IAAImB,MAAM,GAAGX,KAAK,GAAGO,IAAI,CAACC,GAAG,CAACQ,MAAM,GAAGC,IAAI,CAAC;MAC5C,IAAIC,IAAI,GAAG1E,GAAG,CAACoC,MAAM,CAACK,GAAG,CAAC,UAAUC,KAAK,EAAE;QACzC,OAAOA,KAAK,CAACI,CAAC,IAAI,CAAC;MACrB,CAAC,CAAC,CAAC;MACH,IAAIrC,QAAQ,CAACiD,QAAQ,CAAC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QACtDgB,IAAI,GAAGX,IAAI,CAAC/D,GAAG,CAAC0D,QAAQ,EAAEgB,IAAI,CAAC;MACjC,CAAC,MAAM,IAAIhB,QAAQ,IAAIrC,KAAK,CAAC6C,OAAO,CAACR,QAAQ,CAAC,IAAIA,QAAQ,CAAC/H,MAAM,EAAE;QACjE+I,IAAI,GAAGX,IAAI,CAAC/D,GAAG,CAACA,GAAG,CAAC0D,QAAQ,CAACjB,GAAG,CAAC,UAAUC,KAAK,EAAE;UAChD,OAAOA,KAAK,CAACI,CAAC,IAAI,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE4B,IAAI,CAAC;MACZ;MACA,IAAIjE,QAAQ,CAACiE,IAAI,CAAC,EAAE;QAClB,OAAO,aAAa/E,KAAK,CAAC0D,aAAa,CAAC,MAAM,EAAE;UAC9CP,CAAC,EAAE,CAAC;UACJE,CAAC,EAAEwB,MAAM,GAAGC,IAAI,GAAGD,MAAM,GAAGA,MAAM,GAAGL,MAAM;UAC3CL,KAAK,EAAEY,IAAI,IAAIf,WAAW,GAAGU,QAAQ,CAAC,EAAE,CAAC9C,MAAM,CAACoC,WAAW,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;UACtEQ,MAAM,EAAEJ,IAAI,CAACK,KAAK,CAACD,MAAM;QAC3B,CAAC,CAAC;MACJ;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACD7I,GAAG,EAAE,gBAAgB;IACrB6D,KAAK,EAAE,SAASwF,cAAcA,CAACnB,KAAK,EAAE;MACpC,IAAIoB,MAAM,GAAG,IAAI,CAACnH,KAAK,CAACmH,MAAM;MAC9B,IAAIA,MAAM,KAAK,UAAU,EAAE;QACzB,OAAO,IAAI,CAACN,kBAAkB,CAACd,KAAK,CAAC;MACvC;MACA,OAAO,IAAI,CAACD,oBAAoB,CAACC,KAAK,CAAC;IACzC;EACF,CAAC,EAAE;IACDlI,GAAG,EAAE,sBAAsB;IAC3B6D,KAAK,EAAE,SAAS0F,oBAAoBA,CAACzC,MAAM,EAAEsB,QAAQ,EAAE7B,QAAQ,EAAEE,UAAU,EAAE;MAC3E,IAAI+C,YAAY,GAAG,IAAI,CAACrH,KAAK;QAC3BmH,MAAM,GAAGE,YAAY,CAACF,MAAM;QAC5BG,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxBC,MAAM,GAAGF,YAAY,CAACE,MAAM;QAC5BC,YAAY,GAAGH,YAAY,CAACG,YAAY;QACxCC,OAAO,GAAGJ,YAAY,CAACI,OAAO;QAC9BC,GAAG,GAAGL,YAAY,CAACK,GAAG;QACtBC,MAAM,GAAGnK,wBAAwB,CAAC6J,YAAY,EAAEtK,SAAS,CAAC;MAC5D,OAAO,aAAamF,KAAK,CAAC0D,aAAa,CAAC/C,KAAK,EAAE;QAC7C8C,QAAQ,EAAEvB,QAAQ,GAAG,gBAAgB,CAACN,MAAM,CAACQ,UAAU,EAAE,GAAG,CAAC,GAAG;MAClE,CAAC,EAAE,aAAapC,KAAK,CAAC0D,aAAa,CAACjD,KAAK,EAAEpE,QAAQ,CAAC,CAAC,CAAC,EAAE8E,WAAW,CAACsE,MAAM,EAAE,IAAI,CAAC,EAAE;QACjFhD,MAAM,EAAEA,MAAM;QACd6C,YAAY,EAAEA,YAAY;QAC1BF,IAAI,EAAEA,IAAI;QACVrB,QAAQ,EAAEA,QAAQ;QAClBkB,MAAM,EAAEA,MAAM;QACdI,MAAM,EAAE,MAAM;QACd1B,SAAS,EAAE;MACb,CAAC,CAAC,CAAC,EAAE0B,MAAM,KAAK,MAAM,IAAI,aAAarF,KAAK,CAAC0D,aAAa,CAACjD,KAAK,EAAEpE,QAAQ,CAAC,CAAC,CAAC,EAAE8E,WAAW,CAAC,IAAI,CAACrD,KAAK,EAAE,KAAK,CAAC,EAAE;QAC7G6F,SAAS,EAAE,qBAAqB;QAChCsB,MAAM,EAAEA,MAAM;QACdG,IAAI,EAAEA,IAAI;QACVE,YAAY,EAAEA,YAAY;QAC1BI,IAAI,EAAE,MAAM;QACZjD,MAAM,EAAEA;MACV,CAAC,CAAC,CAAC,EAAE4C,MAAM,KAAK,MAAM,IAAIE,OAAO,IAAI,aAAavF,KAAK,CAAC0D,aAAa,CAACjD,KAAK,EAAEpE,QAAQ,CAAC,CAAC,CAAC,EAAE8E,WAAW,CAAC,IAAI,CAACrD,KAAK,EAAE,KAAK,CAAC,EAAE;QACxH6F,SAAS,EAAE,qBAAqB;QAChCsB,MAAM,EAAEA,MAAM;QACdG,IAAI,EAAEA,IAAI;QACVE,YAAY,EAAEA,YAAY;QAC1BI,IAAI,EAAE,MAAM;QACZjD,MAAM,EAAEsB;MACV,CAAC,CAAC,CAAC,CAAC;IACN;EACF,CAAC,EAAE;IACDpI,GAAG,EAAE,yBAAyB;IAC9B6D,KAAK,EAAE,SAASmG,uBAAuBA,CAACzD,QAAQ,EAAEE,UAAU,EAAE;MAC5D,IAAIwD,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAAC/H,KAAK;QAC3B2E,MAAM,GAAGoD,YAAY,CAACpD,MAAM;QAC5BsB,QAAQ,GAAG8B,YAAY,CAAC9B,QAAQ;QAChC1B,iBAAiB,GAAGwD,YAAY,CAACxD,iBAAiB;QAClDyD,cAAc,GAAGD,YAAY,CAACC,cAAc;QAC5CC,iBAAiB,GAAGF,YAAY,CAACE,iBAAiB;QAClDC,eAAe,GAAGH,YAAY,CAACG,eAAe;QAC9CC,WAAW,GAAGJ,YAAY,CAACI,WAAW;MACxC,IAAIC,WAAW,GAAG,IAAI,CAAC5D,KAAK;QAC1B6D,UAAU,GAAGD,WAAW,CAACC,UAAU;QACnCC,YAAY,GAAGF,WAAW,CAACE,YAAY;MACzC;;MAEA,OAAO,aAAapG,KAAK,CAAC0D,aAAa,CAACvD,OAAO,EAAE;QAC/CkG,KAAK,EAAEP,cAAc;QACrBQ,QAAQ,EAAEP,iBAAiB;QAC3BQ,QAAQ,EAAElE,iBAAiB;QAC3BmE,MAAM,EAAER,eAAe;QACvBS,IAAI,EAAE;UACJ5J,CAAC,EAAE;QACL,CAAC;QACD6J,EAAE,EAAE;UACF7J,CAAC,EAAE;QACL,CAAC;QACDlB,GAAG,EAAE,OAAO,CAACiG,MAAM,CAACqE,WAAW,CAAC;QAChCnE,cAAc,EAAE,IAAI,CAAC6E,kBAAkB;QACvC3E,gBAAgB,EAAE,IAAI,CAAC4E;MACzB,CAAC,EAAE,UAAUC,IAAI,EAAE;QACjB,IAAIhK,CAAC,GAAGgK,IAAI,CAAChK,CAAC;QACd,IAAIsJ,UAAU,EAAE;UACd,IAAIW,oBAAoB,GAAGX,UAAU,CAACnK,MAAM,GAAGyG,MAAM,CAACzG,MAAM;UAC5D;UACA,IAAI+K,UAAU,GAAGtE,MAAM,CAACK,GAAG,CAAC,UAAUC,KAAK,EAAEE,KAAK,EAAE;YAClD,IAAI+D,cAAc,GAAG5C,IAAI,CAACK,KAAK,CAACxB,KAAK,GAAG6D,oBAAoB,CAAC;YAC7D,IAAIX,UAAU,CAACa,cAAc,CAAC,EAAE;cAC9B,IAAIC,IAAI,GAAGd,UAAU,CAACa,cAAc,CAAC;cACrC,IAAIE,aAAa,GAAGlG,iBAAiB,CAACiG,IAAI,CAAC9D,CAAC,EAAEJ,KAAK,CAACI,CAAC,CAAC;cACtD,IAAIgE,aAAa,GAAGnG,iBAAiB,CAACiG,IAAI,CAAC5D,CAAC,EAAEN,KAAK,CAACM,CAAC,CAAC;cACtD,OAAOlG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4F,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;gBACjDI,CAAC,EAAE+D,aAAa,CAACrK,CAAC,CAAC;gBACnBwG,CAAC,EAAE8D,aAAa,CAACtK,CAAC;cACpB,CAAC,CAAC;YACJ;YACA,OAAOkG,KAAK;UACd,CAAC,CAAC;UACF,IAAIqE,YAAY;UAChB,IAAItG,QAAQ,CAACiD,QAAQ,CAAC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;YACtD,IAAIsD,YAAY,GAAGrG,iBAAiB,CAACoF,YAAY,EAAErC,QAAQ,CAAC;YAC5DqD,YAAY,GAAGC,YAAY,CAACxK,CAAC,CAAC;UAChC,CAAC,MAAM,IAAIyD,KAAK,CAACyD,QAAQ,CAAC,IAAIxD,KAAK,CAACwD,QAAQ,CAAC,EAAE;YAC7C,IAAIuD,aAAa,GAAGtG,iBAAiB,CAACoF,YAAY,EAAE,CAAC,CAAC;YACtDgB,YAAY,GAAGE,aAAa,CAACzK,CAAC,CAAC;UACjC,CAAC,MAAM;YACLuK,YAAY,GAAGrD,QAAQ,CAACjB,GAAG,CAAC,UAAUC,KAAK,EAAEE,KAAK,EAAE;cAClD,IAAI+D,cAAc,GAAG5C,IAAI,CAACK,KAAK,CAACxB,KAAK,GAAG6D,oBAAoB,CAAC;cAC7D,IAAIV,YAAY,CAACY,cAAc,CAAC,EAAE;gBAChC,IAAIC,IAAI,GAAGb,YAAY,CAACY,cAAc,CAAC;gBACvC,IAAIE,aAAa,GAAGlG,iBAAiB,CAACiG,IAAI,CAAC9D,CAAC,EAAEJ,KAAK,CAACI,CAAC,CAAC;gBACtD,IAAIgE,aAAa,GAAGnG,iBAAiB,CAACiG,IAAI,CAAC5D,CAAC,EAAEN,KAAK,CAACM,CAAC,CAAC;gBACtD,OAAOlG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4F,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;kBACjDI,CAAC,EAAE+D,aAAa,CAACrK,CAAC,CAAC;kBACnBwG,CAAC,EAAE8D,aAAa,CAACtK,CAAC;gBACpB,CAAC,CAAC;cACJ;cACA,OAAOkG,KAAK;YACd,CAAC,CAAC;UACJ;UACA,OAAO6C,MAAM,CAACV,oBAAoB,CAAC6B,UAAU,EAAEK,YAAY,EAAElF,QAAQ,EAAEE,UAAU,CAAC;QACpF;QACA,OAAO,aAAapC,KAAK,CAAC0D,aAAa,CAAC/C,KAAK,EAAE,IAAI,EAAE,aAAaX,KAAK,CAAC0D,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAa1D,KAAK,CAAC0D,aAAa,CAAC,UAAU,EAAE;UAC/I6D,EAAE,EAAE,oBAAoB,CAAC3F,MAAM,CAACQ,UAAU;QAC5C,CAAC,EAAEwD,MAAM,CAACZ,cAAc,CAACnI,CAAC,CAAC,CAAC,CAAC,EAAE,aAAamD,KAAK,CAAC0D,aAAa,CAAC/C,KAAK,EAAE;UACrE8C,QAAQ,EAAE,yBAAyB,CAAC7B,MAAM,CAACQ,UAAU,EAAE,GAAG;QAC5D,CAAC,EAAEwD,MAAM,CAACV,oBAAoB,CAACzC,MAAM,EAAEsB,QAAQ,EAAE7B,QAAQ,EAAEE,UAAU,CAAC,CAAC,CAAC;MAC1E,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDzG,GAAG,EAAE,YAAY;IACjB6D,KAAK,EAAE,SAASgI,UAAUA,CAACtF,QAAQ,EAAEE,UAAU,EAAE;MAC/C,IAAIqF,YAAY,GAAG,IAAI,CAAC3J,KAAK;QAC3B2E,MAAM,GAAGgF,YAAY,CAAChF,MAAM;QAC5BsB,QAAQ,GAAG0D,YAAY,CAAC1D,QAAQ;QAChC1B,iBAAiB,GAAGoF,YAAY,CAACpF,iBAAiB;MACpD,IAAIqF,YAAY,GAAG,IAAI,CAACpF,KAAK;QAC3B6D,UAAU,GAAGuB,YAAY,CAACvB,UAAU;QACpCC,YAAY,GAAGsB,YAAY,CAACtB,YAAY;QACxCuB,WAAW,GAAGD,YAAY,CAACC,WAAW;MACxC,IAAItF,iBAAiB,IAAII,MAAM,IAAIA,MAAM,CAACzG,MAAM,KAAK,CAACmK,UAAU,IAAIwB,WAAW,GAAG,CAAC,IAAI,CAACnH,OAAO,CAAC2F,UAAU,EAAE1D,MAAM,CAAC,IAAI,CAACjC,OAAO,CAAC4F,YAAY,EAAErC,QAAQ,CAAC,CAAC,EAAE;QACxJ,OAAO,IAAI,CAAC4B,uBAAuB,CAACzD,QAAQ,EAAEE,UAAU,CAAC;MAC3D;MACA,OAAO,IAAI,CAAC8C,oBAAoB,CAACzC,MAAM,EAAEsB,QAAQ,EAAE7B,QAAQ,EAAEE,UAAU,CAAC;IAC1E;EACF,CAAC,EAAE;IACDzG,GAAG,EAAE,QAAQ;IACb6D,KAAK,EAAE,SAASoI,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY;MAChB,IAAIC,YAAY,GAAG,IAAI,CAAChK,KAAK;QAC3BiK,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxBvF,GAAG,GAAGsF,YAAY,CAACtF,GAAG;QACtBC,MAAM,GAAGqF,YAAY,CAACrF,MAAM;QAC5BkB,SAAS,GAAGmE,YAAY,CAACnE,SAAS;QAClCqE,GAAG,GAAGF,YAAY,CAACE,GAAG;QACtBC,IAAI,GAAGH,YAAY,CAACG,IAAI;QACxBC,KAAK,GAAGJ,YAAY,CAACI,KAAK;QAC1BC,KAAK,GAAGL,YAAY,CAACK,KAAK;QAC1BhE,KAAK,GAAG2D,YAAY,CAAC3D,KAAK;QAC1BK,MAAM,GAAGsD,YAAY,CAACtD,MAAM;QAC5BnC,iBAAiB,GAAGyF,YAAY,CAACzF,iBAAiB;QAClDkF,EAAE,GAAGO,YAAY,CAACP,EAAE;MACtB,IAAIQ,IAAI,IAAI,CAACtF,MAAM,IAAI,CAACA,MAAM,CAACzG,MAAM,EAAE;QACrC,OAAO,IAAI;MACb;MACA,IAAI6F,mBAAmB,GAAG,IAAI,CAACS,KAAK,CAACT,mBAAmB;MACxD,IAAIuG,cAAc,GAAG3F,MAAM,CAACzG,MAAM,KAAK,CAAC;MACxC,IAAIqM,UAAU,GAAGnI,IAAI,CAAC,eAAe,EAAEyD,SAAS,CAAC;MACjD,IAAI2E,SAAS,GAAGJ,KAAK,IAAIA,KAAK,CAACK,iBAAiB;MAChD,IAAIC,SAAS,GAAGL,KAAK,IAAIA,KAAK,CAACI,iBAAiB;MAChD,IAAIrG,QAAQ,GAAGoG,SAAS,IAAIE,SAAS;MACrC,IAAIpG,UAAU,GAAG9B,KAAK,CAACiH,EAAE,CAAC,GAAG,IAAI,CAACA,EAAE,GAAGA,EAAE;MACzC,IAAIkB,KAAK,GAAG,CAACZ,YAAY,GAAG1G,WAAW,CAACqB,GAAG,EAAE,KAAK,CAAC,MAAM,IAAI,IAAIqF,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG;UACtGjL,CAAC,EAAE,CAAC;UACJoH,WAAW,EAAE;QACf,CAAC;QACD0E,OAAO,GAAGD,KAAK,CAAC7L,CAAC;QACjBA,CAAC,GAAG8L,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,OAAO;QACpCC,iBAAiB,GAAGF,KAAK,CAACzE,WAAW;QACrCA,WAAW,GAAG2E,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,iBAAiB;MACpE,IAAIC,KAAK,GAAGxH,UAAU,CAACoB,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC,CAAC;QACpCqG,aAAa,GAAGD,KAAK,CAACzG,OAAO;QAC7BA,OAAO,GAAG0G,aAAa,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,aAAa;MAC3D,IAAIC,OAAO,GAAGlM,CAAC,GAAG,CAAC,GAAGoH,WAAW;MACjC,OAAO,aAAahE,KAAK,CAAC0D,aAAa,CAAC/C,KAAK,EAAE;QAC7CgD,SAAS,EAAE0E;MACb,CAAC,EAAEC,SAAS,IAAIE,SAAS,GAAG,aAAaxI,KAAK,CAAC0D,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAa1D,KAAK,CAAC0D,aAAa,CAAC,UAAU,EAAE;QACtH6D,EAAE,EAAE,WAAW,CAAC3F,MAAM,CAACQ,UAAU;MACnC,CAAC,EAAE,aAAapC,KAAK,CAAC0D,aAAa,CAAC,MAAM,EAAE;QAC1CP,CAAC,EAAEmF,SAAS,GAAGL,IAAI,GAAGA,IAAI,GAAG9D,KAAK,GAAG,CAAC;QACtCd,CAAC,EAAEmF,SAAS,GAAGR,GAAG,GAAGA,GAAG,GAAGxD,MAAM,GAAG,CAAC;QACrCL,KAAK,EAAEmE,SAAS,GAAGnE,KAAK,GAAGA,KAAK,GAAG,CAAC;QACpCK,MAAM,EAAEgE,SAAS,GAAGhE,MAAM,GAAGA,MAAM,GAAG;MACxC,CAAC,CAAC,CAAC,EAAE,CAACrC,OAAO,IAAI,aAAanC,KAAK,CAAC0D,aAAa,CAAC,UAAU,EAAE;QAC5D6D,EAAE,EAAE,gBAAgB,CAAC3F,MAAM,CAACQ,UAAU;MACxC,CAAC,EAAE,aAAapC,KAAK,CAAC0D,aAAa,CAAC,MAAM,EAAE;QAC1CP,CAAC,EAAE8E,IAAI,GAAGa,OAAO,GAAG,CAAC;QACrBzF,CAAC,EAAE2E,GAAG,GAAGc,OAAO,GAAG,CAAC;QACpB3E,KAAK,EAAEA,KAAK,GAAG2E,OAAO;QACtBtE,MAAM,EAAEA,MAAM,GAAGsE;MACnB,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,CAACV,cAAc,GAAG,IAAI,CAACZ,UAAU,CAACtF,QAAQ,EAAEE,UAAU,CAAC,GAAG,IAAI,EAAE,CAACI,GAAG,IAAI4F,cAAc,KAAK,IAAI,CAACnG,UAAU,CAACC,QAAQ,EAAEC,OAAO,EAAEC,UAAU,CAAC,EAAE,CAAC,CAACC,iBAAiB,IAAIR,mBAAmB,KAAKjB,SAAS,CAACmI,kBAAkB,CAAC,IAAI,CAACjL,KAAK,EAAE2E,MAAM,CAAC,CAAC;IAC1P;EACF,CAAC,CAAC,EAAE,CAAC;IACH9G,GAAG,EAAE,0BAA0B;IAC/B6D,KAAK,EAAE,SAASwJ,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAID,SAAS,CAAChD,WAAW,KAAKiD,SAAS,CAACC,eAAe,EAAE;QACvD,OAAO;UACLA,eAAe,EAAEF,SAAS,CAAChD,WAAW;UACtCmD,SAAS,EAAEH,SAAS,CAACxG,MAAM;UAC3B4G,WAAW,EAAEJ,SAAS,CAAClF,QAAQ;UAC/BoC,UAAU,EAAE+C,SAAS,CAACE,SAAS;UAC/BhD,YAAY,EAAE8C,SAAS,CAACG;QAC1B,CAAC;MACH;MACA,IAAIJ,SAAS,CAACxG,MAAM,KAAKyG,SAAS,CAACE,SAAS,IAAIH,SAAS,CAAClF,QAAQ,KAAKmF,SAAS,CAACG,WAAW,EAAE;QAC5F,OAAO;UACLD,SAAS,EAAEH,SAAS,CAACxG,MAAM;UAC3B4G,WAAW,EAAEJ,SAAS,CAAClF;QACzB,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC9D,aAAa,CAAC;AAChBlF,KAAK,GAAGsG,IAAI;AACZhE,eAAe,CAACgE,IAAI,EAAE,aAAa,EAAE,MAAM,CAAC;AAC5ChE,eAAe,CAACgE,IAAI,EAAE,cAAc,EAAE;EACpCgE,MAAM,EAAE,SAAS;EACjBK,IAAI,EAAE,SAAS;EACf4D,WAAW,EAAE,GAAG;EAChBC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,UAAU,EAAE,MAAM;EAClBnE,YAAY,EAAE,KAAK;EACnB;EACA7C,MAAM,EAAE,EAAE;EACVD,GAAG,EAAE,KAAK;EACVkH,SAAS,EAAE,IAAI;EACf3B,IAAI,EAAE,KAAK;EACX1F,iBAAiB,EAAE,CAACxB,MAAM,CAAC8I,KAAK;EAChC7D,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE;AACnB,CAAC,CAAC;AACF3I,eAAe,CAACgE,IAAI,EAAE,cAAc,EAAE,UAAUvD,KAAK,EAAE8L,IAAI,EAAE1B,KAAK,EAAEC,KAAK,EAAE;EACzE,IAAIlD,MAAM,GAAGnH,KAAK,CAACmH,MAAM;IACvB4E,cAAc,GAAG/L,KAAK,CAACgM,SAAS;EAClC,IAAIC,aAAa,GAAGH,IAAI,CAAC9L,KAAK,CAACgM,SAAS;;EAExC;EACA;EACA,IAAIA,SAAS,GAAGC,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAGF,cAAc;EACnG,IAAI/I,QAAQ,CAACgJ,SAAS,CAAC,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACxD,OAAOA,SAAS;EAClB;EACA,IAAIE,WAAW,GAAG/E,MAAM,KAAK,YAAY,GAAGkD,KAAK,GAAGD,KAAK;EACzD,IAAI+B,MAAM,GAAGD,WAAW,CAACE,KAAK,CAACD,MAAM,CAAC,CAAC;EACvC,IAAID,WAAW,CAAC5E,IAAI,KAAK,QAAQ,EAAE;IACjC,IAAI+E,SAAS,GAAG/F,IAAI,CAAC/D,GAAG,CAAC4J,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9C,IAAIG,SAAS,GAAGhG,IAAI,CAACiG,GAAG,CAACJ,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9C,IAAIH,SAAS,KAAK,SAAS,EAAE;MAC3B,OAAOM,SAAS;IAClB;IACA,IAAIN,SAAS,KAAK,SAAS,EAAE;MAC3B,OAAOK,SAAS;IAClB;IACA,OAAOA,SAAS,GAAG,CAAC,GAAGA,SAAS,GAAG/F,IAAI,CAAC/D,GAAG,CAAC+D,IAAI,CAACiG,GAAG,CAACJ,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAChF;EACA,IAAIH,SAAS,KAAK,SAAS,EAAE;IAC3B,OAAOG,MAAM,CAAC,CAAC,CAAC;EAClB;EACA,IAAIH,SAAS,KAAK,SAAS,EAAE;IAC3B,OAAOG,MAAM,CAAC,CAAC,CAAC;EAClB;EACA,OAAOA,MAAM,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC;AACF5M,eAAe,CAACgE,IAAI,EAAE,iBAAiB,EAAE,UAAUiJ,KAAK,EAAE;EACxD,IAAIxM,KAAK,GAAGwM,KAAK,CAACxM,KAAK;IACrB8L,IAAI,GAAGU,KAAK,CAACV,IAAI;IACjB1B,KAAK,GAAGoC,KAAK,CAACpC,KAAK;IACnBC,KAAK,GAAGmC,KAAK,CAACnC,KAAK;IACnBoC,UAAU,GAAGD,KAAK,CAACC,UAAU;IAC7BC,UAAU,GAAGF,KAAK,CAACE,UAAU;IAC7BC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzB/H,OAAO,GAAG4H,KAAK,CAAC5H,OAAO;IACvBgI,WAAW,GAAGJ,KAAK,CAACI,WAAW;IAC/BC,cAAc,GAAGL,KAAK,CAACK,cAAc;IACrCC,aAAa,GAAGN,KAAK,CAACM,aAAa;IACnCC,MAAM,GAAGP,KAAK,CAACO,MAAM;EACvB,IAAI5F,MAAM,GAAGnH,KAAK,CAACmH,MAAM;EACzB,IAAI6F,QAAQ,GAAGJ,WAAW,IAAIA,WAAW,CAAC1O,MAAM;EAChD,IAAI8N,SAAS,GAAG/O,KAAK,CAACgQ,YAAY,CAACjN,KAAK,EAAE8L,IAAI,EAAE1B,KAAK,EAAEC,KAAK,CAAC;EAC7D,IAAI6C,kBAAkB,GAAG/F,MAAM,KAAK,YAAY;EAChD,IAAIM,OAAO,GAAG,KAAK;EACnB,IAAI9C,MAAM,GAAGmI,aAAa,CAAC9H,GAAG,CAAC,UAAUC,KAAK,EAAEE,KAAK,EAAE;IACrD,IAAIzD,KAAK;IACT,IAAIsL,QAAQ,EAAE;MACZtL,KAAK,GAAGkL,WAAW,CAACC,cAAc,GAAG1H,KAAK,CAAC;IAC7C,CAAC,MAAM;MACLzD,KAAK,GAAG0B,iBAAiB,CAAC6B,KAAK,EAAEL,OAAO,CAAC;MACzC,IAAI,CAAChB,KAAK,CAAC6C,OAAO,CAAC/E,KAAK,CAAC,EAAE;QACzBA,KAAK,GAAG,CAACsK,SAAS,EAAEtK,KAAK,CAAC;MAC5B,CAAC,MAAM;QACL+F,OAAO,GAAG,IAAI;MAChB;IACF;IACA,IAAI0F,YAAY,GAAGzL,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIsL,QAAQ,IAAI5J,iBAAiB,CAAC6B,KAAK,EAAEL,OAAO,CAAC,IAAI,IAAI;IAC5F,IAAIsI,kBAAkB,EAAE;MACtB,OAAO;QACL7H,CAAC,EAAElC,uBAAuB,CAAC;UACzBiK,IAAI,EAAEhD,KAAK;UACXiD,KAAK,EAAEZ,UAAU;UACjBE,QAAQ,EAAEA,QAAQ;UAClB1H,KAAK,EAAEA,KAAK;UACZE,KAAK,EAAEA;QACT,CAAC,CAAC;QACFI,CAAC,EAAE4H,YAAY,GAAG,IAAI,GAAG9C,KAAK,CAAC+B,KAAK,CAAC1K,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9CA,KAAK,EAAEA,KAAK;QACZ8D,OAAO,EAAEP;MACX,CAAC;IACH;IACA,OAAO;MACLI,CAAC,EAAE8H,YAAY,GAAG,IAAI,GAAG/C,KAAK,CAACgC,KAAK,CAAC1K,KAAK,CAAC,CAAC,CAAC,CAAC;MAC9C6D,CAAC,EAAEpC,uBAAuB,CAAC;QACzBiK,IAAI,EAAE/C,KAAK;QACXgD,KAAK,EAAEX,UAAU;QACjBC,QAAQ,EAAEA,QAAQ;QAClB1H,KAAK,EAAEA,KAAK;QACZE,KAAK,EAAEA;MACT,CAAC,CAAC;MACFzD,KAAK,EAAEA,KAAK;MACZ8D,OAAO,EAAEP;IACX,CAAC;EACH,CAAC,CAAC;EACF,IAAIgB,QAAQ;EACZ,IAAI+G,QAAQ,IAAIvF,OAAO,EAAE;IACvBxB,QAAQ,GAAGtB,MAAM,CAACK,GAAG,CAAC,UAAUC,KAAK,EAAE;MACrC,IAAII,CAAC,GAAGzB,KAAK,CAAC6C,OAAO,CAACxB,KAAK,CAACvD,KAAK,CAAC,GAAGuD,KAAK,CAACvD,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;MAC1D,IAAIwL,kBAAkB,EAAE;QACtB,OAAO;UACL7H,CAAC,EAAEJ,KAAK,CAACI,CAAC;UACVE,CAAC,EAAEF,CAAC,IAAI,IAAI,IAAIJ,KAAK,CAACM,CAAC,IAAI,IAAI,GAAG8E,KAAK,CAAC+B,KAAK,CAAC/G,CAAC,CAAC,GAAG;QACrD,CAAC;MACH;MACA,OAAO;QACLA,CAAC,EAAEA,CAAC,IAAI,IAAI,GAAG+E,KAAK,CAACgC,KAAK,CAAC/G,CAAC,CAAC,GAAG,IAAI;QACpCE,CAAC,EAAEN,KAAK,CAACM;MACX,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,MAAM;IACLU,QAAQ,GAAGiH,kBAAkB,GAAG7C,KAAK,CAAC+B,KAAK,CAACJ,SAAS,CAAC,GAAG5B,KAAK,CAACgC,KAAK,CAACJ,SAAS,CAAC;EACjF;EACA,OAAO3M,aAAa,CAAC;IACnBsF,MAAM,EAAEA,MAAM;IACdsB,QAAQ,EAAEA,QAAQ;IAClBkB,MAAM,EAAEA,MAAM;IACdM,OAAO,EAAEA;EACX,CAAC,EAAEsF,MAAM,CAAC;AACZ,CAAC,CAAC;AACFxN,eAAe,CAACgE,IAAI,EAAE,eAAe,EAAE,UAAU+J,MAAM,EAAEtN,KAAK,EAAE;EAC9D,IAAIuN,OAAO;EACX,IAAK,aAAarL,KAAK,CAACsL,cAAc,CAACF,MAAM,CAAC,EAAE;IAC9CC,OAAO,GAAG,aAAarL,KAAK,CAACuL,YAAY,CAACH,MAAM,EAAEtN,KAAK,CAAC;EAC1D,CAAC,MAAM,IAAIsC,UAAU,CAACgL,MAAM,CAAC,EAAE;IAC7BC,OAAO,GAAGD,MAAM,CAACtN,KAAK,CAAC;EACzB,CAAC,MAAM;IACL,IAAI6F,SAAS,GAAGzD,IAAI,CAAC,mBAAmB,EAAE,OAAOkL,MAAM,KAAK,SAAS,GAAGA,MAAM,CAACzH,SAAS,GAAG,EAAE,CAAC;IAC9F,IAAIhI,GAAG,GAAGmC,KAAK,CAACnC,GAAG;MACjB6P,IAAI,GAAGlQ,wBAAwB,CAACwC,KAAK,EAAEhD,UAAU,CAAC;IACpDuQ,OAAO,GAAG,aAAarL,KAAK,CAAC0D,aAAa,CAAChD,GAAG,EAAErE,QAAQ,CAAC,CAAC,CAAC,EAAEmP,IAAI,EAAE;MACjE7P,GAAG,EAAEA,GAAG;MACRgI,SAAS,EAAEA;IACb,CAAC,CAAC,CAAC;EACL;EACA,OAAO0H,OAAO;AAChB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}