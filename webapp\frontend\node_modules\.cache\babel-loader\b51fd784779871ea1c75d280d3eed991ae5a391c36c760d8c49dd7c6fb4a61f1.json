{"ast": null, "code": "var _excluded = [\"x\", \"y\", \"lineHeight\", \"capHeight\", \"scaleToFit\", \"textAnchor\", \"verticalAnchor\", \"fill\"],\n  _excluded2 = [\"dx\", \"dy\", \"angle\", \"className\", \"breakAll\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nimport React, { useMemo } from 'react';\nimport isNil from 'lodash/isNil';\nimport clsx from 'clsx';\nimport { isNumber, isNumOrStr } from '../util/DataUtils';\nimport { Global } from '../util/Global';\nimport { filterProps } from '../util/ReactUtils';\nimport { getStringSize } from '../util/DOMUtils';\nimport { reduceCSSCalc } from '../util/ReduceCSSCalc';\nvar BREAKING_SPACES = /[ \\f\\n\\r\\t\\v\\u2028\\u2029]+/;\nvar calculateWordWidths = function calculateWordWidths(_ref) {\n  var children = _ref.children,\n    breakAll = _ref.breakAll,\n    style = _ref.style;\n  try {\n    var words = [];\n    if (!isNil(children)) {\n      if (breakAll) {\n        words = children.toString().split('');\n      } else {\n        words = children.toString().split(BREAKING_SPACES);\n      }\n    }\n    var wordsWithComputedWidth = words.map(function (word) {\n      return {\n        word: word,\n        width: getStringSize(word, style).width\n      };\n    });\n    var spaceWidth = breakAll ? 0 : getStringSize(\"\\xA0\", style).width;\n    return {\n      wordsWithComputedWidth: wordsWithComputedWidth,\n      spaceWidth: spaceWidth\n    };\n  } catch (e) {\n    return null;\n  }\n};\nvar calculateWordsByLines = function calculateWordsByLines(_ref2, initialWordsWithComputedWith, spaceWidth, lineWidth, scaleToFit) {\n  var maxLines = _ref2.maxLines,\n    children = _ref2.children,\n    style = _ref2.style,\n    breakAll = _ref2.breakAll;\n  var shouldLimitLines = isNumber(maxLines);\n  var text = children;\n  var calculate = function calculate() {\n    var words = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    return words.reduce(function (result, _ref3) {\n      var word = _ref3.word,\n        width = _ref3.width;\n      var currentLine = result[result.length - 1];\n      if (currentLine && (lineWidth == null || scaleToFit || currentLine.width + width + spaceWidth < Number(lineWidth))) {\n        // Word can be added to an existing line\n        currentLine.words.push(word);\n        currentLine.width += width + spaceWidth;\n      } else {\n        // Add first word to line or word is too long to scaleToFit on existing line\n        var newLine = {\n          words: [word],\n          width: width\n        };\n        result.push(newLine);\n      }\n      return result;\n    }, []);\n  };\n  var originalResult = calculate(initialWordsWithComputedWith);\n  var findLongestLine = function findLongestLine(words) {\n    return words.reduce(function (a, b) {\n      return a.width > b.width ? a : b;\n    });\n  };\n  if (!shouldLimitLines) {\n    return originalResult;\n  }\n  var suffix = '…';\n  var checkOverflow = function checkOverflow(index) {\n    var tempText = text.slice(0, index);\n    var words = calculateWordWidths({\n      breakAll: breakAll,\n      style: style,\n      children: tempText + suffix\n    }).wordsWithComputedWidth;\n    var result = calculate(words);\n    var doesOverflow = result.length > maxLines || findLongestLine(result).width > Number(lineWidth);\n    return [doesOverflow, result];\n  };\n  var start = 0;\n  var end = text.length - 1;\n  var iterations = 0;\n  var trimmedResult;\n  while (start <= end && iterations <= text.length - 1) {\n    var middle = Math.floor((start + end) / 2);\n    var prev = middle - 1;\n    var _checkOverflow = checkOverflow(prev),\n      _checkOverflow2 = _slicedToArray(_checkOverflow, 2),\n      doesPrevOverflow = _checkOverflow2[0],\n      result = _checkOverflow2[1];\n    var _checkOverflow3 = checkOverflow(middle),\n      _checkOverflow4 = _slicedToArray(_checkOverflow3, 1),\n      doesMiddleOverflow = _checkOverflow4[0];\n    if (!doesPrevOverflow && !doesMiddleOverflow) {\n      start = middle + 1;\n    }\n    if (doesPrevOverflow && doesMiddleOverflow) {\n      end = middle - 1;\n    }\n    if (!doesPrevOverflow && doesMiddleOverflow) {\n      trimmedResult = result;\n      break;\n    }\n    iterations++;\n  }\n\n  // Fallback to originalResult (result without trimming) if we cannot find the\n  // where to trim.  This should not happen :tm:\n  return trimmedResult || originalResult;\n};\nvar getWordsWithoutCalculate = function getWordsWithoutCalculate(children) {\n  var words = !isNil(children) ? children.toString().split(BREAKING_SPACES) : [];\n  return [{\n    words: words\n  }];\n};\nvar getWordsByLines = function getWordsByLines(_ref4) {\n  var width = _ref4.width,\n    scaleToFit = _ref4.scaleToFit,\n    children = _ref4.children,\n    style = _ref4.style,\n    breakAll = _ref4.breakAll,\n    maxLines = _ref4.maxLines;\n  // Only perform calculations if using features that require them (multiline, scaleToFit)\n  if ((width || scaleToFit) && !Global.isSsr) {\n    var wordsWithComputedWidth, spaceWidth;\n    var wordWidths = calculateWordWidths({\n      breakAll: breakAll,\n      children: children,\n      style: style\n    });\n    if (wordWidths) {\n      var wcw = wordWidths.wordsWithComputedWidth,\n        sw = wordWidths.spaceWidth;\n      wordsWithComputedWidth = wcw;\n      spaceWidth = sw;\n    } else {\n      return getWordsWithoutCalculate(children);\n    }\n    return calculateWordsByLines({\n      breakAll: breakAll,\n      children: children,\n      maxLines: maxLines,\n      style: style\n    }, wordsWithComputedWidth, spaceWidth, width, scaleToFit);\n  }\n  return getWordsWithoutCalculate(children);\n};\nvar DEFAULT_FILL = '#808080';\nexport var Text = function Text(_ref5) {\n  var _ref5$x = _ref5.x,\n    propsX = _ref5$x === void 0 ? 0 : _ref5$x,\n    _ref5$y = _ref5.y,\n    propsY = _ref5$y === void 0 ? 0 : _ref5$y,\n    _ref5$lineHeight = _ref5.lineHeight,\n    lineHeight = _ref5$lineHeight === void 0 ? '1em' : _ref5$lineHeight,\n    _ref5$capHeight = _ref5.capHeight,\n    capHeight = _ref5$capHeight === void 0 ? '0.71em' : _ref5$capHeight,\n    _ref5$scaleToFit = _ref5.scaleToFit,\n    scaleToFit = _ref5$scaleToFit === void 0 ? false : _ref5$scaleToFit,\n    _ref5$textAnchor = _ref5.textAnchor,\n    textAnchor = _ref5$textAnchor === void 0 ? 'start' : _ref5$textAnchor,\n    _ref5$verticalAnchor = _ref5.verticalAnchor,\n    verticalAnchor = _ref5$verticalAnchor === void 0 ? 'end' : _ref5$verticalAnchor,\n    _ref5$fill = _ref5.fill,\n    fill = _ref5$fill === void 0 ? DEFAULT_FILL : _ref5$fill,\n    props = _objectWithoutProperties(_ref5, _excluded);\n  var wordsByLines = useMemo(function () {\n    return getWordsByLines({\n      breakAll: props.breakAll,\n      children: props.children,\n      maxLines: props.maxLines,\n      scaleToFit: scaleToFit,\n      style: props.style,\n      width: props.width\n    });\n  }, [props.breakAll, props.children, props.maxLines, scaleToFit, props.style, props.width]);\n  var dx = props.dx,\n    dy = props.dy,\n    angle = props.angle,\n    className = props.className,\n    breakAll = props.breakAll,\n    textProps = _objectWithoutProperties(props, _excluded2);\n  if (!isNumOrStr(propsX) || !isNumOrStr(propsY)) {\n    return null;\n  }\n  var x = propsX + (isNumber(dx) ? dx : 0);\n  var y = propsY + (isNumber(dy) ? dy : 0);\n  var startDy;\n  switch (verticalAnchor) {\n    case 'start':\n      startDy = reduceCSSCalc(\"calc(\".concat(capHeight, \")\"));\n      break;\n    case 'middle':\n      startDy = reduceCSSCalc(\"calc(\".concat((wordsByLines.length - 1) / 2, \" * -\").concat(lineHeight, \" + (\").concat(capHeight, \" / 2))\"));\n      break;\n    default:\n      startDy = reduceCSSCalc(\"calc(\".concat(wordsByLines.length - 1, \" * -\").concat(lineHeight, \")\"));\n      break;\n  }\n  var transforms = [];\n  if (scaleToFit) {\n    var lineWidth = wordsByLines[0].width;\n    var width = props.width;\n    transforms.push(\"scale(\".concat((isNumber(width) ? width / lineWidth : 1) / lineWidth, \")\"));\n  }\n  if (angle) {\n    transforms.push(\"rotate(\".concat(angle, \", \").concat(x, \", \").concat(y, \")\"));\n  }\n  if (transforms.length) {\n    textProps.transform = transforms.join(' ');\n  }\n  return /*#__PURE__*/React.createElement(\"text\", _extends({}, filterProps(textProps, true), {\n    x: x,\n    y: y,\n    className: clsx('recharts-text', className),\n    textAnchor: textAnchor,\n    fill: fill.includes('url') ? DEFAULT_FILL : fill\n  }), wordsByLines.map(function (line, index) {\n    var words = line.words.join(breakAll ? '' : ' ');\n    return (/*#__PURE__*/\n      // duplicate words will cause duplicate keys\n      // eslint-disable-next-line react/no-array-index-key\n      React.createElement(\"tspan\", {\n        x: x,\n        dy: index === 0 ? startDy : lineHeight,\n        key: \"\".concat(words, \"-\").concat(index)\n      }, words)\n    );\n  }));\n};", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "_slicedToArray", "arr", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "toString", "slice", "constructor", "name", "Array", "from", "test", "len", "arr2", "r", "l", "t", "Symbol", "iterator", "e", "u", "a", "f", "next", "done", "push", "value", "isArray", "React", "useMemo", "isNil", "clsx", "isNumber", "isNumOrStr", "Global", "filterProps", "getStringSize", "reduceCSSCalc", "BREAKING_SPACES", "calculateWordWidths", "_ref", "children", "breakAll", "style", "words", "split", "wordsWithComputedWidth", "map", "word", "width", "spaceWidth", "calculateWordsByLines", "_ref2", "initialWordsWithComputedWith", "lineWidth", "scaleToFit", "maxLines", "shouldLimitLines", "text", "calculate", "undefined", "reduce", "result", "_ref3", "currentLine", "Number", "newLine", "originalResult", "findLongestLine", "b", "suffix", "checkOverflow", "index", "tempText", "doesOverflow", "start", "end", "iterations", "trimmedResult", "middle", "Math", "floor", "prev", "_checkOverflow", "_checkOverflow2", "doesPrevOverflow", "_checkOverflow3", "_checkOverflow4", "doesMiddleOverflow", "getWordsWithoutCalculate", "getWordsByLines", "_ref4", "isSsr", "wordWidths", "wcw", "sw", "DEFAULT_FILL", "Text", "_ref5", "_ref5$x", "x", "propsX", "_ref5$y", "y", "propsY", "_ref5$lineHeight", "lineHeight", "_ref5$capHeight", "capHeight", "_ref5$scaleToFit", "_ref5$textAnchor", "textAnchor", "_ref5$verticalAnchor", "verticalAnchor", "_ref5$fill", "fill", "props", "wordsByLines", "dx", "dy", "angle", "className", "textProps", "startDy", "concat", "transforms", "transform", "join", "createElement", "includes", "line"], "sources": ["C:/CMS/webapp/frontend/node_modules/recharts/es6/component/Text.js"], "sourcesContent": ["var _excluded = [\"x\", \"y\", \"lineHeight\", \"capHeight\", \"scaleToFit\", \"textAnchor\", \"verticalAnchor\", \"fill\"],\n  _excluded2 = [\"dx\", \"dy\", \"angle\", \"className\", \"breakAll\"];\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport React, { useMemo } from 'react';\nimport isNil from 'lodash/isNil';\nimport clsx from 'clsx';\nimport { isNumber, isNumOrStr } from '../util/DataUtils';\nimport { Global } from '../util/Global';\nimport { filterProps } from '../util/ReactUtils';\nimport { getStringSize } from '../util/DOMUtils';\nimport { reduceCSSCalc } from '../util/ReduceCSSCalc';\nvar BREAKING_SPACES = /[ \\f\\n\\r\\t\\v\\u2028\\u2029]+/;\nvar calculateWordWidths = function calculateWordWidths(_ref) {\n  var children = _ref.children,\n    breakAll = _ref.breakAll,\n    style = _ref.style;\n  try {\n    var words = [];\n    if (!isNil(children)) {\n      if (breakAll) {\n        words = children.toString().split('');\n      } else {\n        words = children.toString().split(BREAKING_SPACES);\n      }\n    }\n    var wordsWithComputedWidth = words.map(function (word) {\n      return {\n        word: word,\n        width: getStringSize(word, style).width\n      };\n    });\n    var spaceWidth = breakAll ? 0 : getStringSize(\"\\xA0\", style).width;\n    return {\n      wordsWithComputedWidth: wordsWithComputedWidth,\n      spaceWidth: spaceWidth\n    };\n  } catch (e) {\n    return null;\n  }\n};\nvar calculateWordsByLines = function calculateWordsByLines(_ref2, initialWordsWithComputedWith, spaceWidth, lineWidth, scaleToFit) {\n  var maxLines = _ref2.maxLines,\n    children = _ref2.children,\n    style = _ref2.style,\n    breakAll = _ref2.breakAll;\n  var shouldLimitLines = isNumber(maxLines);\n  var text = children;\n  var calculate = function calculate() {\n    var words = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    return words.reduce(function (result, _ref3) {\n      var word = _ref3.word,\n        width = _ref3.width;\n      var currentLine = result[result.length - 1];\n      if (currentLine && (lineWidth == null || scaleToFit || currentLine.width + width + spaceWidth < Number(lineWidth))) {\n        // Word can be added to an existing line\n        currentLine.words.push(word);\n        currentLine.width += width + spaceWidth;\n      } else {\n        // Add first word to line or word is too long to scaleToFit on existing line\n        var newLine = {\n          words: [word],\n          width: width\n        };\n        result.push(newLine);\n      }\n      return result;\n    }, []);\n  };\n  var originalResult = calculate(initialWordsWithComputedWith);\n  var findLongestLine = function findLongestLine(words) {\n    return words.reduce(function (a, b) {\n      return a.width > b.width ? a : b;\n    });\n  };\n  if (!shouldLimitLines) {\n    return originalResult;\n  }\n  var suffix = '…';\n  var checkOverflow = function checkOverflow(index) {\n    var tempText = text.slice(0, index);\n    var words = calculateWordWidths({\n      breakAll: breakAll,\n      style: style,\n      children: tempText + suffix\n    }).wordsWithComputedWidth;\n    var result = calculate(words);\n    var doesOverflow = result.length > maxLines || findLongestLine(result).width > Number(lineWidth);\n    return [doesOverflow, result];\n  };\n  var start = 0;\n  var end = text.length - 1;\n  var iterations = 0;\n  var trimmedResult;\n  while (start <= end && iterations <= text.length - 1) {\n    var middle = Math.floor((start + end) / 2);\n    var prev = middle - 1;\n    var _checkOverflow = checkOverflow(prev),\n      _checkOverflow2 = _slicedToArray(_checkOverflow, 2),\n      doesPrevOverflow = _checkOverflow2[0],\n      result = _checkOverflow2[1];\n    var _checkOverflow3 = checkOverflow(middle),\n      _checkOverflow4 = _slicedToArray(_checkOverflow3, 1),\n      doesMiddleOverflow = _checkOverflow4[0];\n    if (!doesPrevOverflow && !doesMiddleOverflow) {\n      start = middle + 1;\n    }\n    if (doesPrevOverflow && doesMiddleOverflow) {\n      end = middle - 1;\n    }\n    if (!doesPrevOverflow && doesMiddleOverflow) {\n      trimmedResult = result;\n      break;\n    }\n    iterations++;\n  }\n\n  // Fallback to originalResult (result without trimming) if we cannot find the\n  // where to trim.  This should not happen :tm:\n  return trimmedResult || originalResult;\n};\nvar getWordsWithoutCalculate = function getWordsWithoutCalculate(children) {\n  var words = !isNil(children) ? children.toString().split(BREAKING_SPACES) : [];\n  return [{\n    words: words\n  }];\n};\nvar getWordsByLines = function getWordsByLines(_ref4) {\n  var width = _ref4.width,\n    scaleToFit = _ref4.scaleToFit,\n    children = _ref4.children,\n    style = _ref4.style,\n    breakAll = _ref4.breakAll,\n    maxLines = _ref4.maxLines;\n  // Only perform calculations if using features that require them (multiline, scaleToFit)\n  if ((width || scaleToFit) && !Global.isSsr) {\n    var wordsWithComputedWidth, spaceWidth;\n    var wordWidths = calculateWordWidths({\n      breakAll: breakAll,\n      children: children,\n      style: style\n    });\n    if (wordWidths) {\n      var wcw = wordWidths.wordsWithComputedWidth,\n        sw = wordWidths.spaceWidth;\n      wordsWithComputedWidth = wcw;\n      spaceWidth = sw;\n    } else {\n      return getWordsWithoutCalculate(children);\n    }\n    return calculateWordsByLines({\n      breakAll: breakAll,\n      children: children,\n      maxLines: maxLines,\n      style: style\n    }, wordsWithComputedWidth, spaceWidth, width, scaleToFit);\n  }\n  return getWordsWithoutCalculate(children);\n};\nvar DEFAULT_FILL = '#808080';\nexport var Text = function Text(_ref5) {\n  var _ref5$x = _ref5.x,\n    propsX = _ref5$x === void 0 ? 0 : _ref5$x,\n    _ref5$y = _ref5.y,\n    propsY = _ref5$y === void 0 ? 0 : _ref5$y,\n    _ref5$lineHeight = _ref5.lineHeight,\n    lineHeight = _ref5$lineHeight === void 0 ? '1em' : _ref5$lineHeight,\n    _ref5$capHeight = _ref5.capHeight,\n    capHeight = _ref5$capHeight === void 0 ? '0.71em' : _ref5$capHeight,\n    _ref5$scaleToFit = _ref5.scaleToFit,\n    scaleToFit = _ref5$scaleToFit === void 0 ? false : _ref5$scaleToFit,\n    _ref5$textAnchor = _ref5.textAnchor,\n    textAnchor = _ref5$textAnchor === void 0 ? 'start' : _ref5$textAnchor,\n    _ref5$verticalAnchor = _ref5.verticalAnchor,\n    verticalAnchor = _ref5$verticalAnchor === void 0 ? 'end' : _ref5$verticalAnchor,\n    _ref5$fill = _ref5.fill,\n    fill = _ref5$fill === void 0 ? DEFAULT_FILL : _ref5$fill,\n    props = _objectWithoutProperties(_ref5, _excluded);\n  var wordsByLines = useMemo(function () {\n    return getWordsByLines({\n      breakAll: props.breakAll,\n      children: props.children,\n      maxLines: props.maxLines,\n      scaleToFit: scaleToFit,\n      style: props.style,\n      width: props.width\n    });\n  }, [props.breakAll, props.children, props.maxLines, scaleToFit, props.style, props.width]);\n  var dx = props.dx,\n    dy = props.dy,\n    angle = props.angle,\n    className = props.className,\n    breakAll = props.breakAll,\n    textProps = _objectWithoutProperties(props, _excluded2);\n  if (!isNumOrStr(propsX) || !isNumOrStr(propsY)) {\n    return null;\n  }\n  var x = propsX + (isNumber(dx) ? dx : 0);\n  var y = propsY + (isNumber(dy) ? dy : 0);\n  var startDy;\n  switch (verticalAnchor) {\n    case 'start':\n      startDy = reduceCSSCalc(\"calc(\".concat(capHeight, \")\"));\n      break;\n    case 'middle':\n      startDy = reduceCSSCalc(\"calc(\".concat((wordsByLines.length - 1) / 2, \" * -\").concat(lineHeight, \" + (\").concat(capHeight, \" / 2))\"));\n      break;\n    default:\n      startDy = reduceCSSCalc(\"calc(\".concat(wordsByLines.length - 1, \" * -\").concat(lineHeight, \")\"));\n      break;\n  }\n  var transforms = [];\n  if (scaleToFit) {\n    var lineWidth = wordsByLines[0].width;\n    var width = props.width;\n    transforms.push(\"scale(\".concat((isNumber(width) ? width / lineWidth : 1) / lineWidth, \")\"));\n  }\n  if (angle) {\n    transforms.push(\"rotate(\".concat(angle, \", \").concat(x, \", \").concat(y, \")\"));\n  }\n  if (transforms.length) {\n    textProps.transform = transforms.join(' ');\n  }\n  return /*#__PURE__*/React.createElement(\"text\", _extends({}, filterProps(textProps, true), {\n    x: x,\n    y: y,\n    className: clsx('recharts-text', className),\n    textAnchor: textAnchor,\n    fill: fill.includes('url') ? DEFAULT_FILL : fill\n  }), wordsByLines.map(function (line, index) {\n    var words = line.words.join(breakAll ? '' : ' ');\n    return (\n      /*#__PURE__*/\n      // duplicate words will cause duplicate keys\n      // eslint-disable-next-line react/no-array-index-key\n      React.createElement(\"tspan\", {\n        x: x,\n        dy: index === 0 ? startDy : lineHeight,\n        key: \"\".concat(words, \"-\").concat(index)\n      }, words)\n    );\n  }));\n};"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,gBAAgB,EAAE,MAAM,CAAC;EACzGC,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC;AAC7D,SAASC,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEP,SAAS,CAAC;AAAE;AAClV,SAASQ,wBAAwBA,CAACN,MAAM,EAAEO,QAAQ,EAAE;EAAE,IAAIP,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAGY,6BAA6B,CAACR,MAAM,EAAEO,QAAQ,CAAC;EAAE,IAAIN,GAAG,EAAEJ,CAAC;EAAE,IAAIJ,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGjB,MAAM,CAACgB,qBAAqB,CAACT,MAAM,CAAC;IAAE,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,gBAAgB,CAACX,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAEI,GAAG,GAAGS,gBAAgB,CAACb,CAAC,CAAC;MAAE,IAAIU,QAAQ,CAACI,OAAO,CAACV,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACR,MAAM,CAACS,SAAS,CAACU,oBAAoB,CAACR,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;MAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AAC3e,SAASY,6BAA6BA,CAACR,MAAM,EAAEO,QAAQ,EAAE;EAAE,IAAIP,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIK,GAAG,IAAID,MAAM,EAAE;IAAE,IAAIP,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;MAAE,IAAIM,QAAQ,CAACI,OAAO,CAACV,GAAG,CAAC,IAAI,CAAC,EAAE;MAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AACtR,SAASiB,cAAcA,CAACC,GAAG,EAAEjB,CAAC,EAAE;EAAE,OAAOkB,eAAe,CAACD,GAAG,CAAC,IAAIE,qBAAqB,CAACF,GAAG,EAAEjB,CAAC,CAAC,IAAIoB,2BAA2B,CAACH,GAAG,EAAEjB,CAAC,CAAC,IAAIqB,gBAAgB,CAAC,CAAC;AAAE;AAC7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAChM,SAASF,2BAA2BA,CAACG,CAAC,EAAEC,MAAM,EAAE;EAAE,IAAI,CAACD,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAG9B,MAAM,CAACS,SAAS,CAACsB,QAAQ,CAACpB,IAAI,CAACgB,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIF,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACM,WAAW,EAAEH,CAAC,GAAGH,CAAC,CAACM,WAAW,CAACC,IAAI;EAAE,IAAIJ,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOK,KAAK,CAACC,IAAI,CAACT,CAAC,CAAC;EAAE,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACO,IAAI,CAACP,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;AAAE;AAC/Z,SAASC,iBAAiBA,CAACR,GAAG,EAAEiB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGjB,GAAG,CAACf,MAAM,EAAEgC,GAAG,GAAGjB,GAAG,CAACf,MAAM;EAAE,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEmC,IAAI,GAAG,IAAIJ,KAAK,CAACG,GAAG,CAAC,EAAElC,CAAC,GAAGkC,GAAG,EAAElC,CAAC,EAAE,EAAEmC,IAAI,CAACnC,CAAC,CAAC,GAAGiB,GAAG,CAACjB,CAAC,CAAC;EAAE,OAAOmC,IAAI;AAAE;AAClL,SAAShB,qBAAqBA,CAACiB,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAG,IAAI,IAAIF,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAOG,MAAM,IAAIH,CAAC,CAACG,MAAM,CAACC,QAAQ,CAAC,IAAIJ,CAAC,CAAC,YAAY,CAAC;EAAE,IAAI,IAAI,IAAIE,CAAC,EAAE;IAAE,IAAIG,CAAC;MAAEf,CAAC;MAAE1B,CAAC;MAAE0C,CAAC;MAAEC,CAAC,GAAG,EAAE;MAAEC,CAAC,GAAG,CAAC,CAAC;MAAErB,CAAC,GAAG,CAAC,CAAC;IAAE,IAAI;MAAE,IAAIvB,CAAC,GAAG,CAACsC,CAAC,GAAGA,CAAC,CAAC/B,IAAI,CAAC6B,CAAC,CAAC,EAAES,IAAI,EAAE,CAAC,KAAKR,CAAC,EAAE;QAAE,IAAIzC,MAAM,CAAC0C,CAAC,CAAC,KAAKA,CAAC,EAAE;QAAQM,CAAC,GAAG,CAAC,CAAC;MAAE,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAACH,CAAC,GAAGzC,CAAC,CAACO,IAAI,CAAC+B,CAAC,CAAC,EAAEQ,IAAI,CAAC,KAAKH,CAAC,CAACI,IAAI,CAACN,CAAC,CAACO,KAAK,CAAC,EAAEL,CAAC,CAACzC,MAAM,KAAKmC,CAAC,CAAC,EAAEO,CAAC,GAAG,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,OAAOR,CAAC,EAAE;MAAEb,CAAC,GAAG,CAAC,CAAC,EAAEG,CAAC,GAAGU,CAAC;IAAE,CAAC,SAAS;MAAE,IAAI;QAAE,IAAI,CAACQ,CAAC,IAAI,IAAI,IAAIN,CAAC,CAAC,QAAQ,CAAC,KAAKI,CAAC,GAAGJ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE1C,MAAM,CAAC8C,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MAAQ,CAAC,SAAS;QAAE,IAAInB,CAAC,EAAE,MAAMG,CAAC;MAAE;IAAE;IAAE,OAAOiB,CAAC;EAAE;AAAE;AACzhB,SAASzB,eAAeA,CAACD,GAAG,EAAE;EAAE,IAAIc,KAAK,CAACkB,OAAO,CAAChC,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AACpE,OAAOiC,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,QAAQ,EAAEC,UAAU,QAAQ,mBAAmB;AACxD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,IAAIC,eAAe,GAAG,4BAA4B;AAClD,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,IAAI,EAAE;EAC3D,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC1BC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,KAAK,GAAGH,IAAI,CAACG,KAAK;EACpB,IAAI;IACF,IAAIC,KAAK,GAAG,EAAE;IACd,IAAI,CAACd,KAAK,CAACW,QAAQ,CAAC,EAAE;MACpB,IAAIC,QAAQ,EAAE;QACZE,KAAK,GAAGH,QAAQ,CAACpC,QAAQ,CAAC,CAAC,CAACwC,KAAK,CAAC,EAAE,CAAC;MACvC,CAAC,MAAM;QACLD,KAAK,GAAGH,QAAQ,CAACpC,QAAQ,CAAC,CAAC,CAACwC,KAAK,CAACP,eAAe,CAAC;MACpD;IACF;IACA,IAAIQ,sBAAsB,GAAGF,KAAK,CAACG,GAAG,CAAC,UAAUC,IAAI,EAAE;MACrD,OAAO;QACLA,IAAI,EAAEA,IAAI;QACVC,KAAK,EAAEb,aAAa,CAACY,IAAI,EAAEL,KAAK,CAAC,CAACM;MACpC,CAAC;IACH,CAAC,CAAC;IACF,IAAIC,UAAU,GAAGR,QAAQ,GAAG,CAAC,GAAGN,aAAa,CAAC,MAAM,EAAEO,KAAK,CAAC,CAACM,KAAK;IAClE,OAAO;MACLH,sBAAsB,EAAEA,sBAAsB;MAC9CI,UAAU,EAAEA;IACd,CAAC;EACH,CAAC,CAAC,OAAO/B,CAAC,EAAE;IACV,OAAO,IAAI;EACb;AACF,CAAC;AACD,IAAIgC,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,KAAK,EAAEC,4BAA4B,EAAEH,UAAU,EAAEI,SAAS,EAAEC,UAAU,EAAE;EACjI,IAAIC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IAC3Bf,QAAQ,GAAGW,KAAK,CAACX,QAAQ;IACzBE,KAAK,GAAGS,KAAK,CAACT,KAAK;IACnBD,QAAQ,GAAGU,KAAK,CAACV,QAAQ;EAC3B,IAAIe,gBAAgB,GAAGzB,QAAQ,CAACwB,QAAQ,CAAC;EACzC,IAAIE,IAAI,GAAGjB,QAAQ;EACnB,IAAIkB,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,IAAIf,KAAK,GAAGjE,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKiF,SAAS,GAAGjF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IAClF,OAAOiE,KAAK,CAACiB,MAAM,CAAC,UAAUC,MAAM,EAAEC,KAAK,EAAE;MAC3C,IAAIf,IAAI,GAAGe,KAAK,CAACf,IAAI;QACnBC,KAAK,GAAGc,KAAK,CAACd,KAAK;MACrB,IAAIe,WAAW,GAAGF,MAAM,CAACA,MAAM,CAAClF,MAAM,GAAG,CAAC,CAAC;MAC3C,IAAIoF,WAAW,KAAKV,SAAS,IAAI,IAAI,IAAIC,UAAU,IAAIS,WAAW,CAACf,KAAK,GAAGA,KAAK,GAAGC,UAAU,GAAGe,MAAM,CAACX,SAAS,CAAC,CAAC,EAAE;QAClH;QACAU,WAAW,CAACpB,KAAK,CAACnB,IAAI,CAACuB,IAAI,CAAC;QAC5BgB,WAAW,CAACf,KAAK,IAAIA,KAAK,GAAGC,UAAU;MACzC,CAAC,MAAM;QACL;QACA,IAAIgB,OAAO,GAAG;UACZtB,KAAK,EAAE,CAACI,IAAI,CAAC;UACbC,KAAK,EAAEA;QACT,CAAC;QACDa,MAAM,CAACrC,IAAI,CAACyC,OAAO,CAAC;MACtB;MACA,OAAOJ,MAAM;IACf,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;EACD,IAAIK,cAAc,GAAGR,SAAS,CAACN,4BAA4B,CAAC;EAC5D,IAAIe,eAAe,GAAG,SAASA,eAAeA,CAACxB,KAAK,EAAE;IACpD,OAAOA,KAAK,CAACiB,MAAM,CAAC,UAAUxC,CAAC,EAAEgD,CAAC,EAAE;MAClC,OAAOhD,CAAC,CAAC4B,KAAK,GAAGoB,CAAC,CAACpB,KAAK,GAAG5B,CAAC,GAAGgD,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;EACD,IAAI,CAACZ,gBAAgB,EAAE;IACrB,OAAOU,cAAc;EACvB;EACA,IAAIG,MAAM,GAAG,GAAG;EAChB,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;IAChD,IAAIC,QAAQ,GAAGf,IAAI,CAACpD,KAAK,CAAC,CAAC,EAAEkE,KAAK,CAAC;IACnC,IAAI5B,KAAK,GAAGL,mBAAmB,CAAC;MAC9BG,QAAQ,EAAEA,QAAQ;MAClBC,KAAK,EAAEA,KAAK;MACZF,QAAQ,EAAEgC,QAAQ,GAAGH;IACvB,CAAC,CAAC,CAACxB,sBAAsB;IACzB,IAAIgB,MAAM,GAAGH,SAAS,CAACf,KAAK,CAAC;IAC7B,IAAI8B,YAAY,GAAGZ,MAAM,CAAClF,MAAM,GAAG4E,QAAQ,IAAIY,eAAe,CAACN,MAAM,CAAC,CAACb,KAAK,GAAGgB,MAAM,CAACX,SAAS,CAAC;IAChG,OAAO,CAACoB,YAAY,EAAEZ,MAAM,CAAC;EAC/B,CAAC;EACD,IAAIa,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAGlB,IAAI,CAAC9E,MAAM,GAAG,CAAC;EACzB,IAAIiG,UAAU,GAAG,CAAC;EAClB,IAAIC,aAAa;EACjB,OAAOH,KAAK,IAAIC,GAAG,IAAIC,UAAU,IAAInB,IAAI,CAAC9E,MAAM,GAAG,CAAC,EAAE;IACpD,IAAImG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACN,KAAK,GAAGC,GAAG,IAAI,CAAC,CAAC;IAC1C,IAAIM,IAAI,GAAGH,MAAM,GAAG,CAAC;IACrB,IAAII,cAAc,GAAGZ,aAAa,CAACW,IAAI,CAAC;MACtCE,eAAe,GAAG1F,cAAc,CAACyF,cAAc,EAAE,CAAC,CAAC;MACnDE,gBAAgB,GAAGD,eAAe,CAAC,CAAC,CAAC;MACrCtB,MAAM,GAAGsB,eAAe,CAAC,CAAC,CAAC;IAC7B,IAAIE,eAAe,GAAGf,aAAa,CAACQ,MAAM,CAAC;MACzCQ,eAAe,GAAG7F,cAAc,CAAC4F,eAAe,EAAE,CAAC,CAAC;MACpDE,kBAAkB,GAAGD,eAAe,CAAC,CAAC,CAAC;IACzC,IAAI,CAACF,gBAAgB,IAAI,CAACG,kBAAkB,EAAE;MAC5Cb,KAAK,GAAGI,MAAM,GAAG,CAAC;IACpB;IACA,IAAIM,gBAAgB,IAAIG,kBAAkB,EAAE;MAC1CZ,GAAG,GAAGG,MAAM,GAAG,CAAC;IAClB;IACA,IAAI,CAACM,gBAAgB,IAAIG,kBAAkB,EAAE;MAC3CV,aAAa,GAAGhB,MAAM;MACtB;IACF;IACAe,UAAU,EAAE;EACd;;EAEA;EACA;EACA,OAAOC,aAAa,IAAIX,cAAc;AACxC,CAAC;AACD,IAAIsB,wBAAwB,GAAG,SAASA,wBAAwBA,CAAChD,QAAQ,EAAE;EACzE,IAAIG,KAAK,GAAG,CAACd,KAAK,CAACW,QAAQ,CAAC,GAAGA,QAAQ,CAACpC,QAAQ,CAAC,CAAC,CAACwC,KAAK,CAACP,eAAe,CAAC,GAAG,EAAE;EAC9E,OAAO,CAAC;IACNM,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ,CAAC;AACD,IAAI8C,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAE;EACpD,IAAI1C,KAAK,GAAG0C,KAAK,CAAC1C,KAAK;IACrBM,UAAU,GAAGoC,KAAK,CAACpC,UAAU;IAC7Bd,QAAQ,GAAGkD,KAAK,CAAClD,QAAQ;IACzBE,KAAK,GAAGgD,KAAK,CAAChD,KAAK;IACnBD,QAAQ,GAAGiD,KAAK,CAACjD,QAAQ;IACzBc,QAAQ,GAAGmC,KAAK,CAACnC,QAAQ;EAC3B;EACA,IAAI,CAACP,KAAK,IAAIM,UAAU,KAAK,CAACrB,MAAM,CAAC0D,KAAK,EAAE;IAC1C,IAAI9C,sBAAsB,EAAEI,UAAU;IACtC,IAAI2C,UAAU,GAAGtD,mBAAmB,CAAC;MACnCG,QAAQ,EAAEA,QAAQ;MAClBD,QAAQ,EAAEA,QAAQ;MAClBE,KAAK,EAAEA;IACT,CAAC,CAAC;IACF,IAAIkD,UAAU,EAAE;MACd,IAAIC,GAAG,GAAGD,UAAU,CAAC/C,sBAAsB;QACzCiD,EAAE,GAAGF,UAAU,CAAC3C,UAAU;MAC5BJ,sBAAsB,GAAGgD,GAAG;MAC5B5C,UAAU,GAAG6C,EAAE;IACjB,CAAC,MAAM;MACL,OAAON,wBAAwB,CAAChD,QAAQ,CAAC;IAC3C;IACA,OAAOU,qBAAqB,CAAC;MAC3BT,QAAQ,EAAEA,QAAQ;MAClBD,QAAQ,EAAEA,QAAQ;MAClBe,QAAQ,EAAEA,QAAQ;MAClBb,KAAK,EAAEA;IACT,CAAC,EAAEG,sBAAsB,EAAEI,UAAU,EAAED,KAAK,EAAEM,UAAU,CAAC;EAC3D;EACA,OAAOkC,wBAAwB,CAAChD,QAAQ,CAAC;AAC3C,CAAC;AACD,IAAIuD,YAAY,GAAG,SAAS;AAC5B,OAAO,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,KAAK,EAAE;EACrC,IAAIC,OAAO,GAAGD,KAAK,CAACE,CAAC;IACnBC,MAAM,GAAGF,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,OAAO;IACzCG,OAAO,GAAGJ,KAAK,CAACK,CAAC;IACjBC,MAAM,GAAGF,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,OAAO;IACzCG,gBAAgB,GAAGP,KAAK,CAACQ,UAAU;IACnCA,UAAU,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IACnEE,eAAe,GAAGT,KAAK,CAACU,SAAS;IACjCA,SAAS,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,eAAe;IACnEE,gBAAgB,GAAGX,KAAK,CAAC3C,UAAU;IACnCA,UAAU,GAAGsD,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IACnEC,gBAAgB,GAAGZ,KAAK,CAACa,UAAU;IACnCA,UAAU,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,gBAAgB;IACrEE,oBAAoB,GAAGd,KAAK,CAACe,cAAc;IAC3CA,cAAc,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,oBAAoB;IAC/EE,UAAU,GAAGhB,KAAK,CAACiB,IAAI;IACvBA,IAAI,GAAGD,UAAU,KAAK,KAAK,CAAC,GAAGlB,YAAY,GAAGkB,UAAU;IACxDE,KAAK,GAAGjI,wBAAwB,CAAC+G,KAAK,EAAE/H,SAAS,CAAC;EACpD,IAAIkJ,YAAY,GAAGxF,OAAO,CAAC,YAAY;IACrC,OAAO6D,eAAe,CAAC;MACrBhD,QAAQ,EAAE0E,KAAK,CAAC1E,QAAQ;MACxBD,QAAQ,EAAE2E,KAAK,CAAC3E,QAAQ;MACxBe,QAAQ,EAAE4D,KAAK,CAAC5D,QAAQ;MACxBD,UAAU,EAAEA,UAAU;MACtBZ,KAAK,EAAEyE,KAAK,CAACzE,KAAK;MAClBM,KAAK,EAAEmE,KAAK,CAACnE;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,CAACmE,KAAK,CAAC1E,QAAQ,EAAE0E,KAAK,CAAC3E,QAAQ,EAAE2E,KAAK,CAAC5D,QAAQ,EAAED,UAAU,EAAE6D,KAAK,CAACzE,KAAK,EAAEyE,KAAK,CAACnE,KAAK,CAAC,CAAC;EAC1F,IAAIqE,EAAE,GAAGF,KAAK,CAACE,EAAE;IACfC,EAAE,GAAGH,KAAK,CAACG,EAAE;IACbC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3B/E,QAAQ,GAAG0E,KAAK,CAAC1E,QAAQ;IACzBgF,SAAS,GAAGvI,wBAAwB,CAACiI,KAAK,EAAEhJ,UAAU,CAAC;EACzD,IAAI,CAAC6D,UAAU,CAACoE,MAAM,CAAC,IAAI,CAACpE,UAAU,CAACuE,MAAM,CAAC,EAAE;IAC9C,OAAO,IAAI;EACb;EACA,IAAIJ,CAAC,GAAGC,MAAM,IAAIrE,QAAQ,CAACsF,EAAE,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;EACxC,IAAIf,CAAC,GAAGC,MAAM,IAAIxE,QAAQ,CAACuF,EAAE,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;EACxC,IAAII,OAAO;EACX,QAAQV,cAAc;IACpB,KAAK,OAAO;MACVU,OAAO,GAAGtF,aAAa,CAAC,OAAO,CAACuF,MAAM,CAAChB,SAAS,EAAE,GAAG,CAAC,CAAC;MACvD;IACF,KAAK,QAAQ;MACXe,OAAO,GAAGtF,aAAa,CAAC,OAAO,CAACuF,MAAM,CAAC,CAACP,YAAY,CAACzI,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,CAACgJ,MAAM,CAAClB,UAAU,EAAE,MAAM,CAAC,CAACkB,MAAM,CAAChB,SAAS,EAAE,QAAQ,CAAC,CAAC;MACrI;IACF;MACEe,OAAO,GAAGtF,aAAa,CAAC,OAAO,CAACuF,MAAM,CAACP,YAAY,CAACzI,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,CAACgJ,MAAM,CAAClB,UAAU,EAAE,GAAG,CAAC,CAAC;MAChG;EACJ;EACA,IAAImB,UAAU,GAAG,EAAE;EACnB,IAAItE,UAAU,EAAE;IACd,IAAID,SAAS,GAAG+D,YAAY,CAAC,CAAC,CAAC,CAACpE,KAAK;IACrC,IAAIA,KAAK,GAAGmE,KAAK,CAACnE,KAAK;IACvB4E,UAAU,CAACpG,IAAI,CAAC,QAAQ,CAACmG,MAAM,CAAC,CAAC5F,QAAQ,CAACiB,KAAK,CAAC,GAAGA,KAAK,GAAGK,SAAS,GAAG,CAAC,IAAIA,SAAS,EAAE,GAAG,CAAC,CAAC;EAC9F;EACA,IAAIkE,KAAK,EAAE;IACTK,UAAU,CAACpG,IAAI,CAAC,SAAS,CAACmG,MAAM,CAACJ,KAAK,EAAE,IAAI,CAAC,CAACI,MAAM,CAACxB,CAAC,EAAE,IAAI,CAAC,CAACwB,MAAM,CAACrB,CAAC,EAAE,GAAG,CAAC,CAAC;EAC/E;EACA,IAAIsB,UAAU,CAACjJ,MAAM,EAAE;IACrB8I,SAAS,CAACI,SAAS,GAAGD,UAAU,CAACE,IAAI,CAAC,GAAG,CAAC;EAC5C;EACA,OAAO,aAAanG,KAAK,CAACoG,aAAa,CAAC,MAAM,EAAE3J,QAAQ,CAAC,CAAC,CAAC,EAAE8D,WAAW,CAACuF,SAAS,EAAE,IAAI,CAAC,EAAE;IACzFtB,CAAC,EAAEA,CAAC;IACJG,CAAC,EAAEA,CAAC;IACJkB,SAAS,EAAE1F,IAAI,CAAC,eAAe,EAAE0F,SAAS,CAAC;IAC3CV,UAAU,EAAEA,UAAU;IACtBI,IAAI,EAAEA,IAAI,CAACc,QAAQ,CAAC,KAAK,CAAC,GAAGjC,YAAY,GAAGmB;EAC9C,CAAC,CAAC,EAAEE,YAAY,CAACtE,GAAG,CAAC,UAAUmF,IAAI,EAAE1D,KAAK,EAAE;IAC1C,IAAI5B,KAAK,GAAGsF,IAAI,CAACtF,KAAK,CAACmF,IAAI,CAACrF,QAAQ,GAAG,EAAE,GAAG,GAAG,CAAC;IAChD,QACE;MACA;MACA;MACAd,KAAK,CAACoG,aAAa,CAAC,OAAO,EAAE;QAC3B5B,CAAC,EAAEA,CAAC;QACJmB,EAAE,EAAE/C,KAAK,KAAK,CAAC,GAAGmD,OAAO,GAAGjB,UAAU;QACtC5H,GAAG,EAAE,EAAE,CAAC8I,MAAM,CAAChF,KAAK,EAAE,GAAG,CAAC,CAACgF,MAAM,CAACpD,KAAK;MACzC,CAAC,EAAE5B,KAAK;IAAC;EAEb,CAAC,CAAC,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}