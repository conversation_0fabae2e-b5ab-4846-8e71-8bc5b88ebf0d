{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"پ\", \"د\"],\n  abbreviated: [\"پ-ز\", \"د-ز\"],\n  wide: [\"پێش زاین\", \"دوای زاین\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"چ1م\", \"چ2م\", \"چ3م\", \"چ4م\"],\n  wide: [\"چارەگی یەکەم\", \"چارەگی دووەم\", \"چارەگی سێیەم\", \"چارەگی چوارەم\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"ک-د\", \"ش\", \"ئا\", \"ن\", \"م\", \"ح\", \"ت\", \"ئا\", \"ئە\", \"تش-ی\", \"تش-د\", \"ک-ی\"],\n  abbreviated: [\"کان-دوو\", \"شوب\", \"ئاد\", \"نیس\", \"مایس\", \"حوز\", \"تەم\", \"ئاب\", \"ئەل\", \"تش-یەک\", \"تش-دوو\", \"کان-یەک\"],\n  wide: [\"کانوونی دووەم\", \"شوبات\", \"ئادار\", \"نیسان\", \"مایس\", \"حوزەیران\", \"تەمموز\", \"ئاب\", \"ئەیلول\", \"تشرینی یەکەم\", \"تشرینی دووەم\", \"کانوونی یەکەم\"]\n};\nconst dayValues = {\n  narrow: [\"ی-ش\", \"د-ش\", \"س-ش\", \"چ-ش\", \"پ-ش\", \"هە\", \"ش\"],\n  short: [\"یە-شە\", \"دوو-شە\", \"سێ-شە\", \"چو-شە\", \"پێ-شە\", \"هەی\", \"شە\"],\n  abbreviated: [\"یەک-شەم\", \"دوو-شەم\", \"سێ-شەم\", \"چوار-شەم\", \"پێنج-شەم\", \"هەینی\", \"شەمە\"],\n  wide: [\"یەک شەمە\", \"دوو شەمە\", \"سێ شەمە\", \"چوار شەمە\", \"پێنج شەمە\", \"هەینی\", \"شەمە\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"پ\",\n    pm: \"د\",\n    midnight: \"ن-ش\",\n    noon: \"ن\",\n    morning: \"بەیانی\",\n    afternoon: \"دوای نیوەڕۆ\",\n    evening: \"ئێوارە\",\n    night: \"شەو\"\n  },\n  abbreviated: {\n    am: \"پ-ن\",\n    pm: \"د-ن\",\n    midnight: \"نیوە شەو\",\n    noon: \"نیوەڕۆ\",\n    morning: \"بەیانی\",\n    afternoon: \"دوای نیوەڕۆ\",\n    evening: \"ئێوارە\",\n    night: \"شەو\"\n  },\n  wide: {\n    am: \"پێش نیوەڕۆ\",\n    pm: \"دوای نیوەڕۆ\",\n    midnight: \"نیوە شەو\",\n    noon: \"نیوەڕۆ\",\n    morning: \"بەیانی\",\n    afternoon: \"دوای نیوەڕۆ\",\n    evening: \"ئێوارە\",\n    night: \"شەو\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"پ\",\n    pm: \"د\",\n    midnight: \"ن-ش\",\n    noon: \"ن\",\n    morning: \"لە بەیانیدا\",\n    afternoon: \"لە دوای نیوەڕۆدا\",\n    evening: \"لە ئێوارەدا\",\n    night: \"لە شەودا\"\n  },\n  abbreviated: {\n    am: \"پ-ن\",\n    pm: \"د-ن\",\n    midnight: \"نیوە شەو\",\n    noon: \"نیوەڕۆ\",\n    morning: \"لە بەیانیدا\",\n    afternoon: \"لە دوای نیوەڕۆدا\",\n    evening: \"لە ئێوارەدا\",\n    night: \"لە شەودا\"\n  },\n  wide: {\n    am: \"پێش نیوەڕۆ\",\n    pm: \"دوای نیوەڕۆ\",\n    midnight: \"نیوە شەو\",\n    noon: \"نیوەڕۆ\",\n    morning: \"لە بەیانیدا\",\n    afternoon: \"لە دوای نیوەڕۆدا\",\n    evening: \"لە ئێوارەدا\",\n    night: \"لە شەودا\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ckb/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"پ\", \"د\"],\n  abbreviated: [\"پ-ز\", \"د-ز\"],\n  wide: [\"پێش زاین\", \"دوای زاین\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"چ1م\", \"چ2م\", \"چ3م\", \"چ4م\"],\n  wide: [\"چارەگی یەکەم\", \"چارەگی دووەم\", \"چارەگی سێیەم\", \"چارەگی چوارەم\"],\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\n    \"ک-د\",\n    \"ش\",\n    \"ئا\",\n    \"ن\",\n    \"م\",\n    \"ح\",\n    \"ت\",\n    \"ئا\",\n    \"ئە\",\n    \"تش-ی\",\n    \"تش-د\",\n    \"ک-ی\",\n  ],\n\n  abbreviated: [\n    \"کان-دوو\",\n    \"شوب\",\n    \"ئاد\",\n    \"نیس\",\n    \"مایس\",\n    \"حوز\",\n    \"تەم\",\n    \"ئاب\",\n    \"ئەل\",\n    \"تش-یەک\",\n    \"تش-دوو\",\n    \"کان-یەک\",\n  ],\n\n  wide: [\n    \"کانوونی دووەم\",\n    \"شوبات\",\n    \"ئادار\",\n    \"نیسان\",\n    \"مایس\",\n    \"حوزەیران\",\n    \"تەمموز\",\n    \"ئاب\",\n    \"ئەیلول\",\n    \"تشرینی یەکەم\",\n    \"تشرینی دووەم\",\n    \"کانوونی یەکەم\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"ی-ش\", \"د-ش\", \"س-ش\", \"چ-ش\", \"پ-ش\", \"هە\", \"ش\"],\n  short: [\"یە-شە\", \"دوو-شە\", \"سێ-شە\", \"چو-شە\", \"پێ-شە\", \"هەی\", \"شە\"],\n  abbreviated: [\n    \"یەک-شەم\",\n    \"دوو-شەم\",\n    \"سێ-شەم\",\n    \"چوار-شەم\",\n    \"پێنج-شەم\",\n    \"هەینی\",\n    \"شەمە\",\n  ],\n\n  wide: [\n    \"یەک شەمە\",\n    \"دوو شەمە\",\n    \"سێ شەمە\",\n    \"چوار شەمە\",\n    \"پێنج شەمە\",\n    \"هەینی\",\n    \"شەمە\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"پ\",\n    pm: \"د\",\n    midnight: \"ن-ش\",\n    noon: \"ن\",\n    morning: \"بەیانی\",\n    afternoon: \"دوای نیوەڕۆ\",\n    evening: \"ئێوارە\",\n    night: \"شەو\",\n  },\n  abbreviated: {\n    am: \"پ-ن\",\n    pm: \"د-ن\",\n    midnight: \"نیوە شەو\",\n    noon: \"نیوەڕۆ\",\n    morning: \"بەیانی\",\n    afternoon: \"دوای نیوەڕۆ\",\n    evening: \"ئێوارە\",\n    night: \"شەو\",\n  },\n  wide: {\n    am: \"پێش نیوەڕۆ\",\n    pm: \"دوای نیوەڕۆ\",\n    midnight: \"نیوە شەو\",\n    noon: \"نیوەڕۆ\",\n    morning: \"بەیانی\",\n    afternoon: \"دوای نیوەڕۆ\",\n    evening: \"ئێوارە\",\n    night: \"شەو\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"پ\",\n    pm: \"د\",\n    midnight: \"ن-ش\",\n    noon: \"ن\",\n    morning: \"لە بەیانیدا\",\n    afternoon: \"لە دوای نیوەڕۆدا\",\n    evening: \"لە ئێوارەدا\",\n    night: \"لە شەودا\",\n  },\n  abbreviated: {\n    am: \"پ-ن\",\n    pm: \"د-ن\",\n    midnight: \"نیوە شەو\",\n    noon: \"نیوەڕۆ\",\n    morning: \"لە بەیانیدا\",\n    afternoon: \"لە دوای نیوەڕۆدا\",\n    evening: \"لە ئێوارەدا\",\n    night: \"لە شەودا\",\n  },\n  wide: {\n    am: \"پێش نیوەڕۆ\",\n    pm: \"دوای نیوەڕۆ\",\n    midnight: \"نیوە شەو\",\n    noon: \"نیوەڕۆ\",\n    morning: \"لە بەیانیدا\",\n    afternoon: \"لە دوای نیوەڕۆدا\",\n    evening: \"لە ئێوارەدا\",\n    night: \"لە شەودا\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EAClBC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAC3BC,IAAI,EAAE,CAAC,UAAU,EAAE,WAAW;AAChC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACzCC,IAAI,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,eAAe;AACxE,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CACN,KAAK,EACL,GAAG,EACH,IAAI,EACJ,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,MAAM,EACN,KAAK,CACN;EAEDC,WAAW,EAAE,CACX,SAAS,EACT,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,SAAS,CACV;EAEDC,IAAI,EAAE,CACJ,eAAe,EACf,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,UAAU,EACV,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,cAAc,EACd,cAAc,EACd,eAAe;AAEnB,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC;EACtDM,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC;EAClEL,WAAW,EAAE,CACX,SAAS,EACT,SAAS,EACT,QAAQ,EACR,UAAU,EACV,UAAU,EACV,OAAO,EACP,MAAM,CACP;EAEDC,IAAI,EAAE,CACJ,UAAU,EACV,UAAU,EACV,SAAS,EACT,WAAW,EACX,WAAW,EACX,OAAO,EACP,MAAM;AAEV,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,aAAa;IACjBC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,aAAa;IACtBC,SAAS,EAAE,kBAAkB;IAC7BC,OAAO,EAAE,aAAa;IACtBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,aAAa;IACtBC,SAAS,EAAE,kBAAkB;IAC7BC,OAAO,EAAE,aAAa;IACtBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,aAAa;IACjBC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,aAAa;IACtBC,SAAS,EAAE,kBAAkB;IAC7BC,OAAO,EAAE,aAAa;IACtBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,OAAOC,MAAM,CAACF,WAAW,CAAC;AAC5B,CAAC;AAED,OAAO,MAAMG,QAAQ,GAAG;EACtBJ,aAAa;EAEbK,GAAG,EAAExB,eAAe,CAAC;IACnByB,MAAM,EAAExB,SAAS;IACjByB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE3B,eAAe,CAAC;IACvByB,MAAM,EAAEpB,aAAa;IACrBqB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE7B,eAAe,CAAC;IACrByB,MAAM,EAAEnB,WAAW;IACnBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE9B,eAAe,CAAC;IACnByB,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAE/B,eAAe,CAAC;IACzByB,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEd,yBAAyB;IAC3Ce,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}