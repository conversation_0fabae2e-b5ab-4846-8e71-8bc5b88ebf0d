{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Box, Paper, Typography, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, Divider, Alert, CircularProgress, FormHelperText, IconButton, Chip, Dialog, DialogTitle, DialogContent, DialogActions, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, List, ListItem, ListItemText, ListItemSecondaryAction } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, Cancel as CancelIcon, Warning as WarningIcon, Info as InfoIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stato per la gestione dei passi (mantenuto per compatibilità con funzioni esistenti)\n  const [activeStep, setActiveStep] = useState(0);\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null); // Mantenuto per compatibilità\n  const [incompatibleReelData, setIncompatibleReelData] = useState({\n    cavo: null,\n    bobina: null\n  });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = idBobina => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response || loadError.code === 'ECONNABORTED' || loadError.message && loadError.message.includes('Network Error')) {\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(`${API_URL}/cavi/${cantiereId}`, {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 30000 // 30 secondi\n            });\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine - VERSIONE SEMPLIFICATA\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento di TUTTE le bobine disponibili...');\n\n      // Carica TUTTE le bobine disponibili senza filtri di compatibilità\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n      // Filtra solo per stato (disponibile o in uso)\n      const bobineUtilizzabili = bobineData.filter(bobina => (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') && bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata');\n      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);\n\n      // Identifica le bobine compatibili per evidenziarle nell'interfaccia\n      if (selectedCavo && selectedCavo.tipologia && selectedCavo.n_conduttori && selectedCavo.sezione) {\n        console.log('Identificazione bobine compatibili per evidenziarle...');\n\n        // Gestione speciale per n_conduttori che potrebbe essere nel formato \"X x Y\"\n        let cavoConduttori = String(selectedCavo.n_conduttori || '0');\n        if (cavoConduttori.includes(' x ')) {\n          const parts = cavoConduttori.split(' x ');\n          cavoConduttori = parts[0];\n          console.log(`Formato n_conduttori 'X x Y' rilevato: ${selectedCavo.n_conduttori} -> ${cavoConduttori}`);\n        }\n        const cavoTipologia = String(selectedCavo.tipologia || '');\n        const cavoSezione = String(selectedCavo.sezione || '0');\n\n        // Conta quante bobine sono compatibili (solo per log)\n        const compatibili = bobineUtilizzabili.filter(bobina => {\n          const bobinaTipologia = String(bobina.tipologia || '');\n          const bobinaConduttori = String(bobina.n_conduttori || '0');\n          const bobinaSezione = String(bobina.sezione || '0');\n          return bobinaTipologia === cavoTipologia && bobinaConduttori === cavoConduttori && bobinaSezione === cavoSezione;\n        });\n        console.log(`Bobine compatibili: ${compatibili.length} su ${bobineUtilizzabili.length}`);\n      }\n\n      // Ordina le bobine per metri residui (decrescente)\n      bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n      // Imposta le bobine nel componente\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con pattern: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi che corrispondono al pattern\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi in base all'input (ricerca parziale)\n      const filteredCavi = caviData.filter(cavo => cavo.id_cavo.toLowerCase().includes(cavoIdInput.trim().toLowerCase()));\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti al pattern`);\n\n      // Se c'è una corrispondenza esatta, seleziona direttamente quel cavo\n      const exactMatch = filteredCavi.find(cavo => cavo.id_cavo.toLowerCase() === cavoIdInput.trim().toLowerCase());\n      if (exactMatch) {\n        console.log('Trovata corrispondenza esatta:', exactMatch);\n\n        // Verifica se il cavo è già installato\n        if (exactMatch.stato_installazione === 'Installato' || exactMatch.metratura_reale && exactMatch.metratura_reale > 0) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatch);\n          setAlreadyLaidCavo(exactMatch);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatch.modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatch);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatch);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con pattern \"${cavoIdInput.trim()}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = {\n            ...cavo,\n            modificato_manualmente: 0\n          };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n\n          // Carica le bobine compatibili\n          loadBobine();\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n\n      // Carica le bobine compatibili\n      if (cavo.tipologia && cavo.n_conduttori && cavo.sezione) {\n        console.log(`Caricamento bobine compatibili per il cavo ${cavo.id_cavo}...`);\n        loadBobine();\n      }\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async cavoId => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Gestione speciale per il cambio di bobina\n    if (name === 'id_bobina' && value && value !== 'BOBINA_VUOTA') {\n      // Verifica compatibilità tra cavo e bobina\n      const bobina = bobine.find(b => b.id_bobina === value);\n      if (bobina && selectedCavo) {\n        // Gestione speciale per n_conduttori che potrebbe essere nel formato \"X x Y\"\n        let cavoConduttori = selectedCavo.n_conduttori !== undefined && selectedCavo.n_conduttori !== null ? String(selectedCavo.n_conduttori) : '0';\n        if (cavoConduttori.includes(' x ')) {\n          const parts = cavoConduttori.split(' x ');\n          cavoConduttori = parts[0];\n          console.log(`Formato n_conduttori 'X x Y' rilevato: ${selectedCavo.n_conduttori} -> ${cavoConduttori}`);\n        }\n        const bobinaConduttori = String(bobina.n_conduttori || '0');\n        const isCompatible = bobina.tipologia === selectedCavo.tipologia && bobinaConduttori === cavoConduttori && String(bobina.sezione) === String(selectedCavo.sezione);\n        if (!isCompatible) {\n          console.log('Bobina incompatibile selezionata:', bobina);\n          console.log('Cavo corrente:', selectedCavo);\n          console.log('Confronto conduttori:', {\n            cavoConduttori,\n            bobinaConduttori\n          });\n\n          // Mostra il dialogo di incompatibilità\n          setIncompatibleReelData({\n            cavo: selectedCavo,\n            bobina: bobina\n          });\n          setShowIncompatibleReelDialog(true);\n\n          // Non aggiornare il valore del form finché l'utente non conferma\n          return;\n        }\n      }\n    }\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    } else if (name === 'id_bobina') {\n      // Controllo che sia selezionata una bobina\n      if (!value || value.trim() === '') {\n        error = 'È necessario selezionare una bobina';\n        return false;\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Nota: Lo stato per il dialogo di bobina incompatibile è già dichiarato sopra\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    // Validazione bobina (deve essere selezionata)\n    if (!formData.id_bobina || formData.id_bobina.trim() === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina';\n      isValid = false;\n    }\n    if (isValid) {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n    setActiveStep(prevActiveStep => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep(prevActiveStep => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n\n      // Gestione differenziata per cavi non posati e cavi posati\n      if (!idBobina || idBobina === '') {\n        // È necessario selezionare una bobina, anche BOBINA_VUOTA\n        setFormErrors({\n          ...formErrors,\n          id_bobina: 'È necessario selezionare una bobina'\n        });\n        setLoading(false);\n        return;\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        // Per cavi posati con bobina specifica\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Verifica se è necessario forzare lo stato OVER della bobina\n      forceOver = false;\n\n      // Se si usa BOBINA_VUOTA, imposta sempre forceOver a true\n      if (idBobina === 'BOBINA_VUOTA') {\n        forceOver = true;\n        console.log('Forzando operazione per BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          forceOver = true;\n          console.log(`Forzando stato OVER per la bobina ${idBobina} (metri posati > metri residui)`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        // Anche in questo caso forziamo l'operazione\n        forceOver = true;\n        console.log(`Forzando operazione per metri posati (${metriPosati}) > metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n              await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, forceOver);\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n              if (error.response) {\n                var _error$response$data;\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message;\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, forceOver);\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if (error.response) {\n        var _error$response$data2;\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.detail) || error.message;\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Seleziona un cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 942,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Cerca cavo per ID\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 948,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 9,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavo\",\n              variant: \"outlined\",\n              value: cavoIdInput,\n              onChange: e => setCavoIdInput(e.target.value),\n              placeholder: \"Inserisci l'ID del cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 953,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 952,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              color: \"primary\",\n              onClick: handleSearchCavoById,\n              disabled: caviLoading || !cavoIdInput.trim(),\n              startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 969,\n                columnNumber: 42\n              }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 969,\n                columnNumber: 75\n              }, this),\n              children: \"Cerca\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 962,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 951,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 947,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Seleziona dalla lista\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 979,\n          columnNumber: 11\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 985,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 984,\n          columnNumber: 13\n        }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Non ci sono cavi disponibili da installare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 988,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            maxHeight: '400px',\n            overflow: 'auto'\n          },\n          children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCavoSelect(cavo),\n              children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 999,\n                    columnNumber: 27\n                  }, this), isCableSpare(cavo) ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"SPARE\",\n                    color: \"error\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1001,\n                    columnNumber: 29\n                  }, this) : isCableInstalled(cavo) ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"Installato\",\n                    color: \"success\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1008,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: getCableStateColor(cavo.stato_installazione),\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1015,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 998,\n                  columnNumber: 25\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [cavo.tipologia || 'N/A', \" - \", cavo.n_conduttori || 'N/A', \" x \", cavo.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1026,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1029,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', \" - A: \", cavo.ubicazione_arrivo || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1030,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1033,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A', \" - Metri posati: \", cavo.metratura_reale || '0']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1034,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 996,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  edge: \"end\",\n                  onClick: e => {\n                    e.stopPropagation(); // Prevent triggering the ListItem click\n                    setSelectedCavo(cavo);\n                    setShowCavoDetailsDialog(true);\n                  },\n                  children: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1046,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1041,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1040,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 995,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1050,\n              columnNumber: 19\n            }, this)]\n          }, cavo.id_cavo, true, {\n            fileName: _jsxFileName,\n            lineNumber: 994,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 992,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 978,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 941,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserisci metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1066,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n        cavo: selectedCavo,\n        compact: true,\n        title: \"Dettagli del cavo selezionato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1070,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 'bold'\n          },\n          children: \"Inserisci i metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1077,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                bgcolor: '#f5f5f5',\n                borderRadius: 1,\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold',\n                  color: 'primary.main'\n                },\n                children: \"Informazioni cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1085,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 1,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium'\n                    },\n                    children: \"Metri teorici:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1090,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1089,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [selectedCavo.metri_teorici || 'N/A', \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1093,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1092,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium'\n                    },\n                    children: \"Stato attuale:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1096,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1095,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: selectedCavo.stato_installazione || 'N/D',\n                    size: \"small\",\n                    color: getCableStateColor(selectedCavo.stato_installazione),\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1099,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1098,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1088,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1084,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1083,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                bgcolor: '#f5f5f5',\n                borderRadius: 1,\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold',\n                  color: 'secondary.main'\n                },\n                children: \"Informazioni bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1112,\n                columnNumber: 17\n              }, this), formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' ? (() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                return bobina ? /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"ID Bobina:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1120,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1119,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: getBobinaNumber(bobina.id_bobina)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1123,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1122,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"Metri residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1126,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1125,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1129,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1128,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1132,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1131,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: bobina.stato_bobina || 'N/D',\n                      size: \"small\",\n                      color: getReelStateColor(bobina.stato_bobina),\n                      variant: \"outlined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1135,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1134,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1118,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Bobina non trovata\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1144,\n                  columnNumber: 21\n                }, this);\n              })() : /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: formData.id_bobina === 'BOBINA_VUOTA' ? \"Utilizzo BOBINA VUOTA (nessuna bobina associata)\" : \"Nessuna bobina selezionata\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1147,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1111,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1082,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Metratura posata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            fullWidth: true,\n            label: \"Metri posati\",\n            variant: \"outlined\",\n            name: \"metri_posati\",\n            type: \"number\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            sx: {\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1157,\n          columnNumber: 11\n        }, this), formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: formWarnings.metri_posati\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1180,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1186,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1076,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1065,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = numeroBobina => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = bobina => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = e => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo) {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n            const cavoConduttori = String(selectedCavo.n_conduttori || '0');\n            const cavoSezione = String(selectedCavo.sezione || '0');\n            const bobinaTipologia = String(bobinaEsistente.tipologia || '');\n            const bobinaConduttori = String(bobinaEsistente.n_conduttori || '0');\n            const bobinaSezione = String(bobinaEsistente.sezione || '0');\n\n            // Log per debug\n            console.log(`Verifica compatibilità bobina ${bobinaEsistente.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              n_conduttori: `${bobinaConduttori} === ${cavoConduttori}`,\n              sezione: `${bobinaSezione} === ${cavoSezione}`\n            });\n            if (bobinaTipologia !== cavoTipologia || bobinaConduttori !== cavoConduttori || bobinaSezione !== cavoSezione) {\n              // Mostra il dialogo per bobine incompatibili\n              setIncompatibleReelData({\n                cavo: selectedCavo,\n                bobina: bobinaEsistente\n              });\n              setShowIncompatibleReelDialog(true);\n              return;\n            }\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Associa bobina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          paragraph: true,\n          children: \"Seleziona una bobina da associare al cavo. \\xC8 necessario associare sempre una bobina, anche utilizzando l'opzione \\\"BOBINA VUOTA\\\" se non si desidera associare una bobina specifica.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1319,\n          columnNumber: 11\n        }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1325,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1324,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: \"Inserimento diretto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1332,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                size: \"small\",\n                fullWidth: true,\n                label: \"Numero bobina\",\n                variant: \"outlined\",\n                placeholder: \"Solo il numero (Y)\",\n                helperText: formErrors.id_bobina_input || \"Inserisci solo il numero della bobina\",\n                error: !!formErrors.id_bobina_input,\n                onBlur: handleBobinaNumberInput,\n                sx: {\n                  mb: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mb: 1\n                },\n                children: [\"ID Bobina: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: formData.id_bobina || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1347,\n                  columnNumber: 32\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1346,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1331,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: \"Selezione dalla lista\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1353,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  gutterBottom: true,\n                  sx: {\n                    color: 'primary.main',\n                    fontWeight: 'bold',\n                    mb: 1\n                  },\n                  children: selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1357,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  size: \"small\",\n                  error: !!formErrors.id_bobina,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    id: \"bobina-select-label\",\n                    children: \"Seleziona bobina\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1362,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    labelId: \"bobina-select-label\",\n                    id: \"bobina-select\",\n                    name: \"id_bobina\",\n                    value: formData.id_bobina,\n                    label: \"Seleziona bobina\",\n                    onChange: handleFormChange,\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"BOBINA_VUOTA\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"BOBINA VUOTA\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1372,\n                        columnNumber: 27\n                      }, this), \" (nessuna bobina associata)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1371,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1374,\n                      columnNumber: 25\n                    }, this), bobine.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                      component: \"li\",\n                      sx: {\n                        p: 1,\n                        bgcolor: 'background.paper'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          color: 'success.main'\n                        },\n                        children: [bobine.length, \" bobine compatibili trovate\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1377,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1376,\n                      columnNumber: 27\n                    }, this) : null, bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: bobina.id_bobina,\n                      disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                      sx: {\n                        '&.Mui-selected': {\n                          bgcolor: 'success.light'\n                        },\n                        '&.Mui-selected:hover': {\n                          bgcolor: 'success.light'\n                        },\n                        bgcolor: selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) && String(bobina.sezione) === String(selectedCavo.sezione) ? 'rgba(76, 175, 80, 0.08)' : 'inherit'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          flexDirection: 'column',\n                          width: '100%'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center',\n                            width: '100%'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"body2\",\n                            sx: {\n                              fontWeight: 'bold'\n                            },\n                            children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A']\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1399,\n                            columnNumber: 33\n                          }, this), selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) && String(bobina.sezione) === String(selectedCavo.sezione) && /*#__PURE__*/_jsxDEV(Chip, {\n                            size: \"small\",\n                            label: \"Compatibile\",\n                            color: \"success\",\n                            variant: \"outlined\",\n                            sx: {\n                              height: 20,\n                              fontSize: '0.6rem'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1406,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1398,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            width: '100%'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"caption\",\n                            children: [bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1416,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"caption\",\n                            sx: {\n                              fontWeight: 'bold',\n                              color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                            },\n                            children: [bobina.metri_residui || 0, \" m disponibili\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1419,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1415,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1397,\n                        columnNumber: 29\n                      }, this)\n                    }, bobina.id_bobina, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1383,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1363,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                    children: formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1427,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1361,\n                  columnNumber: 21\n                }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n                  severity: \"warning\",\n                  sx: {\n                    mt: 2,\n                    fontSize: '0.8rem'\n                  },\n                  children: \"Nessuna bobina compatibile trovata. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1433,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1356,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1352,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1329,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Nota\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1443,\n                columnNumber: 19\n              }, this), \": Se selezioni \\\"BOBINA VUOTA\\\", potrai associare una bobina specifica in un secondo momento.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1442,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1441,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1328,\n          columnNumber: 13\n        }, this), !bobineLoading && formData.id_bobina && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: 'background.paper',\n            borderRadius: 1,\n            border: '1px solid #e0e0e0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Dettagli bobina selezionata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1452,\n            columnNumber: 15\n          }, this), (() => {\n            const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n            if (bobina) {\n              return /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Numero:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1462,\n                      columnNumber: 27\n                    }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1461,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Tipologia:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1465,\n                      columnNumber: 27\n                    }, this), \" \", bobina.tipologia || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1464,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Conduttori:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1468,\n                      columnNumber: 27\n                    }, this), \" \", bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1467,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1460,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri totali:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1473,\n                      columnNumber: 27\n                    }, this), \" \", bobina.metri_totali || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1472,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1476,\n                      columnNumber: 27\n                    }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1475,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1479,\n                      columnNumber: 27\n                    }, this), \" \", bobina.stato_bobina || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1478,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1471,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1459,\n                columnNumber: 21\n              }, this);\n            }\n            return /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"error\",\n              children: \"Bobina non trovata nel database\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1486,\n              columnNumber: 19\n            }, this);\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1451,\n          columnNumber: 13\n        }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: \"Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1495,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1318,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1313,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Conferma inserimento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1524,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Riepilogo dati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1529,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo,\n          compact: true,\n          title: \"Dettagli del cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1534,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Informazioni sull'operazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1542,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1548,\n                  columnNumber: 19\n                }, this), \" \", formData.metri_posati, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1547,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato Installazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1551,\n                  columnNumber: 19\n                }, this), \" \", statoInstallazione]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1550,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1546,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina Associata:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1556,\n                  columnNumber: 19\n                }, this), \" \", numeroBobina]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1555,\n                columnNumber: 17\n              }, this), bobinaInfo && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Residui Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1560,\n                  columnNumber: 21\n                }, this), \" \", bobinaInfo.metri_residui, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1559,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1554,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1545,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1541,\n          columnNumber: 11\n        }, this), bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Attenzione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1569,\n            columnNumber: 15\n          }, this), \" I metri posati (\", formData.metri_posati, \"m) superano i metri residui della bobina (\", bobinaInfo.metri_residui, \"m). Questo porter\\xE0 la bobina in stato OVER.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1568,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 3\n          },\n          children: [\"Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\", formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1574,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1528,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1523,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = step => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      // Seleziona Cavo\n      case 1:\n        return renderStep3();\n      // Associa Bobina\n      case 2:\n        return renderStep2();\n      // Inserisci Metri\n      case 3:\n        return renderStep4();\n      // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReelData({\n      cavo: null,\n      bobina: null\n    });\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    const {\n      cavo,\n      bobina\n    } = incompatibleReelData;\n    if (!cavo || !bobina) return;\n    try {\n      setLoading(true);\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: bobina.id_bobina\n      });\n      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate per corrispondere alla bobina ${bobina.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Cerca cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1674,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 9,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"ID Cavo\",\n            variant: \"outlined\",\n            value: cavoIdInput,\n            onChange: e => setCavoIdInput(e.target.value),\n            placeholder: \"Inserisci l'ID del cavo o parte di esso\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1679,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1678,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 3,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleSearchCavoById,\n            disabled: caviLoading || !cavoIdInput.trim(),\n            startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1695,\n              columnNumber: 40\n            }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1695,\n              columnNumber: 73\n            }, this),\n            children: \"Cerca\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1689,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1688,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1677,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1673,\n      columnNumber: 7\n    }, this), showSearchResults && searchResults.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Risultati della ricerca\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1706,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                bgcolor: '#f5f5f5'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1713,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1714,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Conduttori\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1715,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1716,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri Teorici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1717,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1718,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1719,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1712,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1711,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: searchResults.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1725,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1726,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [cavo.n_conduttori || 'N/A', \" x \", cavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1727,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1728,\n                  columnNumber: 71\n                }, this), \"A: \", cavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1728,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [cavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1729,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: cavo.stato_installazione || 'N/D',\n                  size: \"small\",\n                  color: getCableStateColor(cavo.stato_installazione),\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1731,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1730,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  variant: \"contained\",\n                  color: \"primary\",\n                  onClick: () => handleCavoSelect(cavo),\n                  disabled: isCableInstalled(cavo),\n                  children: \"Seleziona\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1739,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1738,\n                columnNumber: 21\n              }, this)]\n            }, cavo.id_cavo, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1724,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1722,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1710,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1709,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1705,\n      columnNumber: 9\n    }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserimento metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1760,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2,\n          bgcolor: '#f5f5f5',\n          borderRadius: 1,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 'bold',\n            color: 'primary.main'\n          },\n          children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1766,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1771,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1771,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Conduttori:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1772,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.n_conduttori || 'N/A', \" x \", selectedCavo.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1772,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri teorici:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1773,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.metri_teorici || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1773,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1770,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione partenza:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1776,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1776,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione arrivo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1777,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1777,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Stato:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1779,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: selectedCavo.stato_installazione || 'N/D',\n                size: \"small\",\n                color: getCableStateColor(selectedCavo.stato_installazione),\n                variant: \"outlined\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1780,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1778,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1775,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1769,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1765,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Metratura posata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1795,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            fullWidth: true,\n            label: \"Metri posati\",\n            variant: \"outlined\",\n            name: \"metri_posati\",\n            type: \"number\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            sx: {\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1798,\n            columnNumber: 15\n          }, this), formWarnings.metri_posati && !formErrors.metri_posati && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2\n            },\n            children: formWarnings.metri_posati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1815,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1794,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Associa bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1823,\n            columnNumber: 15\n          }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'center',\n              my: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1828,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1827,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              sx: {\n                color: 'primary.main',\n                fontWeight: 'bold',\n                mb: 1\n              },\n              children: selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1832,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              error: !!formErrors.id_bobina,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"bobina-select-label\",\n                children: \"Seleziona bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1837,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"bobina-select-label\",\n                id: \"bobina-select\",\n                name: \"id_bobina\",\n                value: formData.id_bobina,\n                label: \"Seleziona bobina\",\n                onChange: handleFormChange,\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"BOBINA_VUOTA\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"BOBINA VUOTA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1847,\n                    columnNumber: 25\n                  }, this), \" (nessuna bobina associata)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1846,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1849,\n                  columnNumber: 23\n                }, this), bobine.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"li\",\n                  sx: {\n                    p: 1,\n                    bgcolor: 'background.paper'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      color: 'success.main'\n                    },\n                    children: [bobine.length, \" bobine compatibili trovate\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1852,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1851,\n                  columnNumber: 25\n                }, this) : null, bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: bobina.id_bobina,\n                  disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                  sx: {\n                    '&.Mui-selected': {\n                      bgcolor: 'success.light'\n                    },\n                    '&.Mui-selected:hover': {\n                      bgcolor: 'success.light'\n                    },\n                    bgcolor: selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) && String(bobina.sezione) === String(selectedCavo.sezione) ? 'rgba(76, 175, 80, 0.08)' : 'inherit'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      flexDirection: 'column',\n                      width: '100%'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        width: '100%'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          fontWeight: 'bold'\n                        },\n                        children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1874,\n                        columnNumber: 31\n                      }, this), selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) && String(bobina.sezione) === String(selectedCavo.sezione) && /*#__PURE__*/_jsxDEV(Chip, {\n                        size: \"small\",\n                        label: \"Compatibile\",\n                        color: \"success\",\n                        variant: \"outlined\",\n                        sx: {\n                          height: 20,\n                          fontSize: '0.6rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1881,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1873,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        width: '100%'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        children: [bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1891,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                        },\n                        children: [bobina.metri_residui || 0, \" m disponibili\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1894,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1890,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1872,\n                    columnNumber: 27\n                  }, this)\n                }, bobina.id_bobina, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1858,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1838,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                children: formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1902,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1836,\n              columnNumber: 19\n            }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mt: 2,\n                fontSize: '0.8rem'\n              },\n              children: \"Nessuna bobina compatibile trovata. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1908,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1831,\n            columnNumber: 17\n          }, this), !bobineLoading && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && (() => {\n            const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n            if (bobina) {\n              return /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 2,\n                  p: 2,\n                  bgcolor: '#f5f5f5',\n                  borderRadius: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Bobina:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1921,\n                    columnNumber: 51\n                  }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1921,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Metri residui:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1922,\n                    columnNumber: 51\n                  }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1922,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Stato:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1924,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: bobina.stato_bobina || 'N/D',\n                    size: \"small\",\n                    color: getReelStateColor(bobina.stato_bobina),\n                    variant: \"outlined\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1925,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1923,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1920,\n                columnNumber: 21\n              }, this);\n            }\n            return null;\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1822,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1792,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          onClick: () => {\n            setSelectedCavo(null);\n            setFormData({\n              id_cavo: '',\n              metri_posati: '',\n              id_bobina: ''\n            });\n          },\n          startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1953,\n            columnNumber: 26\n          }, this),\n          disabled: loading,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1942,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleSubmit,\n          endIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1963,\n            columnNumber: 24\n          }, this),\n          disabled: loading || !formData.metri_posati || !formData.id_bobina,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1966,\n            columnNumber: 26\n          }, this) : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1959,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1941,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1759,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showConfirmDialog,\n      onClose: () => setShowConfirmDialog(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1976,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: confirmDialogProps.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1977,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1975,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1974,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            mt: 2\n          },\n          children: confirmDialogProps.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1981,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1980,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowConfirmDialog(false),\n          color: \"secondary\",\n          variant: \"outlined\",\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1986,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setShowConfirmDialog(false);\n            confirmDialogProps.onConfirm();\n          },\n          color: \"primary\",\n          variant: \"contained\",\n          autoFocus: true,\n          children: \"Conferma\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1989,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1985,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1973,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showAlreadyLaidDialog,\n      onClose: handleCloseAlreadyLaidDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2007,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavo gi\\xE0 posato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2008,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2006,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2005,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: alreadyLaidCavo && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: alreadyLaidCavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2015,\n              columnNumber: 25\n            }, this), \" risulta gi\\xE0 posato (\", alreadyLaidCavo.metratura_reale || 0, \"m).\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2014,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: \"Puoi scegliere di:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2017,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            component: \"ul\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Modificare la bobina associata al cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2021,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Selezionare un altro cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2022,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Annullare l'operazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2023,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2020,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2013,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2011,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 2,\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAlreadyLaidDialog,\n          color: \"secondary\",\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2029,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSelectAnotherCable,\n            color: \"primary\",\n            sx: {\n              mr: 1\n            },\n            children: \"Seleziona altro cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2033,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleModifyReel,\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Modifica bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2036,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2032,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2028,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2004,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: handleCloseIncompatibleReelDialog,\n      cavo: incompatibleReelData.cavo,\n      bobina: incompatibleReelData.bobina,\n      onUpdateCavo: handleUpdateCavoToMatchReel,\n      onSelectAnotherReel: handleSelectAnotherReel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2044,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showCavoDetailsDialog,\n      onClose: () => setShowCavoDetailsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(InfoIcon, {\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2062,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Dettagli Cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2063,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2061,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2060,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2067,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2066,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowCavoDetailsDialog(false),\n          color: \"primary\",\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2070,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2069,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2054,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1671,\n    columnNumber: 5\n  }, this);\n};\n_s(InserisciMetriForm, \"5xQmDMZ4lzfRbP71tmjFjXDgm+0=\", false, function () {\n  return [useNavigate];\n});\n_c = InserisciMetriForm;\nexport default InserisciMetriForm;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Search", "SearchIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "useNavigate", "caviService", "axiosInstance", "IncompatibleReelDialog", "CavoDetailsView", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "parcoCaviService", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "searchResults", "setSearchResults", "showSearchResults", "setShowSearchResults", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "activeStep", "setActiveStep", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReel", "setIncompatibleReel", "incompatibleReelData", "setIncompatibleReelData", "cavo", "bobina", "showAlreadyLaidDialog", "setShowAlreadyLaidDialog", "alreadyLaidCavo", "setAlreadyLaidCavo", "showCavoDetailsDialog", "setShowCavoDetailsDialog", "loadBobine", "getBobinaNumber", "idBobina", "includes", "split", "loadCavi", "console", "log", "caviData", "get<PERSON><PERSON>", "length", "loadError", "error", "isNetworkError", "response", "code", "message", "Promise", "resolve", "setTimeout", "token", "localStorage", "getItem", "API_URL", "defaults", "baseURL", "retryResponse", "get", "headers", "timeout", "data", "retryError", "errorMessage", "detail", "bobine<PERSON><PERSON>", "getBobine", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "stato_bobina", "tipologia", "n_conduttori", "sezione", "cavoConduttori", "String", "parts", "cavoTipologia", "cavoSezione", "compatibili", "bobinaTipologia", "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bobinaSezione", "sort", "a", "b", "metri_residui", "handleSearchCavoById", "trim", "filteredCavi", "toLowerCase", "exactMatch", "find", "stato_installazione", "metratura_reale", "modificato_manualmente", "handleCavoSelect", "status", "window", "confirm", "reactivateSpare", "then", "updatedCavo", "catch", "cavoId", "handleFormChange", "e", "name", "value", "target", "undefined", "isCompatible", "validateField", "warning", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "prev", "notificationShown", "setNotificationShown", "showConfirmDialog", "setShowConfirmDialog", "confirmDialogProps", "setConfirmDialogProps", "title", "onConfirm", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "handleNext", "prevActiveStep", "handleBack", "handleReset", "determineInstallationStatus", "metriTeorici", "handleSubmit", "statoInstallazione", "forceOver", "confirmMessage", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "success", "_error$response$data", "request", "_error$response$data2", "renderStep1", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "container", "spacing", "alignItems", "item", "xs", "fullWidth", "label", "onChange", "placeholder", "color", "onClick", "disabled", "startIcon", "size", "display", "justifyContent", "my", "severity", "maxHeight", "overflow", "map", "button", "primary", "ml", "secondary", "component", "ubicazione_partenza", "ubicazione_arrivo", "edge", "stopPropagation", "renderStep2", "compact", "fontWeight", "md", "bgcolor", "borderRadius", "height", "mt", "type", "helperText", "FormHelperTextProps", "renderStep3", "buildFullBobinaId", "numeroBobina", "hasSufficientMeters", "handleBobinaNumberInput", "id_bobina_input", "idBobinaCompleto", "<PERSON><PERSON><PERSON>", "paragraph", "onBlur", "id", "labelId", "flexDirection", "width", "fontSize", "border", "metri_totali", "renderStep4", "bobinaInfo", "getStepContent", "step", "handleCloseAlreadyLaidDialog", "handleModifyReel", "handleSelectAnotherCable", "handleCloseIncompatibleReelDialog", "handleUpdateCavoToMatchReel", "updateCavoForCompatibility", "getCavoById", "handleSelectAnotherReel", "endIcon", "open", "onClose", "max<PERSON><PERSON><PERSON>", "gap", "autoFocus", "mr", "onUpdateCavo", "onSelectAnotherReel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/InserisciMetriForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst InserisciMetriForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stato per la gestione dei passi (mantenuto per compatibilità con funzioni esistenti)\n  const [activeStep, setActiveStep] = useState(0);\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null); // Mantenuto per compatibilità\n  const [incompatibleReelData, setIncompatibleReelData] = useState({ cavo: null, bobina: null });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina) => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response ||\n            loadError.code === 'ECONNABORTED' ||\n            (loadError.message && loadError.message.includes('Network Error'))) {\n\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(\n              `${API_URL}/cavi/${cantiereId}`,\n              {\n                headers: {\n                  'Authorization': `Bearer ${token}`\n                },\n                timeout: 30000 // 30 secondi\n              }\n            );\n\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine - VERSIONE SEMPLIFICATA\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento di TUTTE le bobine disponibili...');\n\n      // Carica TUTTE le bobine disponibili senza filtri di compatibilità\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n      // Filtra solo per stato (disponibile o in uso)\n      const bobineUtilizzabili = bobineData.filter(bobina =>\n        (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') &&\n        bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata'\n      );\n\n      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);\n\n      // Identifica le bobine compatibili per evidenziarle nell'interfaccia\n      if (selectedCavo && selectedCavo.tipologia && selectedCavo.n_conduttori && selectedCavo.sezione) {\n        console.log('Identificazione bobine compatibili per evidenziarle...');\n\n        // Gestione speciale per n_conduttori che potrebbe essere nel formato \"X x Y\"\n        let cavoConduttori = String(selectedCavo.n_conduttori || '0');\n        if (cavoConduttori.includes(' x ')) {\n          const parts = cavoConduttori.split(' x ');\n          cavoConduttori = parts[0];\n          console.log(`Formato n_conduttori 'X x Y' rilevato: ${selectedCavo.n_conduttori} -> ${cavoConduttori}`);\n        }\n\n        const cavoTipologia = String(selectedCavo.tipologia || '');\n        const cavoSezione = String(selectedCavo.sezione || '0');\n\n        // Conta quante bobine sono compatibili (solo per log)\n        const compatibili = bobineUtilizzabili.filter(bobina => {\n          const bobinaTipologia = String(bobina.tipologia || '');\n          const bobinaConduttori = String(bobina.n_conduttori || '0');\n          const bobinaSezione = String(bobina.sezione || '0');\n\n          return bobinaTipologia === cavoTipologia &&\n                 bobinaConduttori === cavoConduttori &&\n                 bobinaSezione === cavoSezione;\n        });\n\n        console.log(`Bobine compatibili: ${compatibili.length} su ${bobineUtilizzabili.length}`);\n      }\n\n      // Ordina le bobine per metri residui (decrescente)\n      bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n      // Imposta le bobine nel componente\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con pattern: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi che corrispondono al pattern\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi in base all'input (ricerca parziale)\n      const filteredCavi = caviData.filter(cavo =>\n        cavo.id_cavo.toLowerCase().includes(cavoIdInput.trim().toLowerCase())\n      );\n\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti al pattern`);\n\n      // Se c'è una corrispondenza esatta, seleziona direttamente quel cavo\n      const exactMatch = filteredCavi.find(cavo =>\n        cavo.id_cavo.toLowerCase() === cavoIdInput.trim().toLowerCase()\n      );\n\n      if (exactMatch) {\n        console.log('Trovata corrispondenza esatta:', exactMatch);\n\n        // Verifica se il cavo è già installato\n        if (exactMatch.stato_installazione === 'Installato' || (exactMatch.metratura_reale && exactMatch.metratura_reale > 0)) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatch);\n          setAlreadyLaidCavo(exactMatch);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatch.modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatch);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatch);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con pattern \"${cavoIdInput.trim()}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0)) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = { ...cavo, modificato_manualmente: 0 };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n\n          // Carica le bobine compatibili\n          loadBobine();\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n\n      // Carica le bobine compatibili\n      if (cavo.tipologia && cavo.n_conduttori && cavo.sezione) {\n        console.log(`Caricamento bobine compatibili per il cavo ${cavo.id_cavo}...`);\n        loadBobine();\n      }\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async (cavoId) => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Gestione speciale per il cambio di bobina\n    if (name === 'id_bobina' && value && value !== 'BOBINA_VUOTA') {\n      // Verifica compatibilità tra cavo e bobina\n      const bobina = bobine.find(b => b.id_bobina === value);\n\n      if (bobina && selectedCavo) {\n        // Gestione speciale per n_conduttori che potrebbe essere nel formato \"X x Y\"\n        let cavoConduttori = selectedCavo.n_conduttori !== undefined && selectedCavo.n_conduttori !== null ? String(selectedCavo.n_conduttori) : '0';\n        if (cavoConduttori.includes(' x ')) {\n          const parts = cavoConduttori.split(' x ');\n          cavoConduttori = parts[0];\n          console.log(`Formato n_conduttori 'X x Y' rilevato: ${selectedCavo.n_conduttori} -> ${cavoConduttori}`);\n        }\n\n        const bobinaConduttori = String(bobina.n_conduttori || '0');\n\n        const isCompatible =\n          bobina.tipologia === selectedCavo.tipologia &&\n          bobinaConduttori === cavoConduttori &&\n          String(bobina.sezione) === String(selectedCavo.sezione);\n\n        if (!isCompatible) {\n          console.log('Bobina incompatibile selezionata:', bobina);\n          console.log('Cavo corrente:', selectedCavo);\n          console.log('Confronto conduttori:', { cavoConduttori, bobinaConduttori });\n\n          // Mostra il dialogo di incompatibilità\n          setIncompatibleReelData({\n            cavo: selectedCavo,\n            bobina: bobina\n          });\n          setShowIncompatibleReelDialog(true);\n\n          // Non aggiornare il valore del form finché l'utente non conferma\n          return;\n        }\n      }\n    }\n\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    } else if (name === 'id_bobina') {\n      // Controllo che sia selezionata una bobina\n      if (!value || value.trim() === '') {\n        error = 'È necessario selezionare una bobina';\n        return false;\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Nota: Lo stato per il dialogo di bobina incompatibile è già dichiarato sopra\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    // Validazione bobina (deve essere selezionata)\n    if (!formData.id_bobina || formData.id_bobina.trim() === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina';\n      isValid = false;\n    }\n\n    if (isValid) {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n\n      // Gestione differenziata per cavi non posati e cavi posati\n      if (!idBobina || idBobina === '') {\n        // È necessario selezionare una bobina, anche BOBINA_VUOTA\n        setFormErrors({\n          ...formErrors,\n          id_bobina: 'È necessario selezionare una bobina'\n        });\n        setLoading(false);\n        return;\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        // Per cavi posati con bobina specifica\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Verifica se è necessario forzare lo stato OVER della bobina\n      forceOver = false;\n\n      // Se si usa BOBINA_VUOTA, imposta sempre forceOver a true\n      if (idBobina === 'BOBINA_VUOTA') {\n        forceOver = true;\n        console.log('Forzando operazione per BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          forceOver = true;\n          console.log(`Forzando stato OVER per la bobina ${idBobina} (metri posati > metri residui)`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        // Anche in questo caso forziamo l'operazione\n        forceOver = true;\n        console.log(`Forzando operazione per metri posati (${metriPosati}) > metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n\n              await caviService.updateMetriPosati(\n                cantiereId,\n                formData.id_cavo,\n                metriPosati,\n                idBobina,\n                forceOver\n              );\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n              if (error.response) {\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = error.response.data?.detail || error.message;\n\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver\n      );\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n      if (error.response) {\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = error.response.data?.detail || error.message;\n\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Seleziona un cavo\n        </Typography>\n\n        {/* Ricerca per ID */}\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Cerca cavo per ID\n          </Typography>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={9}>\n              <TextField\n                fullWidth\n                label=\"ID Cavo\"\n                variant=\"outlined\"\n                value={cavoIdInput}\n                onChange={(e) => setCavoIdInput(e.target.value)}\n                placeholder=\"Inserisci l'ID del cavo\"\n              />\n            </Grid>\n            <Grid item xs={3}>\n              <Button\n                fullWidth\n                variant=\"contained\"\n                color=\"primary\"\n                onClick={handleSearchCavoById}\n                disabled={caviLoading || !cavoIdInput.trim()}\n                startIcon={caviLoading ? <CircularProgress size={20} /> : <SearchIcon />}\n              >\n                Cerca\n              </Button>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Lista cavi */}\n        <Paper sx={{ p: 2 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Seleziona dalla lista\n          </Typography>\n\n          {caviLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : cavi.length === 0 ? (\n            <Alert severity=\"info\">\n              Non ci sono cavi disponibili da installare.\n            </Alert>\n          ) : (\n            <List sx={{ maxHeight: '400px', overflow: 'auto' }}>\n              {cavi.map((cavo) => (\n                <React.Fragment key={cavo.id_cavo}>\n                  <ListItem button onClick={() => handleCavoSelect(cavo)}>\n                    <ListItemText\n                      primary={\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography variant=\"subtitle1\">{cavo.id_cavo}</Typography>\n                          {isCableSpare(cavo) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"SPARE\"\n                              color=\"error\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : isCableInstalled(cavo) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"Installato\"\n                              color=\"success\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : (\n                            <Chip\n                              size=\"small\"\n                              label={cavo.stato_installazione}\n                              color={getCableStateColor(cavo.stato_installazione)}\n                              sx={{ ml: 1 }}\n                            />\n                          )}\n                        </Box>\n                      }\n                      secondary={\n                        <>\n                          <Typography variant=\"body2\" component=\"span\">\n                            {cavo.tipologia || 'N/A'} - {cavo.n_conduttori || 'N/A'} x {cavo.sezione || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Da: {cavo.ubicazione_partenza || 'N/A'} - A: {cavo.ubicazione_arrivo || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Metri teorici: {cavo.metri_teorici || 'N/A'} - Metri posati: {cavo.metratura_reale || '0'}\n                          </Typography>\n                        </>\n                      }\n                    />\n                    <ListItemSecondaryAction>\n                      <IconButton edge=\"end\" onClick={(e) => {\n                        e.stopPropagation(); // Prevent triggering the ListItem click\n                        setSelectedCavo(cavo);\n                        setShowCavoDetailsDialog(true);\n                      }}>\n                        <InfoIcon />\n                      </IconButton>\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                  <Divider />\n                </React.Fragment>\n              ))}\n            </List>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserisci metri posati\n        </Typography>\n\n        <CavoDetailsView\n          cavo={selectedCavo}\n          compact={true}\n          title=\"Dettagli del cavo selezionato\"\n        />\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom sx={{ fontWeight: 'bold' }}>\n            Inserisci i metri posati\n          </Typography>\n\n          {/* Informazioni sul cavo e sulla bobina in una griglia */}\n          <Grid container spacing={2} sx={{ mb: 3 }}>\n            <Grid item xs={12} md={6}>\n              <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, height: '100%' }}>\n                <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>\n                  Informazioni cavo\n                </Typography>\n                <Grid container spacing={1}>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Metri teorici:</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\">{selectedCavo.metri_teorici || 'N/A'} m</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Stato attuale:</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Chip\n                      label={selectedCavo.stato_installazione || 'N/D'}\n                      size=\"small\"\n                      color={getCableStateColor(selectedCavo.stato_installazione)}\n                      variant=\"outlined\"\n                    />\n                  </Grid>\n                </Grid>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, height: '100%' }}>\n                <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'secondary.main' }}>\n                  Informazioni bobina\n                </Typography>\n                {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' ? (() => {\n                  const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                  return bobina ? (\n                    <Grid container spacing={1}>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>ID Bobina:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\">{getBobinaNumber(bobina.id_bobina)}</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Metri residui:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\">{bobina.metri_residui || 0} m</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Stato:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Chip\n                          label={bobina.stato_bobina || 'N/D'}\n                          size=\"small\"\n                          color={getReelStateColor(bobina.stato_bobina)}\n                          variant=\"outlined\"\n                        />\n                      </Grid>\n                    </Grid>\n                  ) : (\n                    <Typography variant=\"body2\">Bobina non trovata</Typography>\n                  );\n                })() : (\n                  <Typography variant=\"body2\">\n                    {formData.id_bobina === 'BOBINA_VUOTA' ?\n                      \"Utilizzo BOBINA VUOTA (nessuna bobina associata)\" :\n                      \"Nessuna bobina selezionata\"}\n                  </Typography>\n                )}\n              </Box>\n            </Grid>\n          </Grid>\n\n          <Box sx={{ mt: 3, mb: 3 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Metratura posata\n            </Typography>\n            <TextField\n              size=\"small\"\n              fullWidth\n              label=\"Metri posati\"\n              variant=\"outlined\"\n              name=\"metri_posati\"\n              type=\"number\"\n              value={formData.metri_posati}\n              onChange={handleFormChange}\n              error={!!formErrors.metri_posati}\n              helperText={formErrors.metri_posati || formWarnings.metri_posati}\n              FormHelperTextProps={{\n                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n              }}\n              sx={{ mb: 1 }}\n            />\n          </Box>\n\n          {formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mb: 2 }}>\n              {formWarnings.metri_posati}\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\">\n              Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\n            </Typography>\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = (numeroBobina) => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = (bobina) => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = (e) => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo) {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n            const cavoConduttori = String(selectedCavo.n_conduttori || '0');\n            const cavoSezione = String(selectedCavo.sezione || '0');\n\n            const bobinaTipologia = String(bobinaEsistente.tipologia || '');\n            const bobinaConduttori = String(bobinaEsistente.n_conduttori || '0');\n            const bobinaSezione = String(bobinaEsistente.sezione || '0');\n\n            // Log per debug\n            console.log(`Verifica compatibilità bobina ${bobinaEsistente.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              n_conduttori: `${bobinaConduttori} === ${cavoConduttori}`,\n              sezione: `${bobinaSezione} === ${cavoSezione}`\n            });\n\n            if (bobinaTipologia !== cavoTipologia ||\n                bobinaConduttori !== cavoConduttori ||\n                bobinaSezione !== cavoSezione) {\n              // Mostra il dialogo per bobine incompatibili\n              setIncompatibleReelData({\n                cavo: selectedCavo,\n                bobina: bobinaEsistente\n              });\n              setShowIncompatibleReelDialog(true);\n              return;\n            }\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Associa bobina\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"body1\" paragraph>\n            Seleziona una bobina da associare al cavo. È necessario associare sempre una bobina, anche utilizzando l'opzione \"BOBINA VUOTA\" se non si desidera associare una bobina specifica.\n          </Typography>\n\n          {bobineLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              <Grid container spacing={3}>\n                {/* Colonna sinistra: Input diretto */}\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                    Inserimento diretto\n                  </Typography>\n                  <TextField\n                    size=\"small\"\n                    fullWidth\n                    label=\"Numero bobina\"\n                    variant=\"outlined\"\n                    placeholder=\"Solo il numero (Y)\"\n                    helperText={formErrors.id_bobina_input || \"Inserisci solo il numero della bobina\"}\n                    error={!!formErrors.id_bobina_input}\n                    onBlur={handleBobinaNumberInput}\n                    sx={{ mb: 1 }}\n                  />\n                  <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                    ID Bobina: <strong>{formData.id_bobina || '-'}</strong>\n                  </Typography>\n                </Grid>\n\n                {/* Colonna destra: Selezione dalla lista */}\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                    Selezione dalla lista\n                  </Typography>\n                  <Box>\n                    <Typography variant=\"subtitle2\" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold', mb: 1 }}>\n                      {selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'}\n                    </Typography>\n\n                    <FormControl fullWidth size=\"small\" error={!!formErrors.id_bobina}>\n                      <InputLabel id=\"bobina-select-label\">Seleziona bobina</InputLabel>\n                      <Select\n                        labelId=\"bobina-select-label\"\n                        id=\"bobina-select\"\n                        name=\"id_bobina\"\n                        value={formData.id_bobina}\n                        label=\"Seleziona bobina\"\n                        onChange={handleFormChange}\n                      >\n                        <MenuItem value=\"BOBINA_VUOTA\">\n                          <strong>BOBINA VUOTA</strong> (nessuna bobina associata)\n                        </MenuItem>\n                        <Divider />\n                        {bobine.length > 0 ? (\n                          <Box component=\"li\" sx={{ p: 1, bgcolor: 'background.paper' }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: 'success.main' }}>\n                              {bobine.length} bobine compatibili trovate\n                            </Typography>\n                          </Box>\n                        ) : null}\n                        {bobine.map((bobina) => (\n                          <MenuItem\n                            key={bobina.id_bobina}\n                            value={bobina.id_bobina}\n                            disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                            sx={{\n                              '&.Mui-selected': { bgcolor: 'success.light' },\n                              '&.Mui-selected:hover': { bgcolor: 'success.light' },\n                              bgcolor: selectedCavo &&\n                                     bobina.tipologia === selectedCavo.tipologia &&\n                                     String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) &&\n                                     String(bobina.sezione) === String(selectedCavo.sezione) ?\n                                     'rgba(76, 175, 80, 0.08)' : 'inherit'\n                            }}\n                          >\n                            <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>\n                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>\n                                <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                                  {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'}\n                                </Typography>\n                                {selectedCavo &&\n                                 bobina.tipologia === selectedCavo.tipologia &&\n                                 String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) &&\n                                 String(bobina.sezione) === String(selectedCavo.sezione) && (\n                                  <Chip\n                                    size=\"small\"\n                                    label=\"Compatibile\"\n                                    color=\"success\"\n                                    variant=\"outlined\"\n                                    sx={{ height: 20, fontSize: '0.6rem' }}\n                                  />\n                                )}\n                              </Box>\n                              <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>\n                                <Typography variant=\"caption\">\n                                  {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                                </Typography>\n                                <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                  {bobina.metri_residui || 0} m disponibili\n                                </Typography>\n                              </Box>\n                            </Box>\n                          </MenuItem>\n                        ))}\n                      </Select>\n                      <FormHelperText>\n                        {formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'}\n                      </FormHelperText>\n                    </FormControl>\n\n                    {bobine.length === 0 && !bobineLoading && (\n                      <Alert severity=\"warning\" sx={{ mt: 2, fontSize: '0.8rem' }}>\n                        Nessuna bobina compatibile trovata. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\n                      </Alert>\n                    )}\n                  </Box>\n                </Grid>\n              </Grid>\n\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                <Typography variant=\"body2\">\n                  <strong>Nota</strong>: Se selezioni \"BOBINA VUOTA\", potrai associare una bobina specifica in un secondo momento.\n                </Typography>\n              </Alert>\n            </Box>\n          )}\n\n          {/* Mostra dettagli della bobina selezionata */}\n          {!bobineLoading && formData.id_bobina && (\n            <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #e0e0e0' }}>\n              <Typography variant=\"subtitle2\" gutterBottom>\n                Dettagli bobina selezionata\n              </Typography>\n              {(() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                if (bobina) {\n                  return (\n                    <Grid container spacing={2}>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"body2\">\n                          <strong>Numero:</strong> {getBobinaNumber(bobina.id_bobina)}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Tipologia:</strong> {bobina.tipologia || 'N/A'}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Conduttori:</strong> {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                        </Typography>\n                      </Grid>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"body2\">\n                          <strong>Metri totali:</strong> {bobina.metri_totali || 0} m\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Metri residui:</strong> {bobina.metri_residui || 0} m\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Stato:</strong> {bobina.stato_bobina || 'N/A'}\n                        </Typography>\n                      </Grid>\n                    </Grid>\n                  );\n                }\n                return (\n                  <Typography variant=\"body2\" color=\"error\">\n                    Bobina non trovata nel database\n                  </Typography>\n                );\n              })()}\n            </Box>\n          )}\n\n          {bobine.length === 0 && !bobineLoading && (\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\n            </Alert>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Conferma inserimento\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Riepilogo dati\n          </Typography>\n\n          {/* Dettagli del cavo */}\n          <CavoDetailsView\n            cavo={selectedCavo}\n            compact={true}\n            title=\"Dettagli del cavo\"\n          />\n\n          {/* Informazioni sull'operazione */}\n          <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Informazioni sull'operazione:\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Metri Posati:</strong> {formData.metri_posati} m\n                </Typography>\n                <Typography variant=\"body2\">\n                  <strong>Stato Installazione:</strong> {statoInstallazione}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Bobina Associata:</strong> {numeroBobina}\n                </Typography>\n                {bobinaInfo && (\n                  <Typography variant=\"body2\">\n                    <strong>Metri Residui Bobina:</strong> {bobinaInfo.metri_residui} m\n                  </Typography>\n                )}\n              </Grid>\n            </Grid>\n          </Box>\n\n          {bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mt: 3 }}>\n              <strong>Attenzione:</strong> I metri posati ({formData.metri_posati}m) superano i metri residui della bobina ({bobinaInfo.metri_residui}m).\n              Questo porterà la bobina in stato OVER.\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 3 }}>\n            Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\n            {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.'}\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = (step) => {\n    switch (step) {\n      case 0:\n        return renderStep1(); // Seleziona Cavo\n      case 1:\n        return renderStep3(); // Associa Bobina\n      case 2:\n        return renderStep2(); // Inserisci Metri\n      case 3:\n        return renderStep4(); // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReelData({ cavo: null, bobina: null });\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    const { cavo, bobina } = incompatibleReelData;\n    if (!cavo || !bobina) return;\n\n    try {\n      setLoading(true);\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: bobina.id_bobina\n      });\n\n      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate per corrispondere alla bobina ${bobina.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n\n  return (\n    <Box>\n      {/* Sezione di ricerca */}\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Cerca cavo\n        </Typography>\n        <Grid container spacing={2} alignItems=\"center\">\n          <Grid item xs={9}>\n            <TextField\n              fullWidth\n              label=\"ID Cavo\"\n              variant=\"outlined\"\n              value={cavoIdInput}\n              onChange={(e) => setCavoIdInput(e.target.value)}\n              placeholder=\"Inserisci l'ID del cavo o parte di esso\"\n            />\n          </Grid>\n          <Grid item xs={3}>\n            <Button\n              fullWidth\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSearchCavoById}\n              disabled={caviLoading || !cavoIdInput.trim()}\n              startIcon={caviLoading ? <CircularProgress size={20} /> : <SearchIcon />}\n            >\n              Cerca\n            </Button>\n          </Grid>\n        </Grid>\n      </Paper>\n\n      {/* Risultati della ricerca */}\n      {showSearchResults && searchResults.length > 0 && (\n        <Paper sx={{ p: 3, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Risultati della ricerca\n          </Typography>\n          <TableContainer>\n            <Table size=\"small\">\n              <TableHead>\n                <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n                  <TableCell>ID Cavo</TableCell>\n                  <TableCell>Tipologia</TableCell>\n                  <TableCell>Conduttori</TableCell>\n                  <TableCell>Ubicazione</TableCell>\n                  <TableCell>Metri Teorici</TableCell>\n                  <TableCell>Stato</TableCell>\n                  <TableCell>Azioni</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {searchResults.map((cavo) => (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>{cavo.id_cavo}</TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>{cavo.n_conduttori || 'N/A'} x {cavo.sezione || 'N/A'}</TableCell>\n                    <TableCell>Da: {cavo.ubicazione_partenza || 'N/A'}<br/>A: {cavo.ubicazione_arrivo || 'N/A'}</TableCell>\n                    <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={cavo.stato_installazione || 'N/D'}\n                        size=\"small\"\n                        color={getCableStateColor(cavo.stato_installazione)}\n                        variant=\"outlined\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Button\n                        size=\"small\"\n                        variant=\"contained\"\n                        color=\"primary\"\n                        onClick={() => handleCavoSelect(cavo)}\n                        disabled={isCableInstalled(cavo)}\n                      >\n                        Seleziona\n                      </Button>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </Paper>\n      )}\n\n      {/* Form per inserimento metri e selezione bobina */}\n      {selectedCavo && (\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Inserimento metri posati\n          </Typography>\n\n          {/* Dettagli del cavo selezionato */}\n          <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, mb: 3 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>\n              Cavo selezionato: {selectedCavo.id_cavo}\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Conduttori:</strong> {selectedCavo.n_conduttori || 'N/A'} x {selectedCavo.sezione || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metri teorici:</strong> {selectedCavo.metri_teorici || 'N/A'} m</Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\"><strong>Ubicazione partenza:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ubicazione arrivo:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\">\n                  <strong>Stato:</strong>\n                  <Chip\n                    label={selectedCavo.stato_installazione || 'N/D'}\n                    size=\"small\"\n                    color={getCableStateColor(selectedCavo.stato_installazione)}\n                    variant=\"outlined\"\n                    sx={{ ml: 1 }}\n                  />\n                </Typography>\n              </Grid>\n            </Grid>\n          </Box>\n\n          <Grid container spacing={3}>\n            {/* Colonna sinistra: Metri posati */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                Metratura posata\n              </Typography>\n              <TextField\n                size=\"small\"\n                fullWidth\n                label=\"Metri posati\"\n                variant=\"outlined\"\n                name=\"metri_posati\"\n                type=\"number\"\n                value={formData.metri_posati}\n                onChange={handleFormChange}\n                error={!!formErrors.metri_posati}\n                helperText={formErrors.metri_posati || formWarnings.metri_posati}\n                FormHelperTextProps={{\n                  sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n                }}\n                sx={{ mb: 1 }}\n              />\n              {formWarnings.metri_posati && !formErrors.metri_posati && (\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  {formWarnings.metri_posati}\n                </Alert>\n              )}\n            </Grid>\n\n            {/* Colonna destra: Selezione bobina */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                Associa bobina\n              </Typography>\n              {bobineLoading ? (\n                <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n                  <CircularProgress />\n                </Box>\n              ) : (\n                <Box>\n                  <Typography variant=\"subtitle2\" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold', mb: 1 }}>\n                    {selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'}\n                  </Typography>\n\n                  <FormControl fullWidth size=\"small\" error={!!formErrors.id_bobina}>\n                    <InputLabel id=\"bobina-select-label\">Seleziona bobina</InputLabel>\n                    <Select\n                      labelId=\"bobina-select-label\"\n                      id=\"bobina-select\"\n                      name=\"id_bobina\"\n                      value={formData.id_bobina}\n                      label=\"Seleziona bobina\"\n                      onChange={handleFormChange}\n                    >\n                      <MenuItem value=\"BOBINA_VUOTA\">\n                        <strong>BOBINA VUOTA</strong> (nessuna bobina associata)\n                      </MenuItem>\n                      <Divider />\n                      {bobine.length > 0 ? (\n                        <Box component=\"li\" sx={{ p: 1, bgcolor: 'background.paper' }}>\n                          <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: 'success.main' }}>\n                            {bobine.length} bobine compatibili trovate\n                          </Typography>\n                        </Box>\n                      ) : null}\n                      {bobine.map((bobina) => (\n                        <MenuItem\n                          key={bobina.id_bobina}\n                          value={bobina.id_bobina}\n                          disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                          sx={{\n                            '&.Mui-selected': { bgcolor: 'success.light' },\n                            '&.Mui-selected:hover': { bgcolor: 'success.light' },\n                            bgcolor: selectedCavo &&\n                                   bobina.tipologia === selectedCavo.tipologia &&\n                                   String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) &&\n                                   String(bobina.sezione) === String(selectedCavo.sezione) ?\n                                   'rgba(76, 175, 80, 0.08)' : 'inherit'\n                          }}\n                        >\n                          <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>\n                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                                {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'}\n                              </Typography>\n                              {selectedCavo &&\n                               bobina.tipologia === selectedCavo.tipologia &&\n                               String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) &&\n                               String(bobina.sezione) === String(selectedCavo.sezione) && (\n                                <Chip\n                                  size=\"small\"\n                                  label=\"Compatibile\"\n                                  color=\"success\"\n                                  variant=\"outlined\"\n                                  sx={{ height: 20, fontSize: '0.6rem' }}\n                                />\n                              )}\n                            </Box>\n                            <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>\n                              <Typography variant=\"caption\">\n                                {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                              </Typography>\n                              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                {bobina.metri_residui || 0} m disponibili\n                              </Typography>\n                            </Box>\n                          </Box>\n                        </MenuItem>\n                      ))}\n                    </Select>\n                    <FormHelperText>\n                      {formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'}\n                    </FormHelperText>\n                  </FormControl>\n\n                  {bobine.length === 0 && !bobineLoading && (\n                    <Alert severity=\"warning\" sx={{ mt: 2, fontSize: '0.8rem' }}>\n                      Nessuna bobina compatibile trovata. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\n                    </Alert>\n                  )}\n                </Box>\n              )}\n\n              {/* Mostra dettagli della bobina selezionata */}\n              {!bobineLoading && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && (() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                if (bobina) {\n                  return (\n                    <Box sx={{ mt: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n                      <Typography variant=\"body2\"><strong>Bobina:</strong> {getBobinaNumber(bobina.id_bobina)}</Typography>\n                      <Typography variant=\"body2\"><strong>Metri residui:</strong> {bobina.metri_residui || 0} m</Typography>\n                      <Typography variant=\"body2\">\n                        <strong>Stato:</strong>\n                        <Chip\n                          label={bobina.stato_bobina || 'N/D'}\n                          size=\"small\"\n                          color={getReelStateColor(bobina.stato_bobina)}\n                          variant=\"outlined\"\n                          sx={{ ml: 1 }}\n                        />\n                      </Typography>\n                    </Box>\n                  );\n                }\n                return null;\n              })()}\n            </Grid>\n          </Grid>\n\n          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>\n            <Button\n              variant=\"outlined\"\n              color=\"secondary\"\n              onClick={() => {\n                setSelectedCavo(null);\n                setFormData({\n                  id_cavo: '',\n                  metri_posati: '',\n                  id_bobina: ''\n                });\n              }}\n              startIcon={<CancelIcon />}\n              disabled={loading}\n            >\n              Annulla\n            </Button>\n\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSubmit}\n              endIcon={<SaveIcon />}\n              disabled={loading || !formData.metri_posati || !formData.id_bobina}\n            >\n              {loading ? <CircularProgress size={24} /> : 'Salva'}\n            </Button>\n          </Box>\n        </Paper>\n      )}\n\n      {/* Dialogo di conferma generico */}\n      <Dialog open={showConfirmDialog} onClose={() => setShowConfirmDialog(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">{confirmDialogProps.title}</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <Typography variant=\"body1\" sx={{ mt: 2 }}>\n            {confirmDialogProps.message}\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowConfirmDialog(false)} color=\"secondary\" variant=\"outlined\">\n            Annulla\n          </Button>\n          <Button\n            onClick={() => {\n              setShowConfirmDialog(false);\n              confirmDialogProps.onConfirm();\n            }}\n            color=\"primary\"\n            variant=\"contained\"\n            autoFocus\n          >\n            Conferma\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per cavi già posati */}\n      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">Cavo già posato</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          {alreadyLaidCavo && (\n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"body1\" paragraph>\n                Il cavo <strong>{alreadyLaidCavo.id_cavo}</strong> risulta già posato ({alreadyLaidCavo.metratura_reale || 0}m).\n              </Typography>\n              <Typography variant=\"body1\" paragraph>\n                Puoi scegliere di:\n              </Typography>\n              <Typography variant=\"body2\" component=\"ul\">\n                <li>Modificare la bobina associata al cavo</li>\n                <li>Selezionare un altro cavo</li>\n                <li>Annullare l'operazione</li>\n              </Typography>\n            </Box>\n          )}\n        </DialogContent>\n        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>\n          <Button onClick={handleCloseAlreadyLaidDialog} color=\"secondary\">\n            Annulla operazione\n          </Button>\n          <Box>\n            <Button onClick={handleSelectAnotherCable} color=\"primary\" sx={{ mr: 1 }}>\n              Seleziona altro cavo\n            </Button>\n            <Button onClick={handleModifyReel} variant=\"contained\" color=\"primary\">\n              Modifica bobina\n            </Button>\n          </Box>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={handleCloseIncompatibleReelDialog}\n        cavo={incompatibleReelData.cavo}\n        bobina={incompatibleReelData.bobina}\n        onUpdateCavo={handleUpdateCavoToMatchReel}\n        onSelectAnotherReel={handleSelectAnotherReel}\n      />\n\n      {/* Dialogo per visualizzare i dettagli del cavo */}\n      <Dialog\n        open={showCavoDetailsDialog}\n        onClose={() => setShowCavoDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <InfoIcon color=\"primary\" />\n            <Typography variant=\"h6\">Dettagli Cavo</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <CavoDetailsView cavo={selectedCavo} />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowCavoDetailsDialog(false)} color=\"primary\">\n            Chiudi\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default InserisciMetriForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,QAClB,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;AAC/B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,wBAAwB,QAAQ,6BAA6B;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuE,WAAW,EAAEC,cAAc,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyE,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2E,aAAa,EAAEC,gBAAgB,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC+E,IAAI,EAAEC,OAAO,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiF,MAAM,EAAEC,SAAS,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmF,YAAY,EAAEC,eAAe,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqF,WAAW,EAAEC,cAAc,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACuF,UAAU,EAAEC,aAAa,CAAC,GAAGxF,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAM,CAACyF,QAAQ,EAAEC,WAAW,CAAC,GAAG1F,QAAQ,CAAC;IACvC2F,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACgG,YAAY,EAAEC,eAAe,CAAC,GAAGjG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAACkG,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACoG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChE,MAAM,CAACsG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvG,QAAQ,CAAC;IAAEwG,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK,CAAC,CAAC;EAC9F,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC4G,eAAe,EAAEC,kBAAkB,CAAC,GAAG7G,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC8G,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG/G,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACd+G,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAChD,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMiD,eAAe,GAAIC,QAAQ,IAAK;IACpC;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvC,OAAOD,QAAQ,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAOF,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMG,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF7C,cAAc,CAAC,IAAI,CAAC;MACpB8C,OAAO,CAACC,GAAG,CAAC,oCAAoCvD,UAAU,KAAK,CAAC;;MAEhE;MACA,IAAI;QACF,MAAMwD,QAAQ,GAAG,MAAM5E,WAAW,CAAC6E,OAAO,CAACzD,UAAU,CAAC;QACtDsD,OAAO,CAACC,GAAG,CAAC,YAAYC,QAAQ,CAACE,MAAM,OAAO,CAAC;;QAE/C;QACA;QACA1C,OAAO,CAACwC,QAAQ,CAAC;MACnB,CAAC,CAAC,OAAOG,SAAS,EAAE;QAClBL,OAAO,CAACM,KAAK,CAAC,qDAAqD,EAAED,SAAS,CAAC;;QAE/E;QACA,IAAIA,SAAS,CAACE,cAAc,IAAI,CAACF,SAAS,CAACG,QAAQ,IAC/CH,SAAS,CAACI,IAAI,KAAK,cAAc,IAChCJ,SAAS,CAACK,OAAO,IAAIL,SAAS,CAACK,OAAO,CAACb,QAAQ,CAAC,eAAe,CAAE,EAAE;UAEtEG,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;UACtE,MAAM,IAAIU,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;UAEvD,IAAI;YACF;YACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;YAC3C,MAAMC,OAAO,GAAG1F,aAAa,CAAC2F,QAAQ,CAACC,OAAO;;YAE9C;YACA,MAAMC,aAAa,GAAG,MAAMxI,KAAK,CAACyI,GAAG,CACnC,GAAGJ,OAAO,SAASvE,UAAU,EAAE,EAC/B;cACE4E,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUR,KAAK;cAClC,CAAC;cACDS,OAAO,EAAE,KAAK,CAAC;YACjB,CACF,CAAC;YAEDvB,OAAO,CAACC,GAAG,CAAC,2CAA2CmB,aAAa,CAACI,IAAI,CAACpB,MAAM,OAAO,CAAC;YACxF1C,OAAO,CAAC0D,aAAa,CAACI,IAAI,CAAC;UAC7B,CAAC,CAAC,OAAOC,UAAU,EAAE;YACnBzB,OAAO,CAACM,KAAK,CAAC,uCAAuC,EAAEmB,UAAU,CAAC;YAClE,MAAMA,UAAU;UAClB;QACF,CAAC,MAAM;UACL,MAAMpB,SAAS;QACjB;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIoB,YAAY,GAAG,iCAAiC;MAEpD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEA9D,OAAO,CAAC8E,YAAY,CAAC;IACvB,CAAC,SAAS;MACRxE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMwC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFtC,gBAAgB,CAAC,IAAI,CAAC;MACtB4C,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;;MAE5D;MACA,MAAM2B,UAAU,GAAG,MAAMzF,gBAAgB,CAAC0F,SAAS,CAACnF,UAAU,CAAC;MAC/DsD,OAAO,CAACC,GAAG,CAAC,oBAAoB2B,UAAU,CAACxB,MAAM,EAAE,CAAC;;MAEpD;MACA,MAAM0B,kBAAkB,GAAGF,UAAU,CAACG,MAAM,CAAC5C,MAAM,IACjD,CAACA,MAAM,CAAC6C,YAAY,KAAK,aAAa,IAAI7C,MAAM,CAAC6C,YAAY,KAAK,QAAQ,KAC1E7C,MAAM,CAAC6C,YAAY,KAAK,MAAM,IAAI7C,MAAM,CAAC6C,YAAY,KAAK,WAC5D,CAAC;MAEDhC,OAAO,CAACC,GAAG,CAAC,wBAAwB6B,kBAAkB,CAAC1B,MAAM,EAAE,CAAC;;MAEhE;MACA,IAAIvC,YAAY,IAAIA,YAAY,CAACoE,SAAS,IAAIpE,YAAY,CAACqE,YAAY,IAAIrE,YAAY,CAACsE,OAAO,EAAE;QAC/FnC,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;;QAErE;QACA,IAAImC,cAAc,GAAGC,MAAM,CAACxE,YAAY,CAACqE,YAAY,IAAI,GAAG,CAAC;QAC7D,IAAIE,cAAc,CAACvC,QAAQ,CAAC,KAAK,CAAC,EAAE;UAClC,MAAMyC,KAAK,GAAGF,cAAc,CAACtC,KAAK,CAAC,KAAK,CAAC;UACzCsC,cAAc,GAAGE,KAAK,CAAC,CAAC,CAAC;UACzBtC,OAAO,CAACC,GAAG,CAAC,0CAA0CpC,YAAY,CAACqE,YAAY,OAAOE,cAAc,EAAE,CAAC;QACzG;QAEA,MAAMG,aAAa,GAAGF,MAAM,CAACxE,YAAY,CAACoE,SAAS,IAAI,EAAE,CAAC;QAC1D,MAAMO,WAAW,GAAGH,MAAM,CAACxE,YAAY,CAACsE,OAAO,IAAI,GAAG,CAAC;;QAEvD;QACA,MAAMM,WAAW,GAAGX,kBAAkB,CAACC,MAAM,CAAC5C,MAAM,IAAI;UACtD,MAAMuD,eAAe,GAAGL,MAAM,CAAClD,MAAM,CAAC8C,SAAS,IAAI,EAAE,CAAC;UACtD,MAAMU,gBAAgB,GAAGN,MAAM,CAAClD,MAAM,CAAC+C,YAAY,IAAI,GAAG,CAAC;UAC3D,MAAMU,aAAa,GAAGP,MAAM,CAAClD,MAAM,CAACgD,OAAO,IAAI,GAAG,CAAC;UAEnD,OAAOO,eAAe,KAAKH,aAAa,IACjCI,gBAAgB,KAAKP,cAAc,IACnCQ,aAAa,KAAKJ,WAAW;QACtC,CAAC,CAAC;QAEFxC,OAAO,CAACC,GAAG,CAAC,uBAAuBwC,WAAW,CAACrC,MAAM,OAAO0B,kBAAkB,CAAC1B,MAAM,EAAE,CAAC;MAC1F;;MAEA;MACA0B,kBAAkB,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,aAAa,GAAGF,CAAC,CAACE,aAAa,CAAC;;MAEpE;MACApF,SAAS,CAACkE,kBAAkB,CAAC;IAC/B,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D1D,OAAO,CAAC,uCAAuC,IAAI0D,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRtD,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM6F,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAAClF,WAAW,CAACmF,IAAI,CAAC,CAAC,EAAE;MACvBtG,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFM,cAAc,CAAC,IAAI,CAAC;MACpB8C,OAAO,CAACC,GAAG,CAAC,6BAA6BlC,WAAW,CAACmF,IAAI,CAAC,CAAC,iBAAiBxG,UAAU,EAAE,CAAC;;MAEzF;MACA,MAAMwD,QAAQ,GAAG,MAAM5E,WAAW,CAAC6E,OAAO,CAACzD,UAAU,CAAC;;MAEtD;MACA,MAAMyG,YAAY,GAAGjD,QAAQ,CAAC6B,MAAM,CAAC7C,IAAI,IACvCA,IAAI,CAACb,OAAO,CAAC+E,WAAW,CAAC,CAAC,CAACvD,QAAQ,CAAC9B,WAAW,CAACmF,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CACtE,CAAC;MAEDpD,OAAO,CAACC,GAAG,CAAC,WAAWkD,YAAY,CAAC/C,MAAM,iCAAiC,CAAC;;MAE5E;MACA,MAAMiD,UAAU,GAAGF,YAAY,CAACG,IAAI,CAACpE,IAAI,IACvCA,IAAI,CAACb,OAAO,CAAC+E,WAAW,CAAC,CAAC,KAAKrF,WAAW,CAACmF,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAChE,CAAC;MAED,IAAIC,UAAU,EAAE;QACdrD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEoD,UAAU,CAAC;;QAEzD;QACA,IAAIA,UAAU,CAACE,mBAAmB,KAAK,YAAY,IAAKF,UAAU,CAACG,eAAe,IAAIH,UAAU,CAACG,eAAe,GAAG,CAAE,EAAE;UACrHxD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEoD,UAAU,CAAC;UAC/D9D,kBAAkB,CAAC8D,UAAU,CAAC;UAC9BhE,wBAAwB,CAAC,IAAI,CAAC;UAC9BnC,cAAc,CAAC,KAAK,CAAC;UACrB;QACF;;QAEA;QACA,IAAImG,UAAU,CAACI,sBAAsB,KAAK,CAAC,EAAE;UAC3CzD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEoD,UAAU,CAAC;UAC9C;QACF;;QAEA;QACAK,gBAAgB,CAACL,UAAU,CAAC;MAC9B,CAAC,MAAM,IAAIF,YAAY,CAAC/C,MAAM,GAAG,CAAC,EAAE;QAClC;QACA9C,gBAAgB,CAAC6F,YAAY,CAAC;QAC9B3F,oBAAoB,CAAC,IAAI,CAAC;MAC5B,CAAC,MAAM;QACL;QACAZ,OAAO,CAAC,oCAAoCmB,WAAW,CAACmF,IAAI,CAAC,CAAC,kBAAkBxG,UAAU,EAAE,CAAC;MAC/F;IACF,CAAC,CAAC,OAAO4D,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;MAEtD;MACA,IAAIoB,YAAY,GAAG,+BAA+B;MAElD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAACqD,MAAM,KAAK,GAAG,EAAE;QAC/BjC,YAAY,GAAG,gBAAgB3D,WAAW,CAACmF,IAAI,CAAC,CAAC,8BAA8BxG,UAAU,EAAE;MAC7F,CAAC,MAAM,IAAI4D,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEA9D,OAAO,CAAC8E,YAAY,CAAC;IACvB,CAAC,SAAS;MACRxE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMwG,gBAAgB,GAAIxE,IAAI,IAAK;IACjC;IACA,IAAIA,IAAI,CAACqE,mBAAmB,KAAK,YAAY,IAAKrE,IAAI,CAACsE,eAAe,IAAItE,IAAI,CAACsE,eAAe,GAAG,CAAE,EAAE;MACnG;MACAjE,kBAAkB,CAACL,IAAI,CAAC;MACxBG,wBAAwB,CAAC,IAAI,CAAC;MAC9B;IACF;IACA;IAAA,KACK,IAAIH,IAAI,CAACuE,sBAAsB,KAAK,CAAC,EAAE;MAC1C;MACA,IAAIG,MAAM,CAACC,OAAO,CAAC,WAAW3E,IAAI,CAACb,OAAO,0CAA0C,CAAC,EAAE;QACrF;QACAyF,eAAe,CAAC5E,IAAI,CAACb,OAAO,CAAC,CAAC0F,IAAI,CAAC,MAAM;UACvC;UACA,MAAMC,WAAW,GAAG;YAAE,GAAG9E,IAAI;YAAEuE,sBAAsB,EAAE;UAAE,CAAC;UAC1D3F,eAAe,CAACkG,WAAW,CAAC;UAC5B5F,WAAW,CAAC;YACV,GAAGD,QAAQ;YACXE,OAAO,EAAE2F,WAAW,CAAC3F,OAAO;YAC5BC,YAAY,EAAE;UAChB,CAAC,CAAC;UACF;UACAd,oBAAoB,CAAC,KAAK,CAAC;;UAE3B;UACAkC,UAAU,CAAC,CAAC;QACd,CAAC,CAAC,CAACuE,KAAK,CAAC3D,KAAK,IAAI;UAChBN,OAAO,CAACM,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvE1D,OAAO,CAAC,kDAAkD,IAAI0D,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;QACvG,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;MACF;IACF,CAAC,MAAM;MACL;MACA5C,eAAe,CAACoB,IAAI,CAAC;MACrBd,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEa,IAAI,CAACb,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF;MACAd,oBAAoB,CAAC,KAAK,CAAC;;MAE3B;MACA,IAAI0B,IAAI,CAAC+C,SAAS,IAAI/C,IAAI,CAACgD,YAAY,IAAIhD,IAAI,CAACiD,OAAO,EAAE;QACvDnC,OAAO,CAACC,GAAG,CAAC,8CAA8Cf,IAAI,CAACb,OAAO,KAAK,CAAC;QAC5EqB,UAAU,CAAC,CAAC;MACd;IACF;EACF,CAAC;;EAED;EACA,MAAMoE,eAAe,GAAG,MAAOI,MAAM,IAAK;IACxC,IAAI;MACF;MACA,MAAM5I,WAAW,CAACwI,eAAe,CAACpH,UAAU,EAAEwH,MAAM,CAAC;MACrDvH,SAAS,CAAC,QAAQuH,MAAM,0BAA0B,CAAC;MACnD,OAAO,IAAI;IACb,CAAC,CAAC,OAAO5D,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvE1D,OAAO,CAAC,kDAAkD,IAAI0D,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACrG,MAAMJ,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM6D,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACA,IAAIF,IAAI,KAAK,WAAW,IAAIC,KAAK,IAAIA,KAAK,KAAK,cAAc,EAAE;MAC7D;MACA,MAAMnF,MAAM,GAAGxB,MAAM,CAAC2F,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACxE,SAAS,KAAK+F,KAAK,CAAC;MAEtD,IAAInF,MAAM,IAAItB,YAAY,EAAE;QAC1B;QACA,IAAIuE,cAAc,GAAGvE,YAAY,CAACqE,YAAY,KAAKsC,SAAS,IAAI3G,YAAY,CAACqE,YAAY,KAAK,IAAI,GAAGG,MAAM,CAACxE,YAAY,CAACqE,YAAY,CAAC,GAAG,GAAG;QAC5I,IAAIE,cAAc,CAACvC,QAAQ,CAAC,KAAK,CAAC,EAAE;UAClC,MAAMyC,KAAK,GAAGF,cAAc,CAACtC,KAAK,CAAC,KAAK,CAAC;UACzCsC,cAAc,GAAGE,KAAK,CAAC,CAAC,CAAC;UACzBtC,OAAO,CAACC,GAAG,CAAC,0CAA0CpC,YAAY,CAACqE,YAAY,OAAOE,cAAc,EAAE,CAAC;QACzG;QAEA,MAAMO,gBAAgB,GAAGN,MAAM,CAAClD,MAAM,CAAC+C,YAAY,IAAI,GAAG,CAAC;QAE3D,MAAMuC,YAAY,GAChBtF,MAAM,CAAC8C,SAAS,KAAKpE,YAAY,CAACoE,SAAS,IAC3CU,gBAAgB,KAAKP,cAAc,IACnCC,MAAM,CAAClD,MAAM,CAACgD,OAAO,CAAC,KAAKE,MAAM,CAACxE,YAAY,CAACsE,OAAO,CAAC;QAEzD,IAAI,CAACsC,YAAY,EAAE;UACjBzE,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEd,MAAM,CAAC;UACxDa,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEpC,YAAY,CAAC;UAC3CmC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;YAAEmC,cAAc;YAAEO;UAAiB,CAAC,CAAC;;UAE1E;UACA1D,uBAAuB,CAAC;YACtBC,IAAI,EAAErB,YAAY;YAClBsB,MAAM,EAAEA;UACV,CAAC,CAAC;UACFN,6BAA6B,CAAC,IAAI,CAAC;;UAEnC;UACA;QACF;MACF;IACF;IAEAT,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACkG,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACAI,aAAa,CAACL,IAAI,EAAEC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMI,aAAa,GAAGA,CAACL,IAAI,EAAEC,KAAK,KAAK;IACrC,IAAIhE,KAAK,GAAG,IAAI;IAChB,IAAIqE,OAAO,GAAG,IAAI;IAElB,IAAIN,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACpB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjC5C,KAAK,GAAG,uCAAuC;QAC/C,OAAO,KAAK;MACd;;MAEA;MACA,IAAIsE,KAAK,CAACC,UAAU,CAACP,KAAK,CAAC,CAAC,IAAIO,UAAU,CAACP,KAAK,CAAC,IAAI,CAAC,EAAE;QACtDhE,KAAK,GAAG,sCAAsC;QAC9C,OAAO,KAAK;MACd;MAEA,MAAMwE,WAAW,GAAGD,UAAU,CAACP,KAAK,CAAC;;MAErC;MACA,IAAIzG,YAAY,IAAIA,YAAY,CAACkH,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAChH,YAAY,CAACkH,aAAa,CAAC,EAAE;QACtGJ,OAAO,GAAG,mBAAmBG,WAAW,yCAAyCjH,YAAY,CAACkH,aAAa,IAAI;MACjH;;MAEA;MACA,IAAI5G,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC/D,MAAMY,MAAM,GAAGxB,MAAM,CAAC2F,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACxE,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIY,MAAM,IAAI2F,WAAW,GAAGD,UAAU,CAAC1F,MAAM,CAAC6D,aAAa,CAAC,EAAE;UAC5D2B,OAAO,GAAG,mBAAmBG,WAAW,6CAA6C3F,MAAM,CAAC6D,aAAa,oCAAoC;QAC/I;MACF;IACF,CAAC,MAAM,IAAIqB,IAAI,KAAK,WAAW,EAAE;MAC/B;MACA,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACpB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjC5C,KAAK,GAAG,qCAAqC;QAC7C,OAAO,KAAK;MACd;IACF;;IAEA;IACA7B,aAAa,CAACuG,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACX,IAAI,GAAG/D;IACV,CAAC,CAAC,CAAC;;IAEH;IACA3B,eAAe,CAACqG,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACX,IAAI,GAAGM;IACV,CAAC,CAAC,CAAC;IAEH,OAAO,CAACrE,KAAK;EACf,CAAC;;EAED;EACA,MAAM,CAAC2E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxM,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACyM,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1M,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2M,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5M,QAAQ,CAAC;IAC3D6M,KAAK,EAAE,EAAE;IACT7E,OAAO,EAAE,EAAE;IACX8E,SAAS,EAAEA,CAAA,KAAM,CAAC;EACpB,CAAC,CAAC;;EAEF;;EAEA;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMC,QAAQ,GAAG,CAAC,CAAC;;IAEnB;IACAV,oBAAoB,CAAC,KAAK,CAAC;;IAE3B;IACA,IAAI,CAAC/G,QAAQ,CAACG,YAAY,IAAIH,QAAQ,CAACG,YAAY,CAAC4E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjEyC,MAAM,CAACrH,YAAY,GAAG,uCAAuC;MAC7DoH,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAId,KAAK,CAACC,UAAU,CAAC1G,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAIuG,UAAU,CAAC1G,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;MAC7FqH,MAAM,CAACrH,YAAY,GAAG,sCAAsC;MAC5DoH,OAAO,GAAG,KAAK;IACjB;;IAEA;IACA,IAAI,CAACvH,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,CAAC2E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC3DyC,MAAM,CAACpH,SAAS,GAAG,qCAAqC;MACxDmH,OAAO,GAAG,KAAK;IACjB;IAEA,IAAIA,OAAO,EAAE;MACX,MAAMZ,WAAW,GAAGD,UAAU,CAAC1G,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA,IAAIT,YAAY,IAAIA,YAAY,CAACkH,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAChH,YAAY,CAACkH,aAAa,CAAC,EAAE;QACtGa,QAAQ,CAACtH,YAAY,GAAG,mBAAmBwG,WAAW,yCAAyCjH,YAAY,CAACkH,aAAa,IAAI;QAC7HG,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5B;QACA;MACF;;MAEA;MACA,IAAIQ,OAAO,IAAIvH,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC1E,MAAMY,MAAM,GAAGxB,MAAM,CAAC2F,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACxE,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIY,MAAM,IAAI2F,WAAW,GAAGD,UAAU,CAAC1F,MAAM,CAAC6D,aAAa,CAAC,EAAE;UAC5D4C,QAAQ,CAACtH,YAAY,GAAG,mBAAmBwG,WAAW,6CAA6C3F,MAAM,CAAC6D,aAAa,oCAAoC;UAC3JkC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;;UAE5B;UACAI,qBAAqB,CAAC;YACpBC,KAAK,EAAE,kCAAkC;YACzC7E,OAAO,EAAE,mBAAmBoE,WAAW,6CAA6C3F,MAAM,CAAC6D,aAAa,8DAA8D;YACtKwC,SAAS,EAAEA,CAAA,KAAM;cACf;cACAK,UAAU,CAAC,CAAC;YACd;UACF,CAAC,CAAC;UACFT,oBAAoB,CAAC,IAAI,CAAC;UAC1B,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;IACF;IAEA3G,aAAa,CAACkH,MAAM,CAAC;IACrBhH,eAAe,CAACiH,QAAQ,CAAC;IACzB,OAAOF,OAAO;EAChB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI5H,UAAU,KAAK,CAAC,EAAE;MACpB;MACA,IAAI,CAACwH,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;IACF,CAAC,MAAM,IAAIxH,UAAU,KAAK,CAAC,EAAE;MAC3B;MACAyB,UAAU,CAAC,CAAC;IACd;IAEAxB,aAAa,CAAE4H,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB7H,aAAa,CAAE4H,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxB9H,aAAa,CAAC,CAAC,CAAC;IAChBJ,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBI,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA;EACA,MAAMsH,2BAA2B,GAAGA,CAACnB,WAAW,EAAEoB,YAAY,KAAK;IACjE,OAAOtK,mBAAmB,CAACkJ,WAAW,EAAEoB,YAAY,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B;IACA;IACA,IAAIvG,QAAQ;IACZ,IAAIwG,kBAAkB;IACtB,IAAItB,WAAW;IACf,IAAIuB,SAAS,GAAG,KAAK;IAErB,IAAI;MACFrJ,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI,CAACyI,YAAY,CAAC,CAAC,EAAE;QACnBzI,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA8H,WAAW,GAAGD,UAAU,CAAC1G,QAAQ,CAACG,YAAY,CAAC;;MAE/C;MACAsB,QAAQ,GAAGzB,QAAQ,CAACI,SAAS;;MAE7B;MACA,IAAI,CAACqB,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;QAChC;QACAnB,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbD,SAAS,EAAE;QACb,CAAC,CAAC;QACFvB,UAAU,CAAC,KAAK,CAAC;QACjB;MACF,CAAC,MAAM,IAAI4C,QAAQ,KAAK,cAAc,EAAE;QACtC;QACAI,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C;QACAL,QAAQ,GAAG,cAAc;MAC3B,CAAC,MAAM;QACL;QACAI,OAAO,CAACC,GAAG,CAAC,wBAAwBL,QAAQ,EAAE,CAAC;MACjD;;MAEA;MACAwG,kBAAkB,GAAGH,2BAA2B,CAACnB,WAAW,EAAEjH,YAAY,CAACkH,aAAa,CAAC;;MAEzF;MACAsB,SAAS,GAAG,KAAK;;MAEjB;MACA,IAAIzG,QAAQ,KAAK,cAAc,EAAE;QAC/ByG,SAAS,GAAG,IAAI;QAChBrG,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACrD;MACA;MAAA,KACK,IAAIL,QAAQ,IAAIA,QAAQ,KAAK,cAAc,EAAE;QAChD,MAAMT,MAAM,GAAGxB,MAAM,CAAC2F,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACxE,SAAS,KAAKqB,QAAQ,CAAC;QACzD,IAAIT,MAAM,IAAI2F,WAAW,GAAGD,UAAU,CAAC1F,MAAM,CAAC6D,aAAa,CAAC,EAAE;UAC5DqD,SAAS,GAAG,IAAI;UAChBrG,OAAO,CAACC,GAAG,CAAC,qCAAqCL,QAAQ,iCAAiC,CAAC;QAC7F;MACF;;MAEA;MACA,IAAI/B,YAAY,IAAIA,YAAY,CAACkH,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAChH,YAAY,CAACkH,aAAa,CAAC,EAAE;QACtG;QACAsB,SAAS,GAAG,IAAI;QAChBrG,OAAO,CAACC,GAAG,CAAC,yCAAyC6E,WAAW,sBAAsBjH,YAAY,CAACkH,aAAa,GAAG,CAAC;MACtH;;MAEA;MACA/E,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBvD,UAAU;QACVwH,MAAM,EAAE/F,QAAQ,CAACE,OAAO;QACxByG,WAAW;QACXlF,QAAQ;QACRyG,SAAS;QACTD;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAACnB,iBAAiB,EAAE;QACtB,MAAMqB,cAAc,GAAG,qCAAqCnI,QAAQ,CAACE,OAAO,QAAQyG,WAAW,WAAW;;QAE1G;QACAQ,qBAAqB,CAAC;UACpBC,KAAK,EAAE,wBAAwB;UAC/B7E,OAAO,EAAE4F,cAAc;UACvBd,SAAS,EAAE,MAAAA,CAAA,KAAY;YACrB;YACA,IAAI;cACFxI,UAAU,CAAC,IAAI,CAAC;cAEhB,MAAM1B,WAAW,CAACiL,iBAAiB,CACjC7J,UAAU,EACVyB,QAAQ,CAACE,OAAO,EAChByG,WAAW,EACXlF,QAAQ,EACRyG,SACF,CAAC;;cAED;cACA,IAAIG,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;cAC9F,IAAIxG,QAAQ,KAAK,cAAc,EAAE;gBAC/B4G,cAAc,IAAI,iCAAiC;cACrD,CAAC,MAAM,IAAI5G,QAAQ,EAAE;gBACnB,MAAMT,MAAM,GAAGxB,MAAM,CAAC2F,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACxE,SAAS,KAAKqB,QAAQ,CAAC;gBACzD,IAAIT,MAAM,EAAE;kBACVqH,cAAc,IAAI,gCAAgC5G,QAAQ,EAAE;gBAC9D;cACF;;cAEA;cACAjD,SAAS,CAAC6J,cAAc,CAAC;;cAEzB;cACAR,WAAW,CAAC,CAAC;;cAEb;cACAjG,QAAQ,CAAC,CAAC;YACZ,CAAC,CAAC,OAAOO,KAAK,EAAE;cACdN,OAAO,CAACM,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;cAEzE;cACA,IAAIV,QAAQ,KAAK,cAAc,IAAIU,KAAK,CAACmG,OAAO,EAAE;gBAChD;gBACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;gBAC7HzJ,SAAS,CAAC6J,cAAc,CAAC;;gBAEzB;gBACAR,WAAW,CAAC,CAAC;;gBAEb;gBACAjG,QAAQ,CAAC,CAAC;gBACV;cACF;;cAEA;cACA,IAAI2B,YAAY,GAAG,kDAAkD;cAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;gBAAA,IAAAkG,oBAAA;gBAClB;gBACA,MAAM/C,MAAM,GAAGrD,KAAK,CAACE,QAAQ,CAACmD,MAAM;gBACpC,MAAMhC,MAAM,GAAG,EAAA+E,oBAAA,GAAApG,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAAkF,oBAAA,uBAAnBA,oBAAA,CAAqB/E,MAAM,KAAIrB,KAAK,CAACI,OAAO;gBAE3D,IAAIiD,MAAM,KAAK,GAAG,EAAE;kBAClB;kBACA,IAAIhC,MAAM,CAAC9B,QAAQ,CAAC,eAAe,CAAC,EAAE;oBACpC6B,YAAY,GAAG,uGAAuG;kBACxH,CAAC,MAAM,IAAIC,MAAM,CAAC9B,QAAQ,CAAC,YAAY,CAAC,EAAE;oBACxC6B,YAAY,GAAG,4EAA4E;kBAC7F,CAAC,MAAM;oBACLA,YAAY,GAAGC,MAAM;kBACvB;gBACF,CAAC,MAAM,IAAIgC,MAAM,KAAK,GAAG,EAAE;kBACzB;kBACA,IAAI/D,QAAQ,KAAK,cAAc,IAAI+B,MAAM,CAAC9B,QAAQ,CAAC,aAAa,CAAC,EAAE;oBACjE,IAAI2G,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;oBAC7HzJ,SAAS,CAAC6J,cAAc,CAAC;;oBAEzB;oBACAR,WAAW,CAAC,CAAC;;oBAEb;oBACAjG,QAAQ,CAAC,CAAC;oBACV;kBACF,CAAC,MAAM;oBACL2B,YAAY,GAAG,8BAA8BC,MAAM,EAAE;kBACvD;gBACF,CAAC,MAAM;kBACLD,YAAY,GAAG,sBAAsBiC,MAAM,MAAMhC,MAAM,EAAE;gBAC3D;cACF,CAAC,MAAM,IAAIrB,KAAK,CAACqG,OAAO,EAAE;gBACxB;gBACAjF,YAAY,GAAG,+DAA+D;cAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAI/B,QAAQ,KAAK,cAAc,EAAE;gBACtD;gBACA8B,YAAY,GAAGpB,KAAK,CAACqB,MAAM;gBAC3B,IAAIrB,KAAK,CAACqD,MAAM,KAAK,GAAG,IAAIrD,KAAK,CAACmG,OAAO,EAAE;kBACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;kBAC7HzJ,SAAS,CAAC6J,cAAc,CAAC;;kBAEzB;kBACAR,WAAW,CAAC,CAAC;;kBAEb;kBACAjG,QAAQ,CAAC,CAAC;kBACV;gBACF;cACF,CAAC,MAAM;gBACL;gBACA2B,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;cACtE;cAEA/E,OAAO,CAAC8E,YAAY,CAAC;YACvB,CAAC,SAAS;cACR1E,UAAU,CAAC,KAAK,CAAC;YACnB;UACF;QACF,CAAC,CAAC;QACFoI,oBAAoB,CAAC,IAAI,CAAC;QAC1BpI,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACAgD,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ED,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEvD,UAAU,CAAC;MACxCsD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE9B,QAAQ,CAACE,OAAO,CAAC;MAC3C2B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE6E,WAAW,CAAC;MAC3C9E,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEL,QAAQ,EAAE,OAAOA,QAAQ,CAAC;MACtDI,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEoG,SAAS,CAAC;MAEtC,MAAM/K,WAAW,CAACiL,iBAAiB,CACjC7J,UAAU,EACVyB,QAAQ,CAACE,OAAO,EAChByG,WAAW,EACXlF,QAAQ,EACRyG,SACF,CAAC;;MAED;MACA,IAAIG,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;MAC9F,IAAIxG,QAAQ,KAAK,cAAc,EAAE;QAC/B4G,cAAc,IAAI,iCAAiC;MACrD,CAAC,MAAM,IAAI5G,QAAQ,EAAE;QACnB,MAAMT,MAAM,GAAGxB,MAAM,CAAC2F,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACxE,SAAS,KAAKqB,QAAQ,CAAC;QACzD,IAAIT,MAAM,EAAE;UACVqH,cAAc,IAAI,gCAAgC5G,QAAQ,EAAE;QAC9D;MACF;;MAEA;MACAjD,SAAS,CAAC6J,cAAc,CAAC;;MAEzB;MACAR,WAAW,CAAC,CAAC;;MAEb;MACAjG,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;MAEzE;MACA,IAAIV,QAAQ,KAAK,cAAc,IAAIU,KAAK,CAACmG,OAAO,EAAE;QAChD;QACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;QAC7HzJ,SAAS,CAAC6J,cAAc,CAAC;;QAEzB;QACAR,WAAW,CAAC,CAAC;;QAEb;QACAjG,QAAQ,CAAC,CAAC;QACV;MACF;;MAEA;MACA,IAAI2B,YAAY,GAAG,kDAAkD;MAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;QAAA,IAAAoG,qBAAA;QAClB;QACA,MAAMjD,MAAM,GAAGrD,KAAK,CAACE,QAAQ,CAACmD,MAAM;QACpC,MAAMhC,MAAM,GAAG,EAAAiF,qBAAA,GAAAtG,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAAoF,qBAAA,uBAAnBA,qBAAA,CAAqBjF,MAAM,KAAIrB,KAAK,CAACI,OAAO;QAE3D,IAAIiD,MAAM,KAAK,GAAG,EAAE;UAClB;UACA,IAAIhC,MAAM,CAAC9B,QAAQ,CAAC,eAAe,CAAC,EAAE;YACpC6B,YAAY,GAAG,uGAAuG;UACxH,CAAC,MAAM,IAAIC,MAAM,CAAC9B,QAAQ,CAAC,YAAY,CAAC,EAAE;YACxC6B,YAAY,GAAG,4EAA4E;UAC7F,CAAC,MAAM;YACLA,YAAY,GAAGC,MAAM;UACvB;QACF,CAAC,MAAM,IAAIgC,MAAM,KAAK,GAAG,EAAE;UACzB;UACA,IAAI/D,QAAQ,KAAK,cAAc,IAAI+B,MAAM,CAAC9B,QAAQ,CAAC,aAAa,CAAC,EAAE;YACjE,IAAI2G,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;YAC7HzJ,SAAS,CAAC6J,cAAc,CAAC;;YAEzB;YACAR,WAAW,CAAC,CAAC;;YAEb;YACAjG,QAAQ,CAAC,CAAC;YACV;UACF,CAAC,MAAM;YACL2B,YAAY,GAAG,8BAA8BC,MAAM,EAAE;UACvD;QACF,CAAC,MAAM;UACLD,YAAY,GAAG,sBAAsBiC,MAAM,MAAMhC,MAAM,EAAE;QAC3D;MACF,CAAC,MAAM,IAAIrB,KAAK,CAACqG,OAAO,EAAE;QACxB;QACAjF,YAAY,GAAG,+DAA+D;MAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAI/B,QAAQ,KAAK,cAAc,EAAE;QACtD;QACA8B,YAAY,GAAGpB,KAAK,CAACqB,MAAM;QAC3B,IAAIrB,KAAK,CAACqD,MAAM,KAAK,GAAG,IAAIrD,KAAK,CAACmG,OAAO,EAAE;UACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;UAC7HzJ,SAAS,CAAC6J,cAAc,CAAC;;UAEzB;UACAR,WAAW,CAAC,CAAC;;UAEb;UACAjG,QAAQ,CAAC,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACL;QACA2B,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;MACtE;MAEA/E,OAAO,CAAC8E,YAAY,CAAC;IACvB,CAAC,SAAS;MACR1E,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6J,WAAW,GAAGA,CAAA,KAAM;IACxB,oBACEvK,OAAA,CAACzD,GAAG;MAAAiO,QAAA,gBACFxK,OAAA,CAACvD,UAAU;QAACgO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb9K,OAAA,CAACxD,KAAK;QAACuO,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzBxK,OAAA,CAACvD,UAAU;UAACgO,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb9K,OAAA,CAACpD,IAAI;UAACsO,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAAZ,QAAA,gBAC7CxK,OAAA,CAACpD,IAAI;YAACyO,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACfxK,OAAA,CAACtD,SAAS;cACR6O,SAAS;cACTC,KAAK,EAAC,SAAS;cACff,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAEvG,WAAY;cACnBgK,QAAQ,EAAG3D,CAAC,IAAKpG,cAAc,CAACoG,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cAChD0D,WAAW,EAAC;YAAyB;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP9K,OAAA,CAACpD,IAAI;YAACyO,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACfxK,OAAA,CAACrD,MAAM;cACL4O,SAAS;cACTd,OAAO,EAAC,WAAW;cACnBkB,KAAK,EAAC,SAAS;cACfC,OAAO,EAAEjF,oBAAqB;cAC9BkF,QAAQ,EAAElL,WAAW,IAAI,CAACc,WAAW,CAACmF,IAAI,CAAC,CAAE;cAC7CkF,SAAS,EAAEnL,WAAW,gBAAGX,OAAA,CAAC7C,gBAAgB;gBAAC4O,IAAI,EAAE;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG9K,OAAA,CAAC1B,UAAU;gBAAAqM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAN,QAAA,EAC1E;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGR9K,OAAA,CAACxD,KAAK;QAACuO,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBxK,OAAA,CAACvD,UAAU;UAACgO,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZnK,WAAW,gBACVX,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DxK,OAAA,CAAC7C,gBAAgB;YAAAwN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJ3J,IAAI,CAAC2C,MAAM,KAAK,CAAC,gBACnB9D,OAAA,CAAC9C,KAAK;UAACiP,QAAQ,EAAC,MAAM;UAAA3B,QAAA,EAAC;QAEvB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAER9K,OAAA,CAAC/B,IAAI;UAAC8M,EAAE,EAAE;YAAEqB,SAAS,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAA7B,QAAA,EAChDrJ,IAAI,CAACmL,GAAG,CAAE1J,IAAI,iBACb5C,OAAA,CAAC7D,KAAK,CAAC8D,QAAQ;YAAAuK,QAAA,gBACbxK,OAAA,CAAC9B,QAAQ;cAACqO,MAAM;cAACX,OAAO,EAAEA,CAAA,KAAMxE,gBAAgB,CAACxE,IAAI,CAAE;cAAA4H,QAAA,gBACrDxK,OAAA,CAAC7B,YAAY;gBACXqO,OAAO,eACLxM,OAAA,CAACzD,GAAG;kBAACwO,EAAE,EAAE;oBAAEiB,OAAO,EAAE,MAAM;oBAAEZ,UAAU,EAAE;kBAAS,CAAE;kBAAAZ,QAAA,gBACjDxK,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,WAAW;oBAAAD,QAAA,EAAE5H,IAAI,CAACb;kBAAO;oBAAA4I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,EAC1DrL,YAAY,CAACmD,IAAI,CAAC,gBACjB5C,OAAA,CAAC1C,IAAI;oBACHyO,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,OAAO;oBACbG,KAAK,EAAC,OAAO;oBACbZ,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,GACApL,gBAAgB,CAACkD,IAAI,CAAC,gBACxB5C,OAAA,CAAC1C,IAAI;oBACHyO,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,YAAY;oBAClBG,KAAK,EAAC,SAAS;oBACfZ,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,gBAEF9K,OAAA,CAAC1C,IAAI;oBACHyO,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAE5I,IAAI,CAACqE,mBAAoB;oBAChC0E,KAAK,EAAEhM,kBAAkB,CAACiD,IAAI,CAACqE,mBAAmB,CAAE;oBACpD8D,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACD4B,SAAS,eACP1M,OAAA,CAAAE,SAAA;kBAAAsK,QAAA,gBACExK,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GACzC5H,IAAI,CAAC+C,SAAS,IAAI,KAAK,EAAC,KAAG,EAAC/C,IAAI,CAACgD,YAAY,IAAI,KAAK,EAAC,KAAG,EAAChD,IAAI,CAACiD,OAAO,IAAI,KAAK;kBAAA;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,eACb9K,OAAA;oBAAA2K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN9K,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GAAC,MACvC,EAAC5H,IAAI,CAACgK,mBAAmB,IAAI,KAAK,EAAC,QAAM,EAAChK,IAAI,CAACiK,iBAAiB,IAAI,KAAK;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,eACb9K,OAAA;oBAAA2K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN9K,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GAAC,iBAC5B,EAAC5H,IAAI,CAAC6F,aAAa,IAAI,KAAK,EAAC,mBAAiB,EAAC7F,IAAI,CAACsE,eAAe,IAAI,GAAG;kBAAA;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC;gBAAA,eACb;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF9K,OAAA,CAAC5B,uBAAuB;gBAAAoM,QAAA,eACtBxK,OAAA,CAAC3C,UAAU;kBAACyP,IAAI,EAAC,KAAK;kBAAClB,OAAO,EAAG9D,CAAC,IAAK;oBACrCA,CAAC,CAACiF,eAAe,CAAC,CAAC,CAAC,CAAC;oBACrBvL,eAAe,CAACoB,IAAI,CAAC;oBACrBO,wBAAwB,CAAC,IAAI,CAAC;kBAChC,CAAE;kBAAAqH,QAAA,eACAxK,OAAA,CAAClB,QAAQ;oBAAA6L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACX9K,OAAA,CAAC/C,OAAO;cAAA0N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAxDQlI,IAAI,CAACb,OAAO;YAAA4I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyDjB,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMkC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACzL,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEvB,OAAA,CAACzD,GAAG;MAAAiO,QAAA,gBACFxK,OAAA,CAACvD,UAAU;QAACgO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb9K,OAAA,CAACb,eAAe;QACdyD,IAAI,EAAErB,YAAa;QACnB0L,OAAO,EAAE,IAAK;QACdhE,KAAK,EAAC;MAA+B;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAEF9K,OAAA,CAACxD,KAAK;QAACuO,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBxK,OAAA,CAACvD,UAAU;UAACgO,OAAO,EAAC,WAAW;UAACC,YAAY;UAACK,EAAE,EAAE;YAAEmC,UAAU,EAAE;UAAO,CAAE;UAAA1C,QAAA,EAAC;QAEzE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGb9K,OAAA,CAACpD,IAAI;UAACsO,SAAS;UAACC,OAAO,EAAE,CAAE;UAACJ,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACxCxK,OAAA,CAACpD,IAAI;YAACyO,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6B,EAAE,EAAE,CAAE;YAAA3C,QAAA,eACvBxK,OAAA,CAACzD,GAAG;cAACwO,EAAE,EAAE;gBAAEC,CAAC,EAAE,CAAC;gBAAEoC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAO,CAAE;cAAA9C,QAAA,gBACrExK,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEmC,UAAU,EAAE,MAAM;kBAAEvB,KAAK,EAAE;gBAAe,CAAE;gBAAAnB,QAAA,EAAC;cAEhG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9K,OAAA,CAACpD,IAAI;gBAACsO,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAX,QAAA,gBACzBxK,OAAA,CAACpD,IAAI;kBAACyO,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACfxK,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAACM,EAAE,EAAE;sBAAEmC,UAAU,EAAE;oBAAS,CAAE;oBAAA1C,QAAA,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACP9K,OAAA,CAACpD,IAAI;kBAACyO,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACfxK,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAEjJ,YAAY,CAACkH,aAAa,IAAI,KAAK,EAAC,IAAE;kBAAA;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eACP9K,OAAA,CAACpD,IAAI;kBAACyO,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACfxK,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAACM,EAAE,EAAE;sBAAEmC,UAAU,EAAE;oBAAS,CAAE;oBAAA1C,QAAA,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACP9K,OAAA,CAACpD,IAAI;kBAACyO,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACfxK,OAAA,CAAC1C,IAAI;oBACHkO,KAAK,EAAEjK,YAAY,CAAC0F,mBAAmB,IAAI,KAAM;oBACjD8E,IAAI,EAAC,OAAO;oBACZJ,KAAK,EAAEhM,kBAAkB,CAAC4B,YAAY,CAAC0F,mBAAmB,CAAE;oBAC5DwD,OAAO,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEP9K,OAAA,CAACpD,IAAI;YAACyO,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6B,EAAE,EAAE,CAAE;YAAA3C,QAAA,eACvBxK,OAAA,CAACzD,GAAG;cAACwO,EAAE,EAAE;gBAAEC,CAAC,EAAE,CAAC;gBAAEoC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAO,CAAE;cAAA9C,QAAA,gBACrExK,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEmC,UAAU,EAAE,MAAM;kBAAEvB,KAAK,EAAE;gBAAiB,CAAE;gBAAAnB,QAAA,EAAC;cAElG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZjJ,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,GAAG,CAAC,MAAM;gBACpE,MAAMY,MAAM,GAAGxB,MAAM,CAAC2F,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACxE,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;gBACnE,OAAOY,MAAM,gBACX7C,OAAA,CAACpD,IAAI;kBAACsO,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAX,QAAA,gBACzBxK,OAAA,CAACpD,IAAI;oBAACyO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfxK,OAAA,CAACvD,UAAU;sBAACgO,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEmC,UAAU,EAAE;sBAAS,CAAE;sBAAA1C,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC,eACP9K,OAAA,CAACpD,IAAI;oBAACyO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfxK,OAAA,CAACvD,UAAU;sBAACgO,OAAO,EAAC,OAAO;sBAAAD,QAAA,EAAEnH,eAAe,CAACR,MAAM,CAACZ,SAAS;oBAAC;sBAAA0I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC,eACP9K,OAAA,CAACpD,IAAI;oBAACyO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfxK,OAAA,CAACvD,UAAU;sBAACgO,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEmC,UAAU,EAAE;sBAAS,CAAE;sBAAA1C,QAAA,EAAC;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC,eACP9K,OAAA,CAACpD,IAAI;oBAACyO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfxK,OAAA,CAACvD,UAAU;sBAACgO,OAAO,EAAC,OAAO;sBAAAD,QAAA,GAAE3H,MAAM,CAAC6D,aAAa,IAAI,CAAC,EAAC,IAAE;oBAAA;sBAAAiE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACP9K,OAAA,CAACpD,IAAI;oBAACyO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfxK,OAAA,CAACvD,UAAU;sBAACgO,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEmC,UAAU,EAAE;sBAAS,CAAE;sBAAA1C,QAAA,EAAC;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eACP9K,OAAA,CAACpD,IAAI;oBAACyO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfxK,OAAA,CAAC1C,IAAI;sBACHkO,KAAK,EAAE3I,MAAM,CAAC6C,YAAY,IAAI,KAAM;sBACpCqG,IAAI,EAAC,OAAO;sBACZJ,KAAK,EAAE/L,iBAAiB,CAACiD,MAAM,CAAC6C,YAAY,CAAE;sBAC9C+E,OAAO,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEP9K,OAAA,CAACvD,UAAU;kBAACgO,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAC;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAC3D;cACH,CAAC,EAAE,CAAC,gBACF9K,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,OAAO;gBAAAD,QAAA,EACxB3I,QAAQ,CAACI,SAAS,KAAK,cAAc,GACpC,kDAAkD,GAClD;cAA4B;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEP9K,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEwC,EAAE,EAAE,CAAC;YAAEtC,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACxBxK,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEmC,UAAU,EAAE;YAAO,CAAE;YAAA1C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9K,OAAA,CAACtD,SAAS;YACRqP,IAAI,EAAC,OAAO;YACZR,SAAS;YACTC,KAAK,EAAC,cAAc;YACpBf,OAAO,EAAC,UAAU;YAClB1C,IAAI,EAAC,cAAc;YACnByF,IAAI,EAAC,QAAQ;YACbxF,KAAK,EAAEnG,QAAQ,CAACG,YAAa;YAC7ByJ,QAAQ,EAAE5D,gBAAiB;YAC3B7D,KAAK,EAAE,CAAC,CAAC9B,UAAU,CAACF,YAAa;YACjCyL,UAAU,EAAEvL,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;YACjE0L,mBAAmB,EAAE;cACnB3C,EAAE,EAAE;gBAAEY,KAAK,EAAEvJ,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACF+I,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL1I,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,IAAI,CAAC2G,iBAAiB,iBAC1E3I,OAAA,CAAC9C,KAAK;UAACiP,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,EACrCpI,YAAY,CAACJ;QAAY;UAAA2I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACR,eAED9K,OAAA,CAAC9C,KAAK;UAACiP,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,eACnCxK,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAC;UAE5B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAM6C,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,MAAMC,iBAAiB,GAAIC,YAAY,IAAK;MAC1C,OAAO,IAAIzN,UAAU,KAAKyN,YAAY,EAAE;IAC1C,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAIjL,MAAM,IAAK;MACtC,IAAI,CAACA,MAAM,IAAI,CAAChB,QAAQ,CAACG,YAAY,EAAE,OAAO,IAAI;MAClD,OAAOuG,UAAU,CAAC1F,MAAM,CAAC6D,aAAa,CAAC,IAAI6B,UAAU,CAAC1G,QAAQ,CAACG,YAAY,CAAC;IAC9E,CAAC;;IAED;IACA,MAAM+L,uBAAuB,GAAIjG,CAAC,IAAK;MACrC,MAAM+F,YAAY,GAAG/F,CAAC,CAACG,MAAM,CAACD,KAAK,CAACpB,IAAI,CAAC,CAAC;;MAE1C;MACA,IAAIiH,YAAY,CAAC/G,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QACtChF,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb8L,eAAe,EAAE;QACnB,CAAC,CAAC;QACF;MACF;MAEA,IAAIH,YAAY,EAAE;QAChB;QACA,MAAMI,gBAAgB,GAAGL,iBAAiB,CAACC,YAAY,CAAC;;QAExD;QACA,MAAMK,eAAe,GAAG7M,MAAM,CAAC2F,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACxE,SAAS,KAAKgM,gBAAgB,CAAC;QAE1E,IAAIC,eAAe,EAAE;UACnB;UACA,IAAIA,eAAe,CAACxI,YAAY,KAAK,MAAM,IAAIwI,eAAe,CAACxI,YAAY,KAAK,WAAW,EAAE;YAC3FvD,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb8L,eAAe,EAAE,aAAaH,YAAY,eAAeK,eAAe,CAACxI,YAAY;YACvF,CAAC,CAAC;YACF;UACF;;UAEA;UACA,IAAInE,YAAY,EAAE;YAChB;YACA,MAAM0E,aAAa,GAAGF,MAAM,CAACxE,YAAY,CAACoE,SAAS,IAAI,EAAE,CAAC;YAC1D,MAAMG,cAAc,GAAGC,MAAM,CAACxE,YAAY,CAACqE,YAAY,IAAI,GAAG,CAAC;YAC/D,MAAMM,WAAW,GAAGH,MAAM,CAACxE,YAAY,CAACsE,OAAO,IAAI,GAAG,CAAC;YAEvD,MAAMO,eAAe,GAAGL,MAAM,CAACmI,eAAe,CAACvI,SAAS,IAAI,EAAE,CAAC;YAC/D,MAAMU,gBAAgB,GAAGN,MAAM,CAACmI,eAAe,CAACtI,YAAY,IAAI,GAAG,CAAC;YACpE,MAAMU,aAAa,GAAGP,MAAM,CAACmI,eAAe,CAACrI,OAAO,IAAI,GAAG,CAAC;;YAE5D;YACAnC,OAAO,CAACC,GAAG,CAAC,iCAAiCuK,eAAe,CAACjM,SAAS,GAAG,EAAE;cACzE0D,SAAS,EAAE,GAAGS,eAAe,QAAQH,aAAa,EAAE;cACpDL,YAAY,EAAE,GAAGS,gBAAgB,QAAQP,cAAc,EAAE;cACzDD,OAAO,EAAE,GAAGS,aAAa,QAAQJ,WAAW;YAC9C,CAAC,CAAC;YAEF,IAAIE,eAAe,KAAKH,aAAa,IACjCI,gBAAgB,KAAKP,cAAc,IACnCQ,aAAa,KAAKJ,WAAW,EAAE;cACjC;cACAvD,uBAAuB,CAAC;gBACtBC,IAAI,EAAErB,YAAY;gBAClBsB,MAAM,EAAEqL;cACV,CAAC,CAAC;cACF3L,6BAA6B,CAAC,IAAI,CAAC;cACnC;YACF;UACF;;UAEA;UACA,IAAIuL,mBAAmB,CAACI,eAAe,CAAC,EAAE;YACxC;YACApM,WAAW,CAAC;cACV,GAAGD,QAAQ;cACXI,SAAS,EAAEgM;YACb,CAAC,CAAC;YACF9L,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb8L,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACA7L,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb8L,eAAe,EAAE,aAAaH,YAAY,sCAAsCK,eAAe,CAACxH,aAAa;YAC/G,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL;UACAvE,aAAa,CAAC;YACZ,GAAGD,UAAU;YACb8L,eAAe,EAAE,UAAUH,YAAY;UACzC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACA/L,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb8L,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,oBACEhO,OAAA,CAACzD,GAAG;MAAAiO,QAAA,gBACFxK,OAAA,CAACvD,UAAU;QAACgO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb9K,OAAA,CAACxD,KAAK;QAACuO,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBxK,OAAA,CAACvD,UAAU;UAACgO,OAAO,EAAC,OAAO;UAAC0D,SAAS;UAAA3D,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZjK,aAAa,gBACZb,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DxK,OAAA,CAAC7C,gBAAgB;YAAAwN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAEN9K,OAAA,CAACzD,GAAG;UAAAiO,QAAA,gBACFxK,OAAA,CAACpD,IAAI;YAACsO,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBAEzBxK,OAAA,CAACpD,IAAI;cAACyO,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAA3C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEmC,UAAU,EAAE;gBAAO,CAAE;gBAAA1C,QAAA,EAAC;cAEzE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9K,OAAA,CAACtD,SAAS;gBACRqP,IAAI,EAAC,OAAO;gBACZR,SAAS;gBACTC,KAAK,EAAC,eAAe;gBACrBf,OAAO,EAAC,UAAU;gBAClBiB,WAAW,EAAC,oBAAoB;gBAChC+B,UAAU,EAAEvL,UAAU,CAAC8L,eAAe,IAAI,uCAAwC;gBAClFhK,KAAK,EAAE,CAAC,CAAC9B,UAAU,CAAC8L,eAAgB;gBACpCI,MAAM,EAAEL,uBAAwB;gBAChChD,EAAE,EAAE;kBAAEE,EAAE,EAAE;gBAAE;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACF9K,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,OAAO;gBAACM,EAAE,EAAE;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAT,QAAA,GAAC,aAC9B,eAAAxK,OAAA;kBAAAwK,QAAA,EAAS3I,QAAQ,CAACI,SAAS,IAAI;gBAAG;kBAAA0I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAGP9K,OAAA,CAACpD,IAAI;cAACyO,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAA3C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEmC,UAAU,EAAE;gBAAO,CAAE;gBAAA1C,QAAA,EAAC;cAEzE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9K,OAAA,CAACzD,GAAG;gBAAAiO,QAAA,gBACFxK,OAAA,CAACvD,UAAU;kBAACgO,OAAO,EAAC,WAAW;kBAACC,YAAY;kBAACK,EAAE,EAAE;oBAAEY,KAAK,EAAE,cAAc;oBAAEuB,UAAU,EAAE,MAAM;oBAAEjC,EAAE,EAAE;kBAAE,CAAE;kBAAAT,QAAA,EACnGjJ,YAAY,GAAG,4CAA4C,GAAG;gBAAsB;kBAAAoJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC,eAEb9K,OAAA,CAACnD,WAAW;kBAAC0O,SAAS;kBAACQ,IAAI,EAAC,OAAO;kBAAC/H,KAAK,EAAE,CAAC,CAAC9B,UAAU,CAACD,SAAU;kBAAAuI,QAAA,gBAChExK,OAAA,CAAClD,UAAU;oBAACuR,EAAE,EAAC,qBAAqB;oBAAA7D,QAAA,EAAC;kBAAgB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClE9K,OAAA,CAACjD,MAAM;oBACLuR,OAAO,EAAC,qBAAqB;oBAC7BD,EAAE,EAAC,eAAe;oBAClBtG,IAAI,EAAC,WAAW;oBAChBC,KAAK,EAAEnG,QAAQ,CAACI,SAAU;oBAC1BuJ,KAAK,EAAC,kBAAkB;oBACxBC,QAAQ,EAAE5D,gBAAiB;oBAAA2C,QAAA,gBAE3BxK,OAAA,CAAChD,QAAQ;sBAACgL,KAAK,EAAC,cAAc;sBAAAwC,QAAA,gBAC5BxK,OAAA;wBAAAwK,QAAA,EAAQ;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,+BAC/B;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eACX9K,OAAA,CAAC/C,OAAO;sBAAA0N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACVzJ,MAAM,CAACyC,MAAM,GAAG,CAAC,gBAChB9D,OAAA,CAACzD,GAAG;sBAACoQ,SAAS,EAAC,IAAI;sBAAC5B,EAAE,EAAE;wBAAEC,CAAC,EAAE,CAAC;wBAAEoC,OAAO,EAAE;sBAAmB,CAAE;sBAAA5C,QAAA,eAC5DxK,OAAA,CAACvD,UAAU;wBAACgO,OAAO,EAAC,SAAS;wBAACM,EAAE,EAAE;0BAAEmC,UAAU,EAAE,MAAM;0BAAEvB,KAAK,EAAE;wBAAe,CAAE;wBAAAnB,QAAA,GAC7EnJ,MAAM,CAACyC,MAAM,EAAC,6BACjB;sBAAA;wBAAA6G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,GACJ,IAAI,EACPzJ,MAAM,CAACiL,GAAG,CAAEzJ,MAAM,iBACjB7C,OAAA,CAAChD,QAAQ;sBAEPgL,KAAK,EAAEnF,MAAM,CAACZ,SAAU;sBACxB4J,QAAQ,EAAEhJ,MAAM,CAAC6D,aAAa,GAAG6B,UAAU,CAAC1G,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;sBACxE+I,EAAE,EAAE;wBACF,gBAAgB,EAAE;0BAAEqC,OAAO,EAAE;wBAAgB,CAAC;wBAC9C,sBAAsB,EAAE;0BAAEA,OAAO,EAAE;wBAAgB,CAAC;wBACpDA,OAAO,EAAE7L,YAAY,IACdsB,MAAM,CAAC8C,SAAS,KAAKpE,YAAY,CAACoE,SAAS,IAC3CI,MAAM,CAAClD,MAAM,CAAC+C,YAAY,CAAC,KAAKG,MAAM,CAACxE,YAAY,CAACqE,YAAY,CAAC,IACjEG,MAAM,CAAClD,MAAM,CAACgD,OAAO,CAAC,KAAKE,MAAM,CAACxE,YAAY,CAACsE,OAAO,CAAC,GACvD,yBAAyB,GAAG;sBACrC,CAAE;sBAAA2E,QAAA,eAEFxK,OAAA,CAACzD,GAAG;wBAACwO,EAAE,EAAE;0BAAEiB,OAAO,EAAE,MAAM;0BAAEuC,aAAa,EAAE,QAAQ;0BAAEC,KAAK,EAAE;wBAAO,CAAE;wBAAAhE,QAAA,gBACnExK,OAAA,CAACzD,GAAG;0BAACwO,EAAE,EAAE;4BAAEiB,OAAO,EAAE,MAAM;4BAAEC,cAAc,EAAE,eAAe;4BAAEb,UAAU,EAAE,QAAQ;4BAAEoD,KAAK,EAAE;0BAAO,CAAE;0BAAAhE,QAAA,gBACjGxK,OAAA,CAACvD,UAAU;4BAACgO,OAAO,EAAC,OAAO;4BAACM,EAAE,EAAE;8BAAEmC,UAAU,EAAE;4BAAO,CAAE;4BAAA1C,QAAA,GACpDnH,eAAe,CAACR,MAAM,CAACZ,SAAS,CAAC,EAAC,KAAG,EAACY,MAAM,CAAC8C,SAAS,IAAI,KAAK;0BAAA;4BAAAgF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtD,CAAC,EACZvJ,YAAY,IACZsB,MAAM,CAAC8C,SAAS,KAAKpE,YAAY,CAACoE,SAAS,IAC3CI,MAAM,CAAClD,MAAM,CAAC+C,YAAY,CAAC,KAAKG,MAAM,CAACxE,YAAY,CAACqE,YAAY,CAAC,IACjEG,MAAM,CAAClD,MAAM,CAACgD,OAAO,CAAC,KAAKE,MAAM,CAACxE,YAAY,CAACsE,OAAO,CAAC,iBACtD7F,OAAA,CAAC1C,IAAI;4BACHyO,IAAI,EAAC,OAAO;4BACZP,KAAK,EAAC,aAAa;4BACnBG,KAAK,EAAC,SAAS;4BACflB,OAAO,EAAC,UAAU;4BAClBM,EAAE,EAAE;8BAAEuC,MAAM,EAAE,EAAE;8BAAEmB,QAAQ,EAAE;4BAAS;0BAAE;4BAAA9D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxC,CACF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACN9K,OAAA,CAACzD,GAAG;0BAACwO,EAAE,EAAE;4BAAEiB,OAAO,EAAE,MAAM;4BAAEC,cAAc,EAAE,eAAe;4BAAEuC,KAAK,EAAE;0BAAO,CAAE;0BAAAhE,QAAA,gBAC3ExK,OAAA,CAACvD,UAAU;4BAACgO,OAAO,EAAC,SAAS;4BAAAD,QAAA,GAC1B3H,MAAM,CAAC+C,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC/C,MAAM,CAACgD,OAAO,IAAI,KAAK;0BAAA;4BAAA8E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/C,CAAC,eACb9K,OAAA,CAACvD,UAAU;4BAACgO,OAAO,EAAC,SAAS;4BAACM,EAAE,EAAE;8BAAEmC,UAAU,EAAE,MAAM;8BAAEvB,KAAK,EAAE9I,MAAM,CAAC6D,aAAa,GAAG6B,UAAU,CAAC1G,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;4BAAe,CAAE;4BAAAwI,QAAA,GAC5J3H,MAAM,CAAC6D,aAAa,IAAI,CAAC,EAAC,gBAC7B;0BAAA;4BAAAiE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GAvCDjI,MAAM,CAACZ,SAAS;sBAAA0I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAwCb,CACX,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACT9K,OAAA,CAAC5C,cAAc;oBAAAoN,QAAA,EACZtI,UAAU,CAACD,SAAS,IAAI;kBAAsD;oBAAA0I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAEbzJ,MAAM,CAACyC,MAAM,KAAK,CAAC,IAAI,CAACjD,aAAa,iBACpCb,OAAA,CAAC9C,KAAK;kBAACiP,QAAQ,EAAC,SAAS;kBAACpB,EAAE,EAAE;oBAAEwC,EAAE,EAAE,CAAC;oBAAEkB,QAAQ,EAAE;kBAAS,CAAE;kBAAAjE,QAAA,EAAC;gBAE7D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP9K,OAAA,CAAC9C,KAAK;YAACiP,QAAQ,EAAC,MAAM;YAACpB,EAAE,EAAE;cAAEwC,EAAE,EAAE;YAAE,CAAE;YAAA/C,QAAA,eACnCxK,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBxK,OAAA;gBAAAwK,QAAA,EAAQ;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,iGACvB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,EAGA,CAACjK,aAAa,IAAIgB,QAAQ,CAACI,SAAS,iBACnCjC,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEwC,EAAE,EAAE,CAAC;YAAEvC,CAAC,EAAE,CAAC;YAAEoC,OAAO,EAAE,kBAAkB;YAAEC,YAAY,EAAE,CAAC;YAAEqB,MAAM,EAAE;UAAoB,CAAE;UAAAlE,QAAA,gBAClGxK,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAF,QAAA,EAAC;UAE7C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ,CAAC,MAAM;YACN,MAAMjI,MAAM,GAAGxB,MAAM,CAAC2F,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACxE,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;YACnE,IAAIY,MAAM,EAAE;cACV,oBACE7C,OAAA,CAACpD,IAAI;gBAACsO,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAX,QAAA,gBACzBxK,OAAA,CAACpD,IAAI;kBAACyO,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAAC6B,EAAE,EAAE,CAAE;kBAAA3C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBxK,OAAA;sBAAAwK,QAAA,EAAQ;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACzH,eAAe,CAACR,MAAM,CAACZ,SAAS,CAAC;kBAAA;oBAAA0I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACb9K,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBxK,OAAA;sBAAAwK,QAAA,EAAQ;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACjI,MAAM,CAAC8C,SAAS,IAAI,KAAK;kBAAA;oBAAAgF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACb9K,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBxK,OAAA;sBAAAwK,QAAA,EAAQ;oBAAW;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACjI,MAAM,CAAC+C,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC/C,MAAM,CAACgD,OAAO,IAAI,KAAK;kBAAA;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACP9K,OAAA,CAACpD,IAAI;kBAACyO,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAAC6B,EAAE,EAAE,CAAE;kBAAA3C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBxK,OAAA;sBAAAwK,QAAA,EAAQ;oBAAa;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACjI,MAAM,CAAC8L,YAAY,IAAI,CAAC,EAAC,IAC3D;kBAAA;oBAAAhE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb9K,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBxK,OAAA;sBAAAwK,QAAA,EAAQ;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACjI,MAAM,CAAC6D,aAAa,IAAI,CAAC,EAAC,IAC7D;kBAAA;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb9K,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBxK,OAAA;sBAAAwK,QAAA,EAAQ;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACjI,MAAM,CAAC6C,YAAY,IAAI,KAAK;kBAAA;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAEX;YACA,oBACE9K,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,OAAO;cAACkB,KAAK,EAAC,OAAO;cAAAnB,QAAA,EAAC;YAE1C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAEjB,CAAC,EAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,EAEAzJ,MAAM,CAACyC,MAAM,KAAK,CAAC,IAAI,CAACjD,aAAa,iBACpCb,OAAA,CAAC9C,KAAK;UAACiP,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,EAAC;QAEzC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAM8D,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,IAAIf,YAAY,GAAG,SAAS;IAC5B,IAAIgB,UAAU,GAAG,IAAI;IAErB,IAAIhN,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;MACzC4L,YAAY,GAAG,cAAc;IAC/B,CAAC,MAAM,IAAIhM,QAAQ,CAACI,SAAS,EAAE;MAC7B4L,YAAY,GAAGxK,eAAe,CAACxB,QAAQ,CAACI,SAAS,CAAC;MAClD;MACA4M,UAAU,GAAGxN,MAAM,CAAC2F,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACxE,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;IACnE;;IAEA;IACA,MAAM6H,kBAAkB,GAAGH,2BAA2B,CAACpB,UAAU,CAAC1G,QAAQ,CAACG,YAAY,CAAC,EAAET,YAAY,CAACkH,aAAa,CAAC;IAErH,oBACEzI,OAAA,CAACzD,GAAG;MAAAiO,QAAA,gBACFxK,OAAA,CAACvD,UAAU;QAACgO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb9K,OAAA,CAACxD,KAAK;QAACuO,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBxK,OAAA,CAACvD,UAAU;UAACgO,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGb9K,OAAA,CAACb,eAAe;UACdyD,IAAI,EAAErB,YAAa;UACnB0L,OAAO,EAAE,IAAK;UACdhE,KAAK,EAAC;QAAmB;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAGF9K,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEwC,EAAE,EAAE,CAAC;YAAEvC,CAAC,EAAE,CAAC;YAAEoC,OAAO,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAA7C,QAAA,gBAC5DxK,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEmC,UAAU,EAAE;YAAO,CAAE;YAAA1C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9K,OAAA,CAACpD,IAAI;YAACsO,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBACzBxK,OAAA,CAACpD,IAAI;cAACyO,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAA3C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBxK,OAAA;kBAAAwK,QAAA,EAAQ;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjJ,QAAQ,CAACG,YAAY,EAAC,IACxD;cAAA;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9K,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBxK,OAAA;kBAAAwK,QAAA,EAAQ;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAChB,kBAAkB;cAAA;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACP9K,OAAA,CAACpD,IAAI;cAACyO,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAA3C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBxK,OAAA;kBAAAwK,QAAA,EAAQ;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC+C,YAAY;cAAA;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,EACZ+D,UAAU,iBACT7O,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBxK,OAAA;kBAAAwK,QAAA,EAAQ;gBAAqB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC+D,UAAU,CAACnI,aAAa,EAAC,IACnE;cAAA;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAEL+D,UAAU,IAAItG,UAAU,CAAC1G,QAAQ,CAACG,YAAY,CAAC,GAAGuG,UAAU,CAACsG,UAAU,CAACnI,aAAa,CAAC,IAAI,CAACiC,iBAAiB,iBAC3G3I,OAAA,CAAC9C,KAAK;UAACiP,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,gBACtCxK,OAAA;YAAAwK,QAAA,EAAQ;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,qBAAiB,EAACjJ,QAAQ,CAACG,YAAY,EAAC,4CAA0C,EAAC6M,UAAU,CAACnI,aAAa,EAAC,gDAE1I;QAAA;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,eAED9K,OAAA,CAAC9C,KAAK;UAACiP,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,GAAC,8EAEpC,EAAC3I,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,gFAAgF;QAAA;UAAA0I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3I,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMgE,cAAc,GAAIC,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAOxE,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOoD,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOX,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAO4B,WAAW,CAAC,CAAC;MAAE;MACxB;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;;EAED;EACA,MAAMI,4BAA4B,GAAGA,CAAA,KAAM;IACzCjM,wBAAwB,CAAC,KAAK,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAID;EACA,MAAMgM,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIjM,eAAe,EAAE;MACnBxC,QAAQ,CAAC,mCAAmCJ,UAAU,IAAI4C,eAAe,CAACjB,OAAO,EAAE,CAAC;IACtF;IACAiN,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,wBAAwB,GAAGA,CAAA,KAAM;IACrCF,4BAA4B,CAAC,CAAC;IAC9B;IACAxN,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBR,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMiO,iCAAiC,GAAGA,CAAA,KAAM;IAC9C5M,6BAA6B,CAAC,KAAK,CAAC;IACpCI,uBAAuB,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAK,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMuM,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,MAAM;MAAExM,IAAI;MAAEC;IAAO,CAAC,GAAGH,oBAAoB;IAC7C,IAAI,CAACE,IAAI,IAAI,CAACC,MAAM,EAAE;IAEtB,IAAI;MACFnC,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAM1B,WAAW,CAACqQ,0BAA0B,CAACjP,UAAU,EAAEwC,IAAI,CAACb,OAAO,EAAEc,MAAM,CAACZ,SAAS,CAAC;;MAExF;MACA,MAAMyF,WAAW,GAAG,MAAM1I,WAAW,CAACsQ,WAAW,CAAClP,UAAU,EAAEwC,IAAI,CAACb,OAAO,CAAC;MAC3EP,eAAe,CAACkG,WAAW,CAAC;;MAE5B;MACA5F,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXI,SAAS,EAAEY,MAAM,CAACZ;MACpB,CAAC,CAAC;MAEF5B,SAAS,CAAC,4BAA4BuC,IAAI,CAACb,OAAO,6CAA6Cc,MAAM,CAACZ,SAAS,EAAE,CAAC;MAClHkN,iCAAiC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOnL,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iEAAiE,EAAEA,KAAK,CAAC;MACvF1D,OAAO,CAAC,kEAAkE,IAAI0D,KAAK,CAACqB,MAAM,IAAIrB,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACvI,CAAC,SAAS;MACR1D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6O,uBAAuB,GAAGA,CAAA,KAAM;IACpCJ,iCAAiC,CAAC,CAAC;IACnC;IACArN,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXI,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,oBACEjC,OAAA,CAACzD,GAAG;IAAAiO,QAAA,gBAEFxK,OAAA,CAACxD,KAAK;MAACuO,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,gBACzBxK,OAAA,CAACvD,UAAU;QAACgO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb9K,OAAA,CAACpD,IAAI;QAACsO,SAAS;QAACC,OAAO,EAAE,CAAE;QAACC,UAAU,EAAC,QAAQ;QAAAZ,QAAA,gBAC7CxK,OAAA,CAACpD,IAAI;UAACyO,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAd,QAAA,eACfxK,OAAA,CAACtD,SAAS;YACR6O,SAAS;YACTC,KAAK,EAAC,SAAS;YACff,OAAO,EAAC,UAAU;YAClBzC,KAAK,EAAEvG,WAAY;YACnBgK,QAAQ,EAAG3D,CAAC,IAAKpG,cAAc,CAACoG,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;YAChD0D,WAAW,EAAC;UAAyC;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP9K,OAAA,CAACpD,IAAI;UAACyO,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAd,QAAA,eACfxK,OAAA,CAACrD,MAAM;YACL4O,SAAS;YACTd,OAAO,EAAC,WAAW;YACnBkB,KAAK,EAAC,SAAS;YACfC,OAAO,EAAEjF,oBAAqB;YAC9BkF,QAAQ,EAAElL,WAAW,IAAI,CAACc,WAAW,CAACmF,IAAI,CAAC,CAAE;YAC7CkF,SAAS,EAAEnL,WAAW,gBAAGX,OAAA,CAAC7C,gBAAgB;cAAC4O,IAAI,EAAE;YAAG;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG9K,OAAA,CAAC1B,UAAU;cAAAqM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAN,QAAA,EAC1E;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGP7J,iBAAiB,IAAIF,aAAa,CAAC+C,MAAM,GAAG,CAAC,iBAC5C9D,OAAA,CAACxD,KAAK;MAACuO,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,gBACzBxK,OAAA,CAACvD,UAAU;QAACgO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb9K,OAAA,CAAClC,cAAc;QAAA0M,QAAA,eACbxK,OAAA,CAACrC,KAAK;UAACoO,IAAI,EAAC,OAAO;UAAAvB,QAAA,gBACjBxK,OAAA,CAACjC,SAAS;YAAAyM,QAAA,eACRxK,OAAA,CAAChC,QAAQ;cAAC+M,EAAE,EAAE;gBAAEqC,OAAO,EAAE;cAAU,CAAE;cAAA5C,QAAA,gBACnCxK,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpC9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5B9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ9K,OAAA,CAACpC,SAAS;YAAA4M,QAAA,EACPzJ,aAAa,CAACuL,GAAG,CAAE1J,IAAI,iBACtB5C,OAAA,CAAChC,QAAQ;cAAAwM,QAAA,gBACPxK,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAE5H,IAAI,CAACb;cAAO;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrC9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAE5H,IAAI,CAAC+C,SAAS,IAAI;cAAK;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChD9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,GAAE5H,IAAI,CAACgD,YAAY,IAAI,KAAK,EAAC,KAAG,EAAChD,IAAI,CAACiD,OAAO,IAAI,KAAK;cAAA;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7E9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,GAAC,MAAI,EAAC5H,IAAI,CAACgK,mBAAmB,IAAI,KAAK,eAAC5M,OAAA;kBAAA2K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,OAAG,EAAClI,IAAI,CAACiK,iBAAiB,IAAI,KAAK;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvG9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,GAAE5H,IAAI,CAAC6F,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACtD9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,eACRxK,OAAA,CAAC1C,IAAI;kBACHkO,KAAK,EAAE5I,IAAI,CAACqE,mBAAmB,IAAI,KAAM;kBACzC8E,IAAI,EAAC,OAAO;kBACZJ,KAAK,EAAEhM,kBAAkB,CAACiD,IAAI,CAACqE,mBAAmB,CAAE;kBACpDwD,OAAO,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,eACRxK,OAAA,CAACrD,MAAM;kBACLoP,IAAI,EAAC,OAAO;kBACZtB,OAAO,EAAC,WAAW;kBACnBkB,KAAK,EAAC,SAAS;kBACfC,OAAO,EAAEA,CAAA,KAAMxE,gBAAgB,CAACxE,IAAI,CAAE;kBACtCiJ,QAAQ,EAAEnM,gBAAgB,CAACkD,IAAI,CAAE;kBAAA4H,QAAA,EAClC;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GAxBClI,IAAI,CAACb,OAAO;cAAA4I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CACR,EAGAvJ,YAAY,iBACXvB,OAAA,CAACxD,KAAK;MAACuO,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,gBAClBxK,OAAA,CAACvD,UAAU;QAACgO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb9K,OAAA,CAACzD,GAAG;QAACwO,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEoC,OAAO,EAAE,SAAS;UAAEC,YAAY,EAAE,CAAC;UAAEpC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBAC5DxK,OAAA,CAACvD,UAAU;UAACgO,OAAO,EAAC,WAAW;UAACC,YAAY;UAACK,EAAE,EAAE;YAAEmC,UAAU,EAAE,MAAM;YAAEvB,KAAK,EAAE;UAAe,CAAE;UAAAnB,QAAA,GAAC,oBAC5E,EAACjJ,YAAY,CAACQ,OAAO;QAAA;UAAA4I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACb9K,OAAA,CAACpD,IAAI;UAACsO,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAX,QAAA,gBACzBxK,OAAA,CAACpD,IAAI;YAACyO,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6B,EAAE,EAAE,CAAE;YAAA3C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACxK,OAAA;gBAAAwK,QAAA,EAAQ;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvJ,YAAY,CAACoE,SAAS,IAAI,KAAK;YAAA;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACtG9K,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACxK,OAAA;gBAAAwK,QAAA,EAAQ;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvJ,YAAY,CAACqE,YAAY,IAAI,KAAK,EAAC,KAAG,EAACrE,YAAY,CAACsE,OAAO,IAAI,KAAK;YAAA;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC5I9K,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACxK,OAAA;gBAAAwK,QAAA,EAAQ;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvJ,YAAY,CAACkH,aAAa,IAAI,KAAK,EAAC,IAAE;YAAA;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5G,CAAC,eACP9K,OAAA,CAACpD,IAAI;YAACyO,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6B,EAAE,EAAE,CAAE;YAAA3C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACxK,OAAA;gBAAAwK,QAAA,EAAQ;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvJ,YAAY,CAACqL,mBAAmB,IAAI,KAAK;YAAA;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC1H9K,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACxK,OAAA;gBAAAwK,QAAA,EAAQ;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvJ,YAAY,CAACsL,iBAAiB,IAAI,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACtH9K,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBxK,OAAA;gBAAAwK,QAAA,EAAQ;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvB9K,OAAA,CAAC1C,IAAI;gBACHkO,KAAK,EAAEjK,YAAY,CAAC0F,mBAAmB,IAAI,KAAM;gBACjD8E,IAAI,EAAC,OAAO;gBACZJ,KAAK,EAAEhM,kBAAkB,CAAC4B,YAAY,CAAC0F,mBAAmB,CAAE;gBAC5DwD,OAAO,EAAC,UAAU;gBAClBM,EAAE,EAAE;kBAAE0B,EAAE,EAAE;gBAAE;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN9K,OAAA,CAACpD,IAAI;QAACsO,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAX,QAAA,gBAEzBxK,OAAA,CAACpD,IAAI;UAACyO,IAAI;UAACC,EAAE,EAAE,EAAG;UAAC6B,EAAE,EAAE,CAAE;UAAA3C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEmC,UAAU,EAAE;YAAO,CAAE;YAAA1C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9K,OAAA,CAACtD,SAAS;YACRqP,IAAI,EAAC,OAAO;YACZR,SAAS;YACTC,KAAK,EAAC,cAAc;YACpBf,OAAO,EAAC,UAAU;YAClB1C,IAAI,EAAC,cAAc;YACnByF,IAAI,EAAC,QAAQ;YACbxF,KAAK,EAAEnG,QAAQ,CAACG,YAAa;YAC7ByJ,QAAQ,EAAE5D,gBAAiB;YAC3B7D,KAAK,EAAE,CAAC,CAAC9B,UAAU,CAACF,YAAa;YACjCyL,UAAU,EAAEvL,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;YACjE0L,mBAAmB,EAAE;cACnB3C,EAAE,EAAE;gBAAEY,KAAK,EAAEvJ,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACF+I,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EACD1I,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,iBACpDhC,OAAA,CAAC9C,KAAK;YAACiP,QAAQ,EAAC,SAAS;YAACpB,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,EACrCpI,YAAY,CAACJ;UAAY;YAAA2I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAGP9K,OAAA,CAACpD,IAAI;UAACyO,IAAI;UAACC,EAAE,EAAE,EAAG;UAAC6B,EAAE,EAAE,CAAE;UAAA3C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEmC,UAAU,EAAE;YAAO,CAAE;YAAA1C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZjK,aAAa,gBACZb,OAAA,CAACzD,GAAG;YAACwO,EAAE,EAAE;cAAEiB,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA1B,QAAA,eAC5DxK,OAAA,CAAC7C,gBAAgB;cAAAwN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,gBAEN9K,OAAA,CAACzD,GAAG;YAAAiO,QAAA,gBACFxK,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,WAAW;cAACC,YAAY;cAACK,EAAE,EAAE;gBAAEY,KAAK,EAAE,cAAc;gBAAEuB,UAAU,EAAE,MAAM;gBAAEjC,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,EACnGjJ,YAAY,GAAG,4CAA4C,GAAG;YAAsB;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,eAEb9K,OAAA,CAACnD,WAAW;cAAC0O,SAAS;cAACQ,IAAI,EAAC,OAAO;cAAC/H,KAAK,EAAE,CAAC,CAAC9B,UAAU,CAACD,SAAU;cAAAuI,QAAA,gBAChExK,OAAA,CAAClD,UAAU;gBAACuR,EAAE,EAAC,qBAAqB;gBAAA7D,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClE9K,OAAA,CAACjD,MAAM;gBACLuR,OAAO,EAAC,qBAAqB;gBAC7BD,EAAE,EAAC,eAAe;gBAClBtG,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAEnG,QAAQ,CAACI,SAAU;gBAC1BuJ,KAAK,EAAC,kBAAkB;gBACxBC,QAAQ,EAAE5D,gBAAiB;gBAAA2C,QAAA,gBAE3BxK,OAAA,CAAChD,QAAQ;kBAACgL,KAAK,EAAC,cAAc;kBAAAwC,QAAA,gBAC5BxK,OAAA;oBAAAwK,QAAA,EAAQ;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,+BAC/B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACX9K,OAAA,CAAC/C,OAAO;kBAAA0N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACVzJ,MAAM,CAACyC,MAAM,GAAG,CAAC,gBAChB9D,OAAA,CAACzD,GAAG;kBAACoQ,SAAS,EAAC,IAAI;kBAAC5B,EAAE,EAAE;oBAAEC,CAAC,EAAE,CAAC;oBAAEoC,OAAO,EAAE;kBAAmB,CAAE;kBAAA5C,QAAA,eAC5DxK,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,SAAS;oBAACM,EAAE,EAAE;sBAAEmC,UAAU,EAAE,MAAM;sBAAEvB,KAAK,EAAE;oBAAe,CAAE;oBAAAnB,QAAA,GAC7EnJ,MAAM,CAACyC,MAAM,EAAC,6BACjB;kBAAA;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,GACJ,IAAI,EACPzJ,MAAM,CAACiL,GAAG,CAAEzJ,MAAM,iBACjB7C,OAAA,CAAChD,QAAQ;kBAEPgL,KAAK,EAAEnF,MAAM,CAACZ,SAAU;kBACxB4J,QAAQ,EAAEhJ,MAAM,CAAC6D,aAAa,GAAG6B,UAAU,CAAC1G,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;kBACxE+I,EAAE,EAAE;oBACF,gBAAgB,EAAE;sBAAEqC,OAAO,EAAE;oBAAgB,CAAC;oBAC9C,sBAAsB,EAAE;sBAAEA,OAAO,EAAE;oBAAgB,CAAC;oBACpDA,OAAO,EAAE7L,YAAY,IACdsB,MAAM,CAAC8C,SAAS,KAAKpE,YAAY,CAACoE,SAAS,IAC3CI,MAAM,CAAClD,MAAM,CAAC+C,YAAY,CAAC,KAAKG,MAAM,CAACxE,YAAY,CAACqE,YAAY,CAAC,IACjEG,MAAM,CAAClD,MAAM,CAACgD,OAAO,CAAC,KAAKE,MAAM,CAACxE,YAAY,CAACsE,OAAO,CAAC,GACvD,yBAAyB,GAAG;kBACrC,CAAE;kBAAA2E,QAAA,eAEFxK,OAAA,CAACzD,GAAG;oBAACwO,EAAE,EAAE;sBAAEiB,OAAO,EAAE,MAAM;sBAAEuC,aAAa,EAAE,QAAQ;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBAAAhE,QAAA,gBACnExK,OAAA,CAACzD,GAAG;sBAACwO,EAAE,EAAE;wBAAEiB,OAAO,EAAE,MAAM;wBAAEC,cAAc,EAAE,eAAe;wBAAEb,UAAU,EAAE,QAAQ;wBAAEoD,KAAK,EAAE;sBAAO,CAAE;sBAAAhE,QAAA,gBACjGxK,OAAA,CAACvD,UAAU;wBAACgO,OAAO,EAAC,OAAO;wBAACM,EAAE,EAAE;0BAAEmC,UAAU,EAAE;wBAAO,CAAE;wBAAA1C,QAAA,GACpDnH,eAAe,CAACR,MAAM,CAACZ,SAAS,CAAC,EAAC,KAAG,EAACY,MAAM,CAAC8C,SAAS,IAAI,KAAK;sBAAA;wBAAAgF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC,EACZvJ,YAAY,IACZsB,MAAM,CAAC8C,SAAS,KAAKpE,YAAY,CAACoE,SAAS,IAC3CI,MAAM,CAAClD,MAAM,CAAC+C,YAAY,CAAC,KAAKG,MAAM,CAACxE,YAAY,CAACqE,YAAY,CAAC,IACjEG,MAAM,CAAClD,MAAM,CAACgD,OAAO,CAAC,KAAKE,MAAM,CAACxE,YAAY,CAACsE,OAAO,CAAC,iBACtD7F,OAAA,CAAC1C,IAAI;wBACHyO,IAAI,EAAC,OAAO;wBACZP,KAAK,EAAC,aAAa;wBACnBG,KAAK,EAAC,SAAS;wBACflB,OAAO,EAAC,UAAU;wBAClBM,EAAE,EAAE;0BAAEuC,MAAM,EAAE,EAAE;0BAAEmB,QAAQ,EAAE;wBAAS;sBAAE;wBAAA9D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CACF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACN9K,OAAA,CAACzD,GAAG;sBAACwO,EAAE,EAAE;wBAAEiB,OAAO,EAAE,MAAM;wBAAEC,cAAc,EAAE,eAAe;wBAAEuC,KAAK,EAAE;sBAAO,CAAE;sBAAAhE,QAAA,gBAC3ExK,OAAA,CAACvD,UAAU;wBAACgO,OAAO,EAAC,SAAS;wBAAAD,QAAA,GAC1B3H,MAAM,CAAC+C,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC/C,MAAM,CAACgD,OAAO,IAAI,KAAK;sBAAA;wBAAA8E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC,eACb9K,OAAA,CAACvD,UAAU;wBAACgO,OAAO,EAAC,SAAS;wBAACM,EAAE,EAAE;0BAAEmC,UAAU,EAAE,MAAM;0BAAEvB,KAAK,EAAE9I,MAAM,CAAC6D,aAAa,GAAG6B,UAAU,CAAC1G,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;wBAAe,CAAE;wBAAAwI,QAAA,GAC5J3H,MAAM,CAAC6D,aAAa,IAAI,CAAC,EAAC,gBAC7B;sBAAA;wBAAAiE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAvCDjI,MAAM,CAACZ,SAAS;kBAAA0I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwCb,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACT9K,OAAA,CAAC5C,cAAc;gBAAAoN,QAAA,EACZtI,UAAU,CAACD,SAAS,IAAI;cAAsD;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAEbzJ,MAAM,CAACyC,MAAM,KAAK,CAAC,IAAI,CAACjD,aAAa,iBACpCb,OAAA,CAAC9C,KAAK;cAACiP,QAAQ,EAAC,SAAS;cAACpB,EAAE,EAAE;gBAAEwC,EAAE,EAAE,CAAC;gBAAEkB,QAAQ,EAAE;cAAS,CAAE;cAAAjE,QAAA,EAAC;YAE7D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGA,CAACjK,aAAa,IAAIgB,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,CAAC,MAAM;YACvF,MAAMY,MAAM,GAAGxB,MAAM,CAAC2F,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACxE,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;YACnE,IAAIY,MAAM,EAAE;cACV,oBACE7C,OAAA,CAACzD,GAAG;gBAACwO,EAAE,EAAE;kBAAEwC,EAAE,EAAE,CAAC;kBAAEvC,CAAC,EAAE,CAAC;kBAAEoC,OAAO,EAAE,SAAS;kBAAEC,YAAY,EAAE;gBAAE,CAAE;gBAAA7C,QAAA,gBAC5DxK,OAAA,CAACvD,UAAU;kBAACgO,OAAO,EAAC,OAAO;kBAAAD,QAAA,gBAACxK,OAAA;oBAAAwK,QAAA,EAAQ;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACzH,eAAe,CAACR,MAAM,CAACZ,SAAS,CAAC;gBAAA;kBAAA0I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACrG9K,OAAA,CAACvD,UAAU;kBAACgO,OAAO,EAAC,OAAO;kBAAAD,QAAA,gBAACxK,OAAA;oBAAAwK,QAAA,EAAQ;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACjI,MAAM,CAAC6D,aAAa,IAAI,CAAC,EAAC,IAAE;gBAAA;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtG9K,OAAA,CAACvD,UAAU;kBAACgO,OAAO,EAAC,OAAO;kBAAAD,QAAA,gBACzBxK,OAAA;oBAAAwK,QAAA,EAAQ;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvB9K,OAAA,CAAC1C,IAAI;oBACHkO,KAAK,EAAE3I,MAAM,CAAC6C,YAAY,IAAI,KAAM;oBACpCqG,IAAI,EAAC,OAAO;oBACZJ,KAAK,EAAE/L,iBAAiB,CAACiD,MAAM,CAAC6C,YAAY,CAAE;oBAC9C+E,OAAO,EAAC,UAAU;oBAClBM,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAEV;YACA,OAAO,IAAI;UACb,CAAC,EAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP9K,OAAA,CAACzD,GAAG;QAACwO,EAAE,EAAE;UAAEwC,EAAE,EAAE,CAAC;UAAEvB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAzB,QAAA,gBACnExK,OAAA,CAACrD,MAAM;UACL8N,OAAO,EAAC,UAAU;UAClBkB,KAAK,EAAC,WAAW;UACjBC,OAAO,EAAEA,CAAA,KAAM;YACbpK,eAAe,CAAC,IAAI,CAAC;YACrBM,WAAW,CAAC;cACVC,OAAO,EAAE,EAAE;cACXC,YAAY,EAAE,EAAE;cAChBC,SAAS,EAAE;YACb,CAAC,CAAC;UACJ,CAAE;UACF6J,SAAS,eAAE9L,OAAA,CAACtB,UAAU;YAAAiM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Be,QAAQ,EAAEpL,OAAQ;UAAA+J,QAAA,EACnB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET9K,OAAA,CAACrD,MAAM;UACL8N,OAAO,EAAC,WAAW;UACnBkB,KAAK,EAAC,SAAS;UACfC,OAAO,EAAE/B,YAAa;UACtB2F,OAAO,eAAExP,OAAA,CAACxB,QAAQ;YAAAmM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBe,QAAQ,EAAEpL,OAAO,IAAI,CAACoB,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,SAAU;UAAAuI,QAAA,EAElE/J,OAAO,gBAAGT,OAAA,CAAC7C,gBAAgB;YAAC4O,IAAI,EAAE;UAAG;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGD9K,OAAA,CAACzC,MAAM;MAACkS,IAAI,EAAE5G,iBAAkB;MAAC6G,OAAO,EAAEA,CAAA,KAAM5G,oBAAoB,CAAC,KAAK,CAAE;MAAC6G,QAAQ,EAAC,IAAI;MAACpE,SAAS;MAAAf,QAAA,gBAClGxK,OAAA,CAACxC,WAAW;QAACuN,EAAE,EAAE;UAAEqC,OAAO,EAAE;QAAgB,CAAE;QAAA5C,QAAA,eAC5CxK,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAEwE,GAAG,EAAE;UAAE,CAAE;UAAApF,QAAA,gBACzDxK,OAAA,CAACpB,WAAW;YAAC+M,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B9K,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAEzB,kBAAkB,CAACE;UAAK;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd9K,OAAA,CAACvC,aAAa;QAAA+M,QAAA,eACZxK,OAAA,CAACvD,UAAU;UAACgO,OAAO,EAAC,OAAO;UAACM,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,EACvCzB,kBAAkB,CAAC3E;QAAO;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChB9K,OAAA,CAACtC,aAAa;QAAA8M,QAAA,gBACZxK,OAAA,CAACrD,MAAM;UAACiP,OAAO,EAAEA,CAAA,KAAM9C,oBAAoB,CAAC,KAAK,CAAE;UAAC6C,KAAK,EAAC,WAAW;UAAClB,OAAO,EAAC,UAAU;UAAAD,QAAA,EAAC;QAEzF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9K,OAAA,CAACrD,MAAM;UACLiP,OAAO,EAAEA,CAAA,KAAM;YACb9C,oBAAoB,CAAC,KAAK,CAAC;YAC3BC,kBAAkB,CAACG,SAAS,CAAC,CAAC;UAChC,CAAE;UACFyC,KAAK,EAAC,SAAS;UACflB,OAAO,EAAC,WAAW;UACnBoF,SAAS;UAAArF,QAAA,EACV;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT9K,OAAA,CAACzC,MAAM;MAACkS,IAAI,EAAE3M,qBAAsB;MAAC4M,OAAO,EAAEV,4BAA6B;MAACW,QAAQ,EAAC,IAAI;MAACpE,SAAS;MAAAf,QAAA,gBACjGxK,OAAA,CAACxC,WAAW;QAACuN,EAAE,EAAE;UAAEqC,OAAO,EAAE;QAAgB,CAAE;QAAA5C,QAAA,eAC5CxK,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAEwE,GAAG,EAAE;UAAE,CAAE;UAAApF,QAAA,gBACzDxK,OAAA,CAACpB,WAAW;YAAC+M,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B9K,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd9K,OAAA,CAACvC,aAAa;QAAA+M,QAAA,EACXxH,eAAe,iBACdhD,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,gBACjBxK,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,OAAO;YAAC0D,SAAS;YAAA3D,QAAA,GAAC,UAC5B,eAAAxK,OAAA;cAAAwK,QAAA,EAASxH,eAAe,CAACjB;YAAO;cAAA4I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,4BAAqB,EAAC9H,eAAe,CAACkE,eAAe,IAAI,CAAC,EAAC,KAC/G;UAAA;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9K,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,OAAO;YAAC0D,SAAS;YAAA3D,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9K,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,OAAO;YAACkC,SAAS,EAAC,IAAI;YAAAnC,QAAA,gBACxCxK,OAAA;cAAAwK,QAAA,EAAI;YAAsC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C9K,OAAA;cAAAwK,QAAA,EAAI;YAAyB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC9K,OAAA;cAAAwK,QAAA,EAAI;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChB9K,OAAA,CAACtC,aAAa;QAACqN,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEiB,cAAc,EAAE;QAAgB,CAAE;QAAAzB,QAAA,gBAC3DxK,OAAA,CAACrD,MAAM;UAACiP,OAAO,EAAEoD,4BAA6B;UAACrD,KAAK,EAAC,WAAW;UAAAnB,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9K,OAAA,CAACzD,GAAG;UAAAiO,QAAA,gBACFxK,OAAA,CAACrD,MAAM;YAACiP,OAAO,EAAEsD,wBAAyB;YAACvD,KAAK,EAAC,SAAS;YAACZ,EAAE,EAAE;cAAE+E,EAAE,EAAE;YAAE,CAAE;YAAAtF,QAAA,EAAC;UAE1E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9K,OAAA,CAACrD,MAAM;YAACiP,OAAO,EAAEqD,gBAAiB;YAACxE,OAAO,EAAC,WAAW;YAACkB,KAAK,EAAC,SAAS;YAAAnB,QAAA,EAAC;UAEvE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT9K,OAAA,CAACd,sBAAsB;MACrBuQ,IAAI,EAAEnN,0BAA2B;MACjCoN,OAAO,EAAEP,iCAAkC;MAC3CvM,IAAI,EAAEF,oBAAoB,CAACE,IAAK;MAChCC,MAAM,EAAEH,oBAAoB,CAACG,MAAO;MACpCkN,YAAY,EAAEX,2BAA4B;MAC1CY,mBAAmB,EAAET;IAAwB;MAAA5E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eAGF9K,OAAA,CAACzC,MAAM;MACLkS,IAAI,EAAEvM,qBAAsB;MAC5BwM,OAAO,EAAEA,CAAA,KAAMvM,wBAAwB,CAAC,KAAK,CAAE;MAC/CwM,QAAQ,EAAC,IAAI;MACbpE,SAAS;MAAAf,QAAA,gBAETxK,OAAA,CAACxC,WAAW;QAAAgN,QAAA,eACVxK,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAEwE,GAAG,EAAE;UAAE,CAAE;UAAApF,QAAA,gBACzDxK,OAAA,CAAClB,QAAQ;YAAC6M,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5B9K,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd9K,OAAA,CAACvC,aAAa;QAAA+M,QAAA,eACZxK,OAAA,CAACb,eAAe;UAACyD,IAAI,EAAErB;QAAa;UAAAoJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAChB9K,OAAA,CAACtC,aAAa;QAAA8M,QAAA,eACZxK,OAAA,CAACrD,MAAM;UAACiP,OAAO,EAAEA,CAAA,KAAMzI,wBAAwB,CAAC,KAAK,CAAE;UAACwI,KAAK,EAAC,SAAS;UAAAnB,QAAA,EAAC;QAExE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACvK,EAAA,CAv9DIJ,kBAAkB;EAAA,QACLpB,WAAW;AAAA;AAAAkR,EAAA,GADxB9P,kBAAkB;AAy9DxB,eAAeA,kBAAkB;AAAC,IAAA8P,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}