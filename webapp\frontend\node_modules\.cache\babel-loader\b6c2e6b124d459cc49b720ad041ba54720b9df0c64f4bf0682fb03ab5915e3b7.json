{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isFirstDayOfMonth} function options.\n */\n\n/**\n * @name isFirstDayOfMonth\n * @category Month Helpers\n * @summary Is the given date the first day of a month?\n *\n * @description\n * Is the given date the first day of a month?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is the first day of a month\n *\n * @example\n * // Is 1 September 2014 the first day of a month?\n * const result = isFirstDayOfMonth(new Date(2014, 8, 1))\n * //=> true\n */\nexport function isFirstDayOfMonth(date, options) {\n  return toDate(date, options?.in).getDate() === 1;\n}\n\n// Fallback for modularized imports:\nexport default isFirstDayOfMonth;", "map": {"version": 3, "names": ["toDate", "isFirstDayOfMonth", "date", "options", "in", "getDate"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/isFirstDayOfMonth.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isFirstDayOfMonth} function options.\n */\n\n/**\n * @name isFirstDayOfMonth\n * @category Month Helpers\n * @summary Is the given date the first day of a month?\n *\n * @description\n * Is the given date the first day of a month?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is the first day of a month\n *\n * @example\n * // Is 1 September 2014 the first day of a month?\n * const result = isFirstDayOfMonth(new Date(2014, 8, 1))\n * //=> true\n */\nexport function isFirstDayOfMonth(date, options) {\n  return toDate(date, options?.in).getDate() === 1;\n}\n\n// Fallback for modularized imports:\nexport default isFirstDayOfMonth;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC/C,OAAOH,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEC,EAAE,CAAC,CAACC,OAAO,CAAC,CAAC,KAAK,CAAC;AAClD;;AAEA;AACA,eAAeJ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}