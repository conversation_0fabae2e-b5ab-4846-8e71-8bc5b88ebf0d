{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\nconst eraValues = {\n  narrow: [\"p.n.e.\", \"n.e.\"],\n  abbreviated: [\"p.n.e.\", \"n.e.\"],\n  wide: [\"przed naszą erą\", \"naszej ery\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"I kw.\", \"II kw.\", \"III kw.\", \"IV kw.\"],\n  wide: [\"I kwartał\", \"II kwartał\", \"III kwartał\", \"IV kwartał\"]\n};\nconst monthValues = {\n  narrow: [\"S\", \"L\", \"M\", \"K\", \"M\", \"C\", \"L\", \"S\", \"W\", \"P\", \"L\", \"G\"],\n  abbreviated: [\"sty\", \"lut\", \"mar\", \"kwi\", \"maj\", \"cze\", \"lip\", \"sie\", \"wrz\", \"paź\", \"lis\", \"gru\"],\n  wide: [\"styczeń\", \"luty\", \"marzec\", \"kwiec<PERSON><PERSON>\", \"maj\", \"czerwiec\", \"lipiec\", \"sierpie<PERSON>\", \"wrzesień\", \"październik\", \"listopad\", \"grudzień\"]\n};\nconst monthFormattingValues = {\n  narrow: [\"s\", \"l\", \"m\", \"k\", \"m\", \"c\", \"l\", \"s\", \"w\", \"p\", \"l\", \"g\"],\n  abbreviated: [\"sty\", \"lut\", \"mar\", \"kwi\", \"maj\", \"cze\", \"lip\", \"sie\", \"wrz\", \"paź\", \"lis\", \"gru\"],\n  wide: [\"stycznia\", \"lutego\", \"marca\", \"kwietnia\", \"maja\", \"czerwca\", \"lipca\", \"sierpnia\", \"września\", \"października\", \"listopada\", \"grudnia\"]\n};\nconst dayValues = {\n  narrow: [\"N\", \"P\", \"W\", \"Ś\", \"C\", \"P\", \"S\"],\n  short: [\"nie\", \"pon\", \"wto\", \"śro\", \"czw\", \"pią\", \"sob\"],\n  abbreviated: [\"niedz.\", \"pon.\", \"wt.\", \"śr.\", \"czw.\", \"pt.\", \"sob.\"],\n  wide: [\"niedziela\", \"poniedziałek\", \"wtorek\", \"środa\", \"czwartek\", \"piątek\", \"sobota\"]\n};\nconst dayFormattingValues = {\n  narrow: [\"n\", \"p\", \"w\", \"ś\", \"c\", \"p\", \"s\"],\n  short: [\"nie\", \"pon\", \"wto\", \"śro\", \"czw\", \"pią\", \"sob\"],\n  abbreviated: [\"niedz.\", \"pon.\", \"wt.\", \"śr.\", \"czw.\", \"pt.\", \"sob.\"],\n  wide: [\"niedziela\", \"poniedziałek\", \"wtorek\", \"środa\", \"czwartek\", \"piątek\", \"sobota\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"półn.\",\n    noon: \"poł\",\n    morning: \"rano\",\n    afternoon: \"popoł.\",\n    evening: \"wiecz.\",\n    night: \"noc\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"północ\",\n    noon: \"południe\",\n    morning: \"rano\",\n    afternoon: \"popołudnie\",\n    evening: \"wieczór\",\n    night: \"noc\"\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"północ\",\n    noon: \"południe\",\n    morning: \"rano\",\n    afternoon: \"popołudnie\",\n    evening: \"wieczór\",\n    night: \"noc\"\n  }\n};\nconst dayPeriodFormattingValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"o półn.\",\n    noon: \"w poł.\",\n    morning: \"rano\",\n    afternoon: \"po poł.\",\n    evening: \"wiecz.\",\n    night: \"w nocy\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"o północy\",\n    noon: \"w południe\",\n    morning: \"rano\",\n    afternoon: \"po południu\",\n    evening: \"wieczorem\",\n    night: \"w nocy\"\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"o północy\",\n    noon: \"w południe\",\n    morning: \"rano\",\n    afternoon: \"po południu\",\n    evening: \"wieczorem\",\n    night: \"w nocy\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: monthFormattingValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n    formattingValues: dayFormattingValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: dayPeriodFormattingValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "monthFormattingValues", "dayV<PERSON><PERSON>", "short", "dayFormattingValues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "dayPeriodFormattingValues", "ordinalNumber", "dirtyNumber", "_options", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/pl/_lib/localize.mjs"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\nconst eraValues = {\n  narrow: [\"p.n.e.\", \"n.e.\"],\n  abbreviated: [\"p.n.e.\", \"n.e.\"],\n  wide: [\"przed naszą erą\", \"naszej ery\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"I kw.\", \"II kw.\", \"III kw.\", \"IV kw.\"],\n  wide: [\"I kwartał\", \"II kwartał\", \"III kwartał\", \"IV kwartał\"],\n};\n\nconst monthValues = {\n  narrow: [\"S\", \"L\", \"M\", \"K\", \"M\", \"C\", \"L\", \"S\", \"W\", \"P\", \"L\", \"G\"],\n  abbreviated: [\n    \"sty\",\n    \"lut\",\n    \"mar\",\n    \"kwi\",\n    \"maj\",\n    \"cze\",\n    \"lip\",\n    \"sie\",\n    \"wrz\",\n    \"paź\",\n    \"lis\",\n    \"gru\",\n  ],\n\n  wide: [\n    \"styczeń\",\n    \"luty\",\n    \"marzec\",\n    \"kwi<PERSON><PERSON><PERSON>\",\n    \"maj\",\n    \"czerwiec\",\n    \"lipiec\",\n    \"sierpień\",\n    \"wrzesień\",\n    \"październik\",\n    \"listopad\",\n    \"grudzień\",\n  ],\n};\nconst monthFormattingValues = {\n  narrow: [\"s\", \"l\", \"m\", \"k\", \"m\", \"c\", \"l\", \"s\", \"w\", \"p\", \"l\", \"g\"],\n  abbreviated: [\n    \"sty\",\n    \"lut\",\n    \"mar\",\n    \"kwi\",\n    \"maj\",\n    \"cze\",\n    \"lip\",\n    \"sie\",\n    \"wrz\",\n    \"paź\",\n    \"lis\",\n    \"gru\",\n  ],\n\n  wide: [\n    \"stycznia\",\n    \"lutego\",\n    \"marca\",\n    \"kwietnia\",\n    \"maja\",\n    \"czerwca\",\n    \"lipca\",\n    \"sierpnia\",\n    \"września\",\n    \"października\",\n    \"listopada\",\n    \"grudnia\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"N\", \"P\", \"W\", \"Ś\", \"C\", \"P\", \"S\"],\n  short: [\"nie\", \"pon\", \"wto\", \"śro\", \"czw\", \"pią\", \"sob\"],\n  abbreviated: [\"niedz.\", \"pon.\", \"wt.\", \"śr.\", \"czw.\", \"pt.\", \"sob.\"],\n  wide: [\n    \"niedziela\",\n    \"poniedziałek\",\n    \"wtorek\",\n    \"środa\",\n    \"czwartek\",\n    \"piątek\",\n    \"sobota\",\n  ],\n};\nconst dayFormattingValues = {\n  narrow: [\"n\", \"p\", \"w\", \"ś\", \"c\", \"p\", \"s\"],\n  short: [\"nie\", \"pon\", \"wto\", \"śro\", \"czw\", \"pią\", \"sob\"],\n  abbreviated: [\"niedz.\", \"pon.\", \"wt.\", \"śr.\", \"czw.\", \"pt.\", \"sob.\"],\n  wide: [\n    \"niedziela\",\n    \"poniedziałek\",\n    \"wtorek\",\n    \"środa\",\n    \"czwartek\",\n    \"piątek\",\n    \"sobota\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"półn.\",\n    noon: \"poł\",\n    morning: \"rano\",\n    afternoon: \"popoł.\",\n    evening: \"wiecz.\",\n    night: \"noc\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"północ\",\n    noon: \"południe\",\n    morning: \"rano\",\n    afternoon: \"popołudnie\",\n    evening: \"wieczór\",\n    night: \"noc\",\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"północ\",\n    noon: \"południe\",\n    morning: \"rano\",\n    afternoon: \"popołudnie\",\n    evening: \"wieczór\",\n    night: \"noc\",\n  },\n};\n\nconst dayPeriodFormattingValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"o półn.\",\n    noon: \"w poł.\",\n    morning: \"rano\",\n    afternoon: \"po poł.\",\n    evening: \"wiecz.\",\n    night: \"w nocy\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"o północy\",\n    noon: \"w południe\",\n    morning: \"rano\",\n    afternoon: \"po południu\",\n    evening: \"wieczorem\",\n    night: \"w nocy\",\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"o północy\",\n    noon: \"w południe\",\n    morning: \"rano\",\n    afternoon: \"po południu\",\n    evening: \"wieczorem\",\n    night: \"w nocy\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: monthFormattingValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n    formattingValues: dayFormattingValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: dayPeriodFormattingValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gCAAgC;AAEhE,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;EAC1BC,WAAW,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;EAC/BC,IAAI,EAAE,CAAC,iBAAiB,EAAE,YAAY;AACxC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC;EACrDC,IAAI,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY;AAC/D,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,SAAS,EACT,MAAM,EACN,QAAQ,EACR,UAAU,EACV,KAAK,EACL,UAAU,EACV,QAAQ,EACR,UAAU,EACV,UAAU,EACV,aAAa,EACb,UAAU,EACV,UAAU;AAEd,CAAC;AACD,MAAMG,qBAAqB,GAAG;EAC5BL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,UAAU,EACV,QAAQ,EACR,OAAO,EACP,UAAU,EACV,MAAM,EACN,SAAS,EACT,OAAO,EACP,UAAU,EACV,UAAU,EACV,cAAc,EACd,WAAW,EACX,SAAS;AAEb,CAAC;AAED,MAAMI,SAAS,GAAG;EAChBN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACxDN,WAAW,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;EACpEC,IAAI,EAAE,CACJ,WAAW,EACX,cAAc,EACd,QAAQ,EACR,OAAO,EACP,UAAU,EACV,QAAQ,EACR,QAAQ;AAEZ,CAAC;AACD,MAAMM,mBAAmB,GAAG;EAC1BR,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACxDN,WAAW,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;EACpEC,IAAI,EAAE,CACJ,WAAW,EACX,cAAc,EACd,QAAQ,EACR,OAAO,EACP,UAAU,EACV,QAAQ,EACR,QAAQ;AAEZ,CAAC;AAED,MAAMO,eAAe,GAAG;EACtBT,MAAM,EAAE;IACNU,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDhB,WAAW,EAAE;IACXS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDf,IAAI,EAAE;IACJQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChClB,MAAM,EAAE;IACNU,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDhB,WAAW,EAAE;IACXS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT,CAAC;EACDf,IAAI,EAAE;IACJQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,OAAOC,MAAM,CAACF,WAAW,CAAC;AAC5B,CAAC;AAED,OAAO,MAAMG,QAAQ,GAAG;EACtBJ,aAAa;EAEbK,GAAG,EAAE1B,eAAe,CAAC;IACnB2B,MAAM,EAAE1B,SAAS;IACjB2B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE7B,eAAe,CAAC;IACvB2B,MAAM,EAAEtB,aAAa;IACrBuB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE/B,eAAe,CAAC;IACrB2B,MAAM,EAAErB,WAAW;IACnBsB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAEzB,qBAAqB;IACvC0B,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EAEFC,GAAG,EAAElC,eAAe,CAAC;IACnB2B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAEtB,mBAAmB;IACrCuB,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EAEFE,SAAS,EAAEnC,eAAe,CAAC;IACzB2B,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAEZ,yBAAyB;IAC3Ca,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}