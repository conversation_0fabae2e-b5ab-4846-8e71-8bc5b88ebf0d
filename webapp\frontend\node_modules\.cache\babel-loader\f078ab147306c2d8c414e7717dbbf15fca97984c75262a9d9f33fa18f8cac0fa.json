{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\common\\\\SelectedCantiereDisplay.js\";\nimport React from 'react';\nimport { Box, Typography, Chip } from '@mui/material';\nimport { Construction as ConstructionIcon } from '@mui/icons-material';\n\n/**\n * Componente che mostra il cantiere selezionato\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SelectedCantiereDisplay = () => {\n  // Recupera l'ID e il nome del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Se non c'è un cantiere selezionato, non mostrare nulla\n  if (!selectedCantiereId) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      alignItems: 'center',\n      mx: 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      color: \"textSecondary\",\n      sx: {\n        mr: 1.5,\n        fontSize: '1rem',\n        fontWeight: 500\n      },\n      children: \"Cantiere attivo:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n      icon: /*#__PURE__*/_jsxDEV(ConstructionIcon, {\n        fontSize: \"medium\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 15\n      }, this),\n      label: selectedCantiereName || selectedCantiereId,\n      color: \"secondary\",\n      variant: \"outlined\",\n      size: \"medium\",\n      sx: {\n        fontWeight: 'bold',\n        fontSize: '1rem',\n        padding: '6px 0',\n        height: '36px',\n        '& .MuiChip-label': {\n          padding: '0 14px'\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_c = SelectedCantiereDisplay;\nexport default SelectedCantiereDisplay;\nvar _c;\n$RefreshReg$(_c, \"SelectedCantiereDisplay\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Chip", "Construction", "ConstructionIcon", "jsxDEV", "_jsxDEV", "SelectedCantiereDisplay", "selectedCantiereId", "localStorage", "getItem", "selectedCantiereName", "sx", "display", "alignItems", "mx", "children", "variant", "color", "mr", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "label", "size", "padding", "height", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/common/SelectedCantiereDisplay.js"], "sourcesContent": ["import React from 'react';\nimport { Box, Typography, Chip } from '@mui/material';\nimport { Construction as ConstructionIcon } from '@mui/icons-material';\n\n/**\n * Componente che mostra il cantiere selezionato\n */\nconst SelectedCantiereDisplay = () => {\n  // Recupera l'ID e il nome del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Se non c'è un cantiere selezionato, non mostrare nulla\n  if (!selectedCantiereId) {\n    return null;\n  }\n\n  return (\n    <Box sx={{ display: 'flex', alignItems: 'center', mx: 2 }}>\n      <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mr: 1.5, fontSize: '1rem', fontWeight: 500 }}>\n        Cantiere attivo:\n      </Typography>\n      <Chip\n        icon={<ConstructionIcon fontSize=\"medium\" />}\n        label={selectedCantiereName || selectedCantiereId}\n        color=\"secondary\"\n        variant=\"outlined\"\n        size=\"medium\"\n        sx={{\n          fontWeight: 'bold',\n          fontSize: '1rem',\n          padding: '6px 0',\n          height: '36px',\n          '& .MuiChip-label': { padding: '0 14px' }\n        }}\n      />\n    </Box>\n  );\n};\n\nexport default SelectedCantiereDisplay;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,QAAQ,eAAe;AACrD,SAASC,YAAY,IAAIC,gBAAgB,QAAQ,qBAAqB;;AAEtE;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA;AAGA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;EACpC;EACA,MAAMC,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EACrE,MAAMC,oBAAoB,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEzE;EACA,IAAI,CAACF,kBAAkB,EAAE;IACvB,OAAO,IAAI;EACb;EAEA,oBACEF,OAAA,CAACN,GAAG;IAACY,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE,QAAQ;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACxDV,OAAA,CAACL,UAAU;MAACgB,OAAO,EAAC,OAAO;MAACC,KAAK,EAAC,eAAe;MAACN,EAAE,EAAE;QAAEO,EAAE,EAAE,GAAG;QAAEC,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAI,CAAE;MAAAL,QAAA,EAAC;IAEtG;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbnB,OAAA,CAACJ,IAAI;MACHwB,IAAI,eAAEpB,OAAA,CAACF,gBAAgB;QAACgB,QAAQ,EAAC;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC7CE,KAAK,EAAEhB,oBAAoB,IAAIH,kBAAmB;MAClDU,KAAK,EAAC,WAAW;MACjBD,OAAO,EAAC,UAAU;MAClBW,IAAI,EAAC,QAAQ;MACbhB,EAAE,EAAE;QACFS,UAAU,EAAE,MAAM;QAClBD,QAAQ,EAAE,MAAM;QAChBS,OAAO,EAAE,OAAO;QAChBC,MAAM,EAAE,MAAM;QACd,kBAAkB,EAAE;UAAED,OAAO,EAAE;QAAS;MAC1C;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACM,EAAA,GA/BIxB,uBAAuB;AAiC7B,eAAeA,uBAAuB;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}