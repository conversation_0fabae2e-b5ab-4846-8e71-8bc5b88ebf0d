{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriDialogCompleto.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Typography, Box, Grid, Paper, Alert, CircularProgress, FormControl, InputLabel, Select, MenuItem, Chip, InputAdornment, IconButton, List, ListItem, ListItemButton } from '@mui/material';\nimport { Search as SearchIcon, Cancel as CancelIcon, AddCircleOutline as AddCircleOutlineIcon } from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Dialog completo per inserire i metri posati di un cavo preselezionato\n * Basato su InserisciMetriForm ma adattato per dialog con cavo preselezionato\n * \n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Se il dialogo è aperto\n * @param {Function} props.onClose - Funzione chiamata quando il dialogo si chiude\n * @param {Object} props.cavo - Cavo preselezionato\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n * @param {boolean} props.loading - Indica se il salvataggio è in corso\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriDialogCompleto = ({\n  open = false,\n  onClose = () => {},\n  cavo = null,\n  cantiereId,\n  onSuccess = () => {},\n  onError = () => {},\n  loading = false\n}) => {\n  _s();\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [saving, setSaving] = useState(false);\n\n  // Stati per bobine\n  const [bobine, setBobine] = useState([]);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per la ricerca delle bobine\n  const [searchText, setSearchText] = useState('');\n  const [searchType, setSearchType] = useState('contains');\n\n  // Carica le bobine disponibili\n  const loadBobine = useCallback(async () => {\n    if (!cantiereId || !cavo) return;\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine per cantiere:', cantiereId, 'e cavo:', cavo.id_cavo);\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log('Bobine caricate:', bobineData);\n\n      // Filtra le bobine compatibili con il cavo\n      const bobineCompatibili = bobineData.filter(bobina => {\n        const tipologiaCompatibile = !cavo.tipologia || !bobina.tipologia || cavo.tipologia.toLowerCase() === bobina.tipologia.toLowerCase();\n        const sezioneCompatibile = !cavo.sezione || !bobina.sezione || cavo.sezione.toLowerCase() === bobina.sezione.toLowerCase();\n        return tipologiaCompatibile && sezioneCompatibile && bobina.metri_residui > 0;\n      });\n      console.log('Bobine compatibili filtrate:', bobineCompatibili);\n      setBobine(bobineCompatibili || []);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      setBobine([]);\n    } finally {\n      setBobineLoading(false);\n    }\n  }, [cantiereId, cavo]);\n\n  // Reset quando si apre il dialogo\n  useEffect(() => {\n    if (open && cavo) {\n      var _cavo$metratura_reale;\n      setFormData({\n        metri_posati: ((_cavo$metratura_reale = cavo.metratura_reale) === null || _cavo$metratura_reale === void 0 ? void 0 : _cavo$metratura_reale.toString()) || '',\n        id_bobina: ''\n      });\n      setFormErrors({});\n      setFormWarnings({});\n      setSaving(false);\n\n      // Carica le bobine disponibili\n      loadBobine();\n    }\n  }, [open, cavo, cantiereId, loadBobine]);\n\n  // Gestisce i cambiamenti del form\n  const handleFormChange = event => {\n    const {\n      name,\n      value\n    } = event.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Rimuovi errori quando l'utente inizia a digitare\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n\n    // Validazione in tempo reale per metri posati\n    if (name === 'metri_posati' && value && cavo) {\n      const metri = parseFloat(value);\n      if (!isNaN(metri) && metri > cavo.metri_teorici) {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: `I metri posati (${metri}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n        }));\n      } else {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: ''\n        }));\n      }\n    }\n  };\n\n  // Validazione del form\n  const validateForm = () => {\n    const errors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'I metri posati sono obbligatori';\n    } else {\n      const metri = parseFloat(formData.metri_posati);\n      if (isNaN(metri) || metri < 0) {\n        errors.metri_posati = 'Inserire un valore numerico valido maggiore o uguale a 0';\n      }\n    }\n\n    // Validazione bobina\n    if (!formData.id_bobina || formData.id_bobina === '') {\n      // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n      if (bobine.length === 0 && !bobineLoading) {\n        setFormData(prev => ({\n          ...prev,\n          id_bobina: 'BOBINA_VUOTA'\n        }));\n      } else {\n        errors.id_bobina = 'È necessario selezionare una bobina';\n      }\n    }\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Gestisce il salvataggio\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n\n    // Dichiara le variabili fuori dal try-catch per poterle usare nel catch\n    const metriPosati = parseFloat(formData.metri_posati);\n    let idBobina = formData.id_bobina;\n\n    // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n    if (bobine.length === 0 && !bobineLoading) {\n      idBobina = 'BOBINA_VUOTA';\n    }\n\n    // Assicurati che BOBINA_VUOTA venga passato come stringa\n    if (idBobina === 'BOBINA_VUOTA') {\n      idBobina = 'BOBINA_VUOTA';\n    }\n    try {\n      setSaving(true);\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', cavo.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina);\n\n      // Chiamata API con la funzione originale completa\n      await caviService.updateMetriPosati(cantiereId, cavo.id_cavo, metriPosati, idBobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Successo\n      const successMessage = `Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metriPosati}m`;\n      onSuccess(successMessage);\n\n      // Chiudi il dialog\n      handleClose();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        const successMessage = `Metri posati aggiornati con successo. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n        handleClose();\n        return;\n      }\n\n      // Gestione errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n        errorMessage = error.response.data.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setSaving(false);\n    }\n  };\n  const handleClose = () => {\n    if (!saving && !loading) {\n      setFormErrors({});\n      setFormWarnings({});\n      setFormData({\n        metri_posati: '',\n        id_bobina: ''\n      });\n      onClose();\n    }\n  };\n  const handleKeyPress = event => {\n    if (event.key === 'Enter' && !saving && !loading && formData.metri_posati.trim() && formData.id_bobina) {\n      handleSave();\n    }\n  };\n  if (!cavo) return null;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Dialog, {\n      open: open,\n      onClose: handleClose,\n      maxWidth: \"md\",\n      fullWidth: true,\n      disableEscapeKeyDown: saving || loading,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Inserisci Metri Posati - \", cavo.id_cavo]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: [/*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            mb: 3,\n            bgcolor: 'grey.50'\n          },\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 45\n                }, this), \" \", cavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sezione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 45\n                }, this), \" \", cavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 45\n                }, this), \" \", cavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Da:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 45\n                }, this), \" \", cavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"A:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 45\n                }, this), \" \", cavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Attualmente posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 45\n                }, this), \" \", cavo.metratura_reale || 0, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            autoFocus: true,\n            label: \"Metri Posati\",\n            type: \"number\",\n            fullWidth: true,\n            name: \"metri_posati\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            onKeyPress: handleKeyPress,\n            error: Boolean(formErrors.metri_posati),\n            helperText: formErrors.metri_posati || formWarnings.metri_posati || `Metri teorici: ${cavo.metri_teorici}m`,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            disabled: saving || loading,\n            InputProps: {\n              endAdornment: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"m\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 31\n              }, this)\n            },\n            inputProps: {\n              max: 999999,\n              step: 0.1\n            },\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            error: Boolean(formErrors.id_bobina),\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              name: \"id_bobina\",\n              value: formData.id_bobina,\n              onChange: handleFormChange,\n              label: \"Bobina\",\n              disabled: saving || loading || bobineLoading,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BOBINA_VUOTA\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    label: \"VUOTA\",\n                    size: \"small\",\n                    color: \"warning\",\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    children: \"BOBINA VUOTA (Posa senza bobina specifica)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this), bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: bobina.id_bobina,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    label: bobina.metri_residui ? `${bobina.metri_residui.toFixed(1)}m` : '0m',\n                    size: \"small\",\n                    color: bobina.metri_residui > 0 ? 'success' : 'error',\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    children: [bobina.id_bobina, \" - \", bobina.tipologia || 'N/A', \" \", bobina.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 21\n                }, this)\n              }, bobina.id_bobina, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this), formErrors.id_bobina && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"error\",\n              sx: {\n                mt: 0.5,\n                ml: 1.5\n              },\n              children: formErrors.id_bobina\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this), bobineLoading && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1,\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Caricamento bobine...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 15\n          }, this), !bobineLoading && bobine.length === 0 && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 1\n            },\n            children: \"Nessuna bobina compatibile disponibile. Verr\\xE0 utilizzata BOBINA_VUOTA.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleClose,\n          disabled: saving || loading,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSave,\n          variant: \"contained\",\n          disabled: saving || loading || !formData.metri_posati || !formData.id_bobina,\n          startIcon: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 33\n          }, this) : null,\n          children: saving ? 'Salvando...' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(InserisciMetriDialogCompleto, \"Mzb0Tt5AmTDwUZbcBcVDPJR74sk=\");\n_c = InserisciMetriDialogCompleto;\nexport default InserisciMetriDialogCompleto;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriDialogCompleto\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "Typography", "Box", "Grid", "Paper", "<PERSON><PERSON>", "CircularProgress", "FormControl", "InputLabel", "Select", "MenuItem", "Chip", "InputAdornment", "IconButton", "List", "ListItem", "ListItemButton", "Search", "SearchIcon", "Cancel", "CancelIcon", "AddCircleOutline", "AddCircleOutlineIcon", "caviService", "parcoCaviService", "getReelStateColor", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriDialogCompleto", "open", "onClose", "cavo", "cantiereId", "onSuccess", "onError", "loading", "_s", "formData", "setFormData", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "saving", "setSaving", "bobine", "set<PERSON>ob<PERSON>", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "searchText", "setSearchText", "searchType", "setSearchType", "loadBobine", "console", "log", "id_cavo", "bobine<PERSON><PERSON>", "getBobine", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "filter", "bobina", "tipologiaCompatibile", "tipologia", "toLowerCase", "sezioneCompatibile", "sezione", "metri_residui", "error", "_cavo$metratura_reale", "metratura_reale", "toString", "handleFormChange", "event", "name", "value", "target", "prev", "metri", "parseFloat", "isNaN", "metri_te<PERSON>ci", "validateForm", "errors", "trim", "length", "Object", "keys", "handleSave", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "idBobina", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "handleClose", "_error$response", "_error$response$data", "success", "errorMessage", "response", "data", "detail", "message", "handleKeyPress", "key", "children", "max<PERSON><PERSON><PERSON>", "fullWidth", "disableEscapeKeyDown", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dividers", "sx", "p", "mb", "bgcolor", "container", "spacing", "item", "xs", "md", "variant", "ubicazione_partenza", "ubicazione_arrivo", "autoFocus", "label", "type", "onChange", "onKeyPress", "Boolean", "helperText", "FormHelperTextProps", "color", "disabled", "InputProps", "endAdornment", "inputProps", "max", "step", "display", "alignItems", "gap", "size", "map", "toFixed", "mt", "ml", "severity", "onClick", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/InserisciMetriDialogCompleto.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  TextField,\n  Typography,\n  Box,\n  Grid,\n  Paper,\n  Alert,\n  CircularProgress,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Chip,\n  InputAdornment,\n  IconButton,\n  List,\n  ListItem,\n  ListItemButton\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Cancel as CancelIcon,\n  AddCircleOutline as AddCircleOutlineIcon\n} from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Dialog completo per inserire i metri posati di un cavo preselezionato\n * Basato su InserisciMetriForm ma adattato per dialog con cavo preselezionato\n * \n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Se il dialogo è aperto\n * @param {Function} props.onClose - Funzione chiamata quando il dialogo si chiude\n * @param {Object} props.cavo - Cavo preselezionato\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n * @param {boolean} props.loading - Indica se il salvataggio è in corso\n */\nconst InserisciMetriDialogCompleto = ({\n  open = false,\n  onClose = () => {},\n  cavo = null,\n  cantiereId,\n  onSuccess = () => {},\n  onError = () => {},\n  loading = false\n}) => {\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [saving, setSaving] = useState(false);\n  \n  // Stati per bobine\n  const [bobine, setBobine] = useState([]);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per la ricerca delle bobine\n  const [searchText, setSearchText] = useState('');\n  const [searchType, setSearchType] = useState('contains');\n\n  // Carica le bobine disponibili\n  const loadBobine = useCallback(async () => {\n    if (!cantiereId || !cavo) return;\n\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine per cantiere:', cantiereId, 'e cavo:', cavo.id_cavo);\n\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log('Bobine caricate:', bobineData);\n\n      // Filtra le bobine compatibili con il cavo\n      const bobineCompatibili = bobineData.filter(bobina => {\n        const tipologiaCompatibile = !cavo.tipologia || !bobina.tipologia ||\n          cavo.tipologia.toLowerCase() === bobina.tipologia.toLowerCase();\n        const sezioneCompatibile = !cavo.sezione || !bobina.sezione ||\n          cavo.sezione.toLowerCase() === bobina.sezione.toLowerCase();\n\n        return tipologiaCompatibile && sezioneCompatibile && bobina.metri_residui > 0;\n      });\n\n      console.log('Bobine compatibili filtrate:', bobineCompatibili);\n      setBobine(bobineCompatibili || []);\n\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      setBobine([]);\n    } finally {\n      setBobineLoading(false);\n    }\n  }, [cantiereId, cavo]);\n\n  // Reset quando si apre il dialogo\n  useEffect(() => {\n    if (open && cavo) {\n      setFormData({\n        metri_posati: cavo.metratura_reale?.toString() || '',\n        id_bobina: ''\n      });\n      setFormErrors({});\n      setFormWarnings({});\n      setSaving(false);\n\n      // Carica le bobine disponibili\n      loadBobine();\n    }\n  }, [open, cavo, cantiereId, loadBobine]);\n\n  // Gestisce i cambiamenti del form\n  const handleFormChange = (event) => {\n    const { name, value } = event.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // Rimuovi errori quando l'utente inizia a digitare\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n    \n    // Validazione in tempo reale per metri posati\n    if (name === 'metri_posati' && value && cavo) {\n      const metri = parseFloat(value);\n      if (!isNaN(metri) && metri > cavo.metri_teorici) {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: `I metri posati (${metri}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n        }));\n      } else {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: ''\n        }));\n      }\n    }\n  };\n\n  // Validazione del form\n  const validateForm = () => {\n    const errors = {};\n    \n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'I metri posati sono obbligatori';\n    } else {\n      const metri = parseFloat(formData.metri_posati);\n      if (isNaN(metri) || metri < 0) {\n        errors.metri_posati = 'Inserire un valore numerico valido maggiore o uguale a 0';\n      }\n    }\n    \n    // Validazione bobina\n    if (!formData.id_bobina || formData.id_bobina === '') {\n      // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n      if (bobine.length === 0 && !bobineLoading) {\n        setFormData(prev => ({ ...prev, id_bobina: 'BOBINA_VUOTA' }));\n      } else {\n        errors.id_bobina = 'È necessario selezionare una bobina';\n      }\n    }\n    \n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Gestisce il salvataggio\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n\n    // Dichiara le variabili fuori dal try-catch per poterle usare nel catch\n    const metriPosati = parseFloat(formData.metri_posati);\n    let idBobina = formData.id_bobina;\n\n    // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n    if (bobine.length === 0 && !bobineLoading) {\n      idBobina = 'BOBINA_VUOTA';\n    }\n\n    // Assicurati che BOBINA_VUOTA venga passato come stringa\n    if (idBobina === 'BOBINA_VUOTA') {\n      idBobina = 'BOBINA_VUOTA';\n    }\n\n    try {\n      setSaving(true);\n      \n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', cavo.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina);\n      \n      // Chiamata API con la funzione originale completa\n      await caviService.updateMetriPosati(\n        cantiereId,\n        cavo.id_cavo,\n        metriPosati,\n        idBobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n      \n      // Successo\n      const successMessage = `Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metriPosati}m`;\n      onSuccess(successMessage);\n      \n      // Chiudi il dialog\n      handleClose();\n      \n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n      \n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        const successMessage = `Metri posati aggiornati con successo. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n        handleClose();\n        return;\n      }\n      \n      // Gestione errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if (error.response?.data?.detail) {\n        errorMessage = error.response.data.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      \n      onError(errorMessage);\n      \n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!saving && !loading) {\n      setFormErrors({});\n      setFormWarnings({});\n      setFormData({ metri_posati: '', id_bobina: '' });\n      onClose();\n    }\n  };\n\n  const handleKeyPress = (event) => {\n    if (event.key === 'Enter' && !saving && !loading && formData.metri_posati.trim() && formData.id_bobina) {\n      handleSave();\n    }\n  };\n\n  if (!cavo) return null;\n\n  return (\n    <>\n      <Dialog \n        open={open} \n        onClose={handleClose} \n        maxWidth=\"md\" \n        fullWidth\n        disableEscapeKeyDown={saving || loading}\n      >\n        <DialogTitle>\n          Inserisci Metri Posati - {cavo.id_cavo}\n        </DialogTitle>\n        \n        <DialogContent dividers>\n          {/* Informazioni cavo */}\n          <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {cavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Sezione:</strong> {cavo.sezione || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metri teorici:</strong> {cavo.metri_teorici || 'N/A'} m</Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\"><strong>Da:</strong> {cavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>A:</strong> {cavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Attualmente posati:</strong> {cavo.metratura_reale || 0} m</Typography>\n              </Grid>\n            </Grid>\n          </Paper>\n\n          {/* Campo metri posati */}\n          <Box sx={{ mb: 3 }}>\n            <TextField\n              autoFocus\n              label=\"Metri Posati\"\n              type=\"number\"\n              fullWidth\n              name=\"metri_posati\"\n              value={formData.metri_posati}\n              onChange={handleFormChange}\n              onKeyPress={handleKeyPress}\n              error={Boolean(formErrors.metri_posati)}\n              helperText={formErrors.metri_posati || formWarnings.metri_posati || `Metri teorici: ${cavo.metri_teorici}m`}\n              FormHelperTextProps={{\n                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n              }}\n              disabled={saving || loading}\n              InputProps={{\n                endAdornment: <Typography variant=\"body2\" color=\"text.secondary\">m</Typography>\n              }}\n              inputProps={{\n                max: 999999,\n                step: 0.1\n              }}\n              sx={{ mb: 2 }}\n            />\n          </Box>\n\n          {/* Selezione bobina */}\n          <Box sx={{ mb: 3 }}>\n            <FormControl fullWidth error={Boolean(formErrors.id_bobina)}>\n              <InputLabel>Bobina</InputLabel>\n              <Select\n                name=\"id_bobina\"\n                value={formData.id_bobina}\n                onChange={handleFormChange}\n                label=\"Bobina\"\n                disabled={saving || loading || bobineLoading}\n              >\n                {/* Opzione BOBINA_VUOTA sempre disponibile */}\n                <MenuItem value=\"BOBINA_VUOTA\">\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <Chip label=\"VUOTA\" size=\"small\" color=\"warning\" variant=\"outlined\" />\n                    <Typography>BOBINA VUOTA (Posa senza bobina specifica)</Typography>\n                  </Box>\n                </MenuItem>\n\n                {/* Bobine disponibili */}\n                {bobine.map((bobina) => (\n                  <MenuItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                      <Chip\n                        label={bobina.metri_residui ? `${bobina.metri_residui.toFixed(1)}m` : '0m'}\n                        size=\"small\"\n                        color={bobina.metri_residui > 0 ? 'success' : 'error'}\n                        variant=\"outlined\"\n                      />\n                      <Typography>\n                        {bobina.id_bobina} - {bobina.tipologia || 'N/A'} {bobina.sezione || 'N/A'}\n                      </Typography>\n                    </Box>\n                  </MenuItem>\n                ))}\n              </Select>\n              {formErrors.id_bobina && (\n                <Typography variant=\"caption\" color=\"error\" sx={{ mt: 0.5, ml: 1.5 }}>\n                  {formErrors.id_bobina}\n                </Typography>\n              )}\n            </FormControl>\n\n            {bobineLoading && (\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>\n                <CircularProgress size={16} />\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Caricamento bobine...\n                </Typography>\n              </Box>\n            )}\n\n            {!bobineLoading && bobine.length === 0 && (\n              <Alert severity=\"info\" sx={{ mt: 1 }}>\n                Nessuna bobina compatibile disponibile. Verrà utilizzata BOBINA_VUOTA.\n              </Alert>\n            )}\n          </Box>\n        </DialogContent>\n\n        <DialogActions>\n          <Button\n            onClick={handleClose}\n            disabled={saving || loading}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSave}\n            variant=\"contained\"\n            disabled={saving || loading || !formData.metri_posati || !formData.id_bobina}\n            startIcon={saving ? <CircularProgress size={20} /> : null}\n          >\n            {saving ? 'Salvando...' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </>\n  );\n};\n\nexport default InserisciMetriDialogCompleto;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,gBAAgB,EAChBC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,cAAc,QACT,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,MAAM,IAAIC,UAAU,EACpBC,gBAAgB,IAAIC,oBAAoB,QACnC,qBAAqB;AAC5B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,iBAAiB,QAAQ,wBAAwB;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAaA,MAAMC,4BAA4B,GAAGA,CAAC;EACpCC,IAAI,GAAG,KAAK;EACZC,OAAO,GAAGA,CAAA,KAAM,CAAC,CAAC;EAClBC,IAAI,GAAG,IAAI;EACXC,UAAU;EACVC,SAAS,GAAGA,CAAA,KAAM,CAAC,CAAC;EACpBC,OAAO,GAAGA,CAAA,KAAM,CAAC,CAAC;EAClBC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC;IACvCiD,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACqD,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACuD,MAAM,EAAEC,SAAS,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACA,MAAM,CAACyD,MAAM,EAAEC,SAAS,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC2D,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+D,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,UAAU,CAAC;;EAExD;EACA,MAAMiE,UAAU,GAAG/D,WAAW,CAAC,YAAY;IACzC,IAAI,CAACwC,UAAU,IAAI,CAACD,IAAI,EAAE;IAE1B,IAAI;MACFmB,gBAAgB,CAAC,IAAI,CAAC;MACtBM,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEzB,UAAU,EAAE,SAAS,EAAED,IAAI,CAAC2B,OAAO,CAAC;MAEpF,MAAMC,UAAU,GAAG,MAAMrC,gBAAgB,CAACsC,SAAS,CAAC5B,UAAU,CAAC;MAC/DwB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEE,UAAU,CAAC;;MAE3C;MACA,MAAME,iBAAiB,GAAGF,UAAU,CAACG,MAAM,CAACC,MAAM,IAAI;QACpD,MAAMC,oBAAoB,GAAG,CAACjC,IAAI,CAACkC,SAAS,IAAI,CAACF,MAAM,CAACE,SAAS,IAC/DlC,IAAI,CAACkC,SAAS,CAACC,WAAW,CAAC,CAAC,KAAKH,MAAM,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC;QACjE,MAAMC,kBAAkB,GAAG,CAACpC,IAAI,CAACqC,OAAO,IAAI,CAACL,MAAM,CAACK,OAAO,IACzDrC,IAAI,CAACqC,OAAO,CAACF,WAAW,CAAC,CAAC,KAAKH,MAAM,CAACK,OAAO,CAACF,WAAW,CAAC,CAAC;QAE7D,OAAOF,oBAAoB,IAAIG,kBAAkB,IAAIJ,MAAM,CAACM,aAAa,GAAG,CAAC;MAC/E,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEI,iBAAiB,CAAC;MAC9Db,SAAS,CAACa,iBAAiB,IAAI,EAAE,CAAC;IAEpC,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DtB,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC,EAAE,CAAClB,UAAU,EAAED,IAAI,CAAC,CAAC;;EAEtB;EACAxC,SAAS,CAAC,MAAM;IACd,IAAIsC,IAAI,IAAIE,IAAI,EAAE;MAAA,IAAAwC,qBAAA;MAChBjC,WAAW,CAAC;QACVC,YAAY,EAAE,EAAAgC,qBAAA,GAAAxC,IAAI,CAACyC,eAAe,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,QAAQ,CAAC,CAAC,KAAI,EAAE;QACpDjC,SAAS,EAAE;MACb,CAAC,CAAC;MACFE,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;MACnBE,SAAS,CAAC,KAAK,CAAC;;MAEhB;MACAS,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAAC1B,IAAI,EAAEE,IAAI,EAAEC,UAAU,EAAEuB,UAAU,CAAC,CAAC;;EAExC;EACA,MAAMmB,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,KAAK,CAACG,MAAM;IACpCxC,WAAW,CAACyC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIpC,UAAU,CAACmC,IAAI,CAAC,EAAE;MACpBlC,aAAa,CAACqC,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,CAACH,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,IAAIA,IAAI,KAAK,cAAc,IAAIC,KAAK,IAAI9C,IAAI,EAAE;MAC5C,MAAMiD,KAAK,GAAGC,UAAU,CAACJ,KAAK,CAAC;MAC/B,IAAI,CAACK,KAAK,CAACF,KAAK,CAAC,IAAIA,KAAK,GAAGjD,IAAI,CAACoD,aAAa,EAAE;QAC/CvC,eAAe,CAACmC,IAAI,KAAK;UACvB,GAAGA,IAAI;UACPxC,YAAY,EAAE,mBAAmByC,KAAK,yCAAyCjD,IAAI,CAACoD,aAAa;QACnG,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLvC,eAAe,CAACmC,IAAI,KAAK;UACvB,GAAGA,IAAI;UACPxC,YAAY,EAAE;QAChB,CAAC,CAAC,CAAC;MACL;IACF;EACF,CAAC;;EAED;EACA,MAAM6C,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAChD,QAAQ,CAACE,YAAY,IAAIF,QAAQ,CAACE,YAAY,CAAC+C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjED,MAAM,CAAC9C,YAAY,GAAG,iCAAiC;IACzD,CAAC,MAAM;MACL,MAAMyC,KAAK,GAAGC,UAAU,CAAC5C,QAAQ,CAACE,YAAY,CAAC;MAC/C,IAAI2C,KAAK,CAACF,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;QAC7BK,MAAM,CAAC9C,YAAY,GAAG,0DAA0D;MAClF;IACF;;IAEA;IACA,IAAI,CAACF,QAAQ,CAACG,SAAS,IAAIH,QAAQ,CAACG,SAAS,KAAK,EAAE,EAAE;MACpD;MACA,IAAIO,MAAM,CAACwC,MAAM,KAAK,CAAC,IAAI,CAACtC,aAAa,EAAE;QACzCX,WAAW,CAACyC,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEvC,SAAS,EAAE;QAAe,CAAC,CAAC,CAAC;MAC/D,CAAC,MAAM;QACL6C,MAAM,CAAC7C,SAAS,GAAG,qCAAqC;MAC1D;IACF;IAEAE,aAAa,CAAC2C,MAAM,CAAC;IACrB,OAAOG,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACE,MAAM,KAAK,CAAC;EACzC,CAAC;;EAED;EACA,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;;IAEA;IACA,MAAMO,WAAW,GAAGV,UAAU,CAAC5C,QAAQ,CAACE,YAAY,CAAC;IACrD,IAAIqD,QAAQ,GAAGvD,QAAQ,CAACG,SAAS;;IAEjC;IACA,IAAIO,MAAM,CAACwC,MAAM,KAAK,CAAC,IAAI,CAACtC,aAAa,EAAE;MACzC2C,QAAQ,GAAG,cAAc;IAC3B;;IAEA;IACA,IAAIA,QAAQ,KAAK,cAAc,EAAE;MAC/BA,QAAQ,GAAG,cAAc;IAC3B;IAEA,IAAI;MACF9C,SAAS,CAAC,IAAI,CAAC;MAEfU,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ED,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEzB,UAAU,CAAC;MACxCwB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE1B,IAAI,CAAC2B,OAAO,CAAC;MACvCF,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEkC,WAAW,CAAC;MAC3CnC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEmC,QAAQ,CAAC;;MAErC;MACA,MAAMvE,WAAW,CAACwE,iBAAiB,CACjC7D,UAAU,EACVD,IAAI,CAAC2B,OAAO,EACZiC,WAAW,EACXC,QAAQ,EACR,IAAI,CAAC;MACP,CAAC;;MAED;MACA,MAAME,cAAc,GAAG,oDAAoD/D,IAAI,CAAC2B,OAAO,KAAKiC,WAAW,GAAG;MAC1G1D,SAAS,CAAC6D,cAAc,CAAC;;MAEzB;MACAC,WAAW,CAAC,CAAC;IAEf,CAAC,CAAC,OAAOzB,KAAK,EAAE;MAAA,IAAA0B,eAAA,EAAAC,oBAAA;MACdzC,OAAO,CAACc,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;MAEzE;MACA,IAAIsB,QAAQ,KAAK,cAAc,IAAItB,KAAK,CAAC4B,OAAO,EAAE;QAChD,MAAMJ,cAAc,GAAG,qEAAqE;QAC5F7D,SAAS,CAAC6D,cAAc,CAAC;QACzBC,WAAW,CAAC,CAAC;QACb;MACF;;MAEA;MACA,IAAII,YAAY,GAAG,kDAAkD;MACrE,KAAAH,eAAA,GAAI1B,KAAK,CAAC8B,QAAQ,cAAAJ,eAAA,gBAAAC,oBAAA,GAAdD,eAAA,CAAgBK,IAAI,cAAAJ,oBAAA,eAApBA,oBAAA,CAAsBK,MAAM,EAAE;QAChCH,YAAY,GAAG7B,KAAK,CAAC8B,QAAQ,CAACC,IAAI,CAACC,MAAM;MAC3C,CAAC,MAAM,IAAIhC,KAAK,CAACiC,OAAO,EAAE;QACxBJ,YAAY,GAAG7B,KAAK,CAACiC,OAAO;MAC9B;MAEArE,OAAO,CAACiE,YAAY,CAAC;IAEvB,CAAC,SAAS;MACRrD,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMiD,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAAClD,MAAM,IAAI,CAACV,OAAO,EAAE;MACvBO,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;MACnBN,WAAW,CAAC;QAAEC,YAAY,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAC,CAAC;MAChDV,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,MAAM0E,cAAc,GAAI7B,KAAK,IAAK;IAChC,IAAIA,KAAK,CAAC8B,GAAG,KAAK,OAAO,IAAI,CAAC5D,MAAM,IAAI,CAACV,OAAO,IAAIE,QAAQ,CAACE,YAAY,CAAC+C,IAAI,CAAC,CAAC,IAAIjD,QAAQ,CAACG,SAAS,EAAE;MACtGkD,UAAU,CAAC,CAAC;IACd;EACF,CAAC;EAED,IAAI,CAAC3D,IAAI,EAAE,OAAO,IAAI;EAEtB,oBACEN,OAAA,CAAAE,SAAA;IAAA+E,QAAA,eACEjF,OAAA,CAAChC,MAAM;MACLoC,IAAI,EAAEA,IAAK;MACXC,OAAO,EAAEiE,WAAY;MACrBY,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,oBAAoB,EAAEhE,MAAM,IAAIV,OAAQ;MAAAuE,QAAA,gBAExCjF,OAAA,CAAC/B,WAAW;QAAAgH,QAAA,GAAC,2BACc,EAAC3E,IAAI,CAAC2B,OAAO;MAAA;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eAEdxF,OAAA,CAAC9B,aAAa;QAACuH,QAAQ;QAAAR,QAAA,gBAErBjF,OAAA,CAACvB,KAAK;UAACiH,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEC,EAAE,EAAE,CAAC;YAAEC,OAAO,EAAE;UAAU,CAAE;UAAAZ,QAAA,eAC7CjF,OAAA,CAACxB,IAAI;YAACsH,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAd,QAAA,gBACzBjF,OAAA,CAACxB,IAAI;cAACwH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAjB,QAAA,gBACvBjF,OAAA,CAAC1B,UAAU;gBAAC6H,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAACjF,OAAA;kBAAAiF,QAAA,EAAQ;gBAAU;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClF,IAAI,CAACkC,SAAS,IAAI,KAAK;cAAA;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9FxF,OAAA,CAAC1B,UAAU;gBAAC6H,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAACjF,OAAA;kBAAAiF,QAAA,EAAQ;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClF,IAAI,CAACqC,OAAO,IAAI,KAAK;cAAA;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1FxF,OAAA,CAAC1B,UAAU;gBAAC6H,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAACjF,OAAA;kBAAAiF,QAAA,EAAQ;gBAAc;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClF,IAAI,CAACoD,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpG,CAAC,eACPxF,OAAA,CAACxB,IAAI;cAACwH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAjB,QAAA,gBACvBjF,OAAA,CAAC1B,UAAU;gBAAC6H,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAACjF,OAAA;kBAAAiF,QAAA,EAAQ;gBAAG;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClF,IAAI,CAAC8F,mBAAmB,IAAI,KAAK;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjGxF,OAAA,CAAC1B,UAAU;gBAAC6H,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAACjF,OAAA;kBAAAiF,QAAA,EAAQ;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClF,IAAI,CAAC+F,iBAAiB,IAAI,KAAK;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9FxF,OAAA,CAAC1B,UAAU;gBAAC6H,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAACjF,OAAA;kBAAAiF,QAAA,EAAQ;gBAAmB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClF,IAAI,CAACyC,eAAe,IAAI,CAAC,EAAC,IAAE;cAAA;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGRxF,OAAA,CAACzB,GAAG;UAACmH,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,eACjBjF,OAAA,CAAC3B,SAAS;YACRiI,SAAS;YACTC,KAAK,EAAC,cAAc;YACpBC,IAAI,EAAC,QAAQ;YACbrB,SAAS;YACThC,IAAI,EAAC,cAAc;YACnBC,KAAK,EAAExC,QAAQ,CAACE,YAAa;YAC7B2F,QAAQ,EAAExD,gBAAiB;YAC3ByD,UAAU,EAAE3B,cAAe;YAC3BlC,KAAK,EAAE8D,OAAO,CAAC3F,UAAU,CAACF,YAAY,CAAE;YACxC8F,UAAU,EAAE5F,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAY,IAAI,kBAAkBR,IAAI,CAACoD,aAAa,GAAI;YAC5GmD,mBAAmB,EAAE;cACnBnB,EAAE,EAAE;gBAAEoB,KAAK,EAAE5F,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACFiG,QAAQ,EAAE3F,MAAM,IAAIV,OAAQ;YAC5BsG,UAAU,EAAE;cACVC,YAAY,eAAEjH,OAAA,CAAC1B,UAAU;gBAAC6H,OAAO,EAAC,OAAO;gBAACW,KAAK,EAAC,gBAAgB;gBAAA7B,QAAA,EAAC;cAAC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAChF,CAAE;YACF0B,UAAU,EAAE;cACVC,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE;YACR,CAAE;YACF1B,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNxF,OAAA,CAACzB,GAAG;UAACmH,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,gBACjBjF,OAAA,CAACpB,WAAW;YAACuG,SAAS;YAACtC,KAAK,EAAE8D,OAAO,CAAC3F,UAAU,CAACD,SAAS,CAAE;YAAAkE,QAAA,gBAC1DjF,OAAA,CAACnB,UAAU;cAAAoG,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/BxF,OAAA,CAAClB,MAAM;cACLqE,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAExC,QAAQ,CAACG,SAAU;cAC1B0F,QAAQ,EAAExD,gBAAiB;cAC3BsD,KAAK,EAAC,QAAQ;cACdQ,QAAQ,EAAE3F,MAAM,IAAIV,OAAO,IAAIc,aAAc;cAAAyD,QAAA,gBAG7CjF,OAAA,CAACjB,QAAQ;gBAACqE,KAAK,EAAC,cAAc;gBAAA6B,QAAA,eAC5BjF,OAAA,CAACzB,GAAG;kBAACmH,EAAE,EAAE;oBAAE2B,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEC,GAAG,EAAE;kBAAE,CAAE;kBAAAtC,QAAA,gBACzDjF,OAAA,CAAChB,IAAI;oBAACuH,KAAK,EAAC,OAAO;oBAACiB,IAAI,EAAC,OAAO;oBAACV,KAAK,EAAC,SAAS;oBAACX,OAAO,EAAC;kBAAU;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtExF,OAAA,CAAC1B,UAAU;oBAAA2G,QAAA,EAAC;kBAA0C;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAGVlE,MAAM,CAACmG,GAAG,CAAEnF,MAAM,iBACjBtC,OAAA,CAACjB,QAAQ;gBAAwBqE,KAAK,EAAEd,MAAM,CAACvB,SAAU;gBAAAkE,QAAA,eACvDjF,OAAA,CAACzB,GAAG;kBAACmH,EAAE,EAAE;oBAAE2B,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEC,GAAG,EAAE;kBAAE,CAAE;kBAAAtC,QAAA,gBACzDjF,OAAA,CAAChB,IAAI;oBACHuH,KAAK,EAAEjE,MAAM,CAACM,aAAa,GAAG,GAAGN,MAAM,CAACM,aAAa,CAAC8E,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAK;oBAC3EF,IAAI,EAAC,OAAO;oBACZV,KAAK,EAAExE,MAAM,CAACM,aAAa,GAAG,CAAC,GAAG,SAAS,GAAG,OAAQ;oBACtDuD,OAAO,EAAC;kBAAU;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACFxF,OAAA,CAAC1B,UAAU;oBAAA2G,QAAA,GACR3C,MAAM,CAACvB,SAAS,EAAC,KAAG,EAACuB,MAAM,CAACE,SAAS,IAAI,KAAK,EAAC,GAAC,EAACF,MAAM,CAACK,OAAO,IAAI,KAAK;kBAAA;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAXOlD,MAAM,CAACvB,SAAS;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYrB,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACRxE,UAAU,CAACD,SAAS,iBACnBf,OAAA,CAAC1B,UAAU;cAAC6H,OAAO,EAAC,SAAS;cAACW,KAAK,EAAC,OAAO;cAACpB,EAAE,EAAE;gBAAEiC,EAAE,EAAE,GAAG;gBAAEC,EAAE,EAAE;cAAI,CAAE;cAAA3C,QAAA,EAClEjE,UAAU,CAACD;YAAS;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,EAEbhE,aAAa,iBACZxB,OAAA,CAACzB,GAAG;YAACmH,EAAE,EAAE;cAAE2B,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE,CAAC;cAAEI,EAAE,EAAE;YAAE,CAAE;YAAA1C,QAAA,gBAChEjF,OAAA,CAACrB,gBAAgB;cAAC6I,IAAI,EAAE;YAAG;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BxF,OAAA,CAAC1B,UAAU;cAAC6H,OAAO,EAAC,SAAS;cAACW,KAAK,EAAC,gBAAgB;cAAA7B,QAAA,EAAC;YAErD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN,EAEA,CAAChE,aAAa,IAAIF,MAAM,CAACwC,MAAM,KAAK,CAAC,iBACpC9D,OAAA,CAACtB,KAAK;YAACmJ,QAAQ,EAAC,MAAM;YAACnC,EAAE,EAAE;cAAEiC,EAAE,EAAE;YAAE,CAAE;YAAA1C,QAAA,EAAC;UAEtC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEhBxF,OAAA,CAAC7B,aAAa;QAAA8G,QAAA,gBACZjF,OAAA,CAAC5B,MAAM;UACL0J,OAAO,EAAExD,WAAY;UACrByC,QAAQ,EAAE3F,MAAM,IAAIV,OAAQ;UAAAuE,QAAA,EAC7B;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxF,OAAA,CAAC5B,MAAM;UACL0J,OAAO,EAAE7D,UAAW;UACpBkC,OAAO,EAAC,WAAW;UACnBY,QAAQ,EAAE3F,MAAM,IAAIV,OAAO,IAAI,CAACE,QAAQ,CAACE,YAAY,IAAI,CAACF,QAAQ,CAACG,SAAU;UAC7EgH,SAAS,EAAE3G,MAAM,gBAAGpB,OAAA,CAACrB,gBAAgB;YAAC6I,IAAI,EAAE;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG,IAAK;UAAAP,QAAA,EAEzD7D,MAAM,GAAG,aAAa,GAAG;QAAO;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC,gBACT,CAAC;AAEP,CAAC;AAAC7E,EAAA,CAxWIR,4BAA4B;AAAA6H,EAAA,GAA5B7H,4BAA4B;AA0WlC,eAAeA,4BAA4B;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}