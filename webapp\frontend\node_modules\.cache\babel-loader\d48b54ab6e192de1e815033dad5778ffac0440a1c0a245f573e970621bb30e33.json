{"ast": null, "code": "import { formatDistance } from \"./ckb/_lib/formatDistance.js\";\nimport { formatLong } from \"./ckb/_lib/formatLong.js\";\nimport { formatRelative } from \"./ckb/_lib/formatRelative.js\";\nimport { localize } from \"./ckb/_lib/localize.js\";\nimport { match } from \"./ckb/_lib/match.js\";\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Central Kurdish locale.\n * @language Central Kurdish\n * @iso-639-2 kur\n * <AUTHOR> [@Revan99]{@link https://github.com/Revan99}\n */\nexport const ckb = {\n  code: \"ckb\",\n  formatDistance,\n  formatLong,\n  formatRelative,\n  localize,\n  match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default ckb;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "ckb", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ckb.js"], "sourcesContent": ["import { formatDistance } from \"./ckb/_lib/formatDistance.js\";\nimport { formatLong } from \"./ckb/_lib/formatLong.js\";\nimport { formatRelative } from \"./ckb/_lib/formatRelative.js\";\nimport { localize } from \"./ckb/_lib/localize.js\";\nimport { match } from \"./ckb/_lib/match.js\";\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Central Kurdish locale.\n * @language Central Kurdish\n * @iso-639-2 kur\n * <AUTHOR> [@Revan99]{@link https://github.com/Revan99}\n */\nexport const ckb = {\n  code: \"ckb\",\n  formatDistance,\n  formatLong,\n  formatRelative,\n  localize,\n  match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default ckb;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,KAAK,QAAQ,qBAAqB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,GAAG,GAAG;EACjBC,IAAI,EAAE,KAAK;EACXN,cAAc;EACdC,UAAU;EACVC,cAAc;EACdC,QAAQ;EACRC,KAAK;EACLG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}