{"ast": null, "code": "export { default } from './extractEventHandlers';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/utils/esm/extractEventHandlers/index.js"], "sourcesContent": ["export { default } from './extractEventHandlers';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}