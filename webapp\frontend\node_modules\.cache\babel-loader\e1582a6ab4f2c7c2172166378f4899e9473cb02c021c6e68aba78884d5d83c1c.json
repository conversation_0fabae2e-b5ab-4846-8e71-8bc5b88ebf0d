{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Box, Paper, Typography, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, Divider, Alert, CircularProgress, FormHelperText, IconButton, Chip, Dialog, DialogTitle, DialogContent, DialogActions, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, List, ListItem, ListItemText, ListItemSecondaryAction } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, Cancel as CancelIcon, Warning as WarningIcon, Info as InfoIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stato per la gestione dei passi (mantenuto per compatibilità con funzioni esistenti)\n  const [activeStep, setActiveStep] = useState(0);\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null); // Mantenuto per compatibilità\n  const [incompatibleReelData, setIncompatibleReelData] = useState({\n    cavo: null,\n    bobina: null\n  });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = idBobina => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response || loadError.code === 'ECONNABORTED' || loadError.message && loadError.message.includes('Network Error')) {\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(`${API_URL}/cavi/${cantiereId}`, {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 30000 // 30 secondi\n            });\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Se c'è un cavo selezionato, prova prima a caricare le bobine compatibili\n      if (selectedCavo && selectedCavo.tipologia && selectedCavo.n_conduttori && selectedCavo.sezione) {\n        console.log('Caricamento bobine compatibili per il cavo selezionato...');\n        try {\n          // Gestione speciale per n_conduttori che potrebbe essere nel formato \"X x Y\"\n          let cavoConduttori = String(selectedCavo.n_conduttori || '0');\n          if (cavoConduttori.includes(' x ')) {\n            const parts = cavoConduttori.split(' x ');\n            cavoConduttori = parts[0];\n            console.log(`Formato n_conduttori 'X x Y' rilevato: ${selectedCavo.n_conduttori} -> ${cavoConduttori}`);\n          }\n          const cavoTipologia = String(selectedCavo.tipologia || '');\n          const cavoSezione = String(selectedCavo.sezione || '0');\n          console.log('Parametri per ricerca bobine compatibili:', {\n            tipologia: cavoTipologia,\n            n_conduttori: cavoConduttori,\n            sezione: cavoSezione\n          });\n\n          // Usa l'API per ottenere le bobine compatibili\n          const bobineCompatibili = await parcoCaviService.getBobineCompatibili(cantiereId, cavoTipologia, cavoConduttori, cavoSezione);\n          if (bobineCompatibili && bobineCompatibili.length > 0) {\n            console.log(`Trovate ${bobineCompatibili.length} bobine compatibili via API`);\n            // Ordina le bobine per metri residui (decrescente)\n            bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n            setBobine(bobineCompatibili);\n            setBobineLoading(false);\n            return;\n          } else {\n            console.log('Nessuna bobina compatibile trovata via API, carico tutte le bobine disponibili');\n          }\n        } catch (error) {\n          console.error('Errore nel caricamento delle bobine compatibili:', error);\n          console.log('Fallback al caricamento di tutte le bobine...');\n        }\n      }\n\n      // Se non ci sono bobine compatibili o non c'è un cavo selezionato, carica tutte le bobine\n      console.log('Caricamento di tutte le bobine disponibili...');\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n      // Filtra solo per stato (disponibile o in uso)\n      const bobineUtilizzabili = bobineData.filter(bobina => (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') && bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata');\n      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);\n\n      // Ordina le bobine per metri residui (decrescente)\n      bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n      // Imposta le bobine nel componente\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con pattern: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi che corrispondono al pattern\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi in base all'input (ricerca parziale)\n      const filteredCavi = caviData.filter(cavo => cavo.id_cavo.toLowerCase().includes(cavoIdInput.trim().toLowerCase()));\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti al pattern`);\n\n      // Se c'è una corrispondenza esatta, seleziona direttamente quel cavo\n      const exactMatch = filteredCavi.find(cavo => cavo.id_cavo.toLowerCase() === cavoIdInput.trim().toLowerCase());\n      if (exactMatch) {\n        console.log('Trovata corrispondenza esatta:', exactMatch);\n\n        // Verifica se il cavo è già installato\n        if (exactMatch.stato_installazione === 'Installato' || exactMatch.metratura_reale && exactMatch.metratura_reale > 0) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatch);\n          setAlreadyLaidCavo(exactMatch);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatch.modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatch);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatch);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con pattern \"${cavoIdInput.trim()}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = {\n            ...cavo,\n            modificato_manualmente: 0\n          };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n\n          // Carica le bobine compatibili\n          loadBobine();\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n\n      // Carica le bobine compatibili\n      if (cavo.tipologia && cavo.n_conduttori && cavo.sezione) {\n        console.log(`Caricamento bobine compatibili per il cavo ${cavo.id_cavo}...`);\n        loadBobine();\n      }\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async cavoId => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Gestione speciale per il cambio di bobina\n    if (name === 'id_bobina' && value && value !== 'BOBINA_VUOTA') {\n      // Verifica compatibilità tra cavo e bobina\n      const bobina = bobine.find(b => b.id_bobina === value);\n      if (bobina && selectedCavo) {\n        // Nella nuova configurazione, controlliamo solo tipologia e formazione (sezione)\n        const isCompatible = bobina.tipologia === selectedCavo.tipologia && String(bobina.sezione) === String(selectedCavo.sezione);\n        if (!isCompatible) {\n          console.log('Bobina incompatibile selezionata:', bobina);\n          console.log('Cavo corrente:', selectedCavo);\n          console.log('Confronto conduttori:', {\n            cavoConduttori,\n            bobinaConduttori\n          });\n\n          // Mostra il dialogo di incompatibilità\n          setIncompatibleReelData({\n            cavo: selectedCavo,\n            bobina: bobina\n          });\n          setShowIncompatibleReelDialog(true);\n\n          // Non aggiornare il valore del form finché l'utente non conferma\n          return;\n        }\n      }\n    }\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    } else if (name === 'id_bobina') {\n      // Controllo che sia selezionata una bobina\n      if (!value || value.trim() === '') {\n        error = 'È necessario selezionare una bobina';\n        return false;\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Nota: Lo stato per il dialogo di bobina incompatibile è già dichiarato sopra\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    // Validazione bobina (deve essere selezionata)\n    if (!formData.id_bobina || formData.id_bobina.trim() === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina';\n      isValid = false;\n    }\n    if (isValid) {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n    setActiveStep(prevActiveStep => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep(prevActiveStep => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n\n      // Gestione differenziata per cavi non posati e cavi posati\n      if (!idBobina || idBobina === '') {\n        // È necessario selezionare una bobina, anche BOBINA_VUOTA\n        setFormErrors({\n          ...formErrors,\n          id_bobina: 'È necessario selezionare una bobina'\n        });\n        setLoading(false);\n        return;\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        // Per cavi posati con bobina specifica\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Verifica se è necessario forzare lo stato OVER della bobina\n      forceOver = false;\n\n      // Se si usa BOBINA_VUOTA, imposta sempre forceOver a true\n      if (idBobina === 'BOBINA_VUOTA') {\n        forceOver = true;\n        console.log('Forzando operazione per BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          forceOver = true;\n          console.log(`Forzando stato OVER per la bobina ${idBobina} (metri posati > metri residui)`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        // Anche in questo caso forziamo l'operazione\n        forceOver = true;\n        console.log(`Forzando operazione per metri posati (${metriPosati}) > metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n              await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, forceOver);\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n              if (error.response) {\n                var _error$response$data;\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message;\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, forceOver);\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if (error.response) {\n        var _error$response$data2;\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.detail) || error.message;\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Seleziona un cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 949,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Cerca cavo per ID\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 955,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 9,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavo\",\n              variant: \"outlined\",\n              value: cavoIdInput,\n              onChange: e => setCavoIdInput(e.target.value),\n              placeholder: \"Inserisci l'ID del cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 960,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 959,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              color: \"primary\",\n              onClick: handleSearchCavoById,\n              disabled: caviLoading || !cavoIdInput.trim(),\n              startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 976,\n                columnNumber: 42\n              }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 976,\n                columnNumber: 75\n              }, this),\n              children: \"Cerca\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 970,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 969,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 958,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 954,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Seleziona dalla lista\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 986,\n          columnNumber: 11\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 992,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 991,\n          columnNumber: 13\n        }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Non ci sono cavi disponibili da installare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 995,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            maxHeight: '400px',\n            overflow: 'auto'\n          },\n          children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCavoSelect(cavo),\n              children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1006,\n                    columnNumber: 27\n                  }, this), isCableSpare(cavo) ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"SPARE\",\n                    color: \"error\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1008,\n                    columnNumber: 29\n                  }, this) : isCableInstalled(cavo) ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"Installato\",\n                    color: \"success\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1015,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: getCableStateColor(cavo.stato_installazione),\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1022,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1005,\n                  columnNumber: 25\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [cavo.tipologia || 'N/A', \" - \", cavo.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1033,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1036,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', \" - A: \", cavo.ubicazione_arrivo || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1037,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1040,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A', \" - Metri posati: \", cavo.metratura_reale || '0']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1041,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1003,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  edge: \"end\",\n                  onClick: e => {\n                    e.stopPropagation(); // Prevent triggering the ListItem click\n                    setSelectedCavo(cavo);\n                    setShowCavoDetailsDialog(true);\n                  },\n                  children: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1053,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1048,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1047,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1002,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1057,\n              columnNumber: 19\n            }, this)]\n          }, cavo.id_cavo, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1001,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 999,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 985,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 948,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserisci metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1073,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n        cavo: selectedCavo,\n        compact: true,\n        title: \"Dettagli del cavo selezionato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1077,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 'bold'\n          },\n          children: \"Inserisci i metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1084,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                bgcolor: '#f5f5f5',\n                borderRadius: 1,\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold',\n                  color: 'primary.main'\n                },\n                children: \"Informazioni cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1092,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 1,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium'\n                    },\n                    children: \"Metri teorici:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1097,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1096,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [selectedCavo.metri_teorici || 'N/A', \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1100,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1099,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium'\n                    },\n                    children: \"Stato attuale:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1103,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1102,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: selectedCavo.stato_installazione || 'N/D',\n                    size: \"small\",\n                    color: getCableStateColor(selectedCavo.stato_installazione),\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1106,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1105,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1095,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1091,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1090,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                bgcolor: '#f5f5f5',\n                borderRadius: 1,\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold',\n                  color: 'secondary.main'\n                },\n                children: \"Informazioni bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1119,\n                columnNumber: 17\n              }, this), formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' ? (() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                return bobina ? /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"ID Bobina:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1127,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1126,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: getBobinaNumber(bobina.id_bobina)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1130,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1129,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"Metri residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1133,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1132,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1136,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1135,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1139,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1138,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: bobina.stato_bobina || 'N/D',\n                      size: \"small\",\n                      color: getReelStateColor(bobina.stato_bobina),\n                      variant: \"outlined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1142,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1141,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1125,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Bobina non trovata\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1151,\n                  columnNumber: 21\n                }, this);\n              })() : /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: formData.id_bobina === 'BOBINA_VUOTA' ? \"Utilizzo BOBINA VUOTA (nessuna bobina associata)\" : \"Nessuna bobina selezionata\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1154,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1118,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1089,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Metratura posata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            fullWidth: true,\n            label: \"Metri posati\",\n            variant: \"outlined\",\n            name: \"metri_posati\",\n            type: \"number\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            sx: {\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1168,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1164,\n          columnNumber: 11\n        }, this), formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: formWarnings.metri_posati\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1187,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1193,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1192,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1083,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1072,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = numeroBobina => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = bobina => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = e => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo) {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n            const cavoSezione = String(selectedCavo.sezione || '0');\n            const bobinaTipologia = String(bobinaEsistente.tipologia || '');\n            const bobinaSezione = String(bobinaEsistente.sezione || '0');\n\n            // Log per debug\n            console.log(`Verifica compatibilità bobina ${bobinaEsistente.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              formazione: `${bobinaSezione} === ${cavoSezione}`\n            });\n            if (bobinaTipologia !== cavoTipologia || bobinaSezione !== cavoSezione) {\n              // Mostra il dialogo per bobine incompatibili\n              setIncompatibleReelData({\n                cavo: selectedCavo,\n                bobina: bobinaEsistente\n              });\n              setShowIncompatibleReelDialog(true);\n              return;\n            }\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Associa bobina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1317,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          paragraph: true,\n          children: \"Seleziona una bobina da associare al cavo. \\xC8 necessario associare sempre una bobina, anche utilizzando l'opzione \\\"BOBINA VUOTA\\\" se non si desidera associare una bobina specifica.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1322,\n          columnNumber: 11\n        }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1328,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1327,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: \"Inserimento diretto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                size: \"small\",\n                fullWidth: true,\n                label: \"Numero bobina\",\n                variant: \"outlined\",\n                placeholder: \"Solo il numero (Y)\",\n                helperText: formErrors.id_bobina_input || \"Inserisci solo il numero della bobina\",\n                error: !!formErrors.id_bobina_input,\n                onBlur: handleBobinaNumberInput,\n                sx: {\n                  mb: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1338,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mb: 1\n                },\n                children: [\"ID Bobina: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: formData.id_bobina || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1350,\n                  columnNumber: 32\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1349,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1334,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: \"Selezione dalla lista\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1356,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  gutterBottom: true,\n                  sx: {\n                    color: 'primary.main',\n                    fontWeight: 'bold',\n                    mb: 1\n                  },\n                  children: selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1360,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  size: \"small\",\n                  error: !!formErrors.id_bobina,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    id: \"bobina-select-label\",\n                    children: \"Seleziona bobina\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1365,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    labelId: \"bobina-select-label\",\n                    id: \"bobina-select\",\n                    name: \"id_bobina\",\n                    value: formData.id_bobina,\n                    label: \"Seleziona bobina\",\n                    onChange: handleFormChange,\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"BOBINA_VUOTA\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"BOBINA VUOTA\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1375,\n                        columnNumber: 27\n                      }, this), \" (nessuna bobina associata)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1374,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1377,\n                      columnNumber: 25\n                    }, this), bobine.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                      component: \"li\",\n                      sx: {\n                        p: 1,\n                        bgcolor: 'background.paper'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          color: 'success.main'\n                        },\n                        children: [bobine.length, \" bobine compatibili trovate\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1380,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1379,\n                      columnNumber: 27\n                    }, this) : null, bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: bobina.id_bobina,\n                      disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                      sx: {\n                        '&.Mui-selected': {\n                          bgcolor: 'success.light'\n                        },\n                        '&.Mui-selected:hover': {\n                          bgcolor: 'success.light'\n                        },\n                        bgcolor: selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) && String(bobina.sezione) === String(selectedCavo.sezione) ? 'rgba(76, 175, 80, 0.08)' : 'inherit'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          flexDirection: 'column',\n                          width: '100%'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center',\n                            width: '100%'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"body2\",\n                            sx: {\n                              fontWeight: 'bold'\n                            },\n                            children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A']\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1402,\n                            columnNumber: 33\n                          }, this), selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) && String(bobina.sezione) === String(selectedCavo.sezione) && /*#__PURE__*/_jsxDEV(Chip, {\n                            size: \"small\",\n                            label: \"Compatibile\",\n                            color: \"success\",\n                            variant: \"outlined\",\n                            sx: {\n                              height: 20,\n                              fontSize: '0.6rem'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1409,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1401,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            width: '100%'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"caption\",\n                            children: [bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1419,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"caption\",\n                            sx: {\n                              fontWeight: 'bold',\n                              color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                            },\n                            children: [bobina.metri_residui || 0, \" m disponibili\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1422,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1418,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1400,\n                        columnNumber: 29\n                      }, this)\n                    }, bobina.id_bobina, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1386,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1366,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                    children: formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1430,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1364,\n                  columnNumber: 21\n                }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n                  severity: \"warning\",\n                  sx: {\n                    mt: 2,\n                    fontSize: '0.8rem'\n                  },\n                  children: \"Nessuna bobina compatibile trovata. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1436,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1359,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1355,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1332,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Nota\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1446,\n                columnNumber: 19\n              }, this), \": Se selezioni \\\"BOBINA VUOTA\\\", potrai associare una bobina specifica in un secondo momento.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1445,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1444,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1331,\n          columnNumber: 13\n        }, this), !bobineLoading && formData.id_bobina && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: 'background.paper',\n            borderRadius: 1,\n            border: '1px solid #e0e0e0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Dettagli bobina selezionata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1455,\n            columnNumber: 15\n          }, this), (() => {\n            const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n            if (bobina) {\n              return /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Numero:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1465,\n                      columnNumber: 27\n                    }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1464,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Tipologia:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1468,\n                      columnNumber: 27\n                    }, this), \" \", bobina.tipologia || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1467,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Conduttori:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1471,\n                      columnNumber: 27\n                    }, this), \" \", bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1470,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1463,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri totali:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1476,\n                      columnNumber: 27\n                    }, this), \" \", bobina.metri_totali || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1475,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1479,\n                      columnNumber: 27\n                    }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1478,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1482,\n                      columnNumber: 27\n                    }, this), \" \", bobina.stato_bobina || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1481,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1474,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1462,\n                columnNumber: 21\n              }, this);\n            }\n            return /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"error\",\n              children: \"Bobina non trovata nel database\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1489,\n              columnNumber: 19\n            }, this);\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1454,\n          columnNumber: 13\n        }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: \"Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1498,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1321,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1316,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Conferma inserimento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1527,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Riepilogo dati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1532,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo,\n          compact: true,\n          title: \"Dettagli del cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1537,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Informazioni sull'operazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1545,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1551,\n                  columnNumber: 19\n                }, this), \" \", formData.metri_posati, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1550,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato Installazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1554,\n                  columnNumber: 19\n                }, this), \" \", statoInstallazione]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1553,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1549,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina Associata:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1559,\n                  columnNumber: 19\n                }, this), \" \", numeroBobina]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1558,\n                columnNumber: 17\n              }, this), bobinaInfo && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Residui Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1563,\n                  columnNumber: 21\n                }, this), \" \", bobinaInfo.metri_residui, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1562,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1557,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1548,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1544,\n          columnNumber: 11\n        }, this), bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Attenzione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1572,\n            columnNumber: 15\n          }, this), \" I metri posati (\", formData.metri_posati, \"m) superano i metri residui della bobina (\", bobinaInfo.metri_residui, \"m). Questo porter\\xE0 la bobina in stato OVER.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1571,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 3\n          },\n          children: [\"Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\", formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1577,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1531,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1526,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = step => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      // Seleziona Cavo\n      case 1:\n        return renderStep3();\n      // Associa Bobina\n      case 2:\n        return renderStep2();\n      // Inserisci Metri\n      case 3:\n        return renderStep4();\n      // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReelData({\n      cavo: null,\n      bobina: null\n    });\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    const {\n      cavo,\n      bobina\n    } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per aggiornare il cavo:', {\n        cavo,\n        bobina\n      });\n      onError('Dati mancanti per aggiornare il cavo');\n      return;\n    }\n    try {\n      setLoading(true);\n      console.log(`Aggiornamento caratteristiche del cavo ${cavo.id_cavo} per compatibilità con bobina ${bobina.id_bobina}`);\n      console.log('Dati cavo prima dell\\'aggiornamento:', cavo);\n      console.log('Dati bobina:', bobina);\n\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);\n      console.log('Dati cavo dopo l\\'aggiornamento:', updatedCavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: bobina.id_bobina\n      });\n\n      // Ricarica le bobine per aggiornare l'interfaccia\n      await loadBobine();\n      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate per corrispondere alla bobina ${bobina.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Cerca cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1689,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 9,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"ID Cavo\",\n            variant: \"outlined\",\n            value: cavoIdInput,\n            onChange: e => setCavoIdInput(e.target.value),\n            placeholder: \"Inserisci l'ID del cavo o parte di esso\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1694,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1693,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 3,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleSearchCavoById,\n            disabled: caviLoading || !cavoIdInput.trim(),\n            startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1710,\n              columnNumber: 40\n            }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1710,\n              columnNumber: 73\n            }, this),\n            children: \"Cerca\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1704,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1703,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1692,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1688,\n      columnNumber: 7\n    }, this), showSearchResults && searchResults.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Risultati della ricerca\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1721,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                bgcolor: '#f5f5f5'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1728,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1729,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Formazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1730,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1731,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri Teorici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1732,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1733,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1734,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1727,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1726,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: searchResults.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1740,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1741,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.sezione || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1742,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1743,\n                  columnNumber: 71\n                }, this), \"A: \", cavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1743,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [cavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1744,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: cavo.stato_installazione || 'N/D',\n                  size: \"small\",\n                  color: getCableStateColor(cavo.stato_installazione),\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1746,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1745,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  variant: \"contained\",\n                  color: \"primary\",\n                  onClick: () => handleCavoSelect(cavo),\n                  disabled: isCableInstalled(cavo),\n                  children: \"Seleziona\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1754,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1753,\n                columnNumber: 21\n              }, this)]\n            }, cavo.id_cavo, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1739,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1737,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1725,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1724,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1720,\n      columnNumber: 9\n    }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserimento metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1775,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2,\n          bgcolor: '#f5f5f5',\n          borderRadius: 1,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 'bold',\n            color: 'primary.main'\n          },\n          children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1781,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1786,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1786,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Formazione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1787,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1787,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri teorici:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1788,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.metri_teorici || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1788,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1785,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione partenza:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1791,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1791,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione arrivo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1792,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1792,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Stato:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1794,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: selectedCavo.stato_installazione || 'N/D',\n                size: \"small\",\n                color: getCableStateColor(selectedCavo.stato_installazione),\n                variant: \"outlined\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1795,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1793,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1790,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1784,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1780,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Metratura posata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1810,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            fullWidth: true,\n            label: \"Metri posati\",\n            variant: \"outlined\",\n            name: \"metri_posati\",\n            type: \"number\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            sx: {\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1813,\n            columnNumber: 15\n          }, this), formWarnings.metri_posati && !formErrors.metri_posati && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2\n            },\n            children: formWarnings.metri_posati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1830,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1809,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Associa bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1838,\n            columnNumber: 15\n          }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'center',\n              my: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1843,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1842,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              sx: {\n                color: 'primary.main',\n                fontWeight: 'bold',\n                mb: 1\n              },\n              children: selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1847,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              error: !!formErrors.id_bobina,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"bobina-select-label\",\n                children: \"Seleziona bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1852,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"bobina-select-label\",\n                id: \"bobina-select\",\n                name: \"id_bobina\",\n                value: formData.id_bobina,\n                label: \"Seleziona bobina\",\n                onChange: handleFormChange,\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"BOBINA_VUOTA\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"BOBINA VUOTA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1862,\n                    columnNumber: 25\n                  }, this), \" (nessuna bobina associata)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1861,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1864,\n                  columnNumber: 23\n                }, this), bobine.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"li\",\n                  sx: {\n                    p: 1,\n                    bgcolor: 'background.paper'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      color: 'success.main'\n                    },\n                    children: [bobine.length, \" bobine disponibili\", selectedCavo && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [\" -\", /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: bobine.some(bobina => bobina.tipologia === selectedCavo.tipologia && String(bobina.sezione) === String(selectedCavo.sezione)) ? 'green' : 'orange'\n                        },\n                        children: [bobine.filter(bobina => bobina.tipologia === selectedCavo.tipologia && String(bobina.sezione) === String(selectedCavo.sezione)).length, \" compatibili\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1871,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1867,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1866,\n                  columnNumber: 25\n                }, this) : null, bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: bobina.id_bobina,\n                  disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                  sx: {\n                    '&.Mui-selected': {\n                      bgcolor: 'success.light'\n                    },\n                    '&.Mui-selected:hover': {\n                      bgcolor: 'success.light'\n                    },\n                    bgcolor: selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.sezione) === String(selectedCavo.sezione) ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                    border: selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.sezione) === String(selectedCavo.sezione) ? '1px solid #4caf50' : 'none',\n                    borderRadius: '4px',\n                    margin: '2px 0'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      flexDirection: 'column',\n                      width: '100%'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        width: '100%'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          fontWeight: 'bold'\n                        },\n                        children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1906,\n                        columnNumber: 31\n                      }, this), selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.sezione) === String(selectedCavo.sezione) && /*#__PURE__*/_jsxDEV(Chip, {\n                        size: \"small\",\n                        label: \"Compatibile\",\n                        color: \"success\",\n                        variant: \"outlined\",\n                        sx: {\n                          height: 20,\n                          fontSize: '0.6rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1912,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1905,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        width: '100%'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        children: bobina.sezione || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1922,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                        },\n                        children: [bobina.metri_residui || 0, \" m disponibili\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1925,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1921,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1904,\n                    columnNumber: 27\n                  }, this)\n                }, bobina.id_bobina, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1885,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1853,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                children: formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1933,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1851,\n              columnNumber: 19\n            }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mt: 2,\n                fontSize: '0.8rem'\n              },\n              children: \"Nessuna bobina disponibile trovata. Puoi usare BOBINA VUOTA.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1939,\n              columnNumber: 21\n            }, this), bobine.length > 0 && selectedCavo && !bobine.some(bobina => bobina.tipologia === selectedCavo.tipologia && String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) && String(bobina.sezione) === String(selectedCavo.sezione)) && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              sx: {\n                mt: 2,\n                fontSize: '0.8rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Nessuna bobina compatibile trovata.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1950,\n                columnNumber: 23\n              }, this), \" Hai due opzioni:\", /*#__PURE__*/_jsxDEV(\"ul\", {\n                style: {\n                  margin: '5px 0',\n                  paddingLeft: '20px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Usa \\\"BOBINA VUOTA\\\" (potrai associare una bobina in seguito)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1952,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Seleziona una bobina non compatibile (ti verr\\xE0 chiesto se vuoi aggiornare le caratteristiche del cavo)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1953,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1951,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1949,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1846,\n            columnNumber: 17\n          }, this), !bobineLoading && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && (() => {\n            const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n            if (bobina) {\n              return /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 2,\n                  p: 2,\n                  bgcolor: '#f5f5f5',\n                  borderRadius: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Bobina:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1966,\n                    columnNumber: 51\n                  }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1966,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Metri residui:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1967,\n                    columnNumber: 51\n                  }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1967,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Stato:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1969,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: bobina.stato_bobina || 'N/D',\n                    size: \"small\",\n                    color: getReelStateColor(bobina.stato_bobina),\n                    variant: \"outlined\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1970,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1968,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1965,\n                columnNumber: 21\n              }, this);\n            }\n            return null;\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1837,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1807,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          onClick: () => {\n            setSelectedCavo(null);\n            setFormData({\n              id_cavo: '',\n              metri_posati: '',\n              id_bobina: ''\n            });\n          },\n          startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1998,\n            columnNumber: 26\n          }, this),\n          disabled: loading,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1987,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleSubmit,\n          endIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2008,\n            columnNumber: 24\n          }, this),\n          disabled: loading || !formData.metri_posati || !formData.id_bobina,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2011,\n            columnNumber: 26\n          }, this) : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2004,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1986,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1774,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showConfirmDialog,\n      onClose: () => setShowConfirmDialog(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2021,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: confirmDialogProps.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2022,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2020,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2019,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            mt: 2\n          },\n          children: confirmDialogProps.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2026,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2025,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowConfirmDialog(false),\n          color: \"secondary\",\n          variant: \"outlined\",\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2031,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setShowConfirmDialog(false);\n            confirmDialogProps.onConfirm();\n          },\n          color: \"primary\",\n          variant: \"contained\",\n          autoFocus: true,\n          children: \"Conferma\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2034,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2030,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2018,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showAlreadyLaidDialog,\n      onClose: handleCloseAlreadyLaidDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2052,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavo gi\\xE0 posato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2053,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2051,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2050,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: alreadyLaidCavo && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: alreadyLaidCavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2060,\n              columnNumber: 25\n            }, this), \" risulta gi\\xE0 posato (\", alreadyLaidCavo.metratura_reale || 0, \"m).\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2059,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: \"Puoi scegliere di:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2062,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            component: \"ul\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Modificare la bobina associata al cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2066,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Selezionare un altro cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2067,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Annullare l'operazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2068,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2065,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2058,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2056,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 2,\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAlreadyLaidDialog,\n          color: \"secondary\",\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2074,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSelectAnotherCable,\n            color: \"primary\",\n            sx: {\n              mr: 1\n            },\n            children: \"Seleziona altro cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2078,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleModifyReel,\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Modifica bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2081,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2077,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2073,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2049,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: handleCloseIncompatibleReelDialog,\n      cavo: incompatibleReelData.cavo,\n      bobina: incompatibleReelData.bobina,\n      onUpdateCavo: handleUpdateCavoToMatchReel,\n      onSelectAnotherReel: handleSelectAnotherReel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2089,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showCavoDetailsDialog,\n      onClose: () => setShowCavoDetailsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(InfoIcon, {\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Dettagli Cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2106,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2112,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowCavoDetailsDialog(false),\n          color: \"primary\",\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2115,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2114,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2099,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1686,\n    columnNumber: 5\n  }, this);\n};\n_s(InserisciMetriForm, \"5xQmDMZ4lzfRbP71tmjFjXDgm+0=\", false, function () {\n  return [useNavigate];\n});\n_c = InserisciMetriForm;\nexport default InserisciMetriForm;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Search", "SearchIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "useNavigate", "caviService", "axiosInstance", "IncompatibleReelDialog", "CavoDetailsView", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "parcoCaviService", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "searchResults", "setSearchResults", "showSearchResults", "setShowSearchResults", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "activeStep", "setActiveStep", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReel", "setIncompatibleReel", "incompatibleReelData", "setIncompatibleReelData", "cavo", "bobina", "showAlreadyLaidDialog", "setShowAlreadyLaidDialog", "alreadyLaidCavo", "setAlreadyLaidCavo", "showCavoDetailsDialog", "setShowCavoDetailsDialog", "loadBobine", "getBobinaNumber", "idBobina", "includes", "split", "loadCavi", "console", "log", "caviData", "get<PERSON><PERSON>", "length", "loadError", "error", "isNetworkError", "response", "code", "message", "Promise", "resolve", "setTimeout", "token", "localStorage", "getItem", "API_URL", "defaults", "baseURL", "retryResponse", "get", "headers", "timeout", "data", "retryError", "errorMessage", "detail", "tipologia", "n_conduttori", "sezione", "cavoConduttori", "String", "parts", "cavoTipologia", "cavoSezione", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "getBobineCompatibili", "sort", "a", "b", "metri_residui", "bobine<PERSON><PERSON>", "getBobine", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "stato_bobina", "handleSearchCavoById", "trim", "filteredCavi", "toLowerCase", "exactMatch", "find", "stato_installazione", "metratura_reale", "modificato_manualmente", "handleCavoSelect", "status", "window", "confirm", "reactivateSpare", "then", "updatedCavo", "catch", "cavoId", "handleFormChange", "e", "name", "value", "target", "isCompatible", "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validateField", "warning", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "prev", "notificationShown", "setNotificationShown", "showConfirmDialog", "setShowConfirmDialog", "confirmDialogProps", "setConfirmDialogProps", "title", "onConfirm", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "handleNext", "prevActiveStep", "handleBack", "handleReset", "determineInstallationStatus", "metriTeorici", "handleSubmit", "statoInstallazione", "forceOver", "confirmMessage", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "success", "_error$response$data", "request", "_error$response$data2", "renderStep1", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "container", "spacing", "alignItems", "item", "xs", "fullWidth", "label", "onChange", "placeholder", "color", "onClick", "disabled", "startIcon", "size", "display", "justifyContent", "my", "severity", "maxHeight", "overflow", "map", "button", "primary", "ml", "secondary", "component", "ubicazione_partenza", "ubicazione_arrivo", "edge", "stopPropagation", "renderStep2", "compact", "fontWeight", "md", "bgcolor", "borderRadius", "height", "mt", "type", "helperText", "FormHelperTextProps", "renderStep3", "buildFullBobinaId", "numeroBobina", "hasSufficientMeters", "handleBobinaNumberInput", "id_bobina_input", "idBobinaCompleto", "<PERSON><PERSON><PERSON>", "bobinaTipologia", "bobinaSezione", "formazione", "paragraph", "onBlur", "id", "labelId", "flexDirection", "width", "fontSize", "border", "metri_totali", "renderStep4", "bobinaInfo", "getStepContent", "step", "handleCloseAlreadyLaidDialog", "handleModifyReel", "handleSelectAnotherCable", "handleCloseIncompatibleReelDialog", "handleUpdateCavoToMatchReel", "updateCavoForCompatibility", "getCavoById", "handleSelectAnotherReel", "style", "some", "margin", "paddingLeft", "endIcon", "open", "onClose", "max<PERSON><PERSON><PERSON>", "gap", "autoFocus", "mr", "onUpdateCavo", "onSelectAnotherReel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/InserisciMetriForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst InserisciMetriForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stato per la gestione dei passi (mantenuto per compatibilità con funzioni esistenti)\n  const [activeStep, setActiveStep] = useState(0);\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null); // Mantenuto per compatibilità\n  const [incompatibleReelData, setIncompatibleReelData] = useState({ cavo: null, bobina: null });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina) => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response ||\n            loadError.code === 'ECONNABORTED' ||\n            (loadError.message && loadError.message.includes('Network Error'))) {\n\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(\n              `${API_URL}/cavi/${cantiereId}`,\n              {\n                headers: {\n                  'Authorization': `Bearer ${token}`\n                },\n                timeout: 30000 // 30 secondi\n              }\n            );\n\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Se c'è un cavo selezionato, prova prima a caricare le bobine compatibili\n      if (selectedCavo && selectedCavo.tipologia && selectedCavo.n_conduttori && selectedCavo.sezione) {\n        console.log('Caricamento bobine compatibili per il cavo selezionato...');\n\n        try {\n          // Gestione speciale per n_conduttori che potrebbe essere nel formato \"X x Y\"\n          let cavoConduttori = String(selectedCavo.n_conduttori || '0');\n          if (cavoConduttori.includes(' x ')) {\n            const parts = cavoConduttori.split(' x ');\n            cavoConduttori = parts[0];\n            console.log(`Formato n_conduttori 'X x Y' rilevato: ${selectedCavo.n_conduttori} -> ${cavoConduttori}`);\n          }\n\n          const cavoTipologia = String(selectedCavo.tipologia || '');\n          const cavoSezione = String(selectedCavo.sezione || '0');\n\n          console.log('Parametri per ricerca bobine compatibili:', {\n            tipologia: cavoTipologia,\n            n_conduttori: cavoConduttori,\n            sezione: cavoSezione\n          });\n\n          // Usa l'API per ottenere le bobine compatibili\n          const bobineCompatibili = await parcoCaviService.getBobineCompatibili(\n            cantiereId,\n            cavoTipologia,\n            cavoConduttori,\n            cavoSezione\n          );\n\n          if (bobineCompatibili && bobineCompatibili.length > 0) {\n            console.log(`Trovate ${bobineCompatibili.length} bobine compatibili via API`);\n            // Ordina le bobine per metri residui (decrescente)\n            bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n            setBobine(bobineCompatibili);\n            setBobineLoading(false);\n            return;\n          } else {\n            console.log('Nessuna bobina compatibile trovata via API, carico tutte le bobine disponibili');\n          }\n        } catch (error) {\n          console.error('Errore nel caricamento delle bobine compatibili:', error);\n          console.log('Fallback al caricamento di tutte le bobine...');\n        }\n      }\n\n      // Se non ci sono bobine compatibili o non c'è un cavo selezionato, carica tutte le bobine\n      console.log('Caricamento di tutte le bobine disponibili...');\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n      // Filtra solo per stato (disponibile o in uso)\n      const bobineUtilizzabili = bobineData.filter(bobina =>\n        (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') &&\n        bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata'\n      );\n\n      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);\n\n      // Ordina le bobine per metri residui (decrescente)\n      bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n      // Imposta le bobine nel componente\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con pattern: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi che corrispondono al pattern\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi in base all'input (ricerca parziale)\n      const filteredCavi = caviData.filter(cavo =>\n        cavo.id_cavo.toLowerCase().includes(cavoIdInput.trim().toLowerCase())\n      );\n\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti al pattern`);\n\n      // Se c'è una corrispondenza esatta, seleziona direttamente quel cavo\n      const exactMatch = filteredCavi.find(cavo =>\n        cavo.id_cavo.toLowerCase() === cavoIdInput.trim().toLowerCase()\n      );\n\n      if (exactMatch) {\n        console.log('Trovata corrispondenza esatta:', exactMatch);\n\n        // Verifica se il cavo è già installato\n        if (exactMatch.stato_installazione === 'Installato' || (exactMatch.metratura_reale && exactMatch.metratura_reale > 0)) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatch);\n          setAlreadyLaidCavo(exactMatch);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatch.modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatch);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatch);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con pattern \"${cavoIdInput.trim()}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0)) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = { ...cavo, modificato_manualmente: 0 };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n\n          // Carica le bobine compatibili\n          loadBobine();\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n\n      // Carica le bobine compatibili\n      if (cavo.tipologia && cavo.n_conduttori && cavo.sezione) {\n        console.log(`Caricamento bobine compatibili per il cavo ${cavo.id_cavo}...`);\n        loadBobine();\n      }\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async (cavoId) => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Gestione speciale per il cambio di bobina\n    if (name === 'id_bobina' && value && value !== 'BOBINA_VUOTA') {\n      // Verifica compatibilità tra cavo e bobina\n      const bobina = bobine.find(b => b.id_bobina === value);\n\n      if (bobina && selectedCavo) {\n        // Nella nuova configurazione, controlliamo solo tipologia e formazione (sezione)\n        const isCompatible =\n          bobina.tipologia === selectedCavo.tipologia &&\n          String(bobina.sezione) === String(selectedCavo.sezione);\n\n        if (!isCompatible) {\n          console.log('Bobina incompatibile selezionata:', bobina);\n          console.log('Cavo corrente:', selectedCavo);\n          console.log('Confronto conduttori:', { cavoConduttori, bobinaConduttori });\n\n          // Mostra il dialogo di incompatibilità\n          setIncompatibleReelData({\n            cavo: selectedCavo,\n            bobina: bobina\n          });\n          setShowIncompatibleReelDialog(true);\n\n          // Non aggiornare il valore del form finché l'utente non conferma\n          return;\n        }\n      }\n    }\n\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    } else if (name === 'id_bobina') {\n      // Controllo che sia selezionata una bobina\n      if (!value || value.trim() === '') {\n        error = 'È necessario selezionare una bobina';\n        return false;\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Nota: Lo stato per il dialogo di bobina incompatibile è già dichiarato sopra\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    // Validazione bobina (deve essere selezionata)\n    if (!formData.id_bobina || formData.id_bobina.trim() === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina';\n      isValid = false;\n    }\n\n    if (isValid) {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n\n      // Gestione differenziata per cavi non posati e cavi posati\n      if (!idBobina || idBobina === '') {\n        // È necessario selezionare una bobina, anche BOBINA_VUOTA\n        setFormErrors({\n          ...formErrors,\n          id_bobina: 'È necessario selezionare una bobina'\n        });\n        setLoading(false);\n        return;\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        // Per cavi posati con bobina specifica\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Verifica se è necessario forzare lo stato OVER della bobina\n      forceOver = false;\n\n      // Se si usa BOBINA_VUOTA, imposta sempre forceOver a true\n      if (idBobina === 'BOBINA_VUOTA') {\n        forceOver = true;\n        console.log('Forzando operazione per BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          forceOver = true;\n          console.log(`Forzando stato OVER per la bobina ${idBobina} (metri posati > metri residui)`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        // Anche in questo caso forziamo l'operazione\n        forceOver = true;\n        console.log(`Forzando operazione per metri posati (${metriPosati}) > metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n\n              await caviService.updateMetriPosati(\n                cantiereId,\n                formData.id_cavo,\n                metriPosati,\n                idBobina,\n                forceOver\n              );\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n              if (error.response) {\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = error.response.data?.detail || error.message;\n\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver\n      );\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n      if (error.response) {\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = error.response.data?.detail || error.message;\n\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Seleziona un cavo\n        </Typography>\n\n        {/* Ricerca per ID */}\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Cerca cavo per ID\n          </Typography>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={9}>\n              <TextField\n                fullWidth\n                label=\"ID Cavo\"\n                variant=\"outlined\"\n                value={cavoIdInput}\n                onChange={(e) => setCavoIdInput(e.target.value)}\n                placeholder=\"Inserisci l'ID del cavo\"\n              />\n            </Grid>\n            <Grid item xs={3}>\n              <Button\n                fullWidth\n                variant=\"contained\"\n                color=\"primary\"\n                onClick={handleSearchCavoById}\n                disabled={caviLoading || !cavoIdInput.trim()}\n                startIcon={caviLoading ? <CircularProgress size={20} /> : <SearchIcon />}\n              >\n                Cerca\n              </Button>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Lista cavi */}\n        <Paper sx={{ p: 2 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Seleziona dalla lista\n          </Typography>\n\n          {caviLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : cavi.length === 0 ? (\n            <Alert severity=\"info\">\n              Non ci sono cavi disponibili da installare.\n            </Alert>\n          ) : (\n            <List sx={{ maxHeight: '400px', overflow: 'auto' }}>\n              {cavi.map((cavo) => (\n                <React.Fragment key={cavo.id_cavo}>\n                  <ListItem button onClick={() => handleCavoSelect(cavo)}>\n                    <ListItemText\n                      primary={\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography variant=\"subtitle1\">{cavo.id_cavo}</Typography>\n                          {isCableSpare(cavo) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"SPARE\"\n                              color=\"error\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : isCableInstalled(cavo) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"Installato\"\n                              color=\"success\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : (\n                            <Chip\n                              size=\"small\"\n                              label={cavo.stato_installazione}\n                              color={getCableStateColor(cavo.stato_installazione)}\n                              sx={{ ml: 1 }}\n                            />\n                          )}\n                        </Box>\n                      }\n                      secondary={\n                        <>\n                          <Typography variant=\"body2\" component=\"span\">\n                            {cavo.tipologia || 'N/A'} - {cavo.sezione || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Da: {cavo.ubicazione_partenza || 'N/A'} - A: {cavo.ubicazione_arrivo || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Metri teorici: {cavo.metri_teorici || 'N/A'} - Metri posati: {cavo.metratura_reale || '0'}\n                          </Typography>\n                        </>\n                      }\n                    />\n                    <ListItemSecondaryAction>\n                      <IconButton edge=\"end\" onClick={(e) => {\n                        e.stopPropagation(); // Prevent triggering the ListItem click\n                        setSelectedCavo(cavo);\n                        setShowCavoDetailsDialog(true);\n                      }}>\n                        <InfoIcon />\n                      </IconButton>\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                  <Divider />\n                </React.Fragment>\n              ))}\n            </List>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserisci metri posati\n        </Typography>\n\n        <CavoDetailsView\n          cavo={selectedCavo}\n          compact={true}\n          title=\"Dettagli del cavo selezionato\"\n        />\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom sx={{ fontWeight: 'bold' }}>\n            Inserisci i metri posati\n          </Typography>\n\n          {/* Informazioni sul cavo e sulla bobina in una griglia */}\n          <Grid container spacing={2} sx={{ mb: 3 }}>\n            <Grid item xs={12} md={6}>\n              <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, height: '100%' }}>\n                <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>\n                  Informazioni cavo\n                </Typography>\n                <Grid container spacing={1}>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Metri teorici:</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\">{selectedCavo.metri_teorici || 'N/A'} m</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Stato attuale:</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Chip\n                      label={selectedCavo.stato_installazione || 'N/D'}\n                      size=\"small\"\n                      color={getCableStateColor(selectedCavo.stato_installazione)}\n                      variant=\"outlined\"\n                    />\n                  </Grid>\n                </Grid>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, height: '100%' }}>\n                <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'secondary.main' }}>\n                  Informazioni bobina\n                </Typography>\n                {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' ? (() => {\n                  const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                  return bobina ? (\n                    <Grid container spacing={1}>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>ID Bobina:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\">{getBobinaNumber(bobina.id_bobina)}</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Metri residui:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\">{bobina.metri_residui || 0} m</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Stato:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Chip\n                          label={bobina.stato_bobina || 'N/D'}\n                          size=\"small\"\n                          color={getReelStateColor(bobina.stato_bobina)}\n                          variant=\"outlined\"\n                        />\n                      </Grid>\n                    </Grid>\n                  ) : (\n                    <Typography variant=\"body2\">Bobina non trovata</Typography>\n                  );\n                })() : (\n                  <Typography variant=\"body2\">\n                    {formData.id_bobina === 'BOBINA_VUOTA' ?\n                      \"Utilizzo BOBINA VUOTA (nessuna bobina associata)\" :\n                      \"Nessuna bobina selezionata\"}\n                  </Typography>\n                )}\n              </Box>\n            </Grid>\n          </Grid>\n\n          <Box sx={{ mt: 3, mb: 3 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Metratura posata\n            </Typography>\n            <TextField\n              size=\"small\"\n              fullWidth\n              label=\"Metri posati\"\n              variant=\"outlined\"\n              name=\"metri_posati\"\n              type=\"number\"\n              value={formData.metri_posati}\n              onChange={handleFormChange}\n              error={!!formErrors.metri_posati}\n              helperText={formErrors.metri_posati || formWarnings.metri_posati}\n              FormHelperTextProps={{\n                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n              }}\n              sx={{ mb: 1 }}\n            />\n          </Box>\n\n          {formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mb: 2 }}>\n              {formWarnings.metri_posati}\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\">\n              Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\n            </Typography>\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = (numeroBobina) => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = (bobina) => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = (e) => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo) {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n            const cavoSezione = String(selectedCavo.sezione || '0');\n\n            const bobinaTipologia = String(bobinaEsistente.tipologia || '');\n            const bobinaSezione = String(bobinaEsistente.sezione || '0');\n\n            // Log per debug\n            console.log(`Verifica compatibilità bobina ${bobinaEsistente.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              formazione: `${bobinaSezione} === ${cavoSezione}`\n            });\n\n            if (bobinaTipologia !== cavoTipologia ||\n                bobinaSezione !== cavoSezione) {\n              // Mostra il dialogo per bobine incompatibili\n              setIncompatibleReelData({\n                cavo: selectedCavo,\n                bobina: bobinaEsistente\n              });\n              setShowIncompatibleReelDialog(true);\n              return;\n            }\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Associa bobina\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"body1\" paragraph>\n            Seleziona una bobina da associare al cavo. È necessario associare sempre una bobina, anche utilizzando l'opzione \"BOBINA VUOTA\" se non si desidera associare una bobina specifica.\n          </Typography>\n\n          {bobineLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              <Grid container spacing={3}>\n                {/* Colonna sinistra: Input diretto */}\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                    Inserimento diretto\n                  </Typography>\n                  <TextField\n                    size=\"small\"\n                    fullWidth\n                    label=\"Numero bobina\"\n                    variant=\"outlined\"\n                    placeholder=\"Solo il numero (Y)\"\n                    helperText={formErrors.id_bobina_input || \"Inserisci solo il numero della bobina\"}\n                    error={!!formErrors.id_bobina_input}\n                    onBlur={handleBobinaNumberInput}\n                    sx={{ mb: 1 }}\n                  />\n                  <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                    ID Bobina: <strong>{formData.id_bobina || '-'}</strong>\n                  </Typography>\n                </Grid>\n\n                {/* Colonna destra: Selezione dalla lista */}\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                    Selezione dalla lista\n                  </Typography>\n                  <Box>\n                    <Typography variant=\"subtitle2\" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold', mb: 1 }}>\n                      {selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'}\n                    </Typography>\n\n                    <FormControl fullWidth size=\"small\" error={!!formErrors.id_bobina}>\n                      <InputLabel id=\"bobina-select-label\">Seleziona bobina</InputLabel>\n                      <Select\n                        labelId=\"bobina-select-label\"\n                        id=\"bobina-select\"\n                        name=\"id_bobina\"\n                        value={formData.id_bobina}\n                        label=\"Seleziona bobina\"\n                        onChange={handleFormChange}\n                      >\n                        <MenuItem value=\"BOBINA_VUOTA\">\n                          <strong>BOBINA VUOTA</strong> (nessuna bobina associata)\n                        </MenuItem>\n                        <Divider />\n                        {bobine.length > 0 ? (\n                          <Box component=\"li\" sx={{ p: 1, bgcolor: 'background.paper' }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: 'success.main' }}>\n                              {bobine.length} bobine compatibili trovate\n                            </Typography>\n                          </Box>\n                        ) : null}\n                        {bobine.map((bobina) => (\n                          <MenuItem\n                            key={bobina.id_bobina}\n                            value={bobina.id_bobina}\n                            disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                            sx={{\n                              '&.Mui-selected': { bgcolor: 'success.light' },\n                              '&.Mui-selected:hover': { bgcolor: 'success.light' },\n                              bgcolor: selectedCavo &&\n                                     bobina.tipologia === selectedCavo.tipologia &&\n                                     String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) &&\n                                     String(bobina.sezione) === String(selectedCavo.sezione) ?\n                                     'rgba(76, 175, 80, 0.08)' : 'inherit'\n                            }}\n                          >\n                            <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>\n                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>\n                                <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                                  {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'}\n                                </Typography>\n                                {selectedCavo &&\n                                 bobina.tipologia === selectedCavo.tipologia &&\n                                 String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) &&\n                                 String(bobina.sezione) === String(selectedCavo.sezione) && (\n                                  <Chip\n                                    size=\"small\"\n                                    label=\"Compatibile\"\n                                    color=\"success\"\n                                    variant=\"outlined\"\n                                    sx={{ height: 20, fontSize: '0.6rem' }}\n                                  />\n                                )}\n                              </Box>\n                              <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>\n                                <Typography variant=\"caption\">\n                                  {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                                </Typography>\n                                <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                  {bobina.metri_residui || 0} m disponibili\n                                </Typography>\n                              </Box>\n                            </Box>\n                          </MenuItem>\n                        ))}\n                      </Select>\n                      <FormHelperText>\n                        {formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'}\n                      </FormHelperText>\n                    </FormControl>\n\n                    {bobine.length === 0 && !bobineLoading && (\n                      <Alert severity=\"warning\" sx={{ mt: 2, fontSize: '0.8rem' }}>\n                        Nessuna bobina compatibile trovata. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\n                      </Alert>\n                    )}\n                  </Box>\n                </Grid>\n              </Grid>\n\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                <Typography variant=\"body2\">\n                  <strong>Nota</strong>: Se selezioni \"BOBINA VUOTA\", potrai associare una bobina specifica in un secondo momento.\n                </Typography>\n              </Alert>\n            </Box>\n          )}\n\n          {/* Mostra dettagli della bobina selezionata */}\n          {!bobineLoading && formData.id_bobina && (\n            <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #e0e0e0' }}>\n              <Typography variant=\"subtitle2\" gutterBottom>\n                Dettagli bobina selezionata\n              </Typography>\n              {(() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                if (bobina) {\n                  return (\n                    <Grid container spacing={2}>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"body2\">\n                          <strong>Numero:</strong> {getBobinaNumber(bobina.id_bobina)}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Tipologia:</strong> {bobina.tipologia || 'N/A'}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Conduttori:</strong> {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                        </Typography>\n                      </Grid>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"body2\">\n                          <strong>Metri totali:</strong> {bobina.metri_totali || 0} m\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Metri residui:</strong> {bobina.metri_residui || 0} m\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Stato:</strong> {bobina.stato_bobina || 'N/A'}\n                        </Typography>\n                      </Grid>\n                    </Grid>\n                  );\n                }\n                return (\n                  <Typography variant=\"body2\" color=\"error\">\n                    Bobina non trovata nel database\n                  </Typography>\n                );\n              })()}\n            </Box>\n          )}\n\n          {bobine.length === 0 && !bobineLoading && (\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\n            </Alert>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Conferma inserimento\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Riepilogo dati\n          </Typography>\n\n          {/* Dettagli del cavo */}\n          <CavoDetailsView\n            cavo={selectedCavo}\n            compact={true}\n            title=\"Dettagli del cavo\"\n          />\n\n          {/* Informazioni sull'operazione */}\n          <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Informazioni sull'operazione:\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Metri Posati:</strong> {formData.metri_posati} m\n                </Typography>\n                <Typography variant=\"body2\">\n                  <strong>Stato Installazione:</strong> {statoInstallazione}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Bobina Associata:</strong> {numeroBobina}\n                </Typography>\n                {bobinaInfo && (\n                  <Typography variant=\"body2\">\n                    <strong>Metri Residui Bobina:</strong> {bobinaInfo.metri_residui} m\n                  </Typography>\n                )}\n              </Grid>\n            </Grid>\n          </Box>\n\n          {bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mt: 3 }}>\n              <strong>Attenzione:</strong> I metri posati ({formData.metri_posati}m) superano i metri residui della bobina ({bobinaInfo.metri_residui}m).\n              Questo porterà la bobina in stato OVER.\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 3 }}>\n            Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\n            {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.'}\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = (step) => {\n    switch (step) {\n      case 0:\n        return renderStep1(); // Seleziona Cavo\n      case 1:\n        return renderStep3(); // Associa Bobina\n      case 2:\n        return renderStep2(); // Inserisci Metri\n      case 3:\n        return renderStep4(); // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReelData({ cavo: null, bobina: null });\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    const { cavo, bobina } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per aggiornare il cavo:', { cavo, bobina });\n      onError('Dati mancanti per aggiornare il cavo');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      console.log(`Aggiornamento caratteristiche del cavo ${cavo.id_cavo} per compatibilità con bobina ${bobina.id_bobina}`);\n      console.log('Dati cavo prima dell\\'aggiornamento:', cavo);\n      console.log('Dati bobina:', bobina);\n\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);\n      console.log('Dati cavo dopo l\\'aggiornamento:', updatedCavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: bobina.id_bobina\n      });\n\n      // Ricarica le bobine per aggiornare l'interfaccia\n      await loadBobine();\n\n      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate per corrispondere alla bobina ${bobina.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n\n  return (\n    <Box>\n      {/* Sezione di ricerca */}\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Cerca cavo\n        </Typography>\n        <Grid container spacing={2} alignItems=\"center\">\n          <Grid item xs={9}>\n            <TextField\n              fullWidth\n              label=\"ID Cavo\"\n              variant=\"outlined\"\n              value={cavoIdInput}\n              onChange={(e) => setCavoIdInput(e.target.value)}\n              placeholder=\"Inserisci l'ID del cavo o parte di esso\"\n            />\n          </Grid>\n          <Grid item xs={3}>\n            <Button\n              fullWidth\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSearchCavoById}\n              disabled={caviLoading || !cavoIdInput.trim()}\n              startIcon={caviLoading ? <CircularProgress size={20} /> : <SearchIcon />}\n            >\n              Cerca\n            </Button>\n          </Grid>\n        </Grid>\n      </Paper>\n\n      {/* Risultati della ricerca */}\n      {showSearchResults && searchResults.length > 0 && (\n        <Paper sx={{ p: 3, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Risultati della ricerca\n          </Typography>\n          <TableContainer>\n            <Table size=\"small\">\n              <TableHead>\n                <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n                  <TableCell>ID Cavo</TableCell>\n                  <TableCell>Tipologia</TableCell>\n                  <TableCell>Formazione</TableCell>\n                  <TableCell>Ubicazione</TableCell>\n                  <TableCell>Metri Teorici</TableCell>\n                  <TableCell>Stato</TableCell>\n                  <TableCell>Azioni</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {searchResults.map((cavo) => (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>{cavo.id_cavo}</TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>{cavo.sezione || 'N/A'}</TableCell>\n                    <TableCell>Da: {cavo.ubicazione_partenza || 'N/A'}<br/>A: {cavo.ubicazione_arrivo || 'N/A'}</TableCell>\n                    <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={cavo.stato_installazione || 'N/D'}\n                        size=\"small\"\n                        color={getCableStateColor(cavo.stato_installazione)}\n                        variant=\"outlined\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Button\n                        size=\"small\"\n                        variant=\"contained\"\n                        color=\"primary\"\n                        onClick={() => handleCavoSelect(cavo)}\n                        disabled={isCableInstalled(cavo)}\n                      >\n                        Seleziona\n                      </Button>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </Paper>\n      )}\n\n      {/* Form per inserimento metri e selezione bobina */}\n      {selectedCavo && (\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Inserimento metri posati\n          </Typography>\n\n          {/* Dettagli del cavo selezionato */}\n          <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, mb: 3 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>\n              Cavo selezionato: {selectedCavo.id_cavo}\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metri teorici:</strong> {selectedCavo.metri_teorici || 'N/A'} m</Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\"><strong>Ubicazione partenza:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ubicazione arrivo:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\">\n                  <strong>Stato:</strong>\n                  <Chip\n                    label={selectedCavo.stato_installazione || 'N/D'}\n                    size=\"small\"\n                    color={getCableStateColor(selectedCavo.stato_installazione)}\n                    variant=\"outlined\"\n                    sx={{ ml: 1 }}\n                  />\n                </Typography>\n              </Grid>\n            </Grid>\n          </Box>\n\n          <Grid container spacing={3}>\n            {/* Colonna sinistra: Metri posati */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                Metratura posata\n              </Typography>\n              <TextField\n                size=\"small\"\n                fullWidth\n                label=\"Metri posati\"\n                variant=\"outlined\"\n                name=\"metri_posati\"\n                type=\"number\"\n                value={formData.metri_posati}\n                onChange={handleFormChange}\n                error={!!formErrors.metri_posati}\n                helperText={formErrors.metri_posati || formWarnings.metri_posati}\n                FormHelperTextProps={{\n                  sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n                }}\n                sx={{ mb: 1 }}\n              />\n              {formWarnings.metri_posati && !formErrors.metri_posati && (\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  {formWarnings.metri_posati}\n                </Alert>\n              )}\n            </Grid>\n\n            {/* Colonna destra: Selezione bobina */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                Associa bobina\n              </Typography>\n              {bobineLoading ? (\n                <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n                  <CircularProgress />\n                </Box>\n              ) : (\n                <Box>\n                  <Typography variant=\"subtitle2\" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold', mb: 1 }}>\n                    {selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'}\n                  </Typography>\n\n                  <FormControl fullWidth size=\"small\" error={!!formErrors.id_bobina}>\n                    <InputLabel id=\"bobina-select-label\">Seleziona bobina</InputLabel>\n                    <Select\n                      labelId=\"bobina-select-label\"\n                      id=\"bobina-select\"\n                      name=\"id_bobina\"\n                      value={formData.id_bobina}\n                      label=\"Seleziona bobina\"\n                      onChange={handleFormChange}\n                    >\n                      <MenuItem value=\"BOBINA_VUOTA\">\n                        <strong>BOBINA VUOTA</strong> (nessuna bobina associata)\n                      </MenuItem>\n                      <Divider />\n                      {bobine.length > 0 ? (\n                        <Box component=\"li\" sx={{ p: 1, bgcolor: 'background.paper' }}>\n                          <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: 'success.main' }}>\n                            {bobine.length} bobine disponibili\n                            {selectedCavo && (\n                              <> -\n                                <span style={{ color: bobine.some(bobina =>\n                                  bobina.tipologia === selectedCavo.tipologia &&\n                                  String(bobina.sezione) === String(selectedCavo.sezione)) ? 'green' : 'orange' }}>\n                                  {bobine.filter(bobina =>\n                                    bobina.tipologia === selectedCavo.tipologia &&\n                                    String(bobina.sezione) === String(selectedCavo.sezione)\n                                  ).length} compatibili\n                                </span>\n                              </>\n                            )}\n                          </Typography>\n                        </Box>\n                      ) : null}\n                      {bobine.map((bobina) => (\n                        <MenuItem\n                          key={bobina.id_bobina}\n                          value={bobina.id_bobina}\n                          disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                          sx={{\n                            '&.Mui-selected': { bgcolor: 'success.light' },\n                            '&.Mui-selected:hover': { bgcolor: 'success.light' },\n                            bgcolor: selectedCavo &&\n                                   bobina.tipologia === selectedCavo.tipologia &&\n                                   String(bobina.sezione) === String(selectedCavo.sezione) ?\n                                   'rgba(76, 175, 80, 0.12)' : 'inherit',\n                            border: selectedCavo &&\n                                   bobina.tipologia === selectedCavo.tipologia &&\n                                   String(bobina.sezione) === String(selectedCavo.sezione) ?\n                                   '1px solid #4caf50' : 'none',\n                            borderRadius: '4px',\n                            margin: '2px 0'\n                          }}\n                        >\n                          <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>\n                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                                {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'}\n                              </Typography>\n                              {selectedCavo &&\n                               bobina.tipologia === selectedCavo.tipologia &&\n                               String(bobina.sezione) === String(selectedCavo.sezione) && (\n                                <Chip\n                                  size=\"small\"\n                                  label=\"Compatibile\"\n                                  color=\"success\"\n                                  variant=\"outlined\"\n                                  sx={{ height: 20, fontSize: '0.6rem' }}\n                                />\n                              )}\n                            </Box>\n                            <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>\n                              <Typography variant=\"caption\">\n                                {bobina.sezione || 'N/A'}\n                              </Typography>\n                              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                {bobina.metri_residui || 0} m disponibili\n                              </Typography>\n                            </Box>\n                          </Box>\n                        </MenuItem>\n                      ))}\n                    </Select>\n                    <FormHelperText>\n                      {formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'}\n                    </FormHelperText>\n                  </FormControl>\n\n                  {bobine.length === 0 && !bobineLoading && (\n                    <Alert severity=\"warning\" sx={{ mt: 2, fontSize: '0.8rem' }}>\n                      Nessuna bobina disponibile trovata. Puoi usare BOBINA VUOTA.\n                    </Alert>\n                  )}\n\n                  {bobine.length > 0 && selectedCavo && !bobine.some(bobina =>\n                    bobina.tipologia === selectedCavo.tipologia &&\n                    String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) &&\n                    String(bobina.sezione) === String(selectedCavo.sezione)\n                  ) && (\n                    <Alert severity=\"info\" sx={{ mt: 2, fontSize: '0.8rem' }}>\n                      <strong>Nessuna bobina compatibile trovata.</strong> Hai due opzioni:\n                      <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>\n                        <li>Usa \"BOBINA VUOTA\" (potrai associare una bobina in seguito)</li>\n                        <li>Seleziona una bobina non compatibile (ti verrà chiesto se vuoi aggiornare le caratteristiche del cavo)</li>\n                      </ul>\n                    </Alert>\n                  )}\n                </Box>\n              )}\n\n              {/* Mostra dettagli della bobina selezionata */}\n              {!bobineLoading && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && (() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                if (bobina) {\n                  return (\n                    <Box sx={{ mt: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n                      <Typography variant=\"body2\"><strong>Bobina:</strong> {getBobinaNumber(bobina.id_bobina)}</Typography>\n                      <Typography variant=\"body2\"><strong>Metri residui:</strong> {bobina.metri_residui || 0} m</Typography>\n                      <Typography variant=\"body2\">\n                        <strong>Stato:</strong>\n                        <Chip\n                          label={bobina.stato_bobina || 'N/D'}\n                          size=\"small\"\n                          color={getReelStateColor(bobina.stato_bobina)}\n                          variant=\"outlined\"\n                          sx={{ ml: 1 }}\n                        />\n                      </Typography>\n                    </Box>\n                  );\n                }\n                return null;\n              })()}\n            </Grid>\n          </Grid>\n\n          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>\n            <Button\n              variant=\"outlined\"\n              color=\"secondary\"\n              onClick={() => {\n                setSelectedCavo(null);\n                setFormData({\n                  id_cavo: '',\n                  metri_posati: '',\n                  id_bobina: ''\n                });\n              }}\n              startIcon={<CancelIcon />}\n              disabled={loading}\n            >\n              Annulla\n            </Button>\n\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSubmit}\n              endIcon={<SaveIcon />}\n              disabled={loading || !formData.metri_posati || !formData.id_bobina}\n            >\n              {loading ? <CircularProgress size={24} /> : 'Salva'}\n            </Button>\n          </Box>\n        </Paper>\n      )}\n\n      {/* Dialogo di conferma generico */}\n      <Dialog open={showConfirmDialog} onClose={() => setShowConfirmDialog(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">{confirmDialogProps.title}</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <Typography variant=\"body1\" sx={{ mt: 2 }}>\n            {confirmDialogProps.message}\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowConfirmDialog(false)} color=\"secondary\" variant=\"outlined\">\n            Annulla\n          </Button>\n          <Button\n            onClick={() => {\n              setShowConfirmDialog(false);\n              confirmDialogProps.onConfirm();\n            }}\n            color=\"primary\"\n            variant=\"contained\"\n            autoFocus\n          >\n            Conferma\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per cavi già posati */}\n      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">Cavo già posato</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          {alreadyLaidCavo && (\n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"body1\" paragraph>\n                Il cavo <strong>{alreadyLaidCavo.id_cavo}</strong> risulta già posato ({alreadyLaidCavo.metratura_reale || 0}m).\n              </Typography>\n              <Typography variant=\"body1\" paragraph>\n                Puoi scegliere di:\n              </Typography>\n              <Typography variant=\"body2\" component=\"ul\">\n                <li>Modificare la bobina associata al cavo</li>\n                <li>Selezionare un altro cavo</li>\n                <li>Annullare l'operazione</li>\n              </Typography>\n            </Box>\n          )}\n        </DialogContent>\n        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>\n          <Button onClick={handleCloseAlreadyLaidDialog} color=\"secondary\">\n            Annulla operazione\n          </Button>\n          <Box>\n            <Button onClick={handleSelectAnotherCable} color=\"primary\" sx={{ mr: 1 }}>\n              Seleziona altro cavo\n            </Button>\n            <Button onClick={handleModifyReel} variant=\"contained\" color=\"primary\">\n              Modifica bobina\n            </Button>\n          </Box>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={handleCloseIncompatibleReelDialog}\n        cavo={incompatibleReelData.cavo}\n        bobina={incompatibleReelData.bobina}\n        onUpdateCavo={handleUpdateCavoToMatchReel}\n        onSelectAnotherReel={handleSelectAnotherReel}\n      />\n\n      {/* Dialogo per visualizzare i dettagli del cavo */}\n      <Dialog\n        open={showCavoDetailsDialog}\n        onClose={() => setShowCavoDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <InfoIcon color=\"primary\" />\n            <Typography variant=\"h6\">Dettagli Cavo</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <CavoDetailsView cavo={selectedCavo} />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowCavoDetailsDialog(false)} color=\"primary\">\n            Chiudi\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default InserisciMetriForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,QAClB,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;AAC/B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,wBAAwB,QAAQ,6BAA6B;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuE,WAAW,EAAEC,cAAc,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyE,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2E,aAAa,EAAEC,gBAAgB,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC+E,IAAI,EAAEC,OAAO,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiF,MAAM,EAAEC,SAAS,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmF,YAAY,EAAEC,eAAe,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqF,WAAW,EAAEC,cAAc,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACuF,UAAU,EAAEC,aAAa,CAAC,GAAGxF,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAM,CAACyF,QAAQ,EAAEC,WAAW,CAAC,GAAG1F,QAAQ,CAAC;IACvC2F,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACgG,YAAY,EAAEC,eAAe,CAAC,GAAGjG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAACkG,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACoG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChE,MAAM,CAACsG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvG,QAAQ,CAAC;IAAEwG,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK,CAAC,CAAC;EAC9F,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC4G,eAAe,EAAEC,kBAAkB,CAAC,GAAG7G,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC8G,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG/G,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACd+G,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAChD,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMiD,eAAe,GAAIC,QAAQ,IAAK;IACpC;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvC,OAAOD,QAAQ,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAOF,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMG,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF7C,cAAc,CAAC,IAAI,CAAC;MACpB8C,OAAO,CAACC,GAAG,CAAC,oCAAoCvD,UAAU,KAAK,CAAC;;MAEhE;MACA,IAAI;QACF,MAAMwD,QAAQ,GAAG,MAAM5E,WAAW,CAAC6E,OAAO,CAACzD,UAAU,CAAC;QACtDsD,OAAO,CAACC,GAAG,CAAC,YAAYC,QAAQ,CAACE,MAAM,OAAO,CAAC;;QAE/C;QACA;QACA1C,OAAO,CAACwC,QAAQ,CAAC;MACnB,CAAC,CAAC,OAAOG,SAAS,EAAE;QAClBL,OAAO,CAACM,KAAK,CAAC,qDAAqD,EAAED,SAAS,CAAC;;QAE/E;QACA,IAAIA,SAAS,CAACE,cAAc,IAAI,CAACF,SAAS,CAACG,QAAQ,IAC/CH,SAAS,CAACI,IAAI,KAAK,cAAc,IAChCJ,SAAS,CAACK,OAAO,IAAIL,SAAS,CAACK,OAAO,CAACb,QAAQ,CAAC,eAAe,CAAE,EAAE;UAEtEG,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;UACtE,MAAM,IAAIU,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;UAEvD,IAAI;YACF;YACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;YAC3C,MAAMC,OAAO,GAAG1F,aAAa,CAAC2F,QAAQ,CAACC,OAAO;;YAE9C;YACA,MAAMC,aAAa,GAAG,MAAMxI,KAAK,CAACyI,GAAG,CACnC,GAAGJ,OAAO,SAASvE,UAAU,EAAE,EAC/B;cACE4E,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUR,KAAK;cAClC,CAAC;cACDS,OAAO,EAAE,KAAK,CAAC;YACjB,CACF,CAAC;YAEDvB,OAAO,CAACC,GAAG,CAAC,2CAA2CmB,aAAa,CAACI,IAAI,CAACpB,MAAM,OAAO,CAAC;YACxF1C,OAAO,CAAC0D,aAAa,CAACI,IAAI,CAAC;UAC7B,CAAC,CAAC,OAAOC,UAAU,EAAE;YACnBzB,OAAO,CAACM,KAAK,CAAC,uCAAuC,EAAEmB,UAAU,CAAC;YAClE,MAAMA,UAAU;UAClB;QACF,CAAC,MAAM;UACL,MAAMpB,SAAS;QACjB;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIoB,YAAY,GAAG,iCAAiC;MAEpD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEA9D,OAAO,CAAC8E,YAAY,CAAC;IACvB,CAAC,SAAS;MACRxE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMwC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFtC,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,IAAIS,YAAY,IAAIA,YAAY,CAAC+D,SAAS,IAAI/D,YAAY,CAACgE,YAAY,IAAIhE,YAAY,CAACiE,OAAO,EAAE;QAC/F9B,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;QAExE,IAAI;UACF;UACA,IAAI8B,cAAc,GAAGC,MAAM,CAACnE,YAAY,CAACgE,YAAY,IAAI,GAAG,CAAC;UAC7D,IAAIE,cAAc,CAAClC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAClC,MAAMoC,KAAK,GAAGF,cAAc,CAACjC,KAAK,CAAC,KAAK,CAAC;YACzCiC,cAAc,GAAGE,KAAK,CAAC,CAAC,CAAC;YACzBjC,OAAO,CAACC,GAAG,CAAC,0CAA0CpC,YAAY,CAACgE,YAAY,OAAOE,cAAc,EAAE,CAAC;UACzG;UAEA,MAAMG,aAAa,GAAGF,MAAM,CAACnE,YAAY,CAAC+D,SAAS,IAAI,EAAE,CAAC;UAC1D,MAAMO,WAAW,GAAGH,MAAM,CAACnE,YAAY,CAACiE,OAAO,IAAI,GAAG,CAAC;UAEvD9B,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;YACvD2B,SAAS,EAAEM,aAAa;YACxBL,YAAY,EAAEE,cAAc;YAC5BD,OAAO,EAAEK;UACX,CAAC,CAAC;;UAEF;UACA,MAAMC,iBAAiB,GAAG,MAAMjG,gBAAgB,CAACkG,oBAAoB,CACnE3F,UAAU,EACVwF,aAAa,EACbH,cAAc,EACdI,WACF,CAAC;UAED,IAAIC,iBAAiB,IAAIA,iBAAiB,CAAChC,MAAM,GAAG,CAAC,EAAE;YACrDJ,OAAO,CAACC,GAAG,CAAC,WAAWmC,iBAAiB,CAAChC,MAAM,6BAA6B,CAAC;YAC7E;YACAgC,iBAAiB,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,aAAa,GAAGF,CAAC,CAACE,aAAa,CAAC;YACnE7E,SAAS,CAACwE,iBAAiB,CAAC;YAC5BhF,gBAAgB,CAAC,KAAK,CAAC;YACvB;UACF,CAAC,MAAM;YACL4C,OAAO,CAACC,GAAG,CAAC,gFAAgF,CAAC;UAC/F;QACF,CAAC,CAAC,OAAOK,KAAK,EAAE;UACdN,OAAO,CAACM,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;UACxEN,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC9D;MACF;;MAEA;MACAD,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D,MAAMyC,UAAU,GAAG,MAAMvG,gBAAgB,CAACwG,SAAS,CAACjG,UAAU,CAAC;MAC/DsD,OAAO,CAACC,GAAG,CAAC,oBAAoByC,UAAU,CAACtC,MAAM,EAAE,CAAC;;MAEpD;MACA,MAAMwC,kBAAkB,GAAGF,UAAU,CAACG,MAAM,CAAC1D,MAAM,IACjD,CAACA,MAAM,CAAC2D,YAAY,KAAK,aAAa,IAAI3D,MAAM,CAAC2D,YAAY,KAAK,QAAQ,KAC1E3D,MAAM,CAAC2D,YAAY,KAAK,MAAM,IAAI3D,MAAM,CAAC2D,YAAY,KAAK,WAC5D,CAAC;MAED9C,OAAO,CAACC,GAAG,CAAC,wBAAwB2C,kBAAkB,CAACxC,MAAM,EAAE,CAAC;;MAEhE;MACAwC,kBAAkB,CAACN,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,aAAa,GAAGF,CAAC,CAACE,aAAa,CAAC;;MAEpE;MACA7E,SAAS,CAACgF,kBAAkB,CAAC;IAC/B,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D1D,OAAO,CAAC,uCAAuC,IAAI0D,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRtD,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM2F,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAAChF,WAAW,CAACiF,IAAI,CAAC,CAAC,EAAE;MACvBpG,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFM,cAAc,CAAC,IAAI,CAAC;MACpB8C,OAAO,CAACC,GAAG,CAAC,6BAA6BlC,WAAW,CAACiF,IAAI,CAAC,CAAC,iBAAiBtG,UAAU,EAAE,CAAC;;MAEzF;MACA,MAAMwD,QAAQ,GAAG,MAAM5E,WAAW,CAAC6E,OAAO,CAACzD,UAAU,CAAC;;MAEtD;MACA,MAAMuG,YAAY,GAAG/C,QAAQ,CAAC2C,MAAM,CAAC3D,IAAI,IACvCA,IAAI,CAACb,OAAO,CAAC6E,WAAW,CAAC,CAAC,CAACrD,QAAQ,CAAC9B,WAAW,CAACiF,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CACtE,CAAC;MAEDlD,OAAO,CAACC,GAAG,CAAC,WAAWgD,YAAY,CAAC7C,MAAM,iCAAiC,CAAC;;MAE5E;MACA,MAAM+C,UAAU,GAAGF,YAAY,CAACG,IAAI,CAAClE,IAAI,IACvCA,IAAI,CAACb,OAAO,CAAC6E,WAAW,CAAC,CAAC,KAAKnF,WAAW,CAACiF,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAChE,CAAC;MAED,IAAIC,UAAU,EAAE;QACdnD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEkD,UAAU,CAAC;;QAEzD;QACA,IAAIA,UAAU,CAACE,mBAAmB,KAAK,YAAY,IAAKF,UAAU,CAACG,eAAe,IAAIH,UAAU,CAACG,eAAe,GAAG,CAAE,EAAE;UACrHtD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEkD,UAAU,CAAC;UAC/D5D,kBAAkB,CAAC4D,UAAU,CAAC;UAC9B9D,wBAAwB,CAAC,IAAI,CAAC;UAC9BnC,cAAc,CAAC,KAAK,CAAC;UACrB;QACF;;QAEA;QACA,IAAIiG,UAAU,CAACI,sBAAsB,KAAK,CAAC,EAAE;UAC3CvD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEkD,UAAU,CAAC;UAC9C;QACF;;QAEA;QACAK,gBAAgB,CAACL,UAAU,CAAC;MAC9B,CAAC,MAAM,IAAIF,YAAY,CAAC7C,MAAM,GAAG,CAAC,EAAE;QAClC;QACA9C,gBAAgB,CAAC2F,YAAY,CAAC;QAC9BzF,oBAAoB,CAAC,IAAI,CAAC;MAC5B,CAAC,MAAM;QACL;QACAZ,OAAO,CAAC,oCAAoCmB,WAAW,CAACiF,IAAI,CAAC,CAAC,kBAAkBtG,UAAU,EAAE,CAAC;MAC/F;IACF,CAAC,CAAC,OAAO4D,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;MAEtD;MACA,IAAIoB,YAAY,GAAG,+BAA+B;MAElD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAACmD,MAAM,KAAK,GAAG,EAAE;QAC/B/B,YAAY,GAAG,gBAAgB3D,WAAW,CAACiF,IAAI,CAAC,CAAC,8BAA8BtG,UAAU,EAAE;MAC7F,CAAC,MAAM,IAAI4D,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEA9D,OAAO,CAAC8E,YAAY,CAAC;IACvB,CAAC,SAAS;MACRxE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMsG,gBAAgB,GAAItE,IAAI,IAAK;IACjC;IACA,IAAIA,IAAI,CAACmE,mBAAmB,KAAK,YAAY,IAAKnE,IAAI,CAACoE,eAAe,IAAIpE,IAAI,CAACoE,eAAe,GAAG,CAAE,EAAE;MACnG;MACA/D,kBAAkB,CAACL,IAAI,CAAC;MACxBG,wBAAwB,CAAC,IAAI,CAAC;MAC9B;IACF;IACA;IAAA,KACK,IAAIH,IAAI,CAACqE,sBAAsB,KAAK,CAAC,EAAE;MAC1C;MACA,IAAIG,MAAM,CAACC,OAAO,CAAC,WAAWzE,IAAI,CAACb,OAAO,0CAA0C,CAAC,EAAE;QACrF;QACAuF,eAAe,CAAC1E,IAAI,CAACb,OAAO,CAAC,CAACwF,IAAI,CAAC,MAAM;UACvC;UACA,MAAMC,WAAW,GAAG;YAAE,GAAG5E,IAAI;YAAEqE,sBAAsB,EAAE;UAAE,CAAC;UAC1DzF,eAAe,CAACgG,WAAW,CAAC;UAC5B1F,WAAW,CAAC;YACV,GAAGD,QAAQ;YACXE,OAAO,EAAEyF,WAAW,CAACzF,OAAO;YAC5BC,YAAY,EAAE;UAChB,CAAC,CAAC;UACF;UACAd,oBAAoB,CAAC,KAAK,CAAC;;UAE3B;UACAkC,UAAU,CAAC,CAAC;QACd,CAAC,CAAC,CAACqE,KAAK,CAACzD,KAAK,IAAI;UAChBN,OAAO,CAACM,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvE1D,OAAO,CAAC,kDAAkD,IAAI0D,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;QACvG,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;MACF;IACF,CAAC,MAAM;MACL;MACA5C,eAAe,CAACoB,IAAI,CAAC;MACrBd,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEa,IAAI,CAACb,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF;MACAd,oBAAoB,CAAC,KAAK,CAAC;;MAE3B;MACA,IAAI0B,IAAI,CAAC0C,SAAS,IAAI1C,IAAI,CAAC2C,YAAY,IAAI3C,IAAI,CAAC4C,OAAO,EAAE;QACvD9B,OAAO,CAACC,GAAG,CAAC,8CAA8Cf,IAAI,CAACb,OAAO,KAAK,CAAC;QAC5EqB,UAAU,CAAC,CAAC;MACd;IACF;EACF,CAAC;;EAED;EACA,MAAMkE,eAAe,GAAG,MAAOI,MAAM,IAAK;IACxC,IAAI;MACF;MACA,MAAM1I,WAAW,CAACsI,eAAe,CAAClH,UAAU,EAAEsH,MAAM,CAAC;MACrDrH,SAAS,CAAC,QAAQqH,MAAM,0BAA0B,CAAC;MACnD,OAAO,IAAI;IACb,CAAC,CAAC,OAAO1D,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvE1D,OAAO,CAAC,kDAAkD,IAAI0D,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACrG,MAAMJ,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM2D,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACA,IAAIF,IAAI,KAAK,WAAW,IAAIC,KAAK,IAAIA,KAAK,KAAK,cAAc,EAAE;MAC7D;MACA,MAAMjF,MAAM,GAAGxB,MAAM,CAACyF,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACjE,SAAS,KAAK6F,KAAK,CAAC;MAEtD,IAAIjF,MAAM,IAAItB,YAAY,EAAE;QAC1B;QACA,MAAMyG,YAAY,GAChBnF,MAAM,CAACyC,SAAS,KAAK/D,YAAY,CAAC+D,SAAS,IAC3CI,MAAM,CAAC7C,MAAM,CAAC2C,OAAO,CAAC,KAAKE,MAAM,CAACnE,YAAY,CAACiE,OAAO,CAAC;QAEzD,IAAI,CAACwC,YAAY,EAAE;UACjBtE,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEd,MAAM,CAAC;UACxDa,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEpC,YAAY,CAAC;UAC3CmC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;YAAE8B,cAAc;YAAEwC;UAAiB,CAAC,CAAC;;UAE1E;UACAtF,uBAAuB,CAAC;YACtBC,IAAI,EAAErB,YAAY;YAClBsB,MAAM,EAAEA;UACV,CAAC,CAAC;UACFN,6BAA6B,CAAC,IAAI,CAAC;;UAEnC;UACA;QACF;MACF;IACF;IAEAT,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACgG,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACAI,aAAa,CAACL,IAAI,EAAEC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMI,aAAa,GAAGA,CAACL,IAAI,EAAEC,KAAK,KAAK;IACrC,IAAI9D,KAAK,GAAG,IAAI;IAChB,IAAImE,OAAO,GAAG,IAAI;IAElB,IAAIN,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACpB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjC1C,KAAK,GAAG,uCAAuC;QAC/C,OAAO,KAAK;MACd;;MAEA;MACA,IAAIoE,KAAK,CAACC,UAAU,CAACP,KAAK,CAAC,CAAC,IAAIO,UAAU,CAACP,KAAK,CAAC,IAAI,CAAC,EAAE;QACtD9D,KAAK,GAAG,sCAAsC;QAC9C,OAAO,KAAK;MACd;MAEA,MAAMsE,WAAW,GAAGD,UAAU,CAACP,KAAK,CAAC;;MAErC;MACA,IAAIvG,YAAY,IAAIA,YAAY,CAACgH,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAC9G,YAAY,CAACgH,aAAa,CAAC,EAAE;QACtGJ,OAAO,GAAG,mBAAmBG,WAAW,yCAAyC/G,YAAY,CAACgH,aAAa,IAAI;MACjH;;MAEA;MACA,IAAI1G,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC/D,MAAMY,MAAM,GAAGxB,MAAM,CAACyF,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACjE,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIY,MAAM,IAAIyF,WAAW,GAAGD,UAAU,CAACxF,MAAM,CAACsD,aAAa,CAAC,EAAE;UAC5DgC,OAAO,GAAG,mBAAmBG,WAAW,6CAA6CzF,MAAM,CAACsD,aAAa,oCAAoC;QAC/I;MACF;IACF,CAAC,MAAM,IAAI0B,IAAI,KAAK,WAAW,EAAE;MAC/B;MACA,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACpB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjC1C,KAAK,GAAG,qCAAqC;QAC7C,OAAO,KAAK;MACd;IACF;;IAEA;IACA7B,aAAa,CAACqG,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACX,IAAI,GAAG7D;IACV,CAAC,CAAC,CAAC;;IAEH;IACA3B,eAAe,CAACmG,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACX,IAAI,GAAGM;IACV,CAAC,CAAC,CAAC;IAEH,OAAO,CAACnE,KAAK;EACf,CAAC;;EAED;EACA,MAAM,CAACyE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtM,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACuM,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxM,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACyM,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1M,QAAQ,CAAC;IAC3D2M,KAAK,EAAE,EAAE;IACT3E,OAAO,EAAE,EAAE;IACX4E,SAAS,EAAEA,CAAA,KAAM,CAAC;EACpB,CAAC,CAAC;;EAEF;;EAEA;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMC,QAAQ,GAAG,CAAC,CAAC;;IAEnB;IACAV,oBAAoB,CAAC,KAAK,CAAC;;IAE3B;IACA,IAAI,CAAC7G,QAAQ,CAACG,YAAY,IAAIH,QAAQ,CAACG,YAAY,CAAC0E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjEyC,MAAM,CAACnH,YAAY,GAAG,uCAAuC;MAC7DkH,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAId,KAAK,CAACC,UAAU,CAACxG,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAIqG,UAAU,CAACxG,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;MAC7FmH,MAAM,CAACnH,YAAY,GAAG,sCAAsC;MAC5DkH,OAAO,GAAG,KAAK;IACjB;;IAEA;IACA,IAAI,CAACrH,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,CAACyE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC3DyC,MAAM,CAAClH,SAAS,GAAG,qCAAqC;MACxDiH,OAAO,GAAG,KAAK;IACjB;IAEA,IAAIA,OAAO,EAAE;MACX,MAAMZ,WAAW,GAAGD,UAAU,CAACxG,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA,IAAIT,YAAY,IAAIA,YAAY,CAACgH,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAC9G,YAAY,CAACgH,aAAa,CAAC,EAAE;QACtGa,QAAQ,CAACpH,YAAY,GAAG,mBAAmBsG,WAAW,yCAAyC/G,YAAY,CAACgH,aAAa,IAAI;QAC7HG,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5B;QACA;MACF;;MAEA;MACA,IAAIQ,OAAO,IAAIrH,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC1E,MAAMY,MAAM,GAAGxB,MAAM,CAACyF,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACjE,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIY,MAAM,IAAIyF,WAAW,GAAGD,UAAU,CAACxF,MAAM,CAACsD,aAAa,CAAC,EAAE;UAC5DiD,QAAQ,CAACpH,YAAY,GAAG,mBAAmBsG,WAAW,6CAA6CzF,MAAM,CAACsD,aAAa,oCAAoC;UAC3JuC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;;UAE5B;UACAI,qBAAqB,CAAC;YACpBC,KAAK,EAAE,kCAAkC;YACzC3E,OAAO,EAAE,mBAAmBkE,WAAW,6CAA6CzF,MAAM,CAACsD,aAAa,8DAA8D;YACtK6C,SAAS,EAAEA,CAAA,KAAM;cACf;cACAK,UAAU,CAAC,CAAC;YACd;UACF,CAAC,CAAC;UACFT,oBAAoB,CAAC,IAAI,CAAC;UAC1B,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;IACF;IAEAzG,aAAa,CAACgH,MAAM,CAAC;IACrB9G,eAAe,CAAC+G,QAAQ,CAAC;IACzB,OAAOF,OAAO;EAChB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI1H,UAAU,KAAK,CAAC,EAAE;MACpB;MACA,IAAI,CAACsH,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;IACF,CAAC,MAAM,IAAItH,UAAU,KAAK,CAAC,EAAE;MAC3B;MACAyB,UAAU,CAAC,CAAC;IACd;IAEAxB,aAAa,CAAE0H,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB3H,aAAa,CAAE0H,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxB5H,aAAa,CAAC,CAAC,CAAC;IAChBJ,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBI,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA;EACA,MAAMoH,2BAA2B,GAAGA,CAACnB,WAAW,EAAEoB,YAAY,KAAK;IACjE,OAAOpK,mBAAmB,CAACgJ,WAAW,EAAEoB,YAAY,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B;IACA;IACA,IAAIrG,QAAQ;IACZ,IAAIsG,kBAAkB;IACtB,IAAItB,WAAW;IACf,IAAIuB,SAAS,GAAG,KAAK;IAErB,IAAI;MACFnJ,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI,CAACuI,YAAY,CAAC,CAAC,EAAE;QACnBvI,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA4H,WAAW,GAAGD,UAAU,CAACxG,QAAQ,CAACG,YAAY,CAAC;;MAE/C;MACAsB,QAAQ,GAAGzB,QAAQ,CAACI,SAAS;;MAE7B;MACA,IAAI,CAACqB,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;QAChC;QACAnB,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbD,SAAS,EAAE;QACb,CAAC,CAAC;QACFvB,UAAU,CAAC,KAAK,CAAC;QACjB;MACF,CAAC,MAAM,IAAI4C,QAAQ,KAAK,cAAc,EAAE;QACtC;QACAI,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C;QACAL,QAAQ,GAAG,cAAc;MAC3B,CAAC,MAAM;QACL;QACAI,OAAO,CAACC,GAAG,CAAC,wBAAwBL,QAAQ,EAAE,CAAC;MACjD;;MAEA;MACAsG,kBAAkB,GAAGH,2BAA2B,CAACnB,WAAW,EAAE/G,YAAY,CAACgH,aAAa,CAAC;;MAEzF;MACAsB,SAAS,GAAG,KAAK;;MAEjB;MACA,IAAIvG,QAAQ,KAAK,cAAc,EAAE;QAC/BuG,SAAS,GAAG,IAAI;QAChBnG,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACrD;MACA;MAAA,KACK,IAAIL,QAAQ,IAAIA,QAAQ,KAAK,cAAc,EAAE;QAChD,MAAMT,MAAM,GAAGxB,MAAM,CAACyF,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACjE,SAAS,KAAKqB,QAAQ,CAAC;QACzD,IAAIT,MAAM,IAAIyF,WAAW,GAAGD,UAAU,CAACxF,MAAM,CAACsD,aAAa,CAAC,EAAE;UAC5D0D,SAAS,GAAG,IAAI;UAChBnG,OAAO,CAACC,GAAG,CAAC,qCAAqCL,QAAQ,iCAAiC,CAAC;QAC7F;MACF;;MAEA;MACA,IAAI/B,YAAY,IAAIA,YAAY,CAACgH,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAC9G,YAAY,CAACgH,aAAa,CAAC,EAAE;QACtG;QACAsB,SAAS,GAAG,IAAI;QAChBnG,OAAO,CAACC,GAAG,CAAC,yCAAyC2E,WAAW,sBAAsB/G,YAAY,CAACgH,aAAa,GAAG,CAAC;MACtH;;MAEA;MACA7E,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBvD,UAAU;QACVsH,MAAM,EAAE7F,QAAQ,CAACE,OAAO;QACxBuG,WAAW;QACXhF,QAAQ;QACRuG,SAAS;QACTD;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAACnB,iBAAiB,EAAE;QACtB,MAAMqB,cAAc,GAAG,qCAAqCjI,QAAQ,CAACE,OAAO,QAAQuG,WAAW,WAAW;;QAE1G;QACAQ,qBAAqB,CAAC;UACpBC,KAAK,EAAE,wBAAwB;UAC/B3E,OAAO,EAAE0F,cAAc;UACvBd,SAAS,EAAE,MAAAA,CAAA,KAAY;YACrB;YACA,IAAI;cACFtI,UAAU,CAAC,IAAI,CAAC;cAEhB,MAAM1B,WAAW,CAAC+K,iBAAiB,CACjC3J,UAAU,EACVyB,QAAQ,CAACE,OAAO,EAChBuG,WAAW,EACXhF,QAAQ,EACRuG,SACF,CAAC;;cAED;cACA,IAAIG,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;cAC9F,IAAItG,QAAQ,KAAK,cAAc,EAAE;gBAC/B0G,cAAc,IAAI,iCAAiC;cACrD,CAAC,MAAM,IAAI1G,QAAQ,EAAE;gBACnB,MAAMT,MAAM,GAAGxB,MAAM,CAACyF,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACjE,SAAS,KAAKqB,QAAQ,CAAC;gBACzD,IAAIT,MAAM,EAAE;kBACVmH,cAAc,IAAI,gCAAgC1G,QAAQ,EAAE;gBAC9D;cACF;;cAEA;cACAjD,SAAS,CAAC2J,cAAc,CAAC;;cAEzB;cACAR,WAAW,CAAC,CAAC;;cAEb;cACA/F,QAAQ,CAAC,CAAC;YACZ,CAAC,CAAC,OAAOO,KAAK,EAAE;cACdN,OAAO,CAACM,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;cAEzE;cACA,IAAIV,QAAQ,KAAK,cAAc,IAAIU,KAAK,CAACiG,OAAO,EAAE;gBAChD;gBACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;gBAC7HvJ,SAAS,CAAC2J,cAAc,CAAC;;gBAEzB;gBACAR,WAAW,CAAC,CAAC;;gBAEb;gBACA/F,QAAQ,CAAC,CAAC;gBACV;cACF;;cAEA;cACA,IAAI2B,YAAY,GAAG,kDAAkD;cAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;gBAAA,IAAAgG,oBAAA;gBAClB;gBACA,MAAM/C,MAAM,GAAGnD,KAAK,CAACE,QAAQ,CAACiD,MAAM;gBACpC,MAAM9B,MAAM,GAAG,EAAA6E,oBAAA,GAAAlG,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAAgF,oBAAA,uBAAnBA,oBAAA,CAAqB7E,MAAM,KAAIrB,KAAK,CAACI,OAAO;gBAE3D,IAAI+C,MAAM,KAAK,GAAG,EAAE;kBAClB;kBACA,IAAI9B,MAAM,CAAC9B,QAAQ,CAAC,eAAe,CAAC,EAAE;oBACpC6B,YAAY,GAAG,uGAAuG;kBACxH,CAAC,MAAM,IAAIC,MAAM,CAAC9B,QAAQ,CAAC,YAAY,CAAC,EAAE;oBACxC6B,YAAY,GAAG,4EAA4E;kBAC7F,CAAC,MAAM;oBACLA,YAAY,GAAGC,MAAM;kBACvB;gBACF,CAAC,MAAM,IAAI8B,MAAM,KAAK,GAAG,EAAE;kBACzB;kBACA,IAAI7D,QAAQ,KAAK,cAAc,IAAI+B,MAAM,CAAC9B,QAAQ,CAAC,aAAa,CAAC,EAAE;oBACjE,IAAIyG,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;oBAC7HvJ,SAAS,CAAC2J,cAAc,CAAC;;oBAEzB;oBACAR,WAAW,CAAC,CAAC;;oBAEb;oBACA/F,QAAQ,CAAC,CAAC;oBACV;kBACF,CAAC,MAAM;oBACL2B,YAAY,GAAG,8BAA8BC,MAAM,EAAE;kBACvD;gBACF,CAAC,MAAM;kBACLD,YAAY,GAAG,sBAAsB+B,MAAM,MAAM9B,MAAM,EAAE;gBAC3D;cACF,CAAC,MAAM,IAAIrB,KAAK,CAACmG,OAAO,EAAE;gBACxB;gBACA/E,YAAY,GAAG,+DAA+D;cAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAI/B,QAAQ,KAAK,cAAc,EAAE;gBACtD;gBACA8B,YAAY,GAAGpB,KAAK,CAACqB,MAAM;gBAC3B,IAAIrB,KAAK,CAACmD,MAAM,KAAK,GAAG,IAAInD,KAAK,CAACiG,OAAO,EAAE;kBACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;kBAC7HvJ,SAAS,CAAC2J,cAAc,CAAC;;kBAEzB;kBACAR,WAAW,CAAC,CAAC;;kBAEb;kBACA/F,QAAQ,CAAC,CAAC;kBACV;gBACF;cACF,CAAC,MAAM;gBACL;gBACA2B,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;cACtE;cAEA/E,OAAO,CAAC8E,YAAY,CAAC;YACvB,CAAC,SAAS;cACR1E,UAAU,CAAC,KAAK,CAAC;YACnB;UACF;QACF,CAAC,CAAC;QACFkI,oBAAoB,CAAC,IAAI,CAAC;QAC1BlI,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACAgD,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ED,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEvD,UAAU,CAAC;MACxCsD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE9B,QAAQ,CAACE,OAAO,CAAC;MAC3C2B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE2E,WAAW,CAAC;MAC3C5E,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEL,QAAQ,EAAE,OAAOA,QAAQ,CAAC;MACtDI,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEkG,SAAS,CAAC;MAEtC,MAAM7K,WAAW,CAAC+K,iBAAiB,CACjC3J,UAAU,EACVyB,QAAQ,CAACE,OAAO,EAChBuG,WAAW,EACXhF,QAAQ,EACRuG,SACF,CAAC;;MAED;MACA,IAAIG,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;MAC9F,IAAItG,QAAQ,KAAK,cAAc,EAAE;QAC/B0G,cAAc,IAAI,iCAAiC;MACrD,CAAC,MAAM,IAAI1G,QAAQ,EAAE;QACnB,MAAMT,MAAM,GAAGxB,MAAM,CAACyF,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACjE,SAAS,KAAKqB,QAAQ,CAAC;QACzD,IAAIT,MAAM,EAAE;UACVmH,cAAc,IAAI,gCAAgC1G,QAAQ,EAAE;QAC9D;MACF;;MAEA;MACAjD,SAAS,CAAC2J,cAAc,CAAC;;MAEzB;MACAR,WAAW,CAAC,CAAC;;MAEb;MACA/F,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;MAEzE;MACA,IAAIV,QAAQ,KAAK,cAAc,IAAIU,KAAK,CAACiG,OAAO,EAAE;QAChD;QACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;QAC7HvJ,SAAS,CAAC2J,cAAc,CAAC;;QAEzB;QACAR,WAAW,CAAC,CAAC;;QAEb;QACA/F,QAAQ,CAAC,CAAC;QACV;MACF;;MAEA;MACA,IAAI2B,YAAY,GAAG,kDAAkD;MAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;QAAA,IAAAkG,qBAAA;QAClB;QACA,MAAMjD,MAAM,GAAGnD,KAAK,CAACE,QAAQ,CAACiD,MAAM;QACpC,MAAM9B,MAAM,GAAG,EAAA+E,qBAAA,GAAApG,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAAkF,qBAAA,uBAAnBA,qBAAA,CAAqB/E,MAAM,KAAIrB,KAAK,CAACI,OAAO;QAE3D,IAAI+C,MAAM,KAAK,GAAG,EAAE;UAClB;UACA,IAAI9B,MAAM,CAAC9B,QAAQ,CAAC,eAAe,CAAC,EAAE;YACpC6B,YAAY,GAAG,uGAAuG;UACxH,CAAC,MAAM,IAAIC,MAAM,CAAC9B,QAAQ,CAAC,YAAY,CAAC,EAAE;YACxC6B,YAAY,GAAG,4EAA4E;UAC7F,CAAC,MAAM;YACLA,YAAY,GAAGC,MAAM;UACvB;QACF,CAAC,MAAM,IAAI8B,MAAM,KAAK,GAAG,EAAE;UACzB;UACA,IAAI7D,QAAQ,KAAK,cAAc,IAAI+B,MAAM,CAAC9B,QAAQ,CAAC,aAAa,CAAC,EAAE;YACjE,IAAIyG,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;YAC7HvJ,SAAS,CAAC2J,cAAc,CAAC;;YAEzB;YACAR,WAAW,CAAC,CAAC;;YAEb;YACA/F,QAAQ,CAAC,CAAC;YACV;UACF,CAAC,MAAM;YACL2B,YAAY,GAAG,8BAA8BC,MAAM,EAAE;UACvD;QACF,CAAC,MAAM;UACLD,YAAY,GAAG,sBAAsB+B,MAAM,MAAM9B,MAAM,EAAE;QAC3D;MACF,CAAC,MAAM,IAAIrB,KAAK,CAACmG,OAAO,EAAE;QACxB;QACA/E,YAAY,GAAG,+DAA+D;MAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAI/B,QAAQ,KAAK,cAAc,EAAE;QACtD;QACA8B,YAAY,GAAGpB,KAAK,CAACqB,MAAM;QAC3B,IAAIrB,KAAK,CAACmD,MAAM,KAAK,GAAG,IAAInD,KAAK,CAACiG,OAAO,EAAE;UACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;UAC7HvJ,SAAS,CAAC2J,cAAc,CAAC;;UAEzB;UACAR,WAAW,CAAC,CAAC;;UAEb;UACA/F,QAAQ,CAAC,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACL;QACA2B,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;MACtE;MAEA/E,OAAO,CAAC8E,YAAY,CAAC;IACvB,CAAC,SAAS;MACR1E,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2J,WAAW,GAAGA,CAAA,KAAM;IACxB,oBACErK,OAAA,CAACzD,GAAG;MAAA+N,QAAA,gBACFtK,OAAA,CAACvD,UAAU;QAAC8N,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb5K,OAAA,CAACxD,KAAK;QAACqO,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzBtK,OAAA,CAACvD,UAAU;UAAC8N,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5K,OAAA,CAACpD,IAAI;UAACoO,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAAZ,QAAA,gBAC7CtK,OAAA,CAACpD,IAAI;YAACuO,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACftK,OAAA,CAACtD,SAAS;cACR2O,SAAS;cACTC,KAAK,EAAC,SAAS;cACff,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAErG,WAAY;cACnB8J,QAAQ,EAAG3D,CAAC,IAAKlG,cAAc,CAACkG,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cAChD0D,WAAW,EAAC;YAAyB;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP5K,OAAA,CAACpD,IAAI;YAACuO,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACftK,OAAA,CAACrD,MAAM;cACL0O,SAAS;cACTd,OAAO,EAAC,WAAW;cACnBkB,KAAK,EAAC,SAAS;cACfC,OAAO,EAAEjF,oBAAqB;cAC9BkF,QAAQ,EAAEhL,WAAW,IAAI,CAACc,WAAW,CAACiF,IAAI,CAAC,CAAE;cAC7CkF,SAAS,EAAEjL,WAAW,gBAAGX,OAAA,CAAC7C,gBAAgB;gBAAC0O,IAAI,EAAE;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG5K,OAAA,CAAC1B,UAAU;gBAAAmM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAN,QAAA,EAC1E;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGR5K,OAAA,CAACxD,KAAK;QAACqO,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBtK,OAAA,CAACvD,UAAU;UAAC8N,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZjK,WAAW,gBACVX,OAAA,CAACzD,GAAG;UAACsO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DtK,OAAA,CAAC7C,gBAAgB;YAAAsN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJzJ,IAAI,CAAC2C,MAAM,KAAK,CAAC,gBACnB9D,OAAA,CAAC9C,KAAK;UAAC+O,QAAQ,EAAC,MAAM;UAAA3B,QAAA,EAAC;QAEvB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAER5K,OAAA,CAAC/B,IAAI;UAAC4M,EAAE,EAAE;YAAEqB,SAAS,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAA7B,QAAA,EAChDnJ,IAAI,CAACiL,GAAG,CAAExJ,IAAI,iBACb5C,OAAA,CAAC7D,KAAK,CAAC8D,QAAQ;YAAAqK,QAAA,gBACbtK,OAAA,CAAC9B,QAAQ;cAACmO,MAAM;cAACX,OAAO,EAAEA,CAAA,KAAMxE,gBAAgB,CAACtE,IAAI,CAAE;cAAA0H,QAAA,gBACrDtK,OAAA,CAAC7B,YAAY;gBACXmO,OAAO,eACLtM,OAAA,CAACzD,GAAG;kBAACsO,EAAE,EAAE;oBAAEiB,OAAO,EAAE,MAAM;oBAAEZ,UAAU,EAAE;kBAAS,CAAE;kBAAAZ,QAAA,gBACjDtK,OAAA,CAACvD,UAAU;oBAAC8N,OAAO,EAAC,WAAW;oBAAAD,QAAA,EAAE1H,IAAI,CAACb;kBAAO;oBAAA0I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,EAC1DnL,YAAY,CAACmD,IAAI,CAAC,gBACjB5C,OAAA,CAAC1C,IAAI;oBACHuO,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,OAAO;oBACbG,KAAK,EAAC,OAAO;oBACbZ,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,GACAlL,gBAAgB,CAACkD,IAAI,CAAC,gBACxB5C,OAAA,CAAC1C,IAAI;oBACHuO,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,YAAY;oBAClBG,KAAK,EAAC,SAAS;oBACfZ,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,gBAEF5K,OAAA,CAAC1C,IAAI;oBACHuO,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAE1I,IAAI,CAACmE,mBAAoB;oBAChC0E,KAAK,EAAE9L,kBAAkB,CAACiD,IAAI,CAACmE,mBAAmB,CAAE;oBACpD8D,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACD4B,SAAS,eACPxM,OAAA,CAAAE,SAAA;kBAAAoK,QAAA,gBACEtK,OAAA,CAACvD,UAAU;oBAAC8N,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GACzC1H,IAAI,CAAC0C,SAAS,IAAI,KAAK,EAAC,KAAG,EAAC1C,IAAI,CAAC4C,OAAO,IAAI,KAAK;kBAAA;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,eACb5K,OAAA;oBAAAyK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN5K,OAAA,CAACvD,UAAU;oBAAC8N,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GAAC,MACvC,EAAC1H,IAAI,CAAC8J,mBAAmB,IAAI,KAAK,EAAC,QAAM,EAAC9J,IAAI,CAAC+J,iBAAiB,IAAI,KAAK;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,eACb5K,OAAA;oBAAAyK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN5K,OAAA,CAACvD,UAAU;oBAAC8N,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GAAC,iBAC5B,EAAC1H,IAAI,CAAC2F,aAAa,IAAI,KAAK,EAAC,mBAAiB,EAAC3F,IAAI,CAACoE,eAAe,IAAI,GAAG;kBAAA;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC;gBAAA,eACb;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF5K,OAAA,CAAC5B,uBAAuB;gBAAAkM,QAAA,eACtBtK,OAAA,CAAC3C,UAAU;kBAACuP,IAAI,EAAC,KAAK;kBAAClB,OAAO,EAAG9D,CAAC,IAAK;oBACrCA,CAAC,CAACiF,eAAe,CAAC,CAAC,CAAC,CAAC;oBACrBrL,eAAe,CAACoB,IAAI,CAAC;oBACrBO,wBAAwB,CAAC,IAAI,CAAC;kBAChC,CAAE;kBAAAmH,QAAA,eACAtK,OAAA,CAAClB,QAAQ;oBAAA2L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACX5K,OAAA,CAAC/C,OAAO;cAAAwN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAxDQhI,IAAI,CAACb,OAAO;YAAA0I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyDjB,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMkC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACvL,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEvB,OAAA,CAACzD,GAAG;MAAA+N,QAAA,gBACFtK,OAAA,CAACvD,UAAU;QAAC8N,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb5K,OAAA,CAACb,eAAe;QACdyD,IAAI,EAAErB,YAAa;QACnBwL,OAAO,EAAE,IAAK;QACdhE,KAAK,EAAC;MAA+B;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAEF5K,OAAA,CAACxD,KAAK;QAACqO,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBtK,OAAA,CAACvD,UAAU;UAAC8N,OAAO,EAAC,WAAW;UAACC,YAAY;UAACK,EAAE,EAAE;YAAEmC,UAAU,EAAE;UAAO,CAAE;UAAA1C,QAAA,EAAC;QAEzE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGb5K,OAAA,CAACpD,IAAI;UAACoO,SAAS;UAACC,OAAO,EAAE,CAAE;UAACJ,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACxCtK,OAAA,CAACpD,IAAI;YAACuO,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6B,EAAE,EAAE,CAAE;YAAA3C,QAAA,eACvBtK,OAAA,CAACzD,GAAG;cAACsO,EAAE,EAAE;gBAAEC,CAAC,EAAE,CAAC;gBAAEoC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAO,CAAE;cAAA9C,QAAA,gBACrEtK,OAAA,CAACvD,UAAU;gBAAC8N,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEmC,UAAU,EAAE,MAAM;kBAAEvB,KAAK,EAAE;gBAAe,CAAE;gBAAAnB,QAAA,EAAC;cAEhG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5K,OAAA,CAACpD,IAAI;gBAACoO,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAX,QAAA,gBACzBtK,OAAA,CAACpD,IAAI;kBAACuO,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACftK,OAAA,CAACvD,UAAU;oBAAC8N,OAAO,EAAC,OAAO;oBAACM,EAAE,EAAE;sBAAEmC,UAAU,EAAE;oBAAS,CAAE;oBAAA1C,QAAA,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACP5K,OAAA,CAACpD,IAAI;kBAACuO,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACftK,OAAA,CAACvD,UAAU;oBAAC8N,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAE/I,YAAY,CAACgH,aAAa,IAAI,KAAK,EAAC,IAAE;kBAAA;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eACP5K,OAAA,CAACpD,IAAI;kBAACuO,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACftK,OAAA,CAACvD,UAAU;oBAAC8N,OAAO,EAAC,OAAO;oBAACM,EAAE,EAAE;sBAAEmC,UAAU,EAAE;oBAAS,CAAE;oBAAA1C,QAAA,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACP5K,OAAA,CAACpD,IAAI;kBAACuO,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACftK,OAAA,CAAC1C,IAAI;oBACHgO,KAAK,EAAE/J,YAAY,CAACwF,mBAAmB,IAAI,KAAM;oBACjD8E,IAAI,EAAC,OAAO;oBACZJ,KAAK,EAAE9L,kBAAkB,CAAC4B,YAAY,CAACwF,mBAAmB,CAAE;oBAC5DwD,OAAO,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEP5K,OAAA,CAACpD,IAAI;YAACuO,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6B,EAAE,EAAE,CAAE;YAAA3C,QAAA,eACvBtK,OAAA,CAACzD,GAAG;cAACsO,EAAE,EAAE;gBAAEC,CAAC,EAAE,CAAC;gBAAEoC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAO,CAAE;cAAA9C,QAAA,gBACrEtK,OAAA,CAACvD,UAAU;gBAAC8N,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEmC,UAAU,EAAE,MAAM;kBAAEvB,KAAK,EAAE;gBAAiB,CAAE;gBAAAnB,QAAA,EAAC;cAElG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZ/I,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,GAAG,CAAC,MAAM;gBACpE,MAAMY,MAAM,GAAGxB,MAAM,CAACyF,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACjE,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;gBACnE,OAAOY,MAAM,gBACX7C,OAAA,CAACpD,IAAI;kBAACoO,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAX,QAAA,gBACzBtK,OAAA,CAACpD,IAAI;oBAACuO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACftK,OAAA,CAACvD,UAAU;sBAAC8N,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEmC,UAAU,EAAE;sBAAS,CAAE;sBAAA1C,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC,eACP5K,OAAA,CAACpD,IAAI;oBAACuO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACftK,OAAA,CAACvD,UAAU;sBAAC8N,OAAO,EAAC,OAAO;sBAAAD,QAAA,EAAEjH,eAAe,CAACR,MAAM,CAACZ,SAAS;oBAAC;sBAAAwI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC,eACP5K,OAAA,CAACpD,IAAI;oBAACuO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACftK,OAAA,CAACvD,UAAU;sBAAC8N,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEmC,UAAU,EAAE;sBAAS,CAAE;sBAAA1C,QAAA,EAAC;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC,eACP5K,OAAA,CAACpD,IAAI;oBAACuO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACftK,OAAA,CAACvD,UAAU;sBAAC8N,OAAO,EAAC,OAAO;sBAAAD,QAAA,GAAEzH,MAAM,CAACsD,aAAa,IAAI,CAAC,EAAC,IAAE;oBAAA;sBAAAsE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACP5K,OAAA,CAACpD,IAAI;oBAACuO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACftK,OAAA,CAACvD,UAAU;sBAAC8N,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEmC,UAAU,EAAE;sBAAS,CAAE;sBAAA1C,QAAA,EAAC;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eACP5K,OAAA,CAACpD,IAAI;oBAACuO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACftK,OAAA,CAAC1C,IAAI;sBACHgO,KAAK,EAAEzI,MAAM,CAAC2D,YAAY,IAAI,KAAM;sBACpCqF,IAAI,EAAC,OAAO;sBACZJ,KAAK,EAAE7L,iBAAiB,CAACiD,MAAM,CAAC2D,YAAY,CAAE;sBAC9C+D,OAAO,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEP5K,OAAA,CAACvD,UAAU;kBAAC8N,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAC;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAC3D;cACH,CAAC,EAAE,CAAC,gBACF5K,OAAA,CAACvD,UAAU;gBAAC8N,OAAO,EAAC,OAAO;gBAAAD,QAAA,EACxBzI,QAAQ,CAACI,SAAS,KAAK,cAAc,GACpC,kDAAkD,GAClD;cAA4B;gBAAAwI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEP5K,OAAA,CAACzD,GAAG;UAACsO,EAAE,EAAE;YAAEwC,EAAE,EAAE,CAAC;YAAEtC,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACxBtK,OAAA,CAACvD,UAAU;YAAC8N,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEmC,UAAU,EAAE;YAAO,CAAE;YAAA1C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5K,OAAA,CAACtD,SAAS;YACRmP,IAAI,EAAC,OAAO;YACZR,SAAS;YACTC,KAAK,EAAC,cAAc;YACpBf,OAAO,EAAC,UAAU;YAClB1C,IAAI,EAAC,cAAc;YACnByF,IAAI,EAAC,QAAQ;YACbxF,KAAK,EAAEjG,QAAQ,CAACG,YAAa;YAC7BuJ,QAAQ,EAAE5D,gBAAiB;YAC3B3D,KAAK,EAAE,CAAC,CAAC9B,UAAU,CAACF,YAAa;YACjCuL,UAAU,EAAErL,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;YACjEwL,mBAAmB,EAAE;cACnB3C,EAAE,EAAE;gBAAEY,KAAK,EAAErJ,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACF6I,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELxI,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,IAAI,CAACyG,iBAAiB,iBAC1EzI,OAAA,CAAC9C,KAAK;UAAC+O,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,EACrClI,YAAY,CAACJ;QAAY;UAAAyI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACR,eAED5K,OAAA,CAAC9C,KAAK;UAAC+O,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,eACnCtK,OAAA,CAACvD,UAAU;YAAC8N,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAC;UAE5B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAM6C,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,MAAMC,iBAAiB,GAAIC,YAAY,IAAK;MAC1C,OAAO,IAAIvN,UAAU,KAAKuN,YAAY,EAAE;IAC1C,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAI/K,MAAM,IAAK;MACtC,IAAI,CAACA,MAAM,IAAI,CAAChB,QAAQ,CAACG,YAAY,EAAE,OAAO,IAAI;MAClD,OAAOqG,UAAU,CAACxF,MAAM,CAACsD,aAAa,CAAC,IAAIkC,UAAU,CAACxG,QAAQ,CAACG,YAAY,CAAC;IAC9E,CAAC;;IAED;IACA,MAAM6L,uBAAuB,GAAIjG,CAAC,IAAK;MACrC,MAAM+F,YAAY,GAAG/F,CAAC,CAACG,MAAM,CAACD,KAAK,CAACpB,IAAI,CAAC,CAAC;;MAE1C;MACA,IAAIiH,YAAY,CAAC/G,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QACtC9E,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb4L,eAAe,EAAE;QACnB,CAAC,CAAC;QACF;MACF;MAEA,IAAIH,YAAY,EAAE;QAChB;QACA,MAAMI,gBAAgB,GAAGL,iBAAiB,CAACC,YAAY,CAAC;;QAExD;QACA,MAAMK,eAAe,GAAG3M,MAAM,CAACyF,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACjE,SAAS,KAAK8L,gBAAgB,CAAC;QAE1E,IAAIC,eAAe,EAAE;UACnB;UACA,IAAIA,eAAe,CAACxH,YAAY,KAAK,MAAM,IAAIwH,eAAe,CAACxH,YAAY,KAAK,WAAW,EAAE;YAC3FrE,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb4L,eAAe,EAAE,aAAaH,YAAY,eAAeK,eAAe,CAACxH,YAAY;YACvF,CAAC,CAAC;YACF;UACF;;UAEA;UACA,IAAIjF,YAAY,EAAE;YAChB;YACA,MAAMqE,aAAa,GAAGF,MAAM,CAACnE,YAAY,CAAC+D,SAAS,IAAI,EAAE,CAAC;YAC1D,MAAMO,WAAW,GAAGH,MAAM,CAACnE,YAAY,CAACiE,OAAO,IAAI,GAAG,CAAC;YAEvD,MAAMyI,eAAe,GAAGvI,MAAM,CAACsI,eAAe,CAAC1I,SAAS,IAAI,EAAE,CAAC;YAC/D,MAAM4I,aAAa,GAAGxI,MAAM,CAACsI,eAAe,CAACxI,OAAO,IAAI,GAAG,CAAC;;YAE5D;YACA9B,OAAO,CAACC,GAAG,CAAC,iCAAiCqK,eAAe,CAAC/L,SAAS,GAAG,EAAE;cACzEqD,SAAS,EAAE,GAAG2I,eAAe,QAAQrI,aAAa,EAAE;cACpDuI,UAAU,EAAE,GAAGD,aAAa,QAAQrI,WAAW;YACjD,CAAC,CAAC;YAEF,IAAIoI,eAAe,KAAKrI,aAAa,IACjCsI,aAAa,KAAKrI,WAAW,EAAE;cACjC;cACAlD,uBAAuB,CAAC;gBACtBC,IAAI,EAAErB,YAAY;gBAClBsB,MAAM,EAAEmL;cACV,CAAC,CAAC;cACFzL,6BAA6B,CAAC,IAAI,CAAC;cACnC;YACF;UACF;;UAEA;UACA,IAAIqL,mBAAmB,CAACI,eAAe,CAAC,EAAE;YACxC;YACAlM,WAAW,CAAC;cACV,GAAGD,QAAQ;cACXI,SAAS,EAAE8L;YACb,CAAC,CAAC;YACF5L,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb4L,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACA3L,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb4L,eAAe,EAAE,aAAaH,YAAY,sCAAsCK,eAAe,CAAC7H,aAAa;YAC/G,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL;UACAhE,aAAa,CAAC;YACZ,GAAGD,UAAU;YACb4L,eAAe,EAAE,UAAUH,YAAY;UACzC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACA7L,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb4L,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,oBACE9N,OAAA,CAACzD,GAAG;MAAA+N,QAAA,gBACFtK,OAAA,CAACvD,UAAU;QAAC8N,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb5K,OAAA,CAACxD,KAAK;QAACqO,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBtK,OAAA,CAACvD,UAAU;UAAC8N,OAAO,EAAC,OAAO;UAAC6D,SAAS;UAAA9D,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZ/J,aAAa,gBACZb,OAAA,CAACzD,GAAG;UAACsO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DtK,OAAA,CAAC7C,gBAAgB;YAAAsN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAEN5K,OAAA,CAACzD,GAAG;UAAA+N,QAAA,gBACFtK,OAAA,CAACpD,IAAI;YAACoO,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBAEzBtK,OAAA,CAACpD,IAAI;cAACuO,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAA3C,QAAA,gBACvBtK,OAAA,CAACvD,UAAU;gBAAC8N,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEmC,UAAU,EAAE;gBAAO,CAAE;gBAAA1C,QAAA,EAAC;cAEzE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5K,OAAA,CAACtD,SAAS;gBACRmP,IAAI,EAAC,OAAO;gBACZR,SAAS;gBACTC,KAAK,EAAC,eAAe;gBACrBf,OAAO,EAAC,UAAU;gBAClBiB,WAAW,EAAC,oBAAoB;gBAChC+B,UAAU,EAAErL,UAAU,CAAC4L,eAAe,IAAI,uCAAwC;gBAClF9J,KAAK,EAAE,CAAC,CAAC9B,UAAU,CAAC4L,eAAgB;gBACpCO,MAAM,EAAER,uBAAwB;gBAChChD,EAAE,EAAE;kBAAEE,EAAE,EAAE;gBAAE;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACF5K,OAAA,CAACvD,UAAU;gBAAC8N,OAAO,EAAC,OAAO;gBAACM,EAAE,EAAE;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAT,QAAA,GAAC,aAC9B,eAAAtK,OAAA;kBAAAsK,QAAA,EAASzI,QAAQ,CAACI,SAAS,IAAI;gBAAG;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAGP5K,OAAA,CAACpD,IAAI;cAACuO,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAA3C,QAAA,gBACvBtK,OAAA,CAACvD,UAAU;gBAAC8N,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEmC,UAAU,EAAE;gBAAO,CAAE;gBAAA1C,QAAA,EAAC;cAEzE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5K,OAAA,CAACzD,GAAG;gBAAA+N,QAAA,gBACFtK,OAAA,CAACvD,UAAU;kBAAC8N,OAAO,EAAC,WAAW;kBAACC,YAAY;kBAACK,EAAE,EAAE;oBAAEY,KAAK,EAAE,cAAc;oBAAEuB,UAAU,EAAE,MAAM;oBAAEjC,EAAE,EAAE;kBAAE,CAAE;kBAAAT,QAAA,EACnG/I,YAAY,GAAG,4CAA4C,GAAG;gBAAsB;kBAAAkJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC,eAEb5K,OAAA,CAACnD,WAAW;kBAACwO,SAAS;kBAACQ,IAAI,EAAC,OAAO;kBAAC7H,KAAK,EAAE,CAAC,CAAC9B,UAAU,CAACD,SAAU;kBAAAqI,QAAA,gBAChEtK,OAAA,CAAClD,UAAU;oBAACwR,EAAE,EAAC,qBAAqB;oBAAAhE,QAAA,EAAC;kBAAgB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClE5K,OAAA,CAACjD,MAAM;oBACLwR,OAAO,EAAC,qBAAqB;oBAC7BD,EAAE,EAAC,eAAe;oBAClBzG,IAAI,EAAC,WAAW;oBAChBC,KAAK,EAAEjG,QAAQ,CAACI,SAAU;oBAC1BqJ,KAAK,EAAC,kBAAkB;oBACxBC,QAAQ,EAAE5D,gBAAiB;oBAAA2C,QAAA,gBAE3BtK,OAAA,CAAChD,QAAQ;sBAAC8K,KAAK,EAAC,cAAc;sBAAAwC,QAAA,gBAC5BtK,OAAA;wBAAAsK,QAAA,EAAQ;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,+BAC/B;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eACX5K,OAAA,CAAC/C,OAAO;sBAAAwN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACVvJ,MAAM,CAACyC,MAAM,GAAG,CAAC,gBAChB9D,OAAA,CAACzD,GAAG;sBAACkQ,SAAS,EAAC,IAAI;sBAAC5B,EAAE,EAAE;wBAAEC,CAAC,EAAE,CAAC;wBAAEoC,OAAO,EAAE;sBAAmB,CAAE;sBAAA5C,QAAA,eAC5DtK,OAAA,CAACvD,UAAU;wBAAC8N,OAAO,EAAC,SAAS;wBAACM,EAAE,EAAE;0BAAEmC,UAAU,EAAE,MAAM;0BAAEvB,KAAK,EAAE;wBAAe,CAAE;wBAAAnB,QAAA,GAC7EjJ,MAAM,CAACyC,MAAM,EAAC,6BACjB;sBAAA;wBAAA2G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,GACJ,IAAI,EACPvJ,MAAM,CAAC+K,GAAG,CAAEvJ,MAAM,iBACjB7C,OAAA,CAAChD,QAAQ;sBAEP8K,KAAK,EAAEjF,MAAM,CAACZ,SAAU;sBACxB0J,QAAQ,EAAE9I,MAAM,CAACsD,aAAa,GAAGkC,UAAU,CAACxG,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;sBACxE6I,EAAE,EAAE;wBACF,gBAAgB,EAAE;0BAAEqC,OAAO,EAAE;wBAAgB,CAAC;wBAC9C,sBAAsB,EAAE;0BAAEA,OAAO,EAAE;wBAAgB,CAAC;wBACpDA,OAAO,EAAE3L,YAAY,IACdsB,MAAM,CAACyC,SAAS,KAAK/D,YAAY,CAAC+D,SAAS,IAC3CI,MAAM,CAAC7C,MAAM,CAAC0C,YAAY,CAAC,KAAKG,MAAM,CAACnE,YAAY,CAACgE,YAAY,CAAC,IACjEG,MAAM,CAAC7C,MAAM,CAAC2C,OAAO,CAAC,KAAKE,MAAM,CAACnE,YAAY,CAACiE,OAAO,CAAC,GACvD,yBAAyB,GAAG;sBACrC,CAAE;sBAAA8E,QAAA,eAEFtK,OAAA,CAACzD,GAAG;wBAACsO,EAAE,EAAE;0BAAEiB,OAAO,EAAE,MAAM;0BAAE0C,aAAa,EAAE,QAAQ;0BAAEC,KAAK,EAAE;wBAAO,CAAE;wBAAAnE,QAAA,gBACnEtK,OAAA,CAACzD,GAAG;0BAACsO,EAAE,EAAE;4BAAEiB,OAAO,EAAE,MAAM;4BAAEC,cAAc,EAAE,eAAe;4BAAEb,UAAU,EAAE,QAAQ;4BAAEuD,KAAK,EAAE;0BAAO,CAAE;0BAAAnE,QAAA,gBACjGtK,OAAA,CAACvD,UAAU;4BAAC8N,OAAO,EAAC,OAAO;4BAACM,EAAE,EAAE;8BAAEmC,UAAU,EAAE;4BAAO,CAAE;4BAAA1C,QAAA,GACpDjH,eAAe,CAACR,MAAM,CAACZ,SAAS,CAAC,EAAC,KAAG,EAACY,MAAM,CAACyC,SAAS,IAAI,KAAK;0BAAA;4BAAAmF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtD,CAAC,EACZrJ,YAAY,IACZsB,MAAM,CAACyC,SAAS,KAAK/D,YAAY,CAAC+D,SAAS,IAC3CI,MAAM,CAAC7C,MAAM,CAAC0C,YAAY,CAAC,KAAKG,MAAM,CAACnE,YAAY,CAACgE,YAAY,CAAC,IACjEG,MAAM,CAAC7C,MAAM,CAAC2C,OAAO,CAAC,KAAKE,MAAM,CAACnE,YAAY,CAACiE,OAAO,CAAC,iBACtDxF,OAAA,CAAC1C,IAAI;4BACHuO,IAAI,EAAC,OAAO;4BACZP,KAAK,EAAC,aAAa;4BACnBG,KAAK,EAAC,SAAS;4BACflB,OAAO,EAAC,UAAU;4BAClBM,EAAE,EAAE;8BAAEuC,MAAM,EAAE,EAAE;8BAAEsB,QAAQ,EAAE;4BAAS;0BAAE;4BAAAjE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxC,CACF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACN5K,OAAA,CAACzD,GAAG;0BAACsO,EAAE,EAAE;4BAAEiB,OAAO,EAAE,MAAM;4BAAEC,cAAc,EAAE,eAAe;4BAAE0C,KAAK,EAAE;0BAAO,CAAE;0BAAAnE,QAAA,gBAC3EtK,OAAA,CAACvD,UAAU;4BAAC8N,OAAO,EAAC,SAAS;4BAAAD,QAAA,GAC1BzH,MAAM,CAAC0C,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC1C,MAAM,CAAC2C,OAAO,IAAI,KAAK;0BAAA;4BAAAiF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/C,CAAC,eACb5K,OAAA,CAACvD,UAAU;4BAAC8N,OAAO,EAAC,SAAS;4BAACM,EAAE,EAAE;8BAAEmC,UAAU,EAAE,MAAM;8BAAEvB,KAAK,EAAE5I,MAAM,CAACsD,aAAa,GAAGkC,UAAU,CAACxG,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;4BAAe,CAAE;4BAAAsI,QAAA,GAC5JzH,MAAM,CAACsD,aAAa,IAAI,CAAC,EAAC,gBAC7B;0BAAA;4BAAAsE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GAvCD/H,MAAM,CAACZ,SAAS;sBAAAwI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAwCb,CACX,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACT5K,OAAA,CAAC5C,cAAc;oBAAAkN,QAAA,EACZpI,UAAU,CAACD,SAAS,IAAI;kBAAsD;oBAAAwI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAEbvJ,MAAM,CAACyC,MAAM,KAAK,CAAC,IAAI,CAACjD,aAAa,iBACpCb,OAAA,CAAC9C,KAAK;kBAAC+O,QAAQ,EAAC,SAAS;kBAACpB,EAAE,EAAE;oBAAEwC,EAAE,EAAE,CAAC;oBAAEqB,QAAQ,EAAE;kBAAS,CAAE;kBAAApE,QAAA,EAAC;gBAE7D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP5K,OAAA,CAAC9C,KAAK;YAAC+O,QAAQ,EAAC,MAAM;YAACpB,EAAE,EAAE;cAAEwC,EAAE,EAAE;YAAE,CAAE;YAAA/C,QAAA,eACnCtK,OAAA,CAACvD,UAAU;cAAC8N,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBtK,OAAA;gBAAAsK,QAAA,EAAQ;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,iGACvB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,EAGA,CAAC/J,aAAa,IAAIgB,QAAQ,CAACI,SAAS,iBACnCjC,OAAA,CAACzD,GAAG;UAACsO,EAAE,EAAE;YAAEwC,EAAE,EAAE,CAAC;YAAEvC,CAAC,EAAE,CAAC;YAAEoC,OAAO,EAAE,kBAAkB;YAAEC,YAAY,EAAE,CAAC;YAAEwB,MAAM,EAAE;UAAoB,CAAE;UAAArE,QAAA,gBAClGtK,OAAA,CAACvD,UAAU;YAAC8N,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAF,QAAA,EAAC;UAE7C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ,CAAC,MAAM;YACN,MAAM/H,MAAM,GAAGxB,MAAM,CAACyF,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACjE,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;YACnE,IAAIY,MAAM,EAAE;cACV,oBACE7C,OAAA,CAACpD,IAAI;gBAACoO,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAX,QAAA,gBACzBtK,OAAA,CAACpD,IAAI;kBAACuO,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAAC6B,EAAE,EAAE,CAAE;kBAAA3C,QAAA,gBACvBtK,OAAA,CAACvD,UAAU;oBAAC8N,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBtK,OAAA;sBAAAsK,QAAA,EAAQ;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvH,eAAe,CAACR,MAAM,CAACZ,SAAS,CAAC;kBAAA;oBAAAwI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACb5K,OAAA,CAACvD,UAAU;oBAAC8N,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBtK,OAAA;sBAAAsK,QAAA,EAAQ;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC/H,MAAM,CAACyC,SAAS,IAAI,KAAK;kBAAA;oBAAAmF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACb5K,OAAA,CAACvD,UAAU;oBAAC8N,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBtK,OAAA;sBAAAsK,QAAA,EAAQ;oBAAW;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC/H,MAAM,CAAC0C,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC1C,MAAM,CAAC2C,OAAO,IAAI,KAAK;kBAAA;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACP5K,OAAA,CAACpD,IAAI;kBAACuO,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAAC6B,EAAE,EAAE,CAAE;kBAAA3C,QAAA,gBACvBtK,OAAA,CAACvD,UAAU;oBAAC8N,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBtK,OAAA;sBAAAsK,QAAA,EAAQ;oBAAa;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC/H,MAAM,CAAC+L,YAAY,IAAI,CAAC,EAAC,IAC3D;kBAAA;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb5K,OAAA,CAACvD,UAAU;oBAAC8N,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBtK,OAAA;sBAAAsK,QAAA,EAAQ;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC/H,MAAM,CAACsD,aAAa,IAAI,CAAC,EAAC,IAC7D;kBAAA;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb5K,OAAA,CAACvD,UAAU;oBAAC8N,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBtK,OAAA;sBAAAsK,QAAA,EAAQ;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC/H,MAAM,CAAC2D,YAAY,IAAI,KAAK;kBAAA;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAEX;YACA,oBACE5K,OAAA,CAACvD,UAAU;cAAC8N,OAAO,EAAC,OAAO;cAACkB,KAAK,EAAC,OAAO;cAAAnB,QAAA,EAAC;YAE1C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAEjB,CAAC,EAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,EAEAvJ,MAAM,CAACyC,MAAM,KAAK,CAAC,IAAI,CAACjD,aAAa,iBACpCb,OAAA,CAAC9C,KAAK;UAAC+O,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,EAAC;QAEzC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMiE,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,IAAIlB,YAAY,GAAG,SAAS;IAC5B,IAAImB,UAAU,GAAG,IAAI;IAErB,IAAIjN,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;MACzC0L,YAAY,GAAG,cAAc;IAC/B,CAAC,MAAM,IAAI9L,QAAQ,CAACI,SAAS,EAAE;MAC7B0L,YAAY,GAAGtK,eAAe,CAACxB,QAAQ,CAACI,SAAS,CAAC;MAClD;MACA6M,UAAU,GAAGzN,MAAM,CAACyF,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACjE,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;IACnE;;IAEA;IACA,MAAM2H,kBAAkB,GAAGH,2BAA2B,CAACpB,UAAU,CAACxG,QAAQ,CAACG,YAAY,CAAC,EAAET,YAAY,CAACgH,aAAa,CAAC;IAErH,oBACEvI,OAAA,CAACzD,GAAG;MAAA+N,QAAA,gBACFtK,OAAA,CAACvD,UAAU;QAAC8N,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb5K,OAAA,CAACxD,KAAK;QAACqO,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBtK,OAAA,CAACvD,UAAU;UAAC8N,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGb5K,OAAA,CAACb,eAAe;UACdyD,IAAI,EAAErB,YAAa;UACnBwL,OAAO,EAAE,IAAK;UACdhE,KAAK,EAAC;QAAmB;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAGF5K,OAAA,CAACzD,GAAG;UAACsO,EAAE,EAAE;YAAEwC,EAAE,EAAE,CAAC;YAAEvC,CAAC,EAAE,CAAC;YAAEoC,OAAO,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAA7C,QAAA,gBAC5DtK,OAAA,CAACvD,UAAU;YAAC8N,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEmC,UAAU,EAAE;YAAO,CAAE;YAAA1C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5K,OAAA,CAACpD,IAAI;YAACoO,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBACzBtK,OAAA,CAACpD,IAAI;cAACuO,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAA3C,QAAA,gBACvBtK,OAAA,CAACvD,UAAU;gBAAC8N,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBtK,OAAA;kBAAAsK,QAAA,EAAQ;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC/I,QAAQ,CAACG,YAAY,EAAC,IACxD;cAAA;gBAAAyI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5K,OAAA,CAACvD,UAAU;gBAAC8N,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBtK,OAAA;kBAAAsK,QAAA,EAAQ;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAChB,kBAAkB;cAAA;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACP5K,OAAA,CAACpD,IAAI;cAACuO,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAA3C,QAAA,gBACvBtK,OAAA,CAACvD,UAAU;gBAAC8N,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBtK,OAAA;kBAAAsK,QAAA,EAAQ;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC+C,YAAY;cAAA;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,EACZkE,UAAU,iBACT9O,OAAA,CAACvD,UAAU;gBAAC8N,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBtK,OAAA;kBAAAsK,QAAA,EAAQ;gBAAqB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACkE,UAAU,CAAC3I,aAAa,EAAC,IACnE;cAAA;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAELkE,UAAU,IAAIzG,UAAU,CAACxG,QAAQ,CAACG,YAAY,CAAC,GAAGqG,UAAU,CAACyG,UAAU,CAAC3I,aAAa,CAAC,IAAI,CAACsC,iBAAiB,iBAC3GzI,OAAA,CAAC9C,KAAK;UAAC+O,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,gBACtCtK,OAAA;YAAAsK,QAAA,EAAQ;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,qBAAiB,EAAC/I,QAAQ,CAACG,YAAY,EAAC,4CAA0C,EAAC8M,UAAU,CAAC3I,aAAa,EAAC,gDAE1I;QAAA;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,eAED5K,OAAA,CAAC9C,KAAK;UAAC+O,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,GAAC,8EAEpC,EAACzI,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,gFAAgF;QAAA;UAAAwI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3I,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMmE,cAAc,GAAIC,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAO3E,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOoD,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOX,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAO+B,WAAW,CAAC,CAAC;MAAE;MACxB;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;;EAED;EACA,MAAMI,4BAA4B,GAAGA,CAAA,KAAM;IACzClM,wBAAwB,CAAC,KAAK,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAID;EACA,MAAMiM,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIlM,eAAe,EAAE;MACnBxC,QAAQ,CAAC,mCAAmCJ,UAAU,IAAI4C,eAAe,CAACjB,OAAO,EAAE,CAAC;IACtF;IACAkN,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,wBAAwB,GAAGA,CAAA,KAAM;IACrCF,4BAA4B,CAAC,CAAC;IAC9B;IACAzN,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBR,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMkO,iCAAiC,GAAGA,CAAA,KAAM;IAC9C7M,6BAA6B,CAAC,KAAK,CAAC;IACpCI,uBAAuB,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAK,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMwM,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,MAAM;MAAEzM,IAAI;MAAEC;IAAO,CAAC,GAAGH,oBAAoB;IAC7C,IAAI,CAACE,IAAI,IAAI,CAACC,MAAM,EAAE;MACpBa,OAAO,CAACM,KAAK,CAAC,uCAAuC,EAAE;QAAEpB,IAAI;QAAEC;MAAO,CAAC,CAAC;MACxEvC,OAAO,CAAC,sCAAsC,CAAC;MAC/C;IACF;IAEA,IAAI;MACFI,UAAU,CAAC,IAAI,CAAC;MAChBgD,OAAO,CAACC,GAAG,CAAC,0CAA0Cf,IAAI,CAACb,OAAO,iCAAiCc,MAAM,CAACZ,SAAS,EAAE,CAAC;MACtHyB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEf,IAAI,CAAC;MACzDc,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEd,MAAM,CAAC;;MAEnC;MACA,MAAM7D,WAAW,CAACsQ,0BAA0B,CAAClP,UAAU,EAAEwC,IAAI,CAACb,OAAO,EAAEc,MAAM,CAACZ,SAAS,CAAC;;MAExF;MACA,MAAMuF,WAAW,GAAG,MAAMxI,WAAW,CAACuQ,WAAW,CAACnP,UAAU,EAAEwC,IAAI,CAACb,OAAO,CAAC;MAC3E2B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE6D,WAAW,CAAC;MAC5DhG,eAAe,CAACgG,WAAW,CAAC;;MAE5B;MACA1F,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXI,SAAS,EAAEY,MAAM,CAACZ;MACpB,CAAC,CAAC;;MAEF;MACA,MAAMmB,UAAU,CAAC,CAAC;MAElB/C,SAAS,CAAC,4BAA4BuC,IAAI,CAACb,OAAO,6CAA6Cc,MAAM,CAACZ,SAAS,EAAE,CAAC;MAClHmN,iCAAiC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOpL,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iEAAiE,EAAEA,KAAK,CAAC;MACvF1D,OAAO,CAAC,kEAAkE,IAAI0D,KAAK,CAACqB,MAAM,IAAIrB,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACvI,CAAC,SAAS;MACR1D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8O,uBAAuB,GAAGA,CAAA,KAAM;IACpCJ,iCAAiC,CAAC,CAAC;IACnC;IACAtN,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXI,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,oBACEjC,OAAA,CAACzD,GAAG;IAAA+N,QAAA,gBAEFtK,OAAA,CAACxD,KAAK;MAACqO,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,gBACzBtK,OAAA,CAACvD,UAAU;QAAC8N,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb5K,OAAA,CAACpD,IAAI;QAACoO,SAAS;QAACC,OAAO,EAAE,CAAE;QAACC,UAAU,EAAC,QAAQ;QAAAZ,QAAA,gBAC7CtK,OAAA,CAACpD,IAAI;UAACuO,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAd,QAAA,eACftK,OAAA,CAACtD,SAAS;YACR2O,SAAS;YACTC,KAAK,EAAC,SAAS;YACff,OAAO,EAAC,UAAU;YAClBzC,KAAK,EAAErG,WAAY;YACnB8J,QAAQ,EAAG3D,CAAC,IAAKlG,cAAc,CAACkG,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;YAChD0D,WAAW,EAAC;UAAyC;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP5K,OAAA,CAACpD,IAAI;UAACuO,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAd,QAAA,eACftK,OAAA,CAACrD,MAAM;YACL0O,SAAS;YACTd,OAAO,EAAC,WAAW;YACnBkB,KAAK,EAAC,SAAS;YACfC,OAAO,EAAEjF,oBAAqB;YAC9BkF,QAAQ,EAAEhL,WAAW,IAAI,CAACc,WAAW,CAACiF,IAAI,CAAC,CAAE;YAC7CkF,SAAS,EAAEjL,WAAW,gBAAGX,OAAA,CAAC7C,gBAAgB;cAAC0O,IAAI,EAAE;YAAG;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5K,OAAA,CAAC1B,UAAU;cAAAmM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAN,QAAA,EAC1E;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGP3J,iBAAiB,IAAIF,aAAa,CAAC+C,MAAM,GAAG,CAAC,iBAC5C9D,OAAA,CAACxD,KAAK;MAACqO,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,gBACzBtK,OAAA,CAACvD,UAAU;QAAC8N,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb5K,OAAA,CAAClC,cAAc;QAAAwM,QAAA,eACbtK,OAAA,CAACrC,KAAK;UAACkO,IAAI,EAAC,OAAO;UAAAvB,QAAA,gBACjBtK,OAAA,CAACjC,SAAS;YAAAuM,QAAA,eACRtK,OAAA,CAAChC,QAAQ;cAAC6M,EAAE,EAAE;gBAAEqC,OAAO,EAAE;cAAU,CAAE;cAAA5C,QAAA,gBACnCtK,OAAA,CAACnC,SAAS;gBAAAyM,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B5K,OAAA,CAACnC,SAAS;gBAAAyM,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC5K,OAAA,CAACnC,SAAS;gBAAAyM,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC5K,OAAA,CAACnC,SAAS;gBAAAyM,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC5K,OAAA,CAACnC,SAAS;gBAAAyM,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpC5K,OAAA,CAACnC,SAAS;gBAAAyM,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5B5K,OAAA,CAACnC,SAAS;gBAAAyM,QAAA,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ5K,OAAA,CAACpC,SAAS;YAAA0M,QAAA,EACPvJ,aAAa,CAACqL,GAAG,CAAExJ,IAAI,iBACtB5C,OAAA,CAAChC,QAAQ;cAAAsM,QAAA,gBACPtK,OAAA,CAACnC,SAAS;gBAAAyM,QAAA,EAAE1H,IAAI,CAACb;cAAO;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrC5K,OAAA,CAACnC,SAAS;gBAAAyM,QAAA,EAAE1H,IAAI,CAAC0C,SAAS,IAAI;cAAK;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChD5K,OAAA,CAACnC,SAAS;gBAAAyM,QAAA,EAAE1H,IAAI,CAAC4C,OAAO,IAAI;cAAK;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9C5K,OAAA,CAACnC,SAAS;gBAAAyM,QAAA,GAAC,MAAI,EAAC1H,IAAI,CAAC8J,mBAAmB,IAAI,KAAK,eAAC1M,OAAA;kBAAAyK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,OAAG,EAAChI,IAAI,CAAC+J,iBAAiB,IAAI,KAAK;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvG5K,OAAA,CAACnC,SAAS;gBAAAyM,QAAA,GAAE1H,IAAI,CAAC2F,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACtD5K,OAAA,CAACnC,SAAS;gBAAAyM,QAAA,eACRtK,OAAA,CAAC1C,IAAI;kBACHgO,KAAK,EAAE1I,IAAI,CAACmE,mBAAmB,IAAI,KAAM;kBACzC8E,IAAI,EAAC,OAAO;kBACZJ,KAAK,EAAE9L,kBAAkB,CAACiD,IAAI,CAACmE,mBAAmB,CAAE;kBACpDwD,OAAO,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ5K,OAAA,CAACnC,SAAS;gBAAAyM,QAAA,eACRtK,OAAA,CAACrD,MAAM;kBACLkP,IAAI,EAAC,OAAO;kBACZtB,OAAO,EAAC,WAAW;kBACnBkB,KAAK,EAAC,SAAS;kBACfC,OAAO,EAAEA,CAAA,KAAMxE,gBAAgB,CAACtE,IAAI,CAAE;kBACtC+I,QAAQ,EAAEjM,gBAAgB,CAACkD,IAAI,CAAE;kBAAA0H,QAAA,EAClC;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GAxBChI,IAAI,CAACb,OAAO;cAAA0I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CACR,EAGArJ,YAAY,iBACXvB,OAAA,CAACxD,KAAK;MAACqO,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,gBAClBtK,OAAA,CAACvD,UAAU;QAAC8N,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb5K,OAAA,CAACzD,GAAG;QAACsO,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEoC,OAAO,EAAE,SAAS;UAAEC,YAAY,EAAE,CAAC;UAAEpC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBAC5DtK,OAAA,CAACvD,UAAU;UAAC8N,OAAO,EAAC,WAAW;UAACC,YAAY;UAACK,EAAE,EAAE;YAAEmC,UAAU,EAAE,MAAM;YAAEvB,KAAK,EAAE;UAAe,CAAE;UAAAnB,QAAA,GAAC,oBAC5E,EAAC/I,YAAY,CAACQ,OAAO;QAAA;UAAA0I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACb5K,OAAA,CAACpD,IAAI;UAACoO,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAX,QAAA,gBACzBtK,OAAA,CAACpD,IAAI;YAACuO,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6B,EAAE,EAAE,CAAE;YAAA3C,QAAA,gBACvBtK,OAAA,CAACvD,UAAU;cAAC8N,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACtK,OAAA;gBAAAsK,QAAA,EAAQ;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACrJ,YAAY,CAAC+D,SAAS,IAAI,KAAK;YAAA;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACtG5K,OAAA,CAACvD,UAAU;cAAC8N,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACtK,OAAA;gBAAAsK,QAAA,EAAQ;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACrJ,YAAY,CAACiE,OAAO,IAAI,KAAK;YAAA;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACrG5K,OAAA,CAACvD,UAAU;cAAC8N,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACtK,OAAA;gBAAAsK,QAAA,EAAQ;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACrJ,YAAY,CAACgH,aAAa,IAAI,KAAK,EAAC,IAAE;YAAA;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5G,CAAC,eACP5K,OAAA,CAACpD,IAAI;YAACuO,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6B,EAAE,EAAE,CAAE;YAAA3C,QAAA,gBACvBtK,OAAA,CAACvD,UAAU;cAAC8N,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACtK,OAAA;gBAAAsK,QAAA,EAAQ;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACrJ,YAAY,CAACmL,mBAAmB,IAAI,KAAK;YAAA;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC1H5K,OAAA,CAACvD,UAAU;cAAC8N,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACtK,OAAA;gBAAAsK,QAAA,EAAQ;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACrJ,YAAY,CAACoL,iBAAiB,IAAI,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACtH5K,OAAA,CAACvD,UAAU;cAAC8N,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBtK,OAAA;gBAAAsK,QAAA,EAAQ;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvB5K,OAAA,CAAC1C,IAAI;gBACHgO,KAAK,EAAE/J,YAAY,CAACwF,mBAAmB,IAAI,KAAM;gBACjD8E,IAAI,EAAC,OAAO;gBACZJ,KAAK,EAAE9L,kBAAkB,CAAC4B,YAAY,CAACwF,mBAAmB,CAAE;gBAC5DwD,OAAO,EAAC,UAAU;gBAClBM,EAAE,EAAE;kBAAE0B,EAAE,EAAE;gBAAE;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN5K,OAAA,CAACpD,IAAI;QAACoO,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAX,QAAA,gBAEzBtK,OAAA,CAACpD,IAAI;UAACuO,IAAI;UAACC,EAAE,EAAE,EAAG;UAAC6B,EAAE,EAAE,CAAE;UAAA3C,QAAA,gBACvBtK,OAAA,CAACvD,UAAU;YAAC8N,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEmC,UAAU,EAAE;YAAO,CAAE;YAAA1C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5K,OAAA,CAACtD,SAAS;YACRmP,IAAI,EAAC,OAAO;YACZR,SAAS;YACTC,KAAK,EAAC,cAAc;YACpBf,OAAO,EAAC,UAAU;YAClB1C,IAAI,EAAC,cAAc;YACnByF,IAAI,EAAC,QAAQ;YACbxF,KAAK,EAAEjG,QAAQ,CAACG,YAAa;YAC7BuJ,QAAQ,EAAE5D,gBAAiB;YAC3B3D,KAAK,EAAE,CAAC,CAAC9B,UAAU,CAACF,YAAa;YACjCuL,UAAU,EAAErL,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;YACjEwL,mBAAmB,EAAE;cACnB3C,EAAE,EAAE;gBAAEY,KAAK,EAAErJ,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACF6I,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EACDxI,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,iBACpDhC,OAAA,CAAC9C,KAAK;YAAC+O,QAAQ,EAAC,SAAS;YAACpB,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,EACrClI,YAAY,CAACJ;UAAY;YAAAyI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAGP5K,OAAA,CAACpD,IAAI;UAACuO,IAAI;UAACC,EAAE,EAAE,EAAG;UAAC6B,EAAE,EAAE,CAAE;UAAA3C,QAAA,gBACvBtK,OAAA,CAACvD,UAAU;YAAC8N,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEmC,UAAU,EAAE;YAAO,CAAE;YAAA1C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ/J,aAAa,gBACZb,OAAA,CAACzD,GAAG;YAACsO,EAAE,EAAE;cAAEiB,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA1B,QAAA,eAC5DtK,OAAA,CAAC7C,gBAAgB;cAAAsN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,gBAEN5K,OAAA,CAACzD,GAAG;YAAA+N,QAAA,gBACFtK,OAAA,CAACvD,UAAU;cAAC8N,OAAO,EAAC,WAAW;cAACC,YAAY;cAACK,EAAE,EAAE;gBAAEY,KAAK,EAAE,cAAc;gBAAEuB,UAAU,EAAE,MAAM;gBAAEjC,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,EACnG/I,YAAY,GAAG,4CAA4C,GAAG;YAAsB;cAAAkJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,eAEb5K,OAAA,CAACnD,WAAW;cAACwO,SAAS;cAACQ,IAAI,EAAC,OAAO;cAAC7H,KAAK,EAAE,CAAC,CAAC9B,UAAU,CAACD,SAAU;cAAAqI,QAAA,gBAChEtK,OAAA,CAAClD,UAAU;gBAACwR,EAAE,EAAC,qBAAqB;gBAAAhE,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClE5K,OAAA,CAACjD,MAAM;gBACLwR,OAAO,EAAC,qBAAqB;gBAC7BD,EAAE,EAAC,eAAe;gBAClBzG,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAEjG,QAAQ,CAACI,SAAU;gBAC1BqJ,KAAK,EAAC,kBAAkB;gBACxBC,QAAQ,EAAE5D,gBAAiB;gBAAA2C,QAAA,gBAE3BtK,OAAA,CAAChD,QAAQ;kBAAC8K,KAAK,EAAC,cAAc;kBAAAwC,QAAA,gBAC5BtK,OAAA;oBAAAsK,QAAA,EAAQ;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,+BAC/B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACX5K,OAAA,CAAC/C,OAAO;kBAAAwN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACVvJ,MAAM,CAACyC,MAAM,GAAG,CAAC,gBAChB9D,OAAA,CAACzD,GAAG;kBAACkQ,SAAS,EAAC,IAAI;kBAAC5B,EAAE,EAAE;oBAAEC,CAAC,EAAE,CAAC;oBAAEoC,OAAO,EAAE;kBAAmB,CAAE;kBAAA5C,QAAA,eAC5DtK,OAAA,CAACvD,UAAU;oBAAC8N,OAAO,EAAC,SAAS;oBAACM,EAAE,EAAE;sBAAEmC,UAAU,EAAE,MAAM;sBAAEvB,KAAK,EAAE;oBAAe,CAAE;oBAAAnB,QAAA,GAC7EjJ,MAAM,CAACyC,MAAM,EAAC,qBACf,EAACvC,YAAY,iBACXvB,OAAA,CAAAE,SAAA;sBAAAoK,QAAA,GAAE,IACA,eAAAtK,OAAA;wBAAMyP,KAAK,EAAE;0BAAEhE,KAAK,EAAEpK,MAAM,CAACqO,IAAI,CAAC7M,MAAM,IACtCA,MAAM,CAACyC,SAAS,KAAK/D,YAAY,CAAC+D,SAAS,IAC3CI,MAAM,CAAC7C,MAAM,CAAC2C,OAAO,CAAC,KAAKE,MAAM,CAACnE,YAAY,CAACiE,OAAO,CAAC,CAAC,GAAG,OAAO,GAAG;wBAAS,CAAE;wBAAA8E,QAAA,GAC/EjJ,MAAM,CAACkF,MAAM,CAAC1D,MAAM,IACnBA,MAAM,CAACyC,SAAS,KAAK/D,YAAY,CAAC+D,SAAS,IAC3CI,MAAM,CAAC7C,MAAM,CAAC2C,OAAO,CAAC,KAAKE,MAAM,CAACnE,YAAY,CAACiE,OAAO,CACxD,CAAC,CAAC1B,MAAM,EAAC,cACX;sBAAA;wBAAA2G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA,eACP,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,GACJ,IAAI,EACPvJ,MAAM,CAAC+K,GAAG,CAAEvJ,MAAM,iBACjB7C,OAAA,CAAChD,QAAQ;kBAEP8K,KAAK,EAAEjF,MAAM,CAACZ,SAAU;kBACxB0J,QAAQ,EAAE9I,MAAM,CAACsD,aAAa,GAAGkC,UAAU,CAACxG,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;kBACxE6I,EAAE,EAAE;oBACF,gBAAgB,EAAE;sBAAEqC,OAAO,EAAE;oBAAgB,CAAC;oBAC9C,sBAAsB,EAAE;sBAAEA,OAAO,EAAE;oBAAgB,CAAC;oBACpDA,OAAO,EAAE3L,YAAY,IACdsB,MAAM,CAACyC,SAAS,KAAK/D,YAAY,CAAC+D,SAAS,IAC3CI,MAAM,CAAC7C,MAAM,CAAC2C,OAAO,CAAC,KAAKE,MAAM,CAACnE,YAAY,CAACiE,OAAO,CAAC,GACvD,yBAAyB,GAAG,SAAS;oBAC5CmJ,MAAM,EAAEpN,YAAY,IACbsB,MAAM,CAACyC,SAAS,KAAK/D,YAAY,CAAC+D,SAAS,IAC3CI,MAAM,CAAC7C,MAAM,CAAC2C,OAAO,CAAC,KAAKE,MAAM,CAACnE,YAAY,CAACiE,OAAO,CAAC,GACvD,mBAAmB,GAAG,MAAM;oBACnC2H,YAAY,EAAE,KAAK;oBACnBwC,MAAM,EAAE;kBACV,CAAE;kBAAArF,QAAA,eAEFtK,OAAA,CAACzD,GAAG;oBAACsO,EAAE,EAAE;sBAAEiB,OAAO,EAAE,MAAM;sBAAE0C,aAAa,EAAE,QAAQ;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBAAAnE,QAAA,gBACnEtK,OAAA,CAACzD,GAAG;sBAACsO,EAAE,EAAE;wBAAEiB,OAAO,EAAE,MAAM;wBAAEC,cAAc,EAAE,eAAe;wBAAEb,UAAU,EAAE,QAAQ;wBAAEuD,KAAK,EAAE;sBAAO,CAAE;sBAAAnE,QAAA,gBACjGtK,OAAA,CAACvD,UAAU;wBAAC8N,OAAO,EAAC,OAAO;wBAACM,EAAE,EAAE;0BAAEmC,UAAU,EAAE;wBAAO,CAAE;wBAAA1C,QAAA,GACpDjH,eAAe,CAACR,MAAM,CAACZ,SAAS,CAAC,EAAC,KAAG,EAACY,MAAM,CAACyC,SAAS,IAAI,KAAK;sBAAA;wBAAAmF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC,EACZrJ,YAAY,IACZsB,MAAM,CAACyC,SAAS,KAAK/D,YAAY,CAAC+D,SAAS,IAC3CI,MAAM,CAAC7C,MAAM,CAAC2C,OAAO,CAAC,KAAKE,MAAM,CAACnE,YAAY,CAACiE,OAAO,CAAC,iBACtDxF,OAAA,CAAC1C,IAAI;wBACHuO,IAAI,EAAC,OAAO;wBACZP,KAAK,EAAC,aAAa;wBACnBG,KAAK,EAAC,SAAS;wBACflB,OAAO,EAAC,UAAU;wBAClBM,EAAE,EAAE;0BAAEuC,MAAM,EAAE,EAAE;0BAAEsB,QAAQ,EAAE;wBAAS;sBAAE;wBAAAjE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CACF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACN5K,OAAA,CAACzD,GAAG;sBAACsO,EAAE,EAAE;wBAAEiB,OAAO,EAAE,MAAM;wBAAEC,cAAc,EAAE,eAAe;wBAAE0C,KAAK,EAAE;sBAAO,CAAE;sBAAAnE,QAAA,gBAC3EtK,OAAA,CAACvD,UAAU;wBAAC8N,OAAO,EAAC,SAAS;wBAAAD,QAAA,EAC1BzH,MAAM,CAAC2C,OAAO,IAAI;sBAAK;wBAAAiF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC,eACb5K,OAAA,CAACvD,UAAU;wBAAC8N,OAAO,EAAC,SAAS;wBAACM,EAAE,EAAE;0BAAEmC,UAAU,EAAE,MAAM;0BAAEvB,KAAK,EAAE5I,MAAM,CAACsD,aAAa,GAAGkC,UAAU,CAACxG,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;wBAAe,CAAE;wBAAAsI,QAAA,GAC5JzH,MAAM,CAACsD,aAAa,IAAI,CAAC,EAAC,gBAC7B;sBAAA;wBAAAsE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GA3CD/H,MAAM,CAACZ,SAAS;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4Cb,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACT5K,OAAA,CAAC5C,cAAc;gBAAAkN,QAAA,EACZpI,UAAU,CAACD,SAAS,IAAI;cAAsD;gBAAAwI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAEbvJ,MAAM,CAACyC,MAAM,KAAK,CAAC,IAAI,CAACjD,aAAa,iBACpCb,OAAA,CAAC9C,KAAK;cAAC+O,QAAQ,EAAC,SAAS;cAACpB,EAAE,EAAE;gBAAEwC,EAAE,EAAE,CAAC;gBAAEqB,QAAQ,EAAE;cAAS,CAAE;cAAApE,QAAA,EAAC;YAE7D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR,EAEAvJ,MAAM,CAACyC,MAAM,GAAG,CAAC,IAAIvC,YAAY,IAAI,CAACF,MAAM,CAACqO,IAAI,CAAC7M,MAAM,IACvDA,MAAM,CAACyC,SAAS,KAAK/D,YAAY,CAAC+D,SAAS,IAC3CI,MAAM,CAAC7C,MAAM,CAAC0C,YAAY,CAAC,KAAKG,MAAM,CAACnE,YAAY,CAACgE,YAAY,CAAC,IACjEG,MAAM,CAAC7C,MAAM,CAAC2C,OAAO,CAAC,KAAKE,MAAM,CAACnE,YAAY,CAACiE,OAAO,CACxD,CAAC,iBACCxF,OAAA,CAAC9C,KAAK;cAAC+O,QAAQ,EAAC,MAAM;cAACpB,EAAE,EAAE;gBAAEwC,EAAE,EAAE,CAAC;gBAAEqB,QAAQ,EAAE;cAAS,CAAE;cAAApE,QAAA,gBACvDtK,OAAA;gBAAAsK,QAAA,EAAQ;cAAmC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,qBACpD,eAAA5K,OAAA;gBAAIyP,KAAK,EAAE;kBAAEE,MAAM,EAAE,OAAO;kBAAEC,WAAW,EAAE;gBAAO,CAAE;gBAAAtF,QAAA,gBAClDtK,OAAA;kBAAAsK,QAAA,EAAI;gBAA2D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpE5K,OAAA;kBAAAsK,QAAA,EAAI;gBAAsG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGA,CAAC/J,aAAa,IAAIgB,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,CAAC,MAAM;YACvF,MAAMY,MAAM,GAAGxB,MAAM,CAACyF,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACjE,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;YACnE,IAAIY,MAAM,EAAE;cACV,oBACE7C,OAAA,CAACzD,GAAG;gBAACsO,EAAE,EAAE;kBAAEwC,EAAE,EAAE,CAAC;kBAAEvC,CAAC,EAAE,CAAC;kBAAEoC,OAAO,EAAE,SAAS;kBAAEC,YAAY,EAAE;gBAAE,CAAE;gBAAA7C,QAAA,gBAC5DtK,OAAA,CAACvD,UAAU;kBAAC8N,OAAO,EAAC,OAAO;kBAAAD,QAAA,gBAACtK,OAAA;oBAAAsK,QAAA,EAAQ;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACvH,eAAe,CAACR,MAAM,CAACZ,SAAS,CAAC;gBAAA;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACrG5K,OAAA,CAACvD,UAAU;kBAAC8N,OAAO,EAAC,OAAO;kBAAAD,QAAA,gBAACtK,OAAA;oBAAAsK,QAAA,EAAQ;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC/H,MAAM,CAACsD,aAAa,IAAI,CAAC,EAAC,IAAE;gBAAA;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtG5K,OAAA,CAACvD,UAAU;kBAAC8N,OAAO,EAAC,OAAO;kBAAAD,QAAA,gBACzBtK,OAAA;oBAAAsK,QAAA,EAAQ;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvB5K,OAAA,CAAC1C,IAAI;oBACHgO,KAAK,EAAEzI,MAAM,CAAC2D,YAAY,IAAI,KAAM;oBACpCqF,IAAI,EAAC,OAAO;oBACZJ,KAAK,EAAE7L,iBAAiB,CAACiD,MAAM,CAAC2D,YAAY,CAAE;oBAC9C+D,OAAO,EAAC,UAAU;oBAClBM,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAEV;YACA,OAAO,IAAI;UACb,CAAC,EAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP5K,OAAA,CAACzD,GAAG;QAACsO,EAAE,EAAE;UAAEwC,EAAE,EAAE,CAAC;UAAEvB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAzB,QAAA,gBACnEtK,OAAA,CAACrD,MAAM;UACL4N,OAAO,EAAC,UAAU;UAClBkB,KAAK,EAAC,WAAW;UACjBC,OAAO,EAAEA,CAAA,KAAM;YACblK,eAAe,CAAC,IAAI,CAAC;YACrBM,WAAW,CAAC;cACVC,OAAO,EAAE,EAAE;cACXC,YAAY,EAAE,EAAE;cAChBC,SAAS,EAAE;YACb,CAAC,CAAC;UACJ,CAAE;UACF2J,SAAS,eAAE5L,OAAA,CAACtB,UAAU;YAAA+L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Be,QAAQ,EAAElL,OAAQ;UAAA6J,QAAA,EACnB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET5K,OAAA,CAACrD,MAAM;UACL4N,OAAO,EAAC,WAAW;UACnBkB,KAAK,EAAC,SAAS;UACfC,OAAO,EAAE/B,YAAa;UACtBkG,OAAO,eAAE7P,OAAA,CAACxB,QAAQ;YAAAiM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBe,QAAQ,EAAElL,OAAO,IAAI,CAACoB,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,SAAU;UAAAqI,QAAA,EAElE7J,OAAO,gBAAGT,OAAA,CAAC7C,gBAAgB;YAAC0O,IAAI,EAAE;UAAG;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGD5K,OAAA,CAACzC,MAAM;MAACuS,IAAI,EAAEnH,iBAAkB;MAACoH,OAAO,EAAEA,CAAA,KAAMnH,oBAAoB,CAAC,KAAK,CAAE;MAACoH,QAAQ,EAAC,IAAI;MAAC3E,SAAS;MAAAf,QAAA,gBAClGtK,OAAA,CAACxC,WAAW;QAACqN,EAAE,EAAE;UAAEqC,OAAO,EAAE;QAAgB,CAAE;QAAA5C,QAAA,eAC5CtK,OAAA,CAACzD,GAAG;UAACsO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAE+E,GAAG,EAAE;UAAE,CAAE;UAAA3F,QAAA,gBACzDtK,OAAA,CAACpB,WAAW;YAAC6M,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B5K,OAAA,CAACvD,UAAU;YAAC8N,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAEzB,kBAAkB,CAACE;UAAK;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd5K,OAAA,CAACvC,aAAa;QAAA6M,QAAA,eACZtK,OAAA,CAACvD,UAAU;UAAC8N,OAAO,EAAC,OAAO;UAACM,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,EACvCzB,kBAAkB,CAACzE;QAAO;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChB5K,OAAA,CAACtC,aAAa;QAAA4M,QAAA,gBACZtK,OAAA,CAACrD,MAAM;UAAC+O,OAAO,EAAEA,CAAA,KAAM9C,oBAAoB,CAAC,KAAK,CAAE;UAAC6C,KAAK,EAAC,WAAW;UAAClB,OAAO,EAAC,UAAU;UAAAD,QAAA,EAAC;QAEzF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5K,OAAA,CAACrD,MAAM;UACL+O,OAAO,EAAEA,CAAA,KAAM;YACb9C,oBAAoB,CAAC,KAAK,CAAC;YAC3BC,kBAAkB,CAACG,SAAS,CAAC,CAAC;UAChC,CAAE;UACFyC,KAAK,EAAC,SAAS;UACflB,OAAO,EAAC,WAAW;UACnB2F,SAAS;UAAA5F,QAAA,EACV;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT5K,OAAA,CAACzC,MAAM;MAACuS,IAAI,EAAEhN,qBAAsB;MAACiN,OAAO,EAAEd,4BAA6B;MAACe,QAAQ,EAAC,IAAI;MAAC3E,SAAS;MAAAf,QAAA,gBACjGtK,OAAA,CAACxC,WAAW;QAACqN,EAAE,EAAE;UAAEqC,OAAO,EAAE;QAAgB,CAAE;QAAA5C,QAAA,eAC5CtK,OAAA,CAACzD,GAAG;UAACsO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAE+E,GAAG,EAAE;UAAE,CAAE;UAAA3F,QAAA,gBACzDtK,OAAA,CAACpB,WAAW;YAAC6M,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B5K,OAAA,CAACvD,UAAU;YAAC8N,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd5K,OAAA,CAACvC,aAAa;QAAA6M,QAAA,EACXtH,eAAe,iBACdhD,OAAA,CAACzD,GAAG;UAACsO,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,gBACjBtK,OAAA,CAACvD,UAAU;YAAC8N,OAAO,EAAC,OAAO;YAAC6D,SAAS;YAAA9D,QAAA,GAAC,UAC5B,eAAAtK,OAAA;cAAAsK,QAAA,EAAStH,eAAe,CAACjB;YAAO;cAAA0I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,4BAAqB,EAAC5H,eAAe,CAACgE,eAAe,IAAI,CAAC,EAAC,KAC/G;UAAA;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5K,OAAA,CAACvD,UAAU;YAAC8N,OAAO,EAAC,OAAO;YAAC6D,SAAS;YAAA9D,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5K,OAAA,CAACvD,UAAU;YAAC8N,OAAO,EAAC,OAAO;YAACkC,SAAS,EAAC,IAAI;YAAAnC,QAAA,gBACxCtK,OAAA;cAAAsK,QAAA,EAAI;YAAsC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C5K,OAAA;cAAAsK,QAAA,EAAI;YAAyB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC5K,OAAA;cAAAsK,QAAA,EAAI;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChB5K,OAAA,CAACtC,aAAa;QAACmN,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEiB,cAAc,EAAE;QAAgB,CAAE;QAAAzB,QAAA,gBAC3DtK,OAAA,CAACrD,MAAM;UAAC+O,OAAO,EAAEuD,4BAA6B;UAACxD,KAAK,EAAC,WAAW;UAAAnB,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5K,OAAA,CAACzD,GAAG;UAAA+N,QAAA,gBACFtK,OAAA,CAACrD,MAAM;YAAC+O,OAAO,EAAEyD,wBAAyB;YAAC1D,KAAK,EAAC,SAAS;YAACZ,EAAE,EAAE;cAAEsF,EAAE,EAAE;YAAE,CAAE;YAAA7F,QAAA,EAAC;UAE1E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5K,OAAA,CAACrD,MAAM;YAAC+O,OAAO,EAAEwD,gBAAiB;YAAC3E,OAAO,EAAC,WAAW;YAACkB,KAAK,EAAC,SAAS;YAAAnB,QAAA,EAAC;UAEvE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT5K,OAAA,CAACd,sBAAsB;MACrB4Q,IAAI,EAAExN,0BAA2B;MACjCyN,OAAO,EAAEX,iCAAkC;MAC3CxM,IAAI,EAAEF,oBAAoB,CAACE,IAAK;MAChCC,MAAM,EAAEH,oBAAoB,CAACG,MAAO;MACpCuN,YAAY,EAAEf,2BAA4B;MAC1CgB,mBAAmB,EAAEb;IAAwB;MAAA/E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eAGF5K,OAAA,CAACzC,MAAM;MACLuS,IAAI,EAAE5M,qBAAsB;MAC5B6M,OAAO,EAAEA,CAAA,KAAM5M,wBAAwB,CAAC,KAAK,CAAE;MAC/C6M,QAAQ,EAAC,IAAI;MACb3E,SAAS;MAAAf,QAAA,gBAETtK,OAAA,CAACxC,WAAW;QAAA8M,QAAA,eACVtK,OAAA,CAACzD,GAAG;UAACsO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAE+E,GAAG,EAAE;UAAE,CAAE;UAAA3F,QAAA,gBACzDtK,OAAA,CAAClB,QAAQ;YAAC2M,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5B5K,OAAA,CAACvD,UAAU;YAAC8N,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd5K,OAAA,CAACvC,aAAa;QAAA6M,QAAA,eACZtK,OAAA,CAACb,eAAe;UAACyD,IAAI,EAAErB;QAAa;UAAAkJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAChB5K,OAAA,CAACtC,aAAa;QAAA4M,QAAA,eACZtK,OAAA,CAACrD,MAAM;UAAC+O,OAAO,EAAEA,CAAA,KAAMvI,wBAAwB,CAAC,KAAK,CAAE;UAACsI,KAAK,EAAC,SAAS;UAAAnB,QAAA,EAAC;QAExE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACrK,EAAA,CApgEIJ,kBAAkB;EAAA,QACLpB,WAAW;AAAA;AAAAuR,EAAA,GADxBnQ,kBAAkB;AAsgExB,eAAeA,kBAAkB;AAAC,IAAAmQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}