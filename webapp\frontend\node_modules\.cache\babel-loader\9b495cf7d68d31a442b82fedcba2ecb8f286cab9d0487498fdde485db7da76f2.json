{"ast": null, "code": "export { PickersShortcuts } from \"./PickersShortcuts.js\";", "map": {"version": 3, "names": ["PickersShortcuts"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/PickersShortcuts/index.js"], "sourcesContent": ["export { PickersShortcuts } from \"./PickersShortcuts.js\";"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}