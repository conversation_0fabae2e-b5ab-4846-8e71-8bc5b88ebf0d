{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M18 11H6V4h7v5h5z\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M18 20H6v-3H4v3c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-3h-2zM6 4h7v5h5v2h2V8l-6-6H6c-1.1 0-2 .9-2 2v7h2zm3 9h6v2H9zm8 0h6v2h-6zM1 13h6v2H1z\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M6 17h12v3H6z\",\n  opacity: \".3\"\n}, \"2\")], 'InsertPageBreakTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "opacity"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/icons-material/esm/InsertPageBreakTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M18 11H6V4h7v5h5z\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M18 20H6v-3H4v3c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-3h-2zM6 4h7v5h5v2h2V8l-6-6H6c-1.1 0-2 .9-2 2v7h2zm3 9h6v2H9zm8 0h6v2h-6zM1 13h6v2H1z\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M6 17h12v3H6z\",\n  opacity: \".3\"\n}, \"2\")], 'InsertPageBreakTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE,mBAAmB;EACtBC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaF,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE,eAAe;EAClBC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}