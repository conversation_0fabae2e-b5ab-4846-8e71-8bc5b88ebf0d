{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"unnit go ovtta sekundda\",\n    other: \"unnit go {{count}} sekundda\"\n  },\n  xSeconds: {\n    one: \"sekundda\",\n    other: \"{{count}} sekundda\"\n  },\n  halfAMinute: \"bealle minuhta\",\n  lessThanXMinutes: {\n    one: \"unnit go bealle minuhta\",\n    other: \"unnit go {{count}} minuhta\"\n  },\n  xMinutes: {\n    one: \"minuhta\",\n    other: \"{{count}} minuhta\"\n  },\n  aboutXHours: {\n    one: \"sullii ovtta diimmu\",\n    other: \"sullii {{count}} diimmu\"\n  },\n  xHours: {\n    one: \"diimmu\",\n    other: \"{{count}} diimmu\"\n  },\n  xDays: {\n    one: \"beaivvi\",\n    other: \"{{count}} beaivvi\"\n  },\n  aboutXWeeks: {\n    one: \"sullii ovtta vahku\",\n    other: \"sullii {{count}} vahku\"\n  },\n  xWeeks: {\n    one: \"vahku\",\n    other: \"{{count}} vahku\"\n  },\n  aboutXMonths: {\n    one: \"sullii ovtta mánu\",\n    other: \"sullii {{count}} mánu\"\n  },\n  xMonths: {\n    one: \"mánu\",\n    other: \"{{count}} mánu\"\n  },\n  aboutXYears: {\n    one: \"sullii ovtta jagi\",\n    other: \"sullii {{count}} jagi\"\n  },\n  xYears: {\n    one: \"jagi\",\n    other: \"{{count}} jagi\"\n  },\n  overXYears: {\n    one: \"guhkit go jagi\",\n    other: \"guhkit go {{count}} jagi\"\n  },\n  almostXYears: {\n    one: \"measta jagi\",\n    other: \"measta {{count}} jagi\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"geahčen \" + result;\n    } else {\n      return result + \" áigi\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/se/_lib/formatDistance.mjs"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"unnit go ovtta sekundda\",\n    other: \"unnit go {{count}} sekundda\",\n  },\n\n  xSeconds: {\n    one: \"sekundda\",\n    other: \"{{count}} sekundda\",\n  },\n\n  halfAMinute: \"bealle minuhta\",\n\n  lessThanXMinutes: {\n    one: \"unnit go bealle minuhta\",\n    other: \"unnit go {{count}} minuhta\",\n  },\n\n  xMinutes: {\n    one: \"minuhta\",\n    other: \"{{count}} minuhta\",\n  },\n\n  aboutXHours: {\n    one: \"sullii ovtta diimmu\",\n    other: \"sullii {{count}} diimmu\",\n  },\n\n  xHours: {\n    one: \"diimmu\",\n    other: \"{{count}} diimmu\",\n  },\n\n  xDays: {\n    one: \"beaivvi\",\n    other: \"{{count}} beaivvi\",\n  },\n\n  aboutXWeeks: {\n    one: \"sullii ovtta vahku\",\n    other: \"sullii {{count}} vahku\",\n  },\n\n  xWeeks: {\n    one: \"vahku\",\n    other: \"{{count}} vahku\",\n  },\n\n  aboutXMonths: {\n    one: \"sullii ovtta mánu\",\n    other: \"sullii {{count}} mánu\",\n  },\n\n  xMonths: {\n    one: \"mánu\",\n    other: \"{{count}} mánu\",\n  },\n\n  aboutXYears: {\n    one: \"sullii ovtta jagi\",\n    other: \"sullii {{count}} jagi\",\n  },\n\n  xYears: {\n    one: \"jagi\",\n    other: \"{{count}} jagi\",\n  },\n\n  overXYears: {\n    one: \"guhkit go jagi\",\n    other: \"guhkit go {{count}} jagi\",\n  },\n\n  almostXYears: {\n    one: \"measta jagi\",\n    other: \"measta {{count}} jagi\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"geahčen \" + result;\n    } else {\n      return result + \" áigi\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,yBAAyB;IAC9BC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,gBAAgB;EAE7BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,yBAAyB;IAC9BC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,EAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,UAAU,GAAGL,MAAM;IAC5B,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,OAAO;IACzB;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}