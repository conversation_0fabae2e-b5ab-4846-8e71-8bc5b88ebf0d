{"ast": null, "code": "import * as React from 'react';\nimport { validateDate } from '../internals/utils/validation/validateDate';\nimport { useLocalizationContext } from '../internals/hooks/useUtils';\nexport const useIsDateDisabled = ({\n  shouldDisableDate,\n  shouldDisableMonth,\n  shouldDisableYear,\n  minDate,\n  maxDate,\n  disableFuture,\n  disablePast,\n  timezone\n}) => {\n  const adapter = useLocalizationContext();\n  return React.useCallback(day => validateDate({\n    adapter,\n    value: day,\n    props: {\n      shouldDisableDate,\n      shouldDisableMonth,\n      shouldDisableYear,\n      minDate,\n      maxDate,\n      disableFuture,\n      disablePast,\n      timezone\n    }\n  }) !== null, [adapter, shouldDisableDate, shouldDisableMonth, shouldDisableYear, minDate, maxDate, disableFuture, disablePast, timezone]);\n};", "map": {"version": 3, "names": ["React", "validateDate", "useLocalizationContext", "useIsDateDisabled", "shouldDisableDate", "shouldDisableMonth", "shouldDisableYear", "minDate", "maxDate", "disableFuture", "disablePast", "timezone", "adapter", "useCallback", "day", "value", "props"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/DateCalendar/useIsDateDisabled.js"], "sourcesContent": ["import * as React from 'react';\nimport { validateDate } from '../internals/utils/validation/validateDate';\nimport { useLocalizationContext } from '../internals/hooks/useUtils';\nexport const useIsDateDisabled = ({\n  shouldDisableDate,\n  shouldDisableMonth,\n  shouldDisableYear,\n  minDate,\n  maxDate,\n  disableFuture,\n  disablePast,\n  timezone\n}) => {\n  const adapter = useLocalizationContext();\n  return React.useCallback(day => validateDate({\n    adapter,\n    value: day,\n    props: {\n      shouldDisableDate,\n      shouldDisableMonth,\n      shouldDisableYear,\n      minDate,\n      maxDate,\n      disableFuture,\n      disablePast,\n      timezone\n    }\n  }) !== null, [adapter, shouldDisableDate, shouldDisableMonth, shouldDisableYear, minDate, maxDate, disableFuture, disablePast, timezone]);\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,4CAA4C;AACzE,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,OAAO,MAAMC,iBAAiB,GAAGA,CAAC;EAChCC,iBAAiB;EACjBC,kBAAkB;EAClBC,iBAAiB;EACjBC,OAAO;EACPC,OAAO;EACPC,aAAa;EACbC,WAAW;EACXC;AACF,CAAC,KAAK;EACJ,MAAMC,OAAO,GAAGV,sBAAsB,CAAC,CAAC;EACxC,OAAOF,KAAK,CAACa,WAAW,CAACC,GAAG,IAAIb,YAAY,CAAC;IAC3CW,OAAO;IACPG,KAAK,EAAED,GAAG;IACVE,KAAK,EAAE;MACLZ,iBAAiB;MACjBC,kBAAkB;MAClBC,iBAAiB;MACjBC,OAAO;MACPC,OAAO;MACPC,aAAa;MACbC,WAAW;MACXC;IACF;EACF,CAAC,CAAC,KAAK,IAAI,EAAE,CAACC,OAAO,EAAER,iBAAiB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,OAAO,EAAEC,aAAa,EAAEC,WAAW,EAAEC,QAAQ,CAAC,CAAC;AAC3I,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}