{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\MainMenu.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { List, ListItem, ListItemIcon, ListItemText, Divider, Box, Typography, Collapse, ListItemButton } from '@mui/material';\nimport { Home as HomeIcon, AdminPanelSettings as AdminIcon, Construction as ConstructionIcon, Cable as CableIcon, Description as ReportIcon, ExpandLess, ExpandMore, ViewList as ViewListIcon, Engineering as EngineeringIcon, Inventory as InventoryIcon, TableChart as TableChartIcon, Assessment as AssessmentIcon, VerifiedUser as VerifiedUserIcon, ShoppingCart as ShoppingCartIcon, Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MainMenu = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    isImpersonating,\n    impersonatedUser\n  } = useAuth();\n\n  // Funzione di utilità per creare ListItemText con dimensione del testo ridotta\n  const createListItemText = (primary, level = 1) => {\n    // Dimensioni del testo in base al livello del menu\n    const fontSize = level === 1 ? '0.9rem' : level === 2 ? '0.85rem' : '0.8rem';\n    return /*#__PURE__*/_jsxDEV(ListItemText, {\n      primary: primary,\n      primaryTypographyProps: {\n        fontSize\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Stati per i menu a cascata\n  const [openCaviMenu, setOpenCaviMenu] = useState(false);\n  const [openCantieriMenu, setOpenCantieriMenu] = useState(false);\n  const [openAdminMenu, setOpenAdminMenu] = useState(false);\n  const [openPosaMenu, setOpenPosaMenu] = useState(false);\n  const [openParcoMenu, setOpenParcoMenu] = useState(false);\n  const [openExcelMenu, setOpenExcelMenu] = useState(false);\n  const [openReportMenu, setOpenReportMenu] = useState(false);\n  const [openCertificazioneMenu, setOpenCertificazioneMenu] = useState(false);\n  const [openComandeMenu, setOpenComandeMenu] = useState(false);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Verifica se un percorso è attivo\n  const isActive = path => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = path => {\n    return location.pathname.startsWith(path);\n  };\n\n  // Gestisce l'apertura/chiusura dei menu a cascata\n  const handleToggleCaviMenu = () => {\n    setOpenCaviMenu(!openCaviMenu);\n  };\n  const handleToggleCantieriMenu = () => {\n    setOpenCantieriMenu(!openCantieriMenu);\n  };\n  const handleToggleAdminMenu = () => {\n    setOpenAdminMenu(!openAdminMenu);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = path => {\n    console.log('Navigazione a:', path, 'isImpersonating:', isImpersonating, 'user:', user);\n    // Se l'utente è un amministratore che sta impersonando un utente e sta cliccando su Home,\n    // reindirizza al menu amministratore invece che alla home generica\n    if (path === '/dashboard' && isImpersonating) {\n      console.log('Utente impersonato, reindirizzamento al menu amministratore');\n      navigate('/dashboard/admin');\n    } else {\n      navigate(path);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(List, {\n    dense: true,\n    children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n      selected: isImpersonating ? isActive('/dashboard/admin') : isActive('/dashboard'),\n      onClick: () => navigateTo('/dashboard'),\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), createListItemText(isImpersonating ? \"Torna al Menu Admin\" : \"Home\", 1)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), (user === null || user === void 0 ? void 0 : user.role) === 'owner' && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n        onClick: handleToggleAdminMenu,\n        selected: isPartOfActive('/dashboard/admin'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(AdminIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 13\n        }, this), createListItemText(\"Amministrazione\", 1), openAdminMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 30\n        }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 47\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n        in: openAdminMenu,\n        timeout: \"auto\",\n        unmountOnExit: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          component: \"div\",\n          disablePadding: true,\n          children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            selected: isActive('/dashboard/admin'),\n            onClick: () => navigateTo('/dashboard/admin'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(AdminIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this), createListItemText(\"Pannello Admin\", 2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), ((user === null || user === void 0 ? void 0 : user.role) !== 'owner' || (user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 11\n      }, this), isImpersonating && impersonatedUser && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2,\n          bgcolor: 'rgba(255, 165, 0, 0.1)',\n          borderLeft: '4px solid orange'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          color: \"textSecondary\",\n          children: \"Accesso come utente:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          fontWeight: \"bold\",\n          children: impersonatedUser.username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n        onClick: handleToggleCantieriMenu,\n        selected: isPartOfActive('/dashboard/cantieri'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(ConstructionIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this), createListItemText(isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\", 1), openCantieriMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 33\n        }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 50\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n        in: openCantieriMenu,\n        timeout: \"auto\",\n        unmountOnExit: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          component: \"div\",\n          disablePadding: true,\n          children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            selected: isActive('/dashboard/cantieri'),\n            onClick: () => navigateTo('/dashboard/cantieri'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(ViewListIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Lista Cantieri\",\n              primaryTypographyProps: {\n                fontSize: '0.85rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n              sx: {\n                pl: 2\n              },\n              selected: isActive(`/dashboard/cantieri/${selectedCantiereId}`),\n              onClick: () => navigateTo(`/dashboard/cantieri/${selectedCantiereId}`),\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                children: /*#__PURE__*/_jsxDEV(ConstructionIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `Cantiere: ${selectedCantiereName || selectedCantiereId}`,\n                primaryTypographyProps: {\n                  fontSize: '0.85rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 19\n            }, this)\n          }, void 0, false)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(ListItemButton, {\n        onClick: handleToggleCaviMenu,\n        selected: isPartOfActive('/dashboard/cavi'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: `Gestione Cavi (${selectedCantiereName || selectedCantiereId})`,\n          primaryTypographyProps: {\n            fontSize: '0.9rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 15\n        }, this), openCaviMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 31\n        }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 48\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 13\n      }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(Collapse, {\n        in: openCaviMenu,\n        timeout: \"auto\",\n        unmountOnExit: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          component: \"div\",\n          disablePadding: true,\n          children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            selected: isActive('/dashboard/cavi/visualizza'),\n            onClick: () => navigateTo('/dashboard/cavi/visualizza'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(ViewListIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Visualizza Cavi\",\n              primaryTypographyProps: {\n                fontSize: '0.85rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            onClick: () => setOpenPosaMenu(!openPosaMenu),\n            selected: isPartOfActive('/dashboard/cavi/posa'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EngineeringIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Posa e Collegamenti\",\n              primaryTypographyProps: {\n                fontSize: '0.85rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 19\n            }, this), openPosaMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 35\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 52\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openPosaMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/posa/inserisci-metri'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/inserisci-metri'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(CableIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Inserisci metri posati\",\n                  primaryTypographyProps: {\n                    fontSize: '0.8rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/posa/modifica-cavo'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/modifica-cavo'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Modifica cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/posa/aggiungi-cavo'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/aggiungi-cavo'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Aggiungi nuovo cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/posa/elimina-cavo'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/elimina-cavo'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Elimina cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/posa/modifica-bobina'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/modifica-bobina'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Modifica bobina cavo posato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/posa/collegamenti'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/collegamenti'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(CableIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Gestisci collegamenti cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            onClick: () => setOpenParcoMenu(!openParcoMenu),\n            selected: isPartOfActive('/dashboard/cavi/parco'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(InventoryIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Parco Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 19\n            }, this), openParcoMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 53\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openParcoMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/parco/visualizza'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/visualizza'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(ViewListIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Visualizza Bobine\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/parco/crea'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/crea'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Crea Nuova Bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/parco/modifica'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/modifica'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Modifica Bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/parco/elimina'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/elimina'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 371,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Elimina Bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/parco/storico'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/storico'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Storico Utilizzo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            onClick: () => setOpenExcelMenu(!openExcelMenu),\n            selected: isPartOfActive('/dashboard/cavi/excel'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(TableChartIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Gestione Excel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 19\n            }, this), openExcelMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 53\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openExcelMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/excel/importa-cavi'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/importa-cavi'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Importa cavi da Excel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/excel/importa-bobine'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/importa-bobine'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 421,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Importa parco bobine\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/excel/template-cavi'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/template-cavi'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(TableChartIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Template Excel per cavi\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/excel/template-bobine'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/template-bobine'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(TableChartIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Template Excel per bobine\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/excel/esporta-cavi'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/esporta-cavi'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(TableChartIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Esporta cavi in Excel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/excel/esporta-bobine'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/esporta-bobine'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(TableChartIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Esporta bobine in Excel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            onClick: () => setOpenReportMenu(!openReportMenu),\n            selected: isPartOfActive('/dashboard/cavi/report'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Report\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 19\n            }, this), openReportMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 37\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 54\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openReportMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/report/avanzamento'),\n                onClick: () => navigateTo('/dashboard/cavi/report/avanzamento'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Report Avanzamento\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/report/boq'),\n                onClick: () => navigateTo('/dashboard/cavi/report/boq'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Bill of Quantities\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/report/utilizzo-bobine'),\n                onClick: () => navigateTo('/dashboard/cavi/report/utilizzo-bobine'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Report Utilizzo Bobine\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/report/posa-periodo'),\n                onClick: () => navigateTo('/dashboard/cavi/report/posa-periodo'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 526,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Report Posa per Periodo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            onClick: () => setOpenCertificazioneMenu(!openCertificazioneMenu),\n            selected: isPartOfActive('/dashboard/cavi/certificazione'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(VerifiedUserIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Certificazione Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 19\n            }, this), openCertificazioneMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 45\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 62\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openCertificazioneMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/visualizza'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/visualizza'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(ViewListIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Visualizza certificazioni\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/filtra'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/filtra'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(ViewListIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Filtra per cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/crea'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/crea'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 576,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Crea certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/dettagli'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/dettagli'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(ViewListIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Dettagli certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/pdf'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/pdf'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Genera PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/elimina'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/elimina'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Elimina certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/strumenti'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/strumenti'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 620,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Gestione strumenti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            onClick: () => setOpenComandeMenu(!openComandeMenu),\n            selected: isPartOfActive('/dashboard/cavi/comande'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(ShoppingCartIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Gestione Comande\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 19\n            }, this), openComandeMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 55\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openComandeMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/visualizza'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/visualizza'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(ViewListIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Visualizza comande\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/crea'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/crea'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 659,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Crea nuova comanda\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 661,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/modifica'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/modifica'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 670,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Modifica comanda\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 672,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/elimina'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/elimina'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 681,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 680,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Elimina comanda\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 683,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/stampa'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/stampa'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 692,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 691,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Stampa comanda\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 694,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/assegna'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/assegna'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 703,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 702,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Assegna comanda a cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n};\n_s(MainMenu, \"Udz8U7QCjEP5k56hCgUxMe5hqis=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = MainMenu;\nexport default MainMenu;\nvar _c;\n$RefreshReg$(_c, \"MainMenu\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "Box", "Typography", "Collapse", "ListItemButton", "Home", "HomeIcon", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "ExpandLess", "ExpandMore", "ViewList", "ViewListIcon", "Engineering", "EngineeringIcon", "Inventory", "InventoryIcon", "Table<PERSON>hart", "TableChartIcon", "Assessment", "AssessmentIcon", "VerifiedUser", "VerifiedUserIcon", "ShoppingCart", "ShoppingCartIcon", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MainMenu", "_s", "navigate", "location", "user", "isImpersonating", "impersonated<PERSON><PERSON>", "createListItemText", "primary", "level", "fontSize", "primaryTypographyProps", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "openCaviMenu", "setOpenCaviMenu", "openCantieriMenu", "setOpenCantieriMenu", "openAdminMenu", "setOpenAdminMenu", "openPosaMenu", "setOpenPosaMenu", "openParcoMenu", "setOpenParcoMenu", "openExcelMenu", "setOpenExcelMenu", "openReportMenu", "setOpenReportMenu", "openCertificazioneMenu", "setOpenCertificazioneMenu", "openComandeMenu", "setOpenComandeMenu", "selectedCantiereId", "localStorage", "getItem", "selectedCantiereName", "isActive", "path", "pathname", "isPartOfActive", "startsWith", "handleToggleCaviMenu", "handleToggleCantieriMenu", "handleToggleAdminMenu", "navigateTo", "console", "log", "dense", "children", "selected", "onClick", "role", "in", "timeout", "unmountOnExit", "component", "disablePadding", "sx", "pl", "p", "bgcolor", "borderLeft", "variant", "color", "fontWeight", "username", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/MainMenu.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Box,\n  Typography,\n  Collapse,\n  ListItemButton\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon,\n  ExpandLess,\n  ExpandMore,\n  ViewList as ViewListIcon,\n  Engineering as EngineeringIcon,\n  Inventory as InventoryIcon,\n  TableChart as TableChartIcon,\n  Assessment as AssessmentIcon,\n  VerifiedUser as VerifiedUserIcon,\n  ShoppingCart as ShoppingCartIcon,\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\n\nconst MainMenu = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, isImpersonating, impersonatedUser } = useAuth();\n\n  // Funzione di utilità per creare ListItemText con dimensione del testo ridotta\n  const createListItemText = (primary, level = 1) => {\n    // Dimensioni del testo in base al livello del menu\n    const fontSize = level === 1 ? '0.9rem' : level === 2 ? '0.85rem' : '0.8rem';\n    return (\n      <ListItemText\n        primary={primary}\n        primaryTypographyProps={{ fontSize }}\n      />\n    );\n  };\n\n  // Stati per i menu a cascata\n  const [openCaviMenu, setOpenCaviMenu] = useState(false);\n  const [openCantieriMenu, setOpenCantieriMenu] = useState(false);\n  const [openAdminMenu, setOpenAdminMenu] = useState(false);\n  const [openPosaMenu, setOpenPosaMenu] = useState(false);\n  const [openParcoMenu, setOpenParcoMenu] = useState(false);\n  const [openExcelMenu, setOpenExcelMenu] = useState(false);\n  const [openReportMenu, setOpenReportMenu] = useState(false);\n  const [openCertificazioneMenu, setOpenCertificazioneMenu] = useState(false);\n  const [openComandeMenu, setOpenComandeMenu] = useState(false);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Verifica se un percorso è attivo\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = (path) => {\n    return location.pathname.startsWith(path);\n  };\n\n  // Gestisce l'apertura/chiusura dei menu a cascata\n  const handleToggleCaviMenu = () => {\n    setOpenCaviMenu(!openCaviMenu);\n  };\n\n  const handleToggleCantieriMenu = () => {\n    setOpenCantieriMenu(!openCantieriMenu);\n  };\n\n  const handleToggleAdminMenu = () => {\n    setOpenAdminMenu(!openAdminMenu);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    console.log('Navigazione a:', path, 'isImpersonating:', isImpersonating, 'user:', user);\n    // Se l'utente è un amministratore che sta impersonando un utente e sta cliccando su Home,\n    // reindirizza al menu amministratore invece che alla home generica\n    if (path === '/dashboard' && isImpersonating) {\n      console.log('Utente impersonato, reindirizzamento al menu amministratore');\n      navigate('/dashboard/admin');\n    } else {\n      navigate(path);\n    }\n  };\n\n  return (\n    <List dense>\n      {/* Home - Se l'utente è un amministratore che sta impersonando un utente, mostra \"Torna al Menu Admin\" */}\n      <ListItemButton\n        selected={isImpersonating ? isActive('/dashboard/admin') : isActive('/dashboard')}\n        onClick={() => navigateTo('/dashboard')}\n      >\n        <ListItemIcon>\n          <HomeIcon />\n        </ListItemIcon>\n        {createListItemText(isImpersonating ? \"Torna al Menu Admin\" : \"Home\", 1)}\n      </ListItemButton>\n\n      {/* Menu Amministratore (solo per admin) */}\n      {user?.role === 'owner' && (\n        <>\n          <Divider />\n          <ListItemButton\n            onClick={handleToggleAdminMenu}\n            selected={isPartOfActive('/dashboard/admin')}\n          >\n            <ListItemIcon>\n              <AdminIcon />\n            </ListItemIcon>\n            {createListItemText(\"Amministrazione\", 1)}\n            {openAdminMenu ? <ExpandLess /> : <ExpandMore />}\n          </ListItemButton>\n          <Collapse in={openAdminMenu} timeout=\"auto\" unmountOnExit>\n            <List component=\"div\" disablePadding>\n              <ListItemButton\n                sx={{ pl: 2 }}\n                selected={isActive('/dashboard/admin')}\n                onClick={() => navigateTo('/dashboard/admin')}\n              >\n                <ListItemIcon>\n                  <AdminIcon />\n                </ListItemIcon>\n                {createListItemText(\"Pannello Admin\", 2)}\n              </ListItemButton>\n              {/* Altri sottomenu admin possono essere aggiunti qui */}\n            </List>\n          </Collapse>\n        </>\n      )}\n\n      {/* Menu per utenti standard e cantieri */}\n      {/* Mostra per utenti standard/cantiere o per admin che sta impersonando un utente */}\n      {(user?.role !== 'owner' || (user?.role === 'owner' && isImpersonating && impersonatedUser)) && (\n        <>\n          <Divider />\n          {isImpersonating && impersonatedUser && (\n            <Box sx={{ p: 2, bgcolor: 'rgba(255, 165, 0, 0.1)', borderLeft: '4px solid orange' }}>\n              <Typography variant=\"subtitle2\" color=\"textSecondary\">\n                Accesso come utente:\n              </Typography>\n              <Typography variant=\"body2\" fontWeight=\"bold\">\n                {impersonatedUser.username}\n              </Typography>\n            </Box>\n          )}\n\n          {/* Menu Cantieri con sottomenu */}\n          <ListItemButton\n            onClick={handleToggleCantieriMenu}\n            selected={isPartOfActive('/dashboard/cantieri')}\n          >\n            <ListItemIcon>\n              <ConstructionIcon />\n            </ListItemIcon>\n            {createListItemText(isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\", 1)}\n            {openCantieriMenu ? <ExpandLess /> : <ExpandMore />}\n          </ListItemButton>\n          <Collapse in={openCantieriMenu} timeout=\"auto\" unmountOnExit>\n            <List component=\"div\" disablePadding>\n              <ListItemButton\n                sx={{ pl: 4 }}\n                selected={isActive('/dashboard/cantieri')}\n                onClick={() => navigateTo('/dashboard/cantieri')}\n              >\n                <ListItemIcon>\n                  <ViewListIcon />\n                </ListItemIcon>\n                <ListItemText primary=\"Lista Cantieri\" primaryTypographyProps={{ fontSize: '0.85rem' }} />\n              </ListItemButton>\n\n              {/* Mostra il cantiere selezionato se presente */}\n              {selectedCantiereId && (\n                <>\n                  <ListItemButton\n                    sx={{ pl: 2 }}\n                    selected={isActive(`/dashboard/cantieri/${selectedCantiereId}`)}\n                    onClick={() => navigateTo(`/dashboard/cantieri/${selectedCantiereId}`)}\n                  >\n                    <ListItemIcon>\n                      <ConstructionIcon />\n                    </ListItemIcon>\n                    <ListItemText primary={`Cantiere: ${selectedCantiereName || selectedCantiereId}`} primaryTypographyProps={{ fontSize: '0.85rem' }} />\n                  </ListItemButton>\n                </>\n              )}\n            </List>\n          </Collapse>\n\n          {/* Menu Cavi con sottomenu - visibile solo se un cantiere è selezionato */}\n          {selectedCantiereId && (\n            <ListItemButton\n              onClick={handleToggleCaviMenu}\n              selected={isPartOfActive('/dashboard/cavi')}\n            >\n              <ListItemIcon>\n                <CableIcon />\n              </ListItemIcon>\n              <ListItemText primary={`Gestione Cavi (${selectedCantiereName || selectedCantiereId})`} primaryTypographyProps={{ fontSize: '0.9rem' }} />\n              {openCaviMenu ? <ExpandLess /> : <ExpandMore />}\n            </ListItemButton>\n          )}\n\n          {selectedCantiereId && (\n            <Collapse in={openCaviMenu} timeout=\"auto\" unmountOnExit>\n              <List component=\"div\" disablePadding>\n                <ListItemButton\n                  sx={{ pl: 4 }}\n                  selected={isActive('/dashboard/cavi/visualizza')}\n                  onClick={() => navigateTo('/dashboard/cavi/visualizza')}\n                >\n                  <ListItemIcon>\n                    <ViewListIcon />\n                  </ListItemIcon>\n                  <ListItemText primary=\"Visualizza Cavi\" primaryTypographyProps={{ fontSize: '0.85rem' }} />\n                </ListItemButton>\n\n                {/* Posa e Collegamenti con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 2 }}\n                  onClick={() => setOpenPosaMenu(!openPosaMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/posa')}\n                >\n                  <ListItemIcon>\n                    <EngineeringIcon />\n                  </ListItemIcon>\n                  <ListItemText primary=\"Posa e Collegamenti\" primaryTypographyProps={{ fontSize: '0.85rem' }} />\n                  {openPosaMenu ? <ExpandLess /> : <ExpandMore />}\n                </ListItemButton>\n\n                <Collapse in={openPosaMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/posa/inserisci-metri')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/inserisci-metri')}\n                    >\n                      <ListItemIcon>\n                        <CableIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Inserisci metri posati\" primaryTypographyProps={{ fontSize: '0.8rem' }} />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/posa/modifica-cavo')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/modifica-cavo')}\n                    >\n                      <ListItemIcon>\n                        <EditIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Modifica cavo\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/posa/aggiungi-cavo')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/aggiungi-cavo')}\n                    >\n                      <ListItemIcon>\n                        <AddIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Aggiungi nuovo cavo\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/posa/elimina-cavo')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/elimina-cavo')}\n                    >\n                      <ListItemIcon>\n                        <DeleteIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Elimina cavo\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/posa/modifica-bobina')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/modifica-bobina')}\n                    >\n                      <ListItemIcon>\n                        <EditIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Modifica bobina cavo posato\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/posa/collegamenti')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/collegamenti')}\n                    >\n                      <ListItemIcon>\n                        <CableIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Gestisci collegamenti cavo\" />\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Parco Cavi con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 4 }}\n                  onClick={() => setOpenParcoMenu(!openParcoMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/parco')}\n                >\n                  <ListItemIcon>\n                    <InventoryIcon />\n                  </ListItemIcon>\n                  <ListItemText primary=\"Parco Cavi\" />\n                  {openParcoMenu ? <ExpandLess /> : <ExpandMore />}\n                </ListItemButton>\n\n                <Collapse in={openParcoMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/parco/visualizza')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/visualizza')}\n                    >\n                      <ListItemIcon>\n                        <ViewListIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Visualizza Bobine\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/parco/crea')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/crea')}\n                    >\n                      <ListItemIcon>\n                        <AddIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Crea Nuova Bobina\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/parco/modifica')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/modifica')}\n                    >\n                      <ListItemIcon>\n                        <EditIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Modifica Bobina\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/parco/elimina')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/elimina')}\n                    >\n                      <ListItemIcon>\n                        <DeleteIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Elimina Bobina\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/parco/storico')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/storico')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Storico Utilizzo\" />\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Gestione Excel con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 4 }}\n                  onClick={() => setOpenExcelMenu(!openExcelMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/excel')}\n                >\n                  <ListItemIcon>\n                    <TableChartIcon />\n                  </ListItemIcon>\n                  <ListItemText primary=\"Gestione Excel\" />\n                  {openExcelMenu ? <ExpandLess /> : <ExpandMore />}\n                </ListItemButton>\n\n                <Collapse in={openExcelMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/excel/importa-cavi')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/importa-cavi')}\n                    >\n                      <ListItemIcon>\n                        <AddIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Importa cavi da Excel\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/excel/importa-bobine')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/importa-bobine')}\n                    >\n                      <ListItemIcon>\n                        <AddIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Importa parco bobine\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/excel/template-cavi')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/template-cavi')}\n                    >\n                      <ListItemIcon>\n                        <TableChartIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Template Excel per cavi\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/excel/template-bobine')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/template-bobine')}\n                    >\n                      <ListItemIcon>\n                        <TableChartIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Template Excel per bobine\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/excel/esporta-cavi')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/esporta-cavi')}\n                    >\n                      <ListItemIcon>\n                        <TableChartIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Esporta cavi in Excel\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/excel/esporta-bobine')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/esporta-bobine')}\n                    >\n                      <ListItemIcon>\n                        <TableChartIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Esporta bobine in Excel\" />\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Report con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 4 }}\n                  onClick={() => setOpenReportMenu(!openReportMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/report')}\n                >\n                  <ListItemIcon>\n                    <AssessmentIcon />\n                  </ListItemIcon>\n                  <ListItemText primary=\"Report\" />\n                  {openReportMenu ? <ExpandLess /> : <ExpandMore />}\n                </ListItemButton>\n\n                <Collapse in={openReportMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/report/avanzamento')}\n                      onClick={() => navigateTo('/dashboard/cavi/report/avanzamento')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Report Avanzamento\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/report/boq')}\n                      onClick={() => navigateTo('/dashboard/cavi/report/boq')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Bill of Quantities\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/report/utilizzo-bobine')}\n                      onClick={() => navigateTo('/dashboard/cavi/report/utilizzo-bobine')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Report Utilizzo Bobine\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/report/posa-periodo')}\n                      onClick={() => navigateTo('/dashboard/cavi/report/posa-periodo')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Report Posa per Periodo\" />\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Certificazione Cavi con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 4 }}\n                  onClick={() => setOpenCertificazioneMenu(!openCertificazioneMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/certificazione')}\n                >\n                  <ListItemIcon>\n                    <VerifiedUserIcon />\n                  </ListItemIcon>\n                  <ListItemText primary=\"Certificazione Cavi\" />\n                  {openCertificazioneMenu ? <ExpandLess /> : <ExpandMore />}\n                </ListItemButton>\n\n                <Collapse in={openCertificazioneMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/visualizza')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/visualizza')}\n                    >\n                      <ListItemIcon>\n                        <ViewListIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Visualizza certificazioni\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/filtra')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/filtra')}\n                    >\n                      <ListItemIcon>\n                        <ViewListIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Filtra per cavo\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/crea')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/crea')}\n                    >\n                      <ListItemIcon>\n                        <AddIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Crea certificazione\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/dettagli')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/dettagli')}\n                    >\n                      <ListItemIcon>\n                        <ViewListIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Dettagli certificazione\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/pdf')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/pdf')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Genera PDF\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/elimina')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/elimina')}\n                    >\n                      <ListItemIcon>\n                        <DeleteIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Elimina certificazione\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/strumenti')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/strumenti')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Gestione strumenti\" />\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Gestione Comande con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 4 }}\n                  onClick={() => setOpenComandeMenu(!openComandeMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/comande')}\n                >\n                  <ListItemIcon>\n                    <ShoppingCartIcon />\n                  </ListItemIcon>\n                  <ListItemText primary=\"Gestione Comande\" />\n                  {openComandeMenu ? <ExpandLess /> : <ExpandMore />}\n                </ListItemButton>\n\n                <Collapse in={openComandeMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/visualizza')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/visualizza')}\n                    >\n                      <ListItemIcon>\n                        <ViewListIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Visualizza comande\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/crea')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/crea')}\n                    >\n                      <ListItemIcon>\n                        <AddIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Crea nuova comanda\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/modifica')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/modifica')}\n                    >\n                      <ListItemIcon>\n                        <EditIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Modifica comanda\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/elimina')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/elimina')}\n                    >\n                      <ListItemIcon>\n                        <DeleteIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Elimina comanda\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/stampa')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/stampa')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Stampa comanda\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/assegna')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/assegna')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Assegna comanda a cavo\" />\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n              </List>\n            </Collapse>\n          )}\n        </>\n      )}\n    </List>\n  );\n};\n\nexport default MainMenu;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,UAAU,EACVC,QAAQ,EACRC,cAAc,QACT,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,kBAAkB,IAAIC,SAAS,EAC/BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,EACzBC,UAAU,EACVC,UAAU,EACVC,QAAQ,IAAIC,YAAY,EACxBC,WAAW,IAAIC,eAAe,EAC9BC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,YAAY,IAAIC,gBAAgB,EAChCC,YAAY,IAAIC,gBAAgB,EAChCC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGlD,WAAW,CAAC,CAAC;EAC9B,MAAMmD,QAAQ,GAAGlD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmD,IAAI;IAAEC,eAAe;IAAEC;EAAiB,CAAC,GAAGX,OAAO,CAAC,CAAC;;EAE7D;EACA,MAAMY,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,KAAK,GAAG,CAAC,KAAK;IACjD;IACA,MAAMC,QAAQ,GAAGD,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,QAAQ;IAC5E,oBACEZ,OAAA,CAACxC,YAAY;MACXmD,OAAO,EAAEA,OAAQ;MACjBG,sBAAsB,EAAE;QAAED;MAAS;IAAE;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAEN,CAAC;;EAED;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACqE,aAAa,EAAEC,gBAAgB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuE,YAAY,EAAEC,eAAe,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyE,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2E,aAAa,EAAEC,gBAAgB,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6E,cAAc,EAAEC,iBAAiB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC+E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACiF,eAAe,EAAEC,kBAAkB,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAMmF,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EACrE,MAAMC,oBAAoB,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEzE;EACA,MAAME,QAAQ,GAAIC,IAAI,IAAK;IACzB,OAAOpC,QAAQ,CAACqC,QAAQ,KAAKD,IAAI;EACnC,CAAC;;EAED;EACA,MAAME,cAAc,GAAIF,IAAI,IAAK;IAC/B,OAAOpC,QAAQ,CAACqC,QAAQ,CAACE,UAAU,CAACH,IAAI,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IACjC1B,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,MAAM4B,wBAAwB,GAAGA,CAAA,KAAM;IACrCzB,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;EACxC,CAAC;EAED,MAAM2B,qBAAqB,GAAGA,CAAA,KAAM;IAClCxB,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAM0B,UAAU,GAAIP,IAAI,IAAK;IAC3BQ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAET,IAAI,EAAE,kBAAkB,EAAElC,eAAe,EAAE,OAAO,EAAED,IAAI,CAAC;IACvF;IACA;IACA,IAAImC,IAAI,KAAK,YAAY,IAAIlC,eAAe,EAAE;MAC5C0C,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1E9C,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM;MACLA,QAAQ,CAACqC,IAAI,CAAC;IAChB;EACF,CAAC;EAED,oBACE1C,OAAA,CAAC3C,IAAI;IAAC+F,KAAK;IAAAC,QAAA,gBAETrD,OAAA,CAACnC,cAAc;MACbyF,QAAQ,EAAE9C,eAAe,GAAGiC,QAAQ,CAAC,kBAAkB,CAAC,GAAGA,QAAQ,CAAC,YAAY,CAAE;MAClFc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,YAAY,CAAE;MAAAI,QAAA,gBAExCrD,OAAA,CAACzC,YAAY;QAAA8F,QAAA,eACXrD,OAAA,CAACjC,QAAQ;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,EACdR,kBAAkB,CAACF,eAAe,GAAG,qBAAqB,GAAG,MAAM,EAAE,CAAC,CAAC;IAAA;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,EAGhB,CAAAX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,IAAI,MAAK,OAAO,iBACrBxD,OAAA,CAAAE,SAAA;MAAAmD,QAAA,gBACErD,OAAA,CAACvC,OAAO;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXlB,OAAA,CAACnC,cAAc;QACb0F,OAAO,EAAEP,qBAAsB;QAC/BM,QAAQ,EAAEV,cAAc,CAAC,kBAAkB,CAAE;QAAAS,QAAA,gBAE7CrD,OAAA,CAACzC,YAAY;UAAA8F,QAAA,eACXrD,OAAA,CAAC/B,SAAS;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACdR,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,CAAC,EACxCa,aAAa,gBAAGvB,OAAA,CAACxB,UAAU;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGlB,OAAA,CAACvB,UAAU;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACjBlB,OAAA,CAACpC,QAAQ;QAAC6F,EAAE,EAAElC,aAAc;QAACmC,OAAO,EAAC,MAAM;QAACC,aAAa;QAAAN,QAAA,eACvDrD,OAAA,CAAC3C,IAAI;UAACuG,SAAS,EAAC,KAAK;UAACC,cAAc;UAAAR,QAAA,eAClCrD,OAAA,CAACnC,cAAc;YACbiG,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdT,QAAQ,EAAEb,QAAQ,CAAC,kBAAkB,CAAE;YACvCc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,kBAAkB,CAAE;YAAAI,QAAA,gBAE9CrD,OAAA,CAACzC,YAAY;cAAA8F,QAAA,eACXrD,OAAA,CAAC/B,SAAS;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,EACdR,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,CAAC;UAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA,eACX,CACH,EAIA,CAAC,CAAAX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,IAAI,MAAK,OAAO,IAAK,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,IAAI,MAAK,OAAO,IAAIhD,eAAe,IAAIC,gBAAiB,kBACzFT,OAAA,CAAAE,SAAA;MAAAmD,QAAA,gBACErD,OAAA,CAACvC,OAAO;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACVV,eAAe,IAAIC,gBAAgB,iBAClCT,OAAA,CAACtC,GAAG;QAACoG,EAAE,EAAE;UAAEE,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE,wBAAwB;UAAEC,UAAU,EAAE;QAAmB,CAAE;QAAAb,QAAA,gBACnFrD,OAAA,CAACrC,UAAU;UAACwG,OAAO,EAAC,WAAW;UAACC,KAAK,EAAC,eAAe;UAAAf,QAAA,EAAC;QAEtD;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblB,OAAA,CAACrC,UAAU;UAACwG,OAAO,EAAC,OAAO;UAACE,UAAU,EAAC,MAAM;UAAAhB,QAAA,EAC1C5C,gBAAgB,CAAC6D;QAAQ;UAAAvD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN,eAGDlB,OAAA,CAACnC,cAAc;QACb0F,OAAO,EAAER,wBAAyB;QAClCO,QAAQ,EAAEV,cAAc,CAAC,qBAAqB,CAAE;QAAAS,QAAA,gBAEhDrD,OAAA,CAACzC,YAAY;UAAA8F,QAAA,eACXrD,OAAA,CAAC7B,gBAAgB;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EACdR,kBAAkB,CAACF,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAAC6D,QAAQ,EAAE,GAAG,iBAAiB,EAAE,CAAC,CAAC,EAC3HjD,gBAAgB,gBAAGrB,OAAA,CAACxB,UAAU;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGlB,OAAA,CAACvB,UAAU;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACjBlB,OAAA,CAACpC,QAAQ;QAAC6F,EAAE,EAAEpC,gBAAiB;QAACqC,OAAO,EAAC,MAAM;QAACC,aAAa;QAAAN,QAAA,eAC1DrD,OAAA,CAAC3C,IAAI;UAACuG,SAAS,EAAC,KAAK;UAACC,cAAc;UAAAR,QAAA,gBAClCrD,OAAA,CAACnC,cAAc;YACbiG,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdT,QAAQ,EAAEb,QAAQ,CAAC,qBAAqB,CAAE;YAC1Cc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,qBAAqB,CAAE;YAAAI,QAAA,gBAEjDrD,OAAA,CAACzC,YAAY;cAAA8F,QAAA,eACXrD,OAAA,CAACrB,YAAY;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACflB,OAAA,CAACxC,YAAY;cAACmD,OAAO,EAAC,gBAAgB;cAACG,sBAAsB,EAAE;gBAAED,QAAQ,EAAE;cAAU;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC,EAGhBmB,kBAAkB,iBACjBrC,OAAA,CAAAE,SAAA;YAAAmD,QAAA,eACErD,OAAA,CAACnC,cAAc;cACbiG,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cACdT,QAAQ,EAAEb,QAAQ,CAAC,uBAAuBJ,kBAAkB,EAAE,CAAE;cAChEkB,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,uBAAuBZ,kBAAkB,EAAE,CAAE;cAAAgB,QAAA,gBAEvErD,OAAA,CAACzC,YAAY;gBAAA8F,QAAA,eACXrD,OAAA,CAAC7B,gBAAgB;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACflB,OAAA,CAACxC,YAAY;gBAACmD,OAAO,EAAE,aAAa6B,oBAAoB,IAAIH,kBAAkB,EAAG;gBAACvB,sBAAsB,EAAE;kBAAED,QAAQ,EAAE;gBAAU;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvH;UAAC,gBACjB,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGVmB,kBAAkB,iBACjBrC,OAAA,CAACnC,cAAc;QACb0F,OAAO,EAAET,oBAAqB;QAC9BQ,QAAQ,EAAEV,cAAc,CAAC,iBAAiB,CAAE;QAAAS,QAAA,gBAE5CrD,OAAA,CAACzC,YAAY;UAAA8F,QAAA,eACXrD,OAAA,CAAC3B,SAAS;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACflB,OAAA,CAACxC,YAAY;UAACmD,OAAO,EAAE,kBAAkB6B,oBAAoB,IAAIH,kBAAkB,GAAI;UAACvB,sBAAsB,EAAE;YAAED,QAAQ,EAAE;UAAS;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACzIC,YAAY,gBAAGnB,OAAA,CAACxB,UAAU;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGlB,OAAA,CAACvB,UAAU;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CACjB,EAEAmB,kBAAkB,iBACjBrC,OAAA,CAACpC,QAAQ;QAAC6F,EAAE,EAAEtC,YAAa;QAACuC,OAAO,EAAC,MAAM;QAACC,aAAa;QAAAN,QAAA,eACtDrD,OAAA,CAAC3C,IAAI;UAACuG,SAAS,EAAC,KAAK;UAACC,cAAc;UAAAR,QAAA,gBAClCrD,OAAA,CAACnC,cAAc;YACbiG,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdT,QAAQ,EAAEb,QAAQ,CAAC,4BAA4B,CAAE;YACjDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,4BAA4B,CAAE;YAAAI,QAAA,gBAExDrD,OAAA,CAACzC,YAAY;cAAA8F,QAAA,eACXrD,OAAA,CAACrB,YAAY;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACflB,OAAA,CAACxC,YAAY;cAACmD,OAAO,EAAC,iBAAiB;cAACG,sBAAsB,EAAE;gBAAED,QAAQ,EAAE;cAAU;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,eAGjBlB,OAAA,CAACnC,cAAc;YACbiG,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdR,OAAO,EAAEA,CAAA,KAAM7B,eAAe,CAAC,CAACD,YAAY,CAAE;YAC9C6B,QAAQ,EAAEV,cAAc,CAAC,sBAAsB,CAAE;YAAAS,QAAA,gBAEjDrD,OAAA,CAACzC,YAAY;cAAA8F,QAAA,eACXrD,OAAA,CAACnB,eAAe;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACflB,OAAA,CAACxC,YAAY;cAACmD,OAAO,EAAC,qBAAqB;cAACG,sBAAsB,EAAE;gBAAED,QAAQ,EAAE;cAAU;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC9FO,YAAY,gBAAGzB,OAAA,CAACxB,UAAU;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlB,OAAA,CAACvB,UAAU;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eAEjBlB,OAAA,CAACpC,QAAQ;YAAC6F,EAAE,EAAEhC,YAAa;YAACiC,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAN,QAAA,eACtDrD,OAAA,CAAC3C,IAAI;cAACuG,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAR,QAAA,gBAClCrD,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,sCAAsC,CAAE;gBAC3Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,sCAAsC,CAAE;gBAAAI,QAAA,gBAElErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAAC3B,SAAS;oBAACwC,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC,wBAAwB;kBAACG,sBAAsB,EAAE;oBAAED,QAAQ,EAAE;kBAAS;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,oCAAoC,CAAE;gBACzDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,oCAAoC,CAAE;gBAAAI,QAAA,gBAEhErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACL,QAAQ;oBAACkB,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAe;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,oCAAoC,CAAE;gBACzDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,oCAAoC,CAAE;gBAAAI,QAAA,gBAEhErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACP,OAAO;oBAACoB,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAqB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,mCAAmC,CAAE;gBACxDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,mCAAmC,CAAE;gBAAAI,QAAA,gBAE/DrD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACH,UAAU;oBAACgB,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAc;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,sCAAsC,CAAE;gBAC3Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,sCAAsC,CAAE;gBAAAI,QAAA,gBAElErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACL,QAAQ;oBAACkB,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAA6B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,mCAAmC,CAAE;gBACxDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,mCAAmC,CAAE;gBAAAI,QAAA,gBAE/DrD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAAC3B,SAAS;oBAACwC,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAA4B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXlB,OAAA,CAACnC,cAAc;YACbiG,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdR,OAAO,EAAEA,CAAA,KAAM3B,gBAAgB,CAAC,CAACD,aAAa,CAAE;YAChD2B,QAAQ,EAAEV,cAAc,CAAC,uBAAuB,CAAE;YAAAS,QAAA,gBAElDrD,OAAA,CAACzC,YAAY;cAAA8F,QAAA,eACXrD,OAAA,CAACjB,aAAa;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACflB,OAAA,CAACxC,YAAY;cAACmD,OAAO,EAAC;YAAY;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACpCS,aAAa,gBAAG3B,OAAA,CAACxB,UAAU;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlB,OAAA,CAACvB,UAAU;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eAEjBlB,OAAA,CAACpC,QAAQ;YAAC6F,EAAE,EAAE9B,aAAc;YAAC+B,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAN,QAAA,eACvDrD,OAAA,CAAC3C,IAAI;cAACuG,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAR,QAAA,gBAClCrD,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,kCAAkC,CAAE;gBACvDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,kCAAkC,CAAE;gBAAAI,QAAA,gBAE9DrD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACrB,YAAY;oBAACkC,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAmB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,4BAA4B,CAAE;gBACjDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,4BAA4B,CAAE;gBAAAI,QAAA,gBAExDrD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACP,OAAO;oBAACoB,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAmB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,gCAAgC,CAAE;gBACrDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,gCAAgC,CAAE;gBAAAI,QAAA,gBAE5DrD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACL,QAAQ;oBAACkB,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAiB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,+BAA+B,CAAE;gBACpDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,+BAA+B,CAAE;gBAAAI,QAAA,gBAE3DrD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACH,UAAU;oBAACgB,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAgB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,+BAA+B,CAAE;gBACpDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,+BAA+B,CAAE;gBAAAI,QAAA,gBAE3DrD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACb,cAAc;oBAAC0B,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAkB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXlB,OAAA,CAACnC,cAAc;YACbiG,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdR,OAAO,EAAEA,CAAA,KAAMzB,gBAAgB,CAAC,CAACD,aAAa,CAAE;YAChDyB,QAAQ,EAAEV,cAAc,CAAC,uBAAuB,CAAE;YAAAS,QAAA,gBAElDrD,OAAA,CAACzC,YAAY;cAAA8F,QAAA,eACXrD,OAAA,CAACf,cAAc;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACflB,OAAA,CAACxC,YAAY;cAACmD,OAAO,EAAC;YAAgB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACxCW,aAAa,gBAAG7B,OAAA,CAACxB,UAAU;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlB,OAAA,CAACvB,UAAU;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eAEjBlB,OAAA,CAACpC,QAAQ;YAAC6F,EAAE,EAAE5B,aAAc;YAAC6B,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAN,QAAA,eACvDrD,OAAA,CAAC3C,IAAI;cAACuG,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAR,QAAA,gBAClCrD,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,oCAAoC,CAAE;gBACzDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,oCAAoC,CAAE;gBAAAI,QAAA,gBAEhErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACP,OAAO;oBAACoB,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,sCAAsC,CAAE;gBAC3Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,sCAAsC,CAAE;gBAAAI,QAAA,gBAElErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACP,OAAO;oBAACoB,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAsB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,qCAAqC,CAAE;gBAC1Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,qCAAqC,CAAE;gBAAAI,QAAA,gBAEjErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACf,cAAc;oBAAC4B,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,uCAAuC,CAAE;gBAC5Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,uCAAuC,CAAE;gBAAAI,QAAA,gBAEnErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACf,cAAc;oBAAC4B,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAA2B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,oCAAoC,CAAE;gBACzDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,oCAAoC,CAAE;gBAAAI,QAAA,gBAEhErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACf,cAAc;oBAAC4B,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,sCAAsC,CAAE;gBAC3Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,sCAAsC,CAAE;gBAAAI,QAAA,gBAElErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACf,cAAc;oBAAC4B,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXlB,OAAA,CAACnC,cAAc;YACbiG,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdR,OAAO,EAAEA,CAAA,KAAMvB,iBAAiB,CAAC,CAACD,cAAc,CAAE;YAClDuB,QAAQ,EAAEV,cAAc,CAAC,wBAAwB,CAAE;YAAAS,QAAA,gBAEnDrD,OAAA,CAACzC,YAAY;cAAA8F,QAAA,eACXrD,OAAA,CAACb,cAAc;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACflB,OAAA,CAACxC,YAAY;cAACmD,OAAO,EAAC;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAChCa,cAAc,gBAAG/B,OAAA,CAACxB,UAAU;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlB,OAAA,CAACvB,UAAU;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAEjBlB,OAAA,CAACpC,QAAQ;YAAC6F,EAAE,EAAE1B,cAAe;YAAC2B,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAN,QAAA,eACxDrD,OAAA,CAAC3C,IAAI;cAACuG,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAR,QAAA,gBAClCrD,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,oCAAoC,CAAE;gBACzDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,oCAAoC,CAAE;gBAAAI,QAAA,gBAEhErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACb,cAAc;oBAAC0B,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAoB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,4BAA4B,CAAE;gBACjDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,4BAA4B,CAAE;gBAAAI,QAAA,gBAExDrD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACb,cAAc;oBAAC0B,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAoB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,wCAAwC,CAAE;gBAC7Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,wCAAwC,CAAE;gBAAAI,QAAA,gBAEpErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACb,cAAc;oBAAC0B,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,qCAAqC,CAAE;gBAC1Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,qCAAqC,CAAE;gBAAAI,QAAA,gBAEjErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACb,cAAc;oBAAC0B,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXlB,OAAA,CAACnC,cAAc;YACbiG,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdR,OAAO,EAAEA,CAAA,KAAMrB,yBAAyB,CAAC,CAACD,sBAAsB,CAAE;YAClEqB,QAAQ,EAAEV,cAAc,CAAC,gCAAgC,CAAE;YAAAS,QAAA,gBAE3DrD,OAAA,CAACzC,YAAY;cAAA8F,QAAA,eACXrD,OAAA,CAACX,gBAAgB;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACflB,OAAA,CAACxC,YAAY;cAACmD,OAAO,EAAC;YAAqB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC7Ce,sBAAsB,gBAAGjC,OAAA,CAACxB,UAAU;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlB,OAAA,CAACvB,UAAU;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAEjBlB,OAAA,CAACpC,QAAQ;YAAC6F,EAAE,EAAExB,sBAAuB;YAACyB,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAN,QAAA,eAChErD,OAAA,CAAC3C,IAAI;cAACuG,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAR,QAAA,gBAClCrD,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,2CAA2C,CAAE;gBAChEc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,2CAA2C,CAAE;gBAAAI,QAAA,gBAEvErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACrB,YAAY;oBAACkC,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAA2B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,uCAAuC,CAAE;gBAC5Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,uCAAuC,CAAE;gBAAAI,QAAA,gBAEnErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACrB,YAAY;oBAACkC,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAiB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,qCAAqC,CAAE;gBAC1Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,qCAAqC,CAAE;gBAAAI,QAAA,gBAEjErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACP,OAAO;oBAACoB,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAqB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,yCAAyC,CAAE;gBAC9Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,yCAAyC,CAAE;gBAAAI,QAAA,gBAErErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACrB,YAAY;oBAACkC,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,oCAAoC,CAAE;gBACzDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,oCAAoC,CAAE;gBAAAI,QAAA,gBAEhErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACb,cAAc;oBAAC0B,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAY;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,wCAAwC,CAAE;gBAC7Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,wCAAwC,CAAE;gBAAAI,QAAA,gBAEpErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACH,UAAU;oBAACgB,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,0CAA0C,CAAE;gBAC/Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,0CAA0C,CAAE;gBAAAI,QAAA,gBAEtErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACb,cAAc;oBAAC0B,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAoB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXlB,OAAA,CAACnC,cAAc;YACbiG,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdR,OAAO,EAAEA,CAAA,KAAMnB,kBAAkB,CAAC,CAACD,eAAe,CAAE;YACpDmB,QAAQ,EAAEV,cAAc,CAAC,yBAAyB,CAAE;YAAAS,QAAA,gBAEpDrD,OAAA,CAACzC,YAAY;cAAA8F,QAAA,eACXrD,OAAA,CAACT,gBAAgB;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACflB,OAAA,CAACxC,YAAY;cAACmD,OAAO,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC1CiB,eAAe,gBAAGnC,OAAA,CAACxB,UAAU;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlB,OAAA,CAACvB,UAAU;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eAEjBlB,OAAA,CAACpC,QAAQ;YAAC6F,EAAE,EAAEtB,eAAgB;YAACuB,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAN,QAAA,eACzDrD,OAAA,CAAC3C,IAAI;cAACuG,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAR,QAAA,gBAClCrD,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,oCAAoC,CAAE;gBACzDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,oCAAoC,CAAE;gBAAAI,QAAA,gBAEhErD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACrB,YAAY;oBAACkC,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAoB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,8BAA8B,CAAE;gBACnDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,8BAA8B,CAAE;gBAAAI,QAAA,gBAE1DrD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACP,OAAO;oBAACoB,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAoB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,kCAAkC,CAAE;gBACvDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,kCAAkC,CAAE;gBAAAI,QAAA,gBAE9DrD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACL,QAAQ;oBAACkB,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAkB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,iCAAiC,CAAE;gBACtDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,iCAAiC,CAAE;gBAAAI,QAAA,gBAE7DrD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACH,UAAU;oBAACgB,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAiB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,gCAAgC,CAAE;gBACrDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,gCAAgC,CAAE;gBAAAI,QAAA,gBAE5DrD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACb,cAAc;oBAAC0B,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAgB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eAEjBlB,OAAA,CAACnC,cAAc;gBACbiG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdT,QAAQ,EAAEb,QAAQ,CAAC,iCAAiC,CAAE;gBACtDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,iCAAiC,CAAE;gBAAAI,QAAA,gBAE7DrD,OAAA,CAACzC,YAAY;kBAAA8F,QAAA,eACXrD,OAAA,CAACb,cAAc;oBAAC0B,QAAQ,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACX;IAAA,eACD,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAACd,EAAA,CAzqBID,QAAQ;EAAA,QACKhD,WAAW,EACXC,WAAW,EACwB0C,OAAO;AAAA;AAAAyE,EAAA,GAHvDpE,QAAQ;AA2qBd,eAAeA,QAAQ;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}